const { chromium } = require('playwright');

(async () => {
  try {
    // ブラウザを起動（既存のブラウザを閉じない設定）
    const browser = await chromium.launch({
      headless: false,
      args: ['--start-maximized']
    });
    
    // 新しいコンテキストを作成
    const context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    // 新しいページを開く
    const page = await context.newPage();
    
    await page.goto('http://localhost:3000/sign-in');
    
    // ページが完全に読み込まれるのを待つ
    await page.waitForLoadState('networkidle');
    
    // メールアドレス入力欄を待つ
    await page.waitForSelector('input[type="email"]', { timeout: 30000 });
    
    // メールアドレスをゆっくり入力（人間らしく）
    await page.fill('input[type="email"]', '<EMAIL>');
    
    // 「続ける」ボタンを待つ
    await page.waitForSelector('button:has-text("続ける")', { timeout: 10000 });
    
    // 少し待ってからクリック → 遷移完了まで待機
await page.waitForTimeout(1000);
await page.click('button:has-text("続ける")');
await page.waitForURL('**/sign-in/factor-one', { timeout: 60000 });


    
    // ブラウザは開いたままにする
    // await browser.close();
    
  } catch (error) {
  }
})();
