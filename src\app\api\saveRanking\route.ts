import { NextRequest, NextResponse } from 'next/server';

// ファイルが正しく読み込まれているかを確認
console.log('🔧 [saveRanking] route.tsファイルが読み込まれました');

// GETメソッドも追加して、エンドポイントが存在することを確認
export async function GET(request: NextRequest) {
  console.log('🔍 [saveRanking] GETリクエストを受信しました');
  return NextResponse.json({
    message: 'saveRanking API is working',
    timestamp: new Date().toISOString(),
    method: 'GET'
  });
}

export async function POST(request: NextRequest) {
  console.log('🔍 [saveRanking] POSTリクエストを受信しました');
  console.log('🔍 [saveRanking] リクエストURL:', request.url);
  console.log('🔍 [saveRanking] リクエストメソッド:', request.method);
  console.log('🔍 [saveRanking] リクエストヘッダー:', Object.fromEntries(request.headers.entries()));

  try {
    const data = await request.json();
    console.log('📋 [saveRanking] 受信データ:', {
      title: data.title,
      user_ID: data.user_ID,
      categoryId: data.categoryId,
      hasImages: data.images ? data.images.length : 0,
      ranking_ID: data.ranking_ID
    });

    // 🔧 シンプルなテスト用レスポンス
    console.log('✅ [saveRanking] テスト用レスポンスを返します');
    return NextResponse.json({
      success: true,
      message: 'POSTメソッドは正常に動作しています',
      timestamp: new Date().toISOString(),
      method: 'POST',
      receivedData: {
        title: data.title,
        user_ID: data.user_ID,
        categoryId: data.categoryId,
        hasImages: data.images ? data.images.length : 0
      }
    });

  } catch (error) {
    console.error('❌ [saveRanking] POSTメソッドでエラーが発生しました:', error);
    console.error('❌ [saveRanking] エラーの詳細:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
    return NextResponse.json({
      error: 'POSTメソッドでエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  }
}

// エクスポートが正しく行われているかを確認
console.log('🔧 [saveRanking] POST関数がエクスポートされました');
