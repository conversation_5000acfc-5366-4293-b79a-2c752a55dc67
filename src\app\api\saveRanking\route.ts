import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { generateUniqueRankingId } from '@/lib/rankingIdGenerator';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // 編集モードか新規作成モードかを判定
    const isEditMode = data.ranking_ID ? true : false;

    // 必須フィールドのバリデーション（ranking_descriptionは任意項目のため除外）
    if (!data.title || !data.user_ID || !data.categoryId) {
      return NextResponse.json({
        error: '必須項目が不足しています',
        missingFields: Object.entries({
          title: !data.title,
          user_ID: !data.user_ID,
          categoryId: !data.categoryId
        }).filter(([_, missing]) => missing).map(([field]) => field)
      }, { status: 400 });
    }

    // 画像URLのバリデーション - 画像は必須
    if (!data.images || !Array.isArray(data.images)) {
      return NextResponse.json({ error: '画像が指定されていません' }, { status: 400 });
    }

    // nullや空の画像URLをフィルタリング
    const validImages = data.images.filter((img: any) => img !== null && img !== '');
    if (validImages.length === 0) {
      return NextResponse.json({ error: '有効な画像が指定されていません' }, { status: 400 });
    }
    
    // ユーザーが存在するか確認
    const user = await prisma.user.findUnique({
      where: {
        user_ID: data.user_ID
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'ユーザーが見つかりません' }, { status: 404 });
    }

    // 編集モードの場合はカテゴリの存在確認をスキップすることもできる
    let category = null;
    if (!isEditMode) {
      // 新規作成モードの場合はカテゴリの存在確認が必要
      category = await prisma.category.findUnique({
        where: {
          id: data.categoryId
        }
      });

      if (!category) {
        return NextResponse.json({ error: '指定されたカテゴリが見つかりません' }, { status: 404 });
      }
    }

    // サブカテゴリの情報
    let subCategoryID = data.subCategoryID || null;

    // 編集モードでサブカテゴリIDが既にある場合はそのまま使用
    if (isEditMode && subCategoryID) {
      // そのまま使用
    }
    // サブカテゴリが指定されている場合は存在確認または新規作成
    else if (data.subCategory) {
      // サブカテゴリの値がIDの場合（UUIDまたはsubcat_で始まる場合）
      if (data.subCategory.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i) ||
          data.subCategory.startsWith('subcat_')) {

        // IDが実際に存在するか確認
        const existingSubCategoryById = await prisma.category.findUnique({
          where: {
            id: data.subCategory
          }
        });

        if (existingSubCategoryById) {
          subCategoryID = existingSubCategoryById.id;
        }
      }
      // サブカテゴリの値が名前の場合
      else {
        
        try {
          // 親カテゴリが実際に存在するか確認
          const parentCategory = await prisma.category.findUnique({
            where: {
              id: data.categoryId
            }
          });
          
          if (!parentCategory) {
            console.log('親カテゴリが見つかりません:', data.categoryId);
            return NextResponse.json({ error: '指定された親カテゴリが見つかりません' }, { status: 404 });
          }
          
          // 同じ親カテゴリの下に同名のサブカテゴリが存在するか確認
          const existingSubCategory = await prisma.category.findFirst({
            where: {
              user_ID: data.user_ID,
              parent_ID: data.categoryId,
              category_name: data.subCategory
            }
          });

          if (existingSubCategory) {
            subCategoryID = existingSubCategory.id;
          } else {
            // 新規作成モードの場合のみサブカテゴリを作成
            if (!isEditMode) {
              // 一意のカテゴリIDを生成
              const randomString = Math.random().toString(36);
              const uniqueCategoryID = `subcat_${Date.now()}_${typeof randomString === 'string' && randomString.length > 2 ? randomString.substring(2, 7) : 'xxxxx'}`;

              const newSubCategory = await prisma.category.create({
                data: {
                  user_ID: data.user_ID,
                  category_name: data.subCategory,
                  parent_ID: data.categoryId,
                  category_ID: uniqueCategoryID
                }
              });

              subCategoryID = newSubCategory.id;
            }
          }
        } catch (error) {
          console.error('サブカテゴリ処理エラー:', error);
          if (!isEditMode) {
            return NextResponse.json({
              error: 'サブカテゴリの処理に失敗しました',
              details: error instanceof Error ? error.message : '不明なエラー'
            }, { status: 500 });
          }
        }
      }
    }
    
    console.log('最終的なサブカテゴリID:', subCategoryID);

    // 編集モードの場合は既存のランキングを更新
    if (isEditMode && data.ranking_ID) {
      console.log(`既存のランキングを更新します: ${data.ranking_ID}`);
      
      // ランキングが存在するか確認
      const existingRanking = await prisma.ranking.findFirst({
        where: {
          ranking_ID: data.ranking_ID
        }
      });
      
      if (!existingRanking) {
        return NextResponse.json({ error: '指定されたランキングが見つかりません' }, { status: 404 });
      }
      
      console.log('更新するランキングを見つけました:', existingRanking.id);
      
      // ランキングの更新
      const updatedRanking = await prisma.ranking.update({
        where: {
          id: existingRanking.id // プライマリーキーであるidを使用
        },
        data: {
          ranking_title: data.title,
          ranking_description: data.ranking_description || '', // 空文字列をデフォルト値として設定
          subCategory_ID: subCategoryID,
          amazon_url: data.amazon_url || null,
          rakuten_url: data.rakuten_url || null,
          yahoo_url: data.yahoo_url || null,
          qoo10_url: data.qoo10_url || null,
          official_url: data.official_url || null,
          recommend_rate: data.recommend_rate || null,
          thumbnail_image: validImages.length > 0 ? validImages : [],
          status: 'PUBLISHED', // 公開済みとして保存
          updated_at: new Date() // 更新日時を設定
        }
      });
      
      console.log('ランキングを正常に更新しました:', updatedRanking.ranking_ID);
      
      return NextResponse.json({ 
        success: true, 
        message: 'ランキングが正常に更新されました',
        ranking_ID: updatedRanking.ranking_ID
      }, { status: 200 });
    } 
    // 新規作成モード
    else {
      // ランキングIDを生成（新しい短い形式）
      const ranking_ID = await generateUniqueRankingId();

      console.log('新規ランキングを作成します:', ranking_ID);

      // 同じサブカテゴリ内の最大order値を取得
      const maxOrderResult = await prisma.ranking.aggregate({
        where: {
          user_ID: data.user_ID,
          subCategory_ID: subCategoryID,
        },
        _max: {
          order: true,
        },
      });

      const nextOrder = (maxOrderResult._max.order || 0) + 1;

      // ランキングの保存
      const rankingCreateData = {
        ranking_ID: ranking_ID,
        user_ID: data.user_ID,
        ranking_title: data.title,
        ranking_description: data.ranking_description || '', // 空文字列をデフォルト値として設定
        subCategory_ID: subCategoryID,
        amazon_url: data.amazon_url || null,
        rakuten_url: data.rakuten_url || null,
        yahoo_url: data.yahoo_url || null,
        qoo10_url: data.qoo10_url || null,
        official_url: data.official_url || null,
        recommend_rate: data.recommend_rate || null,
        thumbnail_image: validImages.length > 0 ? validImages : [],
        status: 'PUBLISHED', // 公開済みとして保存
        order: nextOrder
      };

      const ranking = await prisma.ranking.create({
        data: rankingCreateData as any // 一時的な型回避
      });
      
      return NextResponse.json({ 
        success: true, 
        message: 'ランキングが正常に保存されました',
        ranking_ID: ranking.ranking_ID
      }, { status: 201 });
    }
  } catch (error) {
    console.error('ランキング保存中にエラーが発生しました:', error);
    return NextResponse.json({ 
      error: 'ランキングの保存に失敗しました', 
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
