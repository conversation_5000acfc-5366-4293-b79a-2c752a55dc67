import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { generateUniqueRankingId } from '@/lib/rankingIdGenerator';

const prisma = new PrismaClient();

// デバッグ用GETメソッド
export async function GET(request: NextRequest) {
  console.log('🔍 [saveRanking] GETリクエストを受信しました');
  return NextResponse.json({
    message: 'saveRanking API is working',
    timestamp: new Date().toISOString(),
    method: 'GET'
  });
}

export async function POST(request: NextRequest) {
  console.log('🔍 [saveRanking] POSTリクエストを受信しました');
  console.log('🔍 [saveRanking] リクエストURL:', request.url);
  console.log('🔍 [saveRanking] リクエストメソッド:', request.method);
  console.log('🔍 [saveRanking] Content-Type:', request.headers.get('content-type'));

  try {
    console.log('🔍 [saveRanking] JSONパース開始');
    const data = await request.json();
    console.log('🔍 [saveRanking] JSONパース完了:', Object.keys(data));

    // 必須フィールドのバリデーション
    if (!data.title || !data.user_ID || !data.categoryId) {
      return NextResponse.json({
        error: '必須項目が不足しています'
      }, { status: 400 });
    }

    // 画像URLのバリデーション
    if (!data.images || !Array.isArray(data.images)) {
      return NextResponse.json({ error: '画像が指定されていません' }, { status: 400 });
    }

    const validImages = data.images.filter((img: any) => img !== null && img !== '');
    if (validImages.length === 0) {
      return NextResponse.json({ error: '有効な画像が指定されていません' }, { status: 400 });
    }

    // ユーザー存在確認
    const user = await prisma.user.findUnique({
      where: { user_ID: data.user_ID }
    });

    if (!user) {
      return NextResponse.json({ error: 'ユーザーが見つかりません' }, { status: 404 });
    }

    // カテゴリ存在確認
    const category = await prisma.category.findUnique({
      where: { id: data.categoryId }
    });

    if (!category) {
      return NextResponse.json({ error: 'カテゴリが見つかりません' }, { status: 404 });
    }

    // サブカテゴリ処理
    let subCategoryID = data.subCategoryID || null;

    if (data.subCategory && !subCategoryID) {
      const existingSubCategory = await prisma.category.findFirst({
        where: {
          user_ID: data.user_ID,
          parent_ID: data.categoryId,
          category_name: data.subCategory
        }
      });

      if (existingSubCategory) {
        subCategoryID = existingSubCategory.id;
      } else {
        const uniqueCategoryID = `subcat_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
        const newSubCategory = await prisma.category.create({
          data: {
            user_ID: data.user_ID,
            category_name: data.subCategory,
            parent_ID: data.categoryId,
            category_ID: uniqueCategoryID
          }
        });
        subCategoryID = newSubCategory.id;
      }
    }

    // 編集モードか新規作成モードかを判定
    if (data.ranking_ID) {
      // 編集モード
      const existingRanking = await prisma.ranking.findFirst({
        where: {
          ranking_ID: data.ranking_ID,
          user_ID: data.user_ID
        }
      });

      if (!existingRanking) {
        return NextResponse.json({ error: 'ランキングが見つかりません' }, { status: 404 });
      }

      const updatedRanking = await prisma.ranking.update({
        where: { id: existingRanking.id },
        data: {
          ranking_title: data.title,
          ranking_description: data.ranking_description || '',
          subCategory_ID: subCategoryID,
          amazon_url: data.amazon_url || null,
          rakuten_url: data.rakuten_url || null,
          yahoo_url: data.yahoo_url || null,
          qoo10_url: data.qoo10_url || null,
          official_url: data.official_url || null,
          recommend_rate: data.recommend_rate || null,
          thumbnail_image: validImages,
          status: 'PUBLISHED',
          updated_at: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        message: 'ランキングが更新されました',
        ranking_ID: updatedRanking.ranking_ID
      });
    } else {
      // 新規作成モード
      const ranking_ID = await generateUniqueRankingId();

      const maxOrderResult = await prisma.ranking.aggregate({
        where: {
          user_ID: data.user_ID,
          subCategory_ID: subCategoryID,
        },
        _max: { order: true },
      });

      const nextOrder = (maxOrderResult._max.order || 0) + 1;

      const ranking = await prisma.ranking.create({
        data: {
          ranking_ID: ranking_ID,
          user_ID: data.user_ID,
          ranking_title: data.title,
          ranking_description: data.ranking_description || '',
          subCategory_ID: subCategoryID,
          amazon_url: data.amazon_url || null,
          rakuten_url: data.rakuten_url || null,
          yahoo_url: data.yahoo_url || null,
          qoo10_url: data.qoo10_url || null,
          official_url: data.official_url || null,
          recommend_rate: data.recommend_rate || null,
          thumbnail_image: validImages,
          status: 'PUBLISHED',
          order: nextOrder
        }
      });

      return NextResponse.json({
        success: true,
        message: 'ランキングが保存されました',
        ranking_ID: ranking.ranking_ID
      }, { status: 201 });
    }
  } catch (error) {
    console.error('saveRanking API エラー:', error);
    return NextResponse.json({
      error: 'ランキングの保存に失敗しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
