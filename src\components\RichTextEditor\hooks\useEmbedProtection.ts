import { useRef, useEffect } from 'react';
import { trackEmbedState } from '../utils';

// グローバル型定義の拡張
declare global {
  interface Window {
    __isIntentionalEmbedDelete?: boolean;
  }
}

// 🔧 復元処理のデバウンス用
const RESTORE_DEBOUNCE_DELAY = 300;

export const useEmbedProtection = (editorRef: React.RefObject<HTMLDivElement>) => {
  const embedProtectionMap = useRef(new Map<string, HTMLElement>());
  const embedObserver = useRef<MutationObserver | null>(null);
  const lastKnownEmbeds = useRef(new Set<string>());

  // 🔧 復元処理のデバウンス用
  const restoreTimeoutRef = useRef<number | null>(null);
  const isRestoringRef = useRef(false);

  // 🔧 修正: 埋め込み要素の保護強化（インラインスタイル上書き防止）
  const protectEmbed = (embed: HTMLElement, embedId: string) => {
    // contenteditable="false"を確実に設定
    embed.setAttribute('contenteditable', 'false');
    embed.style.userSelect = 'none';
    embed.style.pointerEvents = 'auto';

    // 🔧 削除防止のための追加属性
    embed.setAttribute('data-protected', 'true');
    embed.setAttribute('data-deletable', 'false');

    // 🔧 重要: インラインスタイルでCSS上書きしない（CSSクラスに委ねる）
    // position, z-index, display等はCSSで制御するため、インラインスタイルは設定しない
    // embed.style.position = 'static';  // ← 削除：CSS上書きを防止
    // embed.style.zIndex = 'auto';      // ← 削除：CSS上書きを防止
    // embed.style.clear = 'both';       // ← 削除：CSS上書きを防止
    // embed.style.float = 'none';       // ← 削除：CSS上書きを防止
    // embed.style.display = 'block';    // ← 削除：CSS上書きを防止

    // 保護マップに追加
    embedProtectionMap.current.set(embedId, embed);
    lastKnownEmbeds.current.add(embedId);

    trackEmbedState('EMBED_PROTECTED', embedId, {
      hasIframe: !!embed.querySelector('iframe'),
      hasContent: !!embed.querySelector('.social-embed-content'),
      innerHTML: typeof embed.innerHTML === 'string' ? embed.innerHTML.substring(0, 100) : String(embed.innerHTML).substring(0, 100)
    });
  };

  // 🔧 キーボード削除操作の検出
  const isKeyboardDeletionRef = useRef(false);
  const keyboardDeletionTimeoutRef = useRef<number | null>(null);

  // キーボード削除操作の開始を記録
  const markKeyboardDeletion = () => {
    isKeyboardDeletionRef.current = true;



    // 一定時間後にフラグをリセット
    if (keyboardDeletionTimeoutRef.current !== null) {
      window.clearTimeout(keyboardDeletionTimeoutRef.current);
    }

    keyboardDeletionTimeoutRef.current = window.setTimeout(() => {
      isKeyboardDeletionRef.current = false;
      keyboardDeletionTimeoutRef.current = null;

    }, 3000); // 3秒後にリセット（削除処理完了まで十分な時間を確保）
  };

  // 失われた埋め込み要素の復元（デバウンス付き）
  const restoreLostEmbeds = () => {
    if (!editorRef.current) return;

    // 🔧 意図的削除フラグをチェック
    if (window.__isIntentionalEmbedDelete) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[EMBED_PROTECTION] スキップ: ユーザーによる意図的な削除');
      }
      window.__isIntentionalEmbedDelete = false;
      trackEmbedState('RESTORE_SKIPPED_INTENTIONAL_DELETE');
      return;
    }

    // 🔧 キーボード削除操作中は復元をスキップ
    if (isKeyboardDeletionRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[EMBED_PROTECTION] キーボード削除操作中のため復元をスキップします');
      }
      trackEmbedState('RESTORE_SKIPPED_KEYBOARD_DELETION');
      return;
    }

    // 🔧 復元処理中の場合はスキップ
    if (isRestoringRef.current) {
      trackEmbedState('RESTORE_SKIPPED_ALREADY_RESTORING');
      return;
    }

    // 🔧 既存のタイマーをクリア
    if (restoreTimeoutRef.current !== null) {
      window.clearTimeout(restoreTimeoutRef.current);
      restoreTimeoutRef.current = null;
    }

    // 🔧 デバウンス処理
    restoreTimeoutRef.current = window.setTimeout(() => {
      if (!editorRef.current || isRestoringRef.current) return;

      isRestoringRef.current = true;
      trackEmbedState('RESTORE_CHECK_START');

      try {
        const currentEmbedIds = new Set<string>();
        const currentEmbeds = editorRef.current.querySelectorAll('.social-embed[data-embed-id]');

        currentEmbeds.forEach(embed => {
          const id = embed.getAttribute('data-embed-id');
          if (id) currentEmbedIds.add(id);
        });

        // 🔧 より厳密な復元条件
        // 現在の埋め込み要素数が既知の数と同じ、または増加している場合は復元不要
        if (currentEmbedIds.size >= lastKnownEmbeds.current.size) {
          trackEmbedState('RESTORE_SKIPPED_SAME_OR_MORE_COUNT', undefined, {
            current: currentEmbedIds.size,
            known: lastKnownEmbeds.current.size
          });
          return;
        }

        // 失われた埋め込み要素を検出
        const lostEmbeds: string[] = [];
        lastKnownEmbeds.current.forEach(embedId => {
          if (!currentEmbedIds.has(embedId)) {
            lostEmbeds.push(embedId);
          }
        });

        if (lostEmbeds.length > 0) {
          trackEmbedState('LOST_EMBEDS_DETECTED', undefined, { lostEmbeds });

          // 保護されている要素を復元
          lostEmbeds.forEach(embedId => {
            const protectedEmbed = embedProtectionMap.current.get(embedId);
            if (protectedEmbed) {
              trackEmbedState('RESTORING_EMBED', embedId);

              // エディタの末尾に復元
              const restoredEmbed = protectedEmbed.cloneNode(true) as HTMLElement;
              editorRef.current!.appendChild(restoredEmbed);

              // 新しい要素も保護
              protectEmbed(restoredEmbed, embedId);

              trackEmbedState('EMBED_RESTORED', embedId);
            }
          });
        } else {
          trackEmbedState('NO_LOST_EMBEDS_FOUND');
        }
      } finally {
        isRestoringRef.current = false;
        restoreTimeoutRef.current = null;
      }
    }, RESTORE_DEBOUNCE_DELAY);
  };

  // DOM変更監視システムの設定
  const setupDOMObserver = () => {
    if (!editorRef.current || embedObserver.current) return;

    trackEmbedState('DOM_OBSERVER_SETUP');

    embedObserver.current = new MutationObserver((mutations) => {
      // 🔧 最優先: 意図的削除フラグをチェック
      if (window.__isIntentionalEmbedDelete) {

        window.__isIntentionalEmbedDelete = false;
        trackEmbedState('SKIPPING_MUTATION_OBSERVER_INTENTIONAL_DELETE');
        return;
      }

      let embedsRemoved = false;
      let embedsAdded = false;
      let significantRemoval = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 削除されたノードをチェック
          mutation.removedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.classList?.contains('social-embed') || element.querySelector?.('.social-embed')) {
                embedsRemoved = true;
                const embedId = element.getAttribute?.('data-embed-id') || 'unknown';
                trackEmbedState('EMBED_REMOVED_DETECTED', embedId);

                // 🔧 重要: ブラウザのネイティブ削除操作を検知した場合、意図的削除フラグを設定
                if (!window.__isIntentionalEmbedDelete && !isKeyboardDeletionRef.current) {
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`[EMBED_PROTECTION] ブラウザのネイティブ削除を検知、意図的削除フラグを設定: ${embedId}`);
                  }
                  window.__isIntentionalEmbedDelete = true;
                }

                // 🔧 保護されている要素の削除のみを重要視
                if (embedId !== 'unknown' && lastKnownEmbeds.current.has(embedId)) {
                  significantRemoval = true;
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`[EMBED_PROTECTION] 重要な埋め込み要素の削除を検知: ${embedId} (キーボード削除フラグ: ${isKeyboardDeletionRef.current}) (意図的削除フラグ: ${window.__isIntentionalEmbedDelete})`);
                  }
                }
              }
            }
          });

          // 追加されたノードをチェック
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.classList?.contains('social-embed')) {
                const embedId = element.getAttribute('data-embed-id');
                if (embedId) {
                  protectEmbed(element as HTMLElement, embedId);
                  trackEmbedState('NEW_EMBED_DETECTED', embedId);
                  embedsAdded = true;
                }
              }
            }
          });
        }
      });

      // 🔧 より厳密な復元条件（意図的削除チェックは最初に実行済み）

      // キーボード削除操作中は復元をスキップ
      if (isKeyboardDeletionRef.current) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[EMBED_PROTECTION] DOM監視: キーボード削除操作中のため復元をスキップします');
        }
        trackEmbedState('SKIPPING_RESTORE_KEYBOARD_DELETION_IN_PROGRESS');
        return;
      }

      // 重要な埋め込み要素が削除され、かつ新しい要素が追加されていない場合のみ復元
      if (significantRemoval && !embedsAdded && !isRestoringRef.current) {
        trackEmbedState('SCHEDULING_RESTORE_DUE_TO_SIGNIFICANT_REMOVAL');
        restoreLostEmbeds();
      } else if (embedsAdded) {
        trackEmbedState('SKIPPING_RESTORE_DUE_TO_ADDITION');
      } else if (embedsRemoved && !significantRemoval) {
        trackEmbedState('SKIPPING_RESTORE_NON_SIGNIFICANT_REMOVAL');
      }
    });

    embedObserver.current.observe(editorRef.current, {
      childList: true,
      subtree: true,
      attributes: false
    });
  };

  // DOM監視の開始と終了
  useEffect(() => {
    setupDOMObserver();

    return () => {
      // 🔧 復元タイマーのクリーンアップ
      if (restoreTimeoutRef.current !== null) {
        window.clearTimeout(restoreTimeoutRef.current);
        restoreTimeoutRef.current = null;
      }

      if (embedObserver.current) {
        embedObserver.current.disconnect();
        embedObserver.current = null;
        trackEmbedState('DOM_OBSERVER_CLEANUP');
      }
    };
  }, []);

  // 🔧 埋め込み要素を保護システムから除外する関数
  const removeEmbedFromProtection = (embedId: string) => {
    const wasProtected = embedProtectionMap.current.has(embedId);
    embedProtectionMap.current.delete(embedId);
    lastKnownEmbeds.current.delete(embedId);

    if (process.env.NODE_ENV === 'development') {
      console.log(`[EMBED_PROTECTION] 保護システムから除外: ${embedId} (保護されていた: ${wasProtected})`);
    }

    trackEmbedState('EMBED_REMOVED_FROM_PROTECTION', embedId);
  };

  return {
    protectEmbed,
    restoreLostEmbeds,
    removeEmbedFromProtection,
    markKeyboardDeletion,
    embedProtectionMap,
    lastKnownEmbeds
  };
};