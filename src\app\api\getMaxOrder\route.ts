import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get("parent_ID");

    if (!parentId) {
      return NextResponse.json(
        { error: "parent_IDは必須パラメータです" },
        { status: 400 }
      );
    }

    // 同じparent_IDを持つカテゴリの中で最大のorder値を取得
    const maxOrderResult = await prisma.category.aggregate({
      where: {
        parent_ID: parentId,
      },
      _max: {
        order: true,
      },
    });

    const maxOrder = maxOrderResult._max.order || 0;

    return NextResponse.json({ maxOrder });
  } catch (error) {
    return NextResponse.json(
      { error: "最大order値の取得中にエラーが発生しました" },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
