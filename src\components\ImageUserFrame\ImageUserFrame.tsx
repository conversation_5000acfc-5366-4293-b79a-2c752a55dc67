/* eslint-disable @next/next/no-img-element */
"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { getUserId } from "../../contexts/UserContext";

interface Props {
  profile_image?: string;
  user_ID?: string; // ユーザーIDを追加
  className?: string; // 追加のクラス名
}

export const ImageUserFrame = ({ profile_image, user_ID, className = "" }: Props): JSX.Element => {
  const [imgError, setImgError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>("/static/img/profile-default.png");
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  // フォールバック用のSVGデータ URL
  const fallbackImage = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ccc'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z'/%3E%3C/svg%3E";
  
  // ユーザーIDが渡されていない場合はUserContextから取得
  const currentUserId = user_ID || getUserId();

  // 画像パスの有効性を確認する関数
  const isValidImagePath = (path: string | null | undefined): boolean => {
    if (!path) return false;
    if (typeof path !== 'string') return false;
    
    // 有効なパスの形式をチェック
    return (
      path.startsWith('/img/') ||
      path.startsWith('/static/img/') ||
      path.startsWith('/uploads/') ||
      path.startsWith('/static/uploads/') ||
      path.startsWith('data:') ||
      path.startsWith('blob:') ||
      path.startsWith('http://') ||
      path.startsWith('https://')
    );
  };
  
  // 画像URLにタイムスタンプを追加してキャッシュを回避する関数（本番環境では無効化）
  const addTimestampToUrl = (url: string): string => {
    // 本番環境ではキャッシュを活用するためタイムスタンプを追加しない
    if (process.env.NODE_ENV === 'production') {
      return url;
    }

    // 開発環境のみタイムスタンプを追加
    if (url.includes('?t=')) {
      return url.replace(/\?t=\d+/, `?t=${Date.now()}`);
    }

    if (url.includes('?')) {
      return `${url}&t=${Date.now()}`;
    }

    return `${url}?t=${Date.now()}`;
  };

  // プロフィール画像を取得
  useEffect(() => {
    // プロフィール画像が直接指定されている場合
    if (profile_image) {
      // パスの正規化
      let normalizedPath = profile_image;
      
      // パスの先頭にスラッシュがない場合は追加
      if (normalizedPath && !normalizedPath.startsWith('/') && !normalizedPath.startsWith('http')) {
        normalizedPath = `/${normalizedPath}`;
      }
      
      // パスの有効性を確認
      if (isValidImagePath(normalizedPath)) {
        // 画像URLにタイムスタンプを追加（開発環境のみ）
        if (!normalizedPath.startsWith('data:') && !normalizedPath.startsWith('blob:')) {
          normalizedPath = addTimestampToUrl(normalizedPath);
        }
        setImageSrc(normalizedPath);

        // 画像のプリロード（LCP最適化）
        const preloadImg = new window.Image();
        preloadImg.src = normalizedPath;
      } else {
        // 無効なパスの場合はデフォルト画像を設定
        setImageSrc('/static/img/profile-default.png');
      }
      setIsLoading(false);
    } 
    // user_IDが指定されている場合はプロフィール画像を取得
    else if (user_ID) {
      const fetchProfileImage = async () => {
        try {
          setIsLoading(true);

          // ユーザーIDからプロフィール画像を取得（キャッシュ活用）
          const response = await fetch(`/api/getUser?user_ID=${user_ID}`, {
            cache: 'force-cache',
            next: { revalidate: 300 } // 5分間キャッシュ
          });

          if (!response.ok) {
            throw new Error(`プロフィール画像取得エラー: ${response.status}`);
          }

          const data = await response.json();
          
          if (data && data.profile_image && isValidImagePath(data.profile_image)) {
            // 画像URLにタイムスタンプを追加してキャッシュを回避
            let imageUrl = data.profile_image;
            if (!imageUrl.startsWith('data:') && !imageUrl.startsWith('blob:')) {
              imageUrl = addTimestampToUrl(imageUrl);
            }
            setImageSrc(imageUrl);
          } else {
            // プロフィール画像がないか無効な場合はデフォルト画像を設定
            setImageSrc('/static/img/profile-default.png');
          }
        } catch (error) {
          // エラーログを出力せず、デフォルト画像を設定
          setImageSrc('/static/img/profile-default.png');
        } finally {
          setIsLoading(false);
        }
      };
      
      fetchProfileImage();
    } else {
      // プロフィール画像もuser_IDも指定されていない場合はデフォルト画像を設定
      setImageSrc('/static/img/profile-default.png');
      setIsLoading(false);
    }
  }, [profile_image, user_ID]); // 依存配列に必要な値を追加

  // 画像読み込みエラー時の処理
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    // 開発環境でのみエラーログを出力
    if (process.env.NODE_ENV === 'development') {
      console.error("ImageUserFrame - 画像読み込みエラー:", imageSrc);
    }
    
    // 背景画像と同じ挙動にするため、エラー時にデフォルト画像を表示しない
    // タイムスタンプ付きのURLでエラーが発生した場合、タイムスタンプを除去して再試行
    if (imageSrc.includes('?t=') && !imgError) {
      setImgError(true);
      const cleanUrl = imageSrc.replace(/\?t=\d+/, '');
      setImageSrc(cleanUrl);
      return;
    }
    
    // エラー時は何も表示しない（背景画像と同じ挙動）
    // 透明な画像を設定して何も表示しないようにする
    setImageSrc('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'); // 透明な1x1ピクセル
  };
  
  // 画像クリック時の処理
  const handleImageClick = () => {
    setIsModalOpen(true);
  };

  // モーダル閉じる処理
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // モーダル外クリック時の処理
  const handleModalBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      setIsModalOpen(false);
    }
  };

  return (
    <>
      <div 
        className={`p-1 flex w-[120px] h-[120px] justify-center items-center rounded-full border border-solid border-white overflow-hidden cursor-pointer ${className}`}
        onClick={handleImageClick}
      >
        {isLoading ? (
          <div className="w-full h-full rounded-full bg-gray-200 animate-pulse"></div>
        ) : (
          <Image
            src={imageSrc}
            alt="User image"
            width={120}
            height={120}
            className="w-full h-full object-cover rounded-full"
            onError={handleImageError}
            style={{ display: imgError ? 'none' : 'block' }}
            priority={true}
            quality={85}
            sizes="120px"
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
          />
        )}
      </div>

      {/* 拡大表示モーダル */}
      {isModalOpen && (
        <div 
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/80 transition-opacity duration-300"
          onClick={handleModalBackdropClick}
          style={{ pointerEvents: 'auto' }}
        >
          <div className="relative max-w-[90vw] max-h-[90vh] overflow-hidden">
            {/* 閉じるボタン */}
            <button 
              className="absolute top-4 right-4 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-black/50 text-white"
              onClick={handleCloseModal}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
            
            {/* 拡大画像 */}
            <img
              src={imageSrc}
              alt="Enlarged user profile"
              className="max-w-full max-h-[90vh] object-contain rounded-[1000px] shadow-xl cursor-pointer"
              onError={handleImageError}
              onClick={handleCloseModal}
              style={{ display: imgError ? 'none' : 'block' }} // エラー時は非表示にする
            />
          </div>
        </div>
      )}
    </>
  );
};
