'use client';

import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';

export default function NotFound() {
  const router = useRouter();
  const { user, isLoaded } = useUser();

  const handleGoHome = async () => {
    if (!isLoaded) return;

    if (user) {
      // ログイン済みの場合は、ユーザーのセットアップ状況を確認
      try {
        const response = await fetch(`/api/getUser?user_ID=${user.id}`);
        if (response.ok) {
          const userData = await response.json();
          if (userData.setup_completed && userData.username) {
            // セットアップ完了済みの場合はユーザーページにリダイレクト
            router.push(`/${userData.username}`);
          } else {
            // セットアップ未完了の場合はセットアップページにリダイレクト
            router.push('/setup');
          }
        } else {
          // ユーザーデータが見つからない場合はセットアップページにリダイレクト
          router.push('/setup');
        }
      } catch (error) {
        console.error('ユーザー情報取得エラー:', error);
        router.push('/setup');
      }
    } else {
      // 未ログインの場合はサインインページにリダイレクト
      router.push('/sign-in');
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <div className="flex-1 flex items-center justify-center">
        <div className="max-w-[500px] w-full mx-auto bg-white shadow-md min-h-screen flex items-center justify-center">
          <div className="text-center w-full px-4">
            <h1 className="text-[20px] font-semibold text-[#313131] mb-4">
              このページはご利用いただけません。
            </h1>
            <p className="text-[#676767] mb-8">
              リンクに問題があるか、ページが削除された可能性があります。
            </p>
            <button
              onClick={handleGoHome}
              disabled={!isLoaded}
              className="inline-block w-[340px] h-[40px] bg-[#FFD814] text-[#313131] font-medium rounded-[100px] hover:bg-[#E1BC03] transition-colors leading-[40px] disabled:opacity-50"
            >
              {!isLoaded ? '読み込み中...' : 'ホームに戻る'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
