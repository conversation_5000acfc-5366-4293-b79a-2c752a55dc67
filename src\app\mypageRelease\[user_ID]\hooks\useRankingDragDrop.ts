import { useCallback } from "react";
import { DragStartEvent, DragEndEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import type { RankingType } from "../types";

interface UseRankingDragDropParams {
  pageUserId: string;
  rankingsBySubcategory: Record<string, RankingType[]>;
  setActiveId: (id: string | null) => void;
  setIsDragInProgress: (inProgress: boolean) => void;
  setRankingsBySubcategory: (rankings: Record<string, RankingType[]> | ((prev: Record<string, RankingType[]>) => Record<string, RankingType[]>)) => void;
  setIsServerUpdating: (updating: boolean) => void;
}

interface UseRankingDragDropReturn {
  updateRankingOrder: (rankings: RankingType[], subCategoryId: string) => Promise<any>;
  handleRankingDragStart: (event: DragStartEvent) => void;
  handleRankingDragEnd: (event: DragEndEvent) => void;
}

export const useRankingDragDrop = ({
  pageUserId,
  rankingsBySubcategory,
  setActiveId,
  setIsDragInProgress,
  setRankingsBySubcategory,
  setIsServerUpdating,
}: UseRankingDragDropParams): UseRankingDragDropReturn => {

  /* -------------------------------------------------------------------------- */
  /*                          ランキング順序更新                                    */
  /* -------------------------------------------------------------------------- */
  const updateRankingOrder = useCallback(async (rankings: RankingType[], subCategoryId: string) => {
    try {
      const response = await fetch('/api/updateRankingOrder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rankings: rankings,
          user_ID: pageUserId,
          subCategory_ID: subCategoryId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`ランキング順序の更新に失敗しました: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      throw error;
    }
  }, [pageUserId]);

  /* -------------------------------------------------------------------------- */
  /*                          ドラッグ&ドロップハンドラー（最適化済み）                              */
  /* -------------------------------------------------------------------------- */
  const handleRankingDragStart = useCallback((event: DragStartEvent) => {
    requestAnimationFrame(() => {
      setActiveId(event.active.id as string);
      setIsDragInProgress(true);
    });
  }, [setActiveId, setIsDragInProgress]);

  const handleRankingDragEnd = useCallback((event: DragEndEvent) => {
    requestAnimationFrame(() => {
      const { active, over } = event;
      setActiveId(null);
      setIsDragInProgress(false);

      if (!over || active.id === over.id) {
        return;
      }

      // ドラッグされたアイテムのデータを取得
      const activeData = active.data.current;
      const overData = over.data.current;

      // 同じサブカテゴリ内でのみ並び替えを許可
      if (activeData?.subcatId !== overData?.subcatId) {
        return;
      }

      const subcatId = activeData.subcatId;
      const currentRankings = rankingsBySubcategory[subcatId] || [];

      // 現在の順序でソート
      const sortedRankings = [...currentRankings].sort((a, b) => {
        const orderA = a.order !== undefined ? a.order : 999999;
        const orderB = b.order !== undefined ? b.order : 999999;
        return orderA - orderB;
      });

      const oldIndex = sortedRankings.findIndex(ranking => ranking.ranking_ID === active.id);
      const newIndex = sortedRankings.findIndex(ranking => ranking.ranking_ID === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        // 配列の順序を変更
        const reorderedRankings = arrayMove(sortedRankings, oldIndex, newIndex);

        // 重要：order値を新しい位置に基づいて更新
        const newRankings = reorderedRankings.map((ranking, index) => ({
          ...ranking,
          order: index
        }));

        setRankingsBySubcategory(prev => {
          if (!prev) return prev;
          const updated = {
            ...prev,
            [subcatId]: newRankings
          };

          return updated;
        });

        // サーバー更新フラグを設定
        setIsServerUpdating(true);

        // サーバーに順序を保存（非同期、UI更新を妨げない）
        updateRankingOrder(newRankings, subcatId)
          .then(() => {
            // サーバー更新完了後にフラグを解除
            setTimeout(() => {
              setIsServerUpdating(false);
            }, 100);
          })
          .catch((error) => {
            // エラーが発生した場合は元の順序に戻す
            setRankingsBySubcategory(prev => ({
              ...prev,
              [subcatId]: sortedRankings
            }));
            setIsServerUpdating(false);
          });
      }
    });
  }, [rankingsBySubcategory, updateRankingOrder, setActiveId, setIsDragInProgress, setRankingsBySubcategory, setIsServerUpdating]);

  return {
    updateRankingOrder,
    handleRankingDragStart,
    handleRankingDragEnd,
  };
};