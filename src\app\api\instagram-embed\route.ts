import { NextRequest, NextResponse } from 'next/server';

/**
 * Instagram投稿の埋め込みHTMLを取得するAPIルート
 */
export async function POST(request: NextRequest) {
  try {
    // リクエストボディを取得
    const body = await request.json();
    const { postUrl } = body;

    if (!postUrl) {
      return NextResponse.json({ error: '投稿URLが必要です' }, { status: 400 });
    }

    console.log('Instagram埋め込み処理開始:', postUrl);

    // 環境変数からFacebookアプリのIDとクライアントトークンを取得
    const APP_ID = process.env.FACEBOOK_APP_ID;
    const CLIENT_TOKEN = process.env.FACEBOOK_CLIENT_TOKEN;

    if (!APP_ID || !CLIENT_TOKEN) {
      console.error('環境変数が設定されていません: FACEBOOK_APP_ID または FACEBOOK_CLIENT_TOKEN');
      return NextResponse.json({ error: 'サーバー設定エラー' }, { status: 500 });
    }

    // アプリアクセストークンを作成
    const ACCESS_TOKEN = `${APP_ID}|${CLIENT_TOKEN}`;

    // URLをエンコード
    const encodedUrl = encodeURIComponent(postUrl);

    // Meta (Facebook) のoEmbedエンドポイントにリクエスト
    const oEmbedUrl = `https://graph.facebook.com/v19.0/instagram_oembed?url=${encodedUrl}&access_token=${ACCESS_TOKEN}`;
    
    console.log('oEmbed APIリクエスト:', oEmbedUrl);

    const response = await fetch(oEmbedUrl);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('oEmbed APIエラー:', response.status, errorText);
      return NextResponse.json({ 
        error: 'Instagram埋め込みの取得に失敗しました', 
        details: errorText 
      }, { status: 500 });
    }

    const data = await response.json();
    
    if (!data.html) {
      console.error('oEmbed APIレスポンスにHTMLが含まれていません:', data);
      return NextResponse.json({ 
        error: '埋め込みHTMLが取得できませんでした' 
      }, { status: 500 });
    }

    console.log('Instagram埋め込みHTML取得成功');
    
    return NextResponse.json({ html: data.html });
  } catch (error) {
    console.error('Instagram埋め込み処理エラー:', error);
    return NextResponse.json({ 
      error: '処理中にエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ error: 'POSTメソッドのみサポートしています' }, { status: 405 });
}
