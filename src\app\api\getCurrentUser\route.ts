import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';
import { getAuth } from '@clerk/nextjs/server';
import { PrismaClient } from '@prisma/client';
import { cookies } from 'next/headers';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Clerkから認証情報を取得
    const { userId: clerkUserId } = getAuth(request);
    
    // CookieからユーザーIDを取得
    const cookieStore = cookies();
    const cookieUserId = cookieStore.get('userId')?.value;
    
    // リクエストヘッダーからユーザーIDを取得
    const headerUserId = request.headers.get('X-User-ID');
    
    // URLクエリパラメータからユーザーIDを取得
    const { searchParams } = new URL(request.url);
    const queryUserId = searchParams.get('user_ID');
    
    // 利用可能なユーザーIDを優先順に取得
    const userId = clerkUserId || cookieUserId || headerUserId || queryUserId;
    
    console.log('getCurrentUser - 利用可能なユーザーID:', { clerkUserId, cookieUserId, headerUserId, queryUserId });
    console.log('getCurrentUser - 使用するユーザーID:', userId);
    
    if (!userId) {
      console.log('getCurrentUser - ユーザーIDが見つかりません');
      return NextResponse.json({ error: '認証が必要です' }, { status: 401 });
    }
    
    // Clerkユーザー情報からデータベースのユーザーを検索
    const dbUser = await prisma.user.findFirst({
      where: {
        user_ID: userId
      }
    });
    
    if (!dbUser) {
      console.log('ユーザーがデータベースに見つかりません:', userId);
      return NextResponse.json({ error: 'ユーザーが見つかりません' }, { status: 404 });
    }
    
    console.log('ユーザー情報を取得しました:', dbUser.user_ID);
    
    return NextResponse.json({
      user_ID: dbUser.user_ID,
      name: dbUser.name,
      email: dbUser.email
    }, { status: 200 });
    
  } catch (error) {
    console.error('ユーザー情報の取得中にエラーが発生しました:', error);
    return NextResponse.json({ error: 'ユーザー情報の取得に失敗しました' }, { status: 500 });
  }
}
