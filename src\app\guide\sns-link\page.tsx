'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function SnsLinkGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">SNSリンクを追加する方法</h1>
          <div className="pt-6 text-[14px] text-[#313131]">
            <p>1. マイページの「プロフィール編集」ボタンをタップします。</p>
            <p>2. 「SNSリンク追加」セクションを探します。</p>
            <p>3. 追加したいSNSプラットフォームを選択します。</p>
            <p>4. アカウントURLまたはユーザー名を入力します。</p>
            <p>5. 「追加」ボタンをタップしてSNSリンクを追加します。</p>
            <p>6. 「保存」ボタンをタップして変更を確定します。</p>
          </div>      
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
