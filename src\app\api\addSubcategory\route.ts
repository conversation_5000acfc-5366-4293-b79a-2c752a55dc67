import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// 共通のPrismaインスタンスを使用

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { category_name, user_ID, parent_ID } = body;


    if (!category_name || !user_ID || !parent_ID) {
      return NextResponse.json(
        { success: false, error: '必須パラメータが不足しています' },
        { status: 400 }
      );
    }

    // ユーザー情報を取得（domain が必要なため）
    const user = await prisma.user.findUnique({ where: { user_ID } });
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'ユーザーが見つかりません' },
        { status: 404 }
      );
    }

    // 親カテゴリが存在するか確認
    const parentCategory = await prisma.category.findUnique({ 
      where: { id: parent_ID } 
    });
    
    if (!parentCategory) {
      return NextResponse.json(
        { success: false, error: '親カテゴリが見つかりません' },
        { status: 404 }
      );
    }

    // 同名のサブカテゴリがないか確認
    let baseName = category_name;
    let uniqueName = baseName;
    let count = 1;
    while (true) {
      const dup = await prisma.category.findFirst({
        where: { 
          user_ID, 
          category_name: uniqueName,
          parent_ID
        },
      });
      if (!dup) break;
      count++;
      uniqueName = `${baseName}${count}`;
    }

    // サブカテゴリを作成
    const newSubcategory = await prisma.category.create({
      data: {
        category_name: uniqueName,
        user_ID,
        parent_ID,
      },
    });

    return NextResponse.json(
      { 
        success: true, 
        message: "サブカテゴリを作成しました", 
        subcategory: {
          id: newSubcategory.id,
          category_name: newSubcategory.category_name,
          parent_ID: newSubcategory.parent_ID
        } 
      },
      { status: 201 }
    );

  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        error: `Failed to create subcategory: ${error.message || error}`,
        details: error
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
