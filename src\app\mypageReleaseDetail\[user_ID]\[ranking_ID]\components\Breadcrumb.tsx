"use client";

import React, { useState, useEffect } from "react";

interface BreadcrumbProps {
  userId: string;
  categoryName: string;
  subcategoryName: string;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ userId, categoryName, subcategoryName }) => {
  const [username, setUsername] = useState<string | null>(null);

  // ユーザー名を取得するuseEffect
  useEffect(() => {
    const fetchUsername = async () => {
      if (userId) {
        try {
          const response = await fetch(`/api/getUser?user_ID=${userId}`);
          if (response.ok) {
            const userData = await response.json();
            setUsername(userData.username);
          }
        } catch (error) {
          console.error('ユーザー名取得エラー:', error);
        }
      }
    };

    fetchUsername();
  }, [userId]);

  const profileUrl = username ? `/${username}` : '#';

  return (
    <div className="inline-flex items-center gap-[6px] pl-[16px] pr-0 pb-0 relative w-full">
      <a
        href={profileUrl}
        className="relative w-fit mt-[-1.00px] font-normal text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap"
      >
        TOP
      </a>
      <div className="relative w-fit mt-[-1.00px] font-normal text-black-1 text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
        &gt;
      </div>
      <a
        href={profileUrl}
        className="relative w-fit mt-[-1.00px] font-normal text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap"
      >
        {categoryName || "未分類"}
      </a>
      <div className="relative w-fit mt-[-1.00px] font-normal text-black-1 text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
        &gt;
      </div>
      <a
        href={profileUrl}
        className="relative w-fit mt-[-1.00px] font-normal text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap"
      >
        {subcategoryName || "未分類"}
      </a>
    </div>
  );
};