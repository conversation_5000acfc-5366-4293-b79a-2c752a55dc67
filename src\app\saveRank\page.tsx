'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useUser } from '../../contexts/UserContext';
import CatLoadingAnimation from '../../components/CatLoadingAnimation/CatLoadingAnimation';

// ランキングアイテムの型定義
interface RankingItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  rating: number; // 1-5の評価
  savedAt: string;
}

export default function SavedRankingsPage() {
  const router = useRouter();
  const { userId } = useUser();
  const [savedRankings, setSavedRankings] = useState<RankingItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedRankingId, setSelectedRankingId] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);
  
  // 戻るボタンのハンドラー
  const handleBack = () => {
    window.history.back();
  };

  // コンポーネントがマウントされたことを確認
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 保存したランキングデータを取得
  useEffect(() => {
    if (!isMounted) return;
    
    try {
      // ローカルストレージから保存したランキングデータを取得
      const storedRankings = localStorage.getItem('savedRankings');
      if (storedRankings) {
        const parsedRankings = JSON.parse(storedRankings);
        setSavedRankings(parsedRankings);
      }
      // 少し遅延してからローディングを終了
      setTimeout(() => {
        setIsLoading(false);
      }, 1000); // 1秒後にローディングを終了
    } catch (error) {
      console.error('保存したランキングデータの取得に失敗しました:', error);
      setIsLoading(false);
    }
  }, [isMounted]);

  // ランキングを開く処理
  const openRanking = async (rankingId: string) => {
    // 編集モードの場合は何もしない
    if (isEditMode) return;

    // メニューが開いている場合は何もしない
    if (menuOpenId) return;

    try {
      // ランキングIDから所有者のユーザー情報を取得
      const rankingResponse = await fetch(`/api/getRankingDetail?ranking_ID=${rankingId}`);
      if (rankingResponse.ok) {
        const rankingData = await rankingResponse.json();
        if (rankingData && rankingData.length > 0) {
          const userId = rankingData[0].user_ID;

          // ユーザーIDからユーザー名を取得
          const userResponse = await fetch(`/api/getUser?user_ID=${userId}`);
          if (userResponse.ok) {
            const userData = await userResponse.json();
            const username = userData.username;
            if (username) {
              // 新しいURL形式で遷移
              router.push(`/${username}/post?ranking=${rankingId}`);
              return;
            }
          }
        }
      }

      // フォールバック: ユーザー名が取得できない場合はホームページに遷移
      router.push('/');
    } catch (error) {
      console.error('ランキング詳細取得エラー:', error);
      router.push('/');
    }
  };

  // 削除ボタンをクリックした時の処理
  const handleDeleteClick = (rankingId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 親要素のクリックイベントが発火するのを防ぐ
    setSelectedRankingId(rankingId);
    setShowDeleteModal(true);
  };

  // ランキングを削除する処理
  const deleteRanking = (rankingId: string) => {
    try {
      // ローカルストレージから保存したランキングデータを取得
      const storedRankings = JSON.parse(localStorage.getItem('savedRankings') || '[]');
      // 選択されたランキングを除外
      const updatedRankings = storedRankings.filter((ranking: RankingItem) => ranking.id !== rankingId);
      // 更新されたランキングリストをローカルストレージに保存
      localStorage.setItem('savedRankings', JSON.stringify(updatedRankings));
      // 状態を更新
      setSavedRankings(updatedRankings);
      // メニューを閉じる
      setMenuOpenId(null);
    } catch (error) {
      // ランキングの削除に失敗
    }
  };

  // 編集モードを切り替える
  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
  };

  // メニューを開く/閉じる
  const toggleMenu = (rankingId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 親要素のクリックイベントが発火するのを防ぐ
    if (menuOpenId === rankingId) {
      setMenuOpenId(null);
    } else {
      setMenuOpenId(rankingId);
    }
  };

  // 画面全体をクリックした時にメニューを閉じる
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuOpenId) {
        const target = event.target as HTMLElement;
        // メニューボタンとメニュー自体以外をクリックした場合
        if (!target.closest('.menu-button') && !target.closest('.menu-dropdown')) {
          setMenuOpenId(null);
        }
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [menuOpenId]);

  // 星評価を表示するコンポーネント
  const StarRating = ({ rating }: { rating: number }) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <span key={i} className="text-yellow-400 text-lg">
            {i < rating ? '★' : '☆'}
          </span>
        ))}
      </div>
    );
  };



  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ - 茶色の背景に変更 */}
      <div className="absolute inset-0 -z-10 bg-[#8B4513]/80 w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <div className="flex justify-between max-w-[500px] h-[52px] items-center relative bg-white shadow-[0px_0px_4px_#00000040] w-full">
        <div 
          className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative bg-white cursor-pointer"
          onClick={handleBack}
        >
          <Image 
            alt="Image left arrow" 
            src="/static/img/imageleftarrow.png" 
            width={30}
            height={30}
          />
          <p className="relative flex-[0_0_auto]">保存したランキング</p>
        </div>
        <div className="flex w-[52px] h-[52px] items-center justify-end pl-0 pr-[16px] py-0 relative bg-white">
          <Image 
            alt="Image question" 
            src="/static/img/imagequestion.png" 
            width={20}
            height={20}
          />
        </div>
      </div>

      {/* メインコンテンツ */}
      <div className="w-full bg-white" style={{ height: 'auto', flex: '1 1 0%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        {isLoading ? (
          <CatLoadingAnimation />
        ) : savedRankings.length === 0 ? (
          <div className="flex justify-center items-center h-32">
            <p className="text-gray-500">保存したランキングがありません</p>
          </div>
        ) : (
          <ul className="w-full">
            {savedRankings.map((item) => (
              <li key={item.id} className="border-b border-gray-200 relative">
                <div 
                  className="flex items-center p-4 hover:bg-gray-50 cursor-pointer"
                  onClick={() => openRanking(item.id)}
                  data-component-name="SavedRankingsPage"
                >
                  <div className="w-[100px] h-[100px] flex-shrink-0 bg-gray-200 mr-4 overflow-hidden rounded-md flex items-center justify-center">
                    {item.imageUrl ? (
                      <Image 
                        src={item.imageUrl} 
                        alt={item.title} 
                        className="object-cover"
                        width={100}
                        height={100}
                      />
                    ) : (
                      <div className="flex items-center justify-center w-full h-full bg-gray-100">
                        <span className="text-xs text-gray-400 text-center">no image</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 relative">
                    <div className="flex justify-between items-start">
                      <h3 className="text-sm font-medium text-gray-900 line-clamp-2 pr-8" data-component-name="SavedRankingsPage">{item.title}</h3>
                      
                      {/* 三点リーダーメニュー - タイトルの右側に配置 */}
                      <div className="absolute right-0 top-0">
                        <button 
                          className="text-gray-500 hover:text-gray-700 focus:outline-none menu-button"
                          onClick={(e) => toggleMenu(item.id, e)}
                          aria-label="メニューを開く"
                        >
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <circle cx="12" cy="6" r="2" />
                            <circle cx="12" cy="12" r="2" />
                            <circle cx="12" cy="18" r="2" />
                          </svg>
                        </button>
                        
                        {/* ドロップダウンメニュー */}
                        {menuOpenId === item.id && (
                          <div className="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg z-10 menu-dropdown">
                            <button
                              className="block w-full text-left px-4 py-2 text-sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteRanking(item.id);
                              }}
                            >
                              削除する
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <StarRating rating={item.rating} />
                    <p className="mt-1 text-xs text-gray-500 line-clamp-2" data-component-name="SavedRankingsPage">{item.description}</p>
                  </div>
                  
                  {isEditMode && (
                    <button
                      className="ml-2 px-3 py-1 text-xs text-red-500 border border-red-500 rounded-md"
                      onClick={(e) => handleDeleteClick(item.id, e)}
                    >
                      削除
                    </button>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* 編集ボタン - 保存したランキングがある場合のみ表示 */}
      {isMounted && savedRankings.length > 0 && (
        <div className="fixed bottom-6 right-6 z-50" style={{ position: 'fixed', bottom: '24px', right: '24px' }}>
          <button 
            onClick={toggleEditMode}
            className="block w-[80px] h-[40px] bg-white border border-gray-300 rounded-full flex items-center justify-center shadow-md"
          >
            <span className="text-gray-700 text-sm font-medium">
              {isEditMode ? '完了' : '編集'}
            </span>
          </button>
        </div>
      )}

      {/* 削除確認モーダル */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-[300px] max-w-[90%]">
            <p className="text-center mb-6">削除しますか？</p>
            <div className="flex justify-between">
              <button 
                className="text-blue-500 font-medium px-4 py-2"
                onClick={() => setShowDeleteModal(false)}
              >
                キャンセルする
              </button>
              <button 
                className="text-red-500 font-medium px-4 py-2"
                onClick={() => {
                  if (selectedRankingId) {
                    deleteRanking(selectedRankingId);
                    setShowDeleteModal(false);
                    setSelectedRankingId(null);
                  }
                }}
              >
                削除する
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
