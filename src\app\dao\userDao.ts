import { PrismaClient } from '@prisma/client';
import { User, AccounType, Category, SnsMaster, SnsUser, Ranking } from '../models/User'; // 型をインポート

const prisma = new PrismaClient();

export const userDao = {
  /**
   * 全ユーザーを取得
   */
  async getAllUsers(): Promise<User[]> {
    const users = await prisma.user.findMany({
      include: {
        Category: true,
        SnsUser: {
          include: {
            snsMaster: true
          }
        },
        UserByRanking: true,
      },
    });
    return users as unknown as User[]; // 型アサーションを利用
  },

  /**
   * IDでユーザーを取得
   */
  async getUserById(id: string): Promise<User | null> {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        Category: true,
        SnsUser: {
          include: {
            snsMaster: true
          }
        },
        UserByRanking: true,
      },
    });
    return user as User | null; // 型アサーションを利用
  },

  /**
   * 新しいユーザーを作成
   */
  async createUser(data: Omit<User, 'id' | 'created_at' | 'updated_at' | 'Category' | 'SnsUser' | 'UserByRanking'>): Promise<User> {
    const newUser = await prisma.user.create({
      data,
      include: {
        Category: true,
        SnsUser: {
          include: {
            snsMaster: true
          }
        },
        UserByRanking: true,
      },
    });
    return newUser as unknown as User; // 型アサーションを利用
  },
};
