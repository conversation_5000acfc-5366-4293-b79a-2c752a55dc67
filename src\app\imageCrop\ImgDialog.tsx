// components/ImgDialog.tsx
import React from 'react';

interface ImgDialogProps {
  img: string | null;
  onClose: () => void;
}

const ImgDialog: React.FC<ImgDialogProps> = ({ img, onClose }) => {
  if (!img) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white p-4 rounded-lg">
        <img
          src={img}
          alt="Cropped"
          className="w-64 h-64 object-cover rounded-full"
        />
        <button
          onClick={onClose}
          className="mt-4 bg-blue-500 text-white px-4 py-2 rounded"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ImgDialog;