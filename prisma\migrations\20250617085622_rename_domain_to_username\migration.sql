/*
  Warnings:

  - You are about to drop the column `domain` on the `Category` table. All the data in the column will be lost.
  - You are about to drop the column `domain` on the `Ranking` table. All the data in the column will be lost.
  - You are about to drop the column `domain` on the `User` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[username]` on the table `User` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `username` to the `Category` table without a default value. This is not possible if the table is not empty.
  - Added the required column `username` to the `Ranking` table without a default value. This is not possible if the table is not empty.
  - Added the required column `username` to the `User` table without a default value. This is not possible if the table is not empty.

*/
-- Step 1: Add username column to User table with default values
ALTER TABLE "User" ADD COLUMN "username" TEXT;

-- Step 2: Update username with domain values (convert domain to username format)
UPDATE "User" SET "username" = REPLACE("domain", 'user-', '');

-- Step 3: Make username NOT NULL and UNIQUE
ALTER TABLE "User" ALTER COLUMN "username" SET NOT NULL;
CREATE UNIQUE INDEX "User_username_key" ON "User"("username");

-- Step 4: Add username column to Category table
ALTER TABLE "Category" ADD COLUMN "username" TEXT;

-- Step 5: Update Category username from User table
UPDATE "Category" SET "username" = (
  SELECT "username" FROM "User" WHERE "User"."user_ID" = "Category"."user_ID"
);

-- Step 6: Make Category username NOT NULL
ALTER TABLE "Category" ALTER COLUMN "username" SET NOT NULL;

-- Step 7: Drop foreign key constraint for Ranking
ALTER TABLE "Ranking" DROP CONSTRAINT "Ranking_domain_fkey";

-- Step 8: Add username column to Ranking table
ALTER TABLE "Ranking" ADD COLUMN "username" TEXT;

-- Step 9: Update Ranking username from User table
UPDATE "Ranking" SET "username" = (
  SELECT "username" FROM "User" WHERE "User"."domain" = "Ranking"."domain"
);

-- Step 10: Make Ranking username NOT NULL
ALTER TABLE "Ranking" ALTER COLUMN "username" SET NOT NULL;

-- Step 11: Drop old indexes
DROP INDEX "Ranking_domain_idx";
DROP INDEX "User_domain_key";

-- Step 12: Create new indexes
CREATE INDEX "Ranking_username_idx" ON "Ranking"("username");

-- Step 13: Add new foreign key constraint
ALTER TABLE "Ranking" ADD CONSTRAINT "Ranking_username_fkey" FOREIGN KEY ("username") REFERENCES "User"("username") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Step 14: Drop old domain columns
ALTER TABLE "Category" DROP COLUMN "domain";
ALTER TABLE "Ranking" DROP COLUMN "domain";
ALTER TABLE "User" DROP COLUMN "domain";
