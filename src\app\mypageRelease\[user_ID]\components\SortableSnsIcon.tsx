import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { useDroppable } from "@dnd-kit/core";
import Image from "next/image";
import type { SortableSnsIconProps } from "../types";

export const SortableSnsIcon: React.FC<SortableSnsIconProps> = ({
  id,
  type,
  url,
  iconSrc,
  disabled = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    disabled,
    animateLayoutChanges: () => true,
  });

  // ドロップゾーンとしても機能させる
  const { isOver, setNodeRef: setDroppableRef } = useDroppable({
    id: `droppable-${id}`,
    disabled,
  });

  // 両方のrefを組み合わせる
  const setRefs = (node: HTMLElement | null) => {
    setNodeRef(node);
    setDroppableRef(node);
  };

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
    touchAction: disabled ? "auto" : "none",
    position: "relative" as const,
    background: isDragging
      ? "rgba(240,240,240,0.8)"
      : isOver
        ? "rgba(59, 130, 246, 0.1)"
        : "transparent",
    borderRadius: "8px",
    boxShadow: isDragging
      ? "0 5px 15px rgba(0,0,0,.2)"
      : isOver
        ? "0 0 0 2px rgba(59, 130, 246, 0.3)"
        : "none",
    cursor: disabled ? "default" : "grab",
  };

  return (
    <div
      ref={setRefs}
      style={style}
      className={`flex flex-col items-center transition-all duration-200 sortable-sns-icon p-2 ${
        isDragging ? "scale-105 shadow-lg" : ""
      } ${isOver ? "scale-105 bg-blue-50 border-2 border-blue-300 border-dashed" : ""} ${
        disabled ? "pointer-events-none" : ""
      }`}
      data-id={id}
      {...attributes}
      {...(disabled ? {} : listeners)}
    >
      <div
        className={`w-12 h-12 rounded-full bg-white flex items-center justify-center mb-1 ${
          isDragging ? "border-2 border-blue-400" : ""
        }`}
      >
        <Image
          src={iconSrc}
          alt={type}
          width={38}
          height={38}
          priority={true}
          loading="eager"
          className="h-[2.4rem] w-[2.4rem] object-contain rounded-[100px]"
        />
      </div>
      <span className="text-xs">{type}</span>
      {!isDragging && url && (
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="absolute inset-0 z-0"
          onClick={(e: React.MouseEvent) => e.stopPropagation()}
        />
      )}
    </div>
  );
};