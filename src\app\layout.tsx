import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Script from 'next/script';
import {
  ClerkProvider,
} from '@clerk/nextjs'
import { jaJP } from '@clerk/localizations'
import { UserProvider } from '../contexts/UserContext'

const inter = Inter({ subsets: ['latin'] })




export const metadata: Metadata = {
  title: {
    default: 'mypicks.best | おすすめをまとめよう',
    template: '%s | mypicks.best'
  },
  description: 'おすすめをまとめよう',
  // ファビコンをPNG形式に変更
  icons: {
    icon: '/static/img/mypicks.best-favicon.png',
  },
  // Material Iconsフォントを読み込む
  other: {
    'google-fonts': 'https://fonts.googleapis.com/icon?family=Material+Icons',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // 環境変数が正しく読み込まれているか確認するためのログ出力
  if (typeof window !== 'undefined') {
  }

  return (
    <html lang="ja">
      <body className={`${inter.className} h-screen w-full bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move`}>
        {/* 🔧 最優先: HTMLパース時に即座に実行される拡張機能ログブロック */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                'use strict';

                // 🔧 即座にconsole.logをオーバーライド（HTMLパース時）
                if (typeof window !== 'undefined' && window.console) {
                  const originalMethods = {
                    log: window.console.log,
                    warn: window.console.warn,
                    error: window.console.error,
                    info: window.console.info,
                    debug: window.console.debug
                  };

                  function shouldBlockLog(...args) {
                    try {
                      // 即座にスタックトレースをチェック
                      const error = new Error();
                      const stack = error.stack || '';

                      // 拡張機能のスタックトレースパターン
                      if (stack.includes('chrome-extension://') ||
                          stack.includes('moz-extension://') ||
                          stack.includes('content_script.js') ||
                          stack.includes('content.js') ||
                          stack.includes('webmssdk.js')) {
                        return true;
                      }

                      // 引数の内容をチェック
                      for (let i = 0; i < args.length; i++) {
                        const arg = args[i];

                        if (typeof arg === 'string') {
                          // 特定の拡張機能ログをブロック
                          if (arg === 'started up' ||
                              arg === 'no local pack model' ||
                              arg === '.' ||
                              arg.includes('localPack') ||
                              arg.includes('selected') ||
                              arg.includes('webmssdk')) {
                            return true;
                          }
                        } else if (arg && typeof arg === 'object') {
                          // UUIDキーを持つ大きなオブジェクトをブロック
                          try {
                            const keys = Object.keys(arg);
                            if (keys.length > 10) {
                              // UUIDパターンをチェック
                              const hasUuidKeys = keys.some(key =>
                                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(key)
                              );
                              if (hasUuidKeys) return true;
                            }

                            // localPackやselectedプロパティをチェック
                            if (arg.hasOwnProperty('localPack') || arg.hasOwnProperty('selected')) {
                              return true;
                            }
                          } catch (e) {}
                        }
                      }

                      return false;
                    } catch (e) {
                      return false;
                    }
                  }

                  // 全てのconsoleメソッドをオーバーライド
                  ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
                    window.console[method] = function(...args) {
                      if (!shouldBlockLog(...args)) {
                        return originalMethods[method].apply(this, args);
                      }
                    };
                  });

                  // console監視は削除（無限再帰を防ぐため）

                  // 定期チェックとプロトタイプ操作は削除（安定性のため）
                }
              })();
            `
          }}
        />

        <Script src="https://www.tiktok.com/embed.js" strategy="lazyOnload" />
        <Script src="https://platform.twitter.com/widgets.js" strategy="lazyOnload" />

        {/* 重複したログブロッカーは削除 */}

        {/* 🔧 拡張機能のSecurityErrorループを防止 */}
        <Script
          id="extension-security-fix"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                // 🔧 強化: 埋め込み数が多い場合の拡張機能SecurityError対策
                let errorCount = 0;
                const maxErrors = 1;
                let isProtectionActive = false;

                // 🔧 強化: iframe数を監視して予防的に対策を実行
                const monitorIframes = () => {
                  const iframes = document.querySelectorAll('iframe');
                  if (iframes.length >= 3 && !isProtectionActive) {
                    activateProtection();
                  }
                };

                // 🔧 強化: 予防的保護機能を有効化
                const activateProtection = () => {
                  if (isProtectionActive) return;
                  isProtectionActive = true;

                  // window.framesアクセスを制限
                  const originalFrames = window.frames;
                  Object.defineProperty(window, 'frames', {
                    get: function() {
                      const stack = new Error().stack || '';
                      const isExtensionCall = stack.includes('chrome-extension://') ||
                                             stack.includes('moz-extension://') ||
                                             stack.includes('content.js') ||
                                             stack.includes('content_script');

                      if (isExtensionCall) {
                        return { length: 0 };
                      }
                      return originalFrames;
                    },
                    configurable: true
                  });

                  // 🔧 強化: document.querySelectorAllも制限
                  const originalQuerySelectorAll = document.querySelectorAll;
                  document.querySelectorAll = function(selector) {
                    const stack = new Error().stack || '';
                    const isExtensionCall = stack.includes('chrome-extension://') ||
                                           stack.includes('moz-extension://') ||
                                           stack.includes('content.js');

                    if (isExtensionCall && selector.includes('iframe')) {
                      return { length: 0, forEach: () => {} };
                    }
                    return originalQuerySelectorAll.call(this, selector);
                  };

                  // 予防的保護機能を有効化
                };

                // SecurityErrorの無限ループを防止
                window.addEventListener('error', function(event) {
                  if (event.error && event.error.name === 'SecurityError') {
                    const errorMessage = event.error.message || '';
                    const stack = event.error.stack || '';

                    // 拡張機能からのエラーかチェック
                    const isExtensionError = stack.includes('chrome-extension://') ||
                                            stack.includes('moz-extension://') ||
                                            stack.includes('content.js') ||
                                            stack.includes('content_script');

                    const isIframeError = errorMessage.includes('iframe') ||
                                         errorMessage.includes('contentWindow') ||
                                         errorMessage.includes('contentDocument') ||
                                         errorMessage.includes('cross-origin');

                    if (isExtensionError || isIframeError) {
                      errorCount++;

                      // 拡張機能によるSecurityErrorを抑制

                      // 🔧 緊急修正: エラーの伝播を即座に停止
                      event.preventDefault();
                      event.stopPropagation();
                      event.stopImmediatePropagation();

                      // 🔧 強化: 1回目のエラーで即座に保護機能を有効化
                      activateProtection();

                      return false;
                    }
                  }
                }, true);

                // 🔧 強化: DOM変更を監視してiframe数をチェック
                const observer = new MutationObserver(() => {
                  monitorIframes();
                });

                // 🔧 強化: 即座に保護機能を有効化（拡張機能ログを防ぐため）
                activateProtection();

                // 🔧 強化: ページ読み込み完了後に監視開始
                if (document.readyState === 'loading') {
                  document.addEventListener('DOMContentLoaded', () => {
                    observer.observe(document.body, { childList: true, subtree: true });
                    monitorIframes();
                  });
                } else {
                  observer.observe(document.body, { childList: true, subtree: true });
                  monitorIframes();
                }

                // 強化された拡張機能SecurityError対策を初期化完了
              })();
            `
          }}
        />

        <ClerkProvider
          localization={jaJP}
          publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
          appearance={{
            elements: {
              // Clerkの読み込み最適化（本番環境対応）
              rootBox: 'clerk-root-optimized'
            }
          }}
        >
          <UserProvider>
            {children}
          </UserProvider>
        </ClerkProvider>
      </body>
    </html>
  )
}
