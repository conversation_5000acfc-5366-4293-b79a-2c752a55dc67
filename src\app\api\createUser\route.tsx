import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(req: NextRequest) {
  try {
    // リクエストボディのJSONデータを取得
    const postData = await req.json();

    console.log('createUser API - 受信データ:', postData);

    // 必須フィールドのチェック
    if (!postData.user_ID || !postData.username || !postData.name || !postData.email) {
      return NextResponse.json(
        { error: '必須フィールドが不足しています' },
        { status: 400 }
      );
    }

    // ユーザー名の重複チェック
    const existingUser = await prisma.user.findUnique({
      where: { username: postData.username }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'このユーザー名は既に使用されています' },
        { status: 409 }
      );
    }

    // account_typeの変換
    let accountType = 'email'; // デフォルト値
    if (postData.account_type === 'google' || postData.account_type === 'oauth_google') {
      accountType = 'google';
    } else if (postData.account_type === 'line' || postData.account_type === 'oauth_line') {
      accountType = 'line';
    }

    // Userコレクションに新しいデータを挿入
    const newUser = await prisma.user.create({
      data: {
        user_ID: postData.user_ID,
        username: postData.username,
        account_type: accountType as any, // Prismaの型エラーを回避
        email: postData.email,
        line: postData.line || null,
        google: postData.google || null,
        password: postData.password || null,
        name: postData.name,
        profile_image: postData.profile_image || null,
        contact_url: postData.contact_url || null,
        contact_email: postData.contact_email || null,
        self_introduction: postData.self_introduction || null,
        background_image: postData.background_image || null,
        // setup_completedフィールドは一時的にコメントアウト（Prismaクライアント再生成後に復活）
        // setup_completed: true,
      },
    });

    console.log('createUser API - ユーザー作成成功:', newUser.id);

    return NextResponse.json({
      success: true,
      user: {
        id: newUser.id,
        user_ID: newUser.user_ID,
        username: newUser.username,
        name: newUser.name,
        email: newUser.email
      }
    });
  } catch (error) {
    console.error('createUser API - エラー:', error);

    // Prismaエラーの詳細ログ
    if (error instanceof Error) {
      console.error('createUser API - エラーメッセージ:', error.message);
      console.error('createUser API - エラースタック:', error.stack);
    }

    // Prismaエラーの場合の詳細情報
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('createUser API - Prismaエラーコード:', (error as any).code);
      console.error('createUser API - Prismaエラーメタ:', (error as any).meta);
    }

    return NextResponse.json(
      {
        error: 'ユーザー作成に失敗しました',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
