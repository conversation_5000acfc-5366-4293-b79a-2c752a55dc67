import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

export async function PUT(request: NextRequest) {
  try {
    const { user_ID, categories } = await request.json();

    if (!user_ID || !categories || !Array.isArray(categories)) {
      return NextResponse.json(
        { error: 'user_ID and categories array are required' },
        { status: 400 }
      );
    }

    // 各カテゴリの存在確認とユーザー権限チェック
    const existingCategories = await prisma.category.findMany({
      where: {
        id: { in: categories.map((cat: any) => cat.id) },
        user_ID: user_ID
      },
      select: { id: true, user_ID: true, category_name: true }
    });

    if (existingCategories.length !== categories.length) {
      return NextResponse.json(
        {
          error: '一部のカテゴリが見つからないか、権限がありません',
          details: {
            requestedCount: categories.length,
            foundCount: existingCategories.length,
            missingIds: categories.map((cat: any) => cat.id).filter(id =>
              !existingCategories.some(existing => existing.id === id)
            )
          }
        },
        { status: 403 }
      );
    }

    // 個別更新処理（トランザクションを使わずに順次実行）
    const updateResults = [];
    for (const category of categories) {
      try {
        const result = await prisma.category.update({
          where: {
            id: category.id,
            user_ID: user_ID, // セキュリティ: ユーザーIDも確認
          },
          data: {
            order: category.order,
          },
        });
        updateResults.push(result);
      } catch (updateError: any) {
        // 個別のエラーは記録するが、処理は続行
      }
    }

    return NextResponse.json({
      success: true,
      message: 'カテゴリ順序が正常に更新されました',
      updatedCount: updateResults.length,
      requestedCount: categories.length,
    });

  } catch (error: any) {
    return NextResponse.json(
      {
        error: 'カテゴリ順序の更新に失敗しました',
        details: error.message,
        code: error.code
      },
      { status: 500 }
    );
  }
}
