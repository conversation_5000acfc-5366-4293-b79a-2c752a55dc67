'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, useParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';

// ドラフトアイテムの型定義
interface DraftItem {
  id: string;
  title: string;
  description: string;
  thumbnailImage?: string;
  images: string[];
  userId: string;
  createdAt: string;
  // 追加フィールド（下位互換性のためオプショナル）
  category?: string;
  categoryId?: string;
  subCategory?: string;
  rating?: number;
  amazonUrl?: string;
  rakutenUrl?: string;
  yahooUrl?: string;
  qoo10Url?: string;
  officialUrl?: string;
}

export default function DraftListPage() {
  const router = useRouter();
  const params = useParams();
  const { user, isLoaded } = useUser();
  
  const username = params.username as string;
  
  const [draftItems, setDraftItems] = useState<DraftItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string>("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedDraftId, setSelectedDraftId] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // 戻るボタンのハンドラー
  const handleBack = () => {
    // ユーザーのプロフィールページに戻る
    router.push(`/${username}`);
  };

  // コンポーネントがマウントされたことを確認
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // ユーザー認証とuser_ID取得
  useEffect(() => {
    const checkUserAuth = async () => {
      if (!isLoaded) return;
      
      if (!user) {
        // ログインしていない場合はログインページにリダイレクト
        router.push('/sign-in');
        return;
      }

      try {
        // 現在のユーザーのユーザー名を取得
        const response = await fetch(`/api/getUser?user_ID=${user.id}`);
        if (response.ok) {
          const userData = await response.json();
          const currentUsername = userData.username;

          // URLのユーザー名と現在のユーザーのユーザー名が一致しない場合はリダイレクト
          if (currentUsername && currentUsername !== username) {
            router.push(`/${currentUsername}/draftList`);
            return;
          }

          // ユーザー名が取得できない場合もホームページにリダイレクト
          if (!currentUsername) {
            router.push('/');
            return;
          }
          
          setUserId(user.id);
        } else {
          // ユーザー情報が取得できない場合はホームページにリダイレクト
          router.push('/');
          return;
        }
      } catch (error) {
        console.error("ユーザー認証チェックエラー:", error);
        router.push('/');
      }
    };

    checkUserAuth();
  }, [user, isLoaded, username, router]);

  // 下書きデータを取得
  useEffect(() => {
    if (!isMounted || !userId) return;

    const fetchDrafts = async () => {
      try {
        // まず、ローカルストレージからの移行処理を行う
        await migrateLocalStorageDrafts();

        // データベースから下書きを取得
        const response = await fetch(`/api/getDrafts?user_ID=${userId}`);

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setDraftItems(result.data || []);
          } else {
            console.error('下書き取得エラー:', result.error);
            setDraftItems([]);
          }
        } else {
          console.error('下書き取得APIエラー:', response.status);
          setDraftItems([]);
        }
      } catch (error) {
        console.error('下書きデータの取得に失敗しました:', error);
        setDraftItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDrafts();
  }, [isMounted, userId]);

  // ローカルストレージからデータベースへの移行処理
  const migrateLocalStorageDrafts = async () => {
    try {
      // 新しい形式の下書きデータを確認
      const storedDrafts = localStorage.getItem('rankingDrafts');
      let localDrafts: DraftItem[] = [];

      if (storedDrafts) {
        localDrafts = JSON.parse(storedDrafts);
      }

      // 古い形式の単一下書きがあれば移行
      const oldDraft = localStorage.getItem('draftRanking');
      if (oldDraft) {
        try {
          const oldDraftData = JSON.parse(oldDraft);
          // 古い形式を新しい形式に変換
          const convertedDraft: DraftItem = {
            id: `draft_migrated_${Date.now()}`,
            title: oldDraftData.title || "無題のランキング",
            description: oldDraftData.recommendText || "",
            thumbnailImage: oldDraftData.images && oldDraftData.images.length > 0 ? oldDraftData.images[0] : "",
            images: oldDraftData.images || [],
            userId: oldDraftData.userId || userId,
            createdAt: new Date().toISOString(),
            category: oldDraftData.category || "",
            categoryId: oldDraftData.categoryId || "",
            subCategory: oldDraftData.subCategory || "",
            rating: oldDraftData.rating || 1,
            amazonUrl: oldDraftData.amazonUrl || "",
            rakutenUrl: oldDraftData.rakutenUrl || "",
            yahooUrl: oldDraftData.yahooUrl || "",
            qoo10Url: oldDraftData.qoo10Url || "",
            officialUrl: oldDraftData.officialUrl || ""
          };

          localDrafts = [convertedDraft, ...localDrafts];
          localStorage.removeItem('draftRanking');
          console.log('古い下書きデータを変換しました');
        } catch (migrationError) {
          console.error('下書きデータの移行に失敗しました:', migrationError);
        }
      }

      // ローカルストレージの下書きをデータベースに移行
      if (localDrafts.length > 0) {
        console.log(`${localDrafts.length}件のローカル下書きをデータベースに移行します`);

        for (const draft of localDrafts) {
          try {
            const migrationData = {
              title: draft.title,
              ranking_description: draft.description,
              user_ID: draft.userId || userId,
              categoryId: draft.categoryId || '',
              subCategoryID: draft.subCategory || '',
              images: draft.images || [],
              amazon_url: draft.amazonUrl || '',
              rakuten_url: draft.rakutenUrl || '',
              yahoo_url: draft.yahooUrl || '',
              qoo10_url: draft.qoo10Url || '',
              official_url: draft.officialUrl || '',
              recommend_rate: draft.rating || 1,
            };

            const response = await fetch('/api/saveDraft', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(migrationData),
            });

            if (response.ok) {
              console.log(`下書き "${draft.title}" をデータベースに移行しました`);
            } else {
              console.error(`下書き "${draft.title}" の移行に失敗しました`);
            }
          } catch (error) {
            console.error(`下書き "${draft.title}" の移行中にエラー:`, error);
          }
        }

        // 移行完了後、ローカルストレージをクリア
        localStorage.removeItem('rankingDrafts');
        console.log('ローカルストレージの下書きデータを削除しました');
      }
    } catch (error) {
      console.error('ローカルストレージ移行処理でエラー:', error);
    }
  };

  // 下書きを開く処理
  const openDraft = (draftId: string) => {
    // 下書きアイテムからcategoryParamを取得
    const draftItem = draftItems.find(item => item.id === draftId);
    const categoryParam = (draftItem as any)?.categoryParam || draftItem?.categoryId || '';

    // categoryパラメータも含めてURLを生成
    const url = categoryParam
      ? `/${username}/add?draftId=${draftId}&category=${categoryParam}`
      : `/${username}/add?draftId=${draftId}`;

    router.push(url);
  };

  // 削除ボタンをクリックした時の処理
  const handleDeleteClick = (draftId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 親要素のクリックイベントが発火するのを防ぐ
    setSelectedDraftId(draftId);
    setShowDeleteModal(true);
  };

  // 下書きを削除する処理
  const deleteDraft = async () => {
    if (!selectedDraftId) return;

    try {
      // データベースから下書きを削除
      const response = await fetch('/api/deleteDraft', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ranking_ID: selectedDraftId,
          user_ID: userId,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // 状態から削除された下書きを除外
        const updatedDrafts = draftItems.filter(draft => draft.id !== selectedDraftId);
        setDraftItems(updatedDrafts);
        setShowDeleteModal(false);
        setSelectedDraftId(null);
      } else {
        console.error('下書き削除エラー:', result.error);
        alert(result.error || '下書きの削除に失敗しました。');
      }
    } catch (error) {
      console.error('下書きの削除に失敗しました:', error);
      alert('下書きの削除に失敗しました。もう一度お試しください。');
    }
  };

  // ローディング中の表示
  if (!isLoaded || isLoading) {
    return (
      <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center justify-center overflow-hidden">
        {/* 背景オーバーレイ */}
        <div className="absolute inset-0 -z-10 bg-[#8B4513]/80 w-full" style={{ height: '200%' }}></div>
        
        {/* ヘッダー */}
        <div className="flex justify-between max-w-[500px] h-[52px] items-center relative bg-white shadow-[0px_0px_4px_#00000040] w-full">
          <div className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative bg-white">
            <p className="relative flex-[0_0_auto]">下書き一覧</p>
          </div>
        </div>

        {/* メインコンテンツ */}
        <div className="w-full bg-white" style={{ height: 'auto', flex: '1 1 0%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          <div className="flex-1 flex items-center justify-center">
            <CatLoadingAnimation />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ - 茶色の背景に変更 */}
      <div className="absolute inset-0 -z-10 bg-[#8B4513]/80 w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <div className="flex justify-between max-w-[500px] h-[52px] items-center relative bg-white shadow-[0px_0px_4px_#00000040] w-full">
        <div 
          className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative bg-white cursor-pointer"
          onClick={handleBack}
        >
          <Image 
            alt="Image left arrow" 
            src="/static/img/imageleftarrow.png" 
            width={30}
            height={30}
          />
          <p className="relative flex-[0_0_auto]">下書き一覧</p>
        </div>
        <div className="flex w-[52px] h-[52px] items-center justify-end pl-0 pr-[16px] py-0 relative bg-white">
          <Image 
            alt="Image question" 
            src="/static/img/imagequestion.png" 
            width={20}
            height={20}
          />
        </div>
      </div>

      {/* メインコンテンツ */}
      <div className="w-full bg-white" style={{ height: 'auto', flex: '1 1 0%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        {draftItems.length === 0 ? (
          <div className="flex justify-center items-center h-32">
            <p className="text-gray-500">下書きがありません</p>
          </div>
        ) : (
          <ul className="w-full">
            {draftItems.map((item) => (
              <li key={item.id} className="border-b border-gray-200">
                <div
                  className="flex items-center p-4 hover:bg-gray-50 cursor-pointer"
                  onClick={() => openDraft(item.id)}
                >
                  <div className="w-[45px] h-[45px] flex-shrink-0 bg-gray-200 mr-4 overflow-hidden rounded-md flex items-center justify-center">
                    {item.thumbnailImage ? (
                      <Image
                        src={item.thumbnailImage}
                        alt="ドラフト画像"
                        className="object-cover"
                        width={45}
                        height={45}
                      />
                    ) : (
                      <div className="flex items-center justify-center w-full h-full bg-gray-100">
                        <span className="text-xs text-gray-400 text-center">no image</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-600">{item.title}</p>
                  </div>
                  <button
                    className="px-4 py-2 text-sm text-white bg-[#1f2937] rounded-md"
                    onClick={(e: React.MouseEvent) => handleDeleteClick(item.id, e)}
                  >
                    削除する
                  </button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* 削除確認モーダル */}
      {showDeleteModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0 bg-black opacity-50"></div>
          <div className="bg-white p-6 rounded-lg shadow-xl z-10 max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4">本当に削除しますか？</h3>
            <p className="mb-6 text-sm text-gray-600">
              この下書きを削除すると、元に戻せません。
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm"
              >
                キャンセル
              </button>
              <button
                onClick={deleteDraft}
                className="px-4 py-2 bg-[#1f2937] text-white rounded-md text-sm"
              >
                削除する
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
