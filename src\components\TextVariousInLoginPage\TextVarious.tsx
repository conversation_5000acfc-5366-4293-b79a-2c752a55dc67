/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
}

export const TextVarious = ({ className }: Props): JSX.Element => {
  return (
    <div className={`inline-flex items-center justify-center gap-[10px] pt-[24px] pb-0 px-0 relative ${className}`}>
      <p className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-transparent text-[12px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
        <span className="text-[#313131]">パスワードを忘れた場合は</span>
        <span className="text-[#e63b5f] underline">こちら</span>
      </p>
    </div>
  );
};
