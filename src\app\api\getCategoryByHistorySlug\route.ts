import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * history_slugからカテゴリを検索するAPIエンドポイント
 * GET: 指定されたhistory_slugを持つカテゴリを検索
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const historySlug = searchParams.get('history_slug');
    const user_ID = searchParams.get('user_ID');

    if (!historySlug) {
      return NextResponse.json({
        error: 'history_slugが必要です'
      }, { status: 400 });
    }

    // user_IDが指定されている場合は、そのユーザーのカテゴリのみ検索
    const whereCondition: any = {
      history_slug: historySlug,
      parent_ID: null // メインカテゴリのみ
    };

    if (user_ID) {
      whereCondition.user_ID = user_ID;
    }

    // history_slugでカテゴリを検索
    const category = await prisma.category.findFirst({
      where: whereCondition,
      select: {
        id: true,
        user_ID: true,
        category_ID: true,
        category_name: true,
        category_slug: true,
        history_slug: true,
        created_at: true,
        updated_at: true
      }
    });

    if (!category) {
      return NextResponse.json({
        error: 'カテゴリが見つかりません',
        historySlug,
        user_ID
      }, { status: 404 });
    }

    // ユーザー情報も取得してusernameを含める
    const user = await prisma.user.findUnique({
      where: { user_ID: category.user_ID },
      select: {
        user_ID: true,
        username: true,
        name: true
      }
    });

    const result = {
      success: true,
      category: {
        id: category.id,
        user_ID: category.user_ID,
        category_ID: category.category_ID,
        category_name: category.category_name,
        category_slug: category.category_slug,
        history_slug: category.history_slug,
        created_at: category.created_at,
        updated_at: category.updated_at
      },
      user: user ? {
        user_ID: user.user_ID,
        username: user.username,
        name: user.name
      } : null,
      redirectUrl: user?.username ? `/${user.username}?category=${category.category_slug}` : null
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('getCategoryByHistorySlug エラー:', error);
    return NextResponse.json({
      error: 'サーバーエラーが発生しました',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
