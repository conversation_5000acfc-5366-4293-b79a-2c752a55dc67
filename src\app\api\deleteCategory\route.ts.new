import { NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';

// Prismaクライアントのシングルトンインスタンスを作成
let prisma: PrismaClient;
if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  if (!(global as any).prisma) {
    (global as any).prisma = new PrismaClient();
  }
  prisma = (global as any).prisma;
}

// DELETEリクエストに対応（クエリパラメータからID取得）
export async function DELETE(request: Request) {
  try {
    // URLからクエリパラメータを取得
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const user_ID = url.searchParams.get('user_ID');

    // 必須パラメータのバリデーション
    if (!id || !user_ID) {
      return NextResponse.json(
        { success: false, error: "必須パラメータが不足しています" },
        { 
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

    // 新規カテゴリの場合（IDが"new_category_"で始まる）はローカルでのみ削除
    if (id.startsWith("new_category_")) {
      return NextResponse.json({
        success: true,
        message: "新規カテゴリをローカルで削除しました",
      }, {
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      });
    }

    // カテゴリを削除
    const deletedCategory = await prisma.category.deleteMany({
      where: {
        id: id,
        user_ID: user_ID
      }
    });

    return NextResponse.json({
      success: true,
      message: "カテゴリを削除しました",
      result: deletedCategory,
    }, {
      headers: {
        'Access-Control-Allow-Origin': 'http://localhost:3000',
        'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
      }
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "カテゴリの削除中にエラーが発生しました", details: error },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// GETリクエストに対応（クエリパラメータからID取得）
export async function GET(request: Request) {
  try {
    // URLからクエリパラメータを取得
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    const user_ID = url.searchParams.get('user_ID');

    // 必須パラメータのバリデーション
    if (!id || !user_ID) {
      return NextResponse.json(
        { success: false, error: "必須パラメータが不足しています" },
        { 
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

    // 新規カテゴリの場合（IDが"new_category_"で始まる）はローカルでのみ削除
    if (id.startsWith("new_category_")) {
      return NextResponse.json({
        success: true,
        message: "新規カテゴリをローカルで削除しました",
      }, {
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      });
    }

    // カテゴリを削除
    const deletedCategory = await prisma.category.deleteMany({
      where: {
        id: id,
        user_ID: user_ID
      }
    });

    return NextResponse.json({
      success: true,
      message: "カテゴリを削除しました",
      result: deletedCategory,
    }, {
      headers: {
        'Access-Control-Allow-Origin': 'http://localhost:3000',
        'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
      }
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "カテゴリの削除中にエラーが発生しました", details: error },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// POSTリクエストに対応（後方互換性のため）
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { id, user_ID } = body;

    // 必須パラメータのバリデーション
    if (!id || !user_ID) {
      return NextResponse.json(
        { success: false, error: "必須パラメータが不足しています" },
        { 
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

    // 新規カテゴリの場合（IDが"new_category_"で始まる）はローカルでのみ削除
    if (id.startsWith("new_category_")) {
      return NextResponse.json({
        success: true,
        message: "新規カテゴリをローカルで削除しました",
      }, {
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      });
    }

    // カテゴリを削除
    const deletedCategory = await prisma.category.deleteMany({
      where: {
        id: id,
        user_ID: user_ID
      }
    });

    return NextResponse.json({
      success: true,
      message: "カテゴリを削除しました",
      result: deletedCategory,
    }, {
      headers: {
        'Access-Control-Allow-Origin': 'http://localhost:3000',
        'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
      }
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "カテゴリの削除中にエラーが発生しました", details: error },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}
