"use client";

import React, { useEffect, useRef } from "react";
import { RankingContentProps } from "../types";
import { SNS_EMBED_DELAY } from "../constants";
import { processInstagramEmbeds } from "../../../../../utils/instagramEmbedUtils";

// Memoized content component for SNS embeds
export const RankingContent = React.memo(({ description, rankingId }: RankingContentProps) => {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 型チェックを強化：descriptionが文字列であることを確認
    if (!description || typeof description !== 'string' || !contentRef.current) return;

    const contentElement = contentRef.current;

    // Process SNS embeds only if they exist in the content
    if (description.includes('social-embed') || description.includes('blockquote') || description.includes('iframe')) {
      const timeoutId = setTimeout(() => {
        // Instagram embed processing（安全な実装）
        if (description.includes('instagram-media')) {
          processInstagramEmbeds(contentRef.current || undefined);
        }

        // Twitter/X embed processing
        if (description.includes('twitter-tweet') && window.twttr && window.twttr.widgets) {
          try {
            window.twttr.widgets.load(contentRef.current);
          } catch (error) {
            // Twitter処理エラー（ログ削除）
          }
        }

        // TikTok embed processing
        if (description.includes('tiktok-embed') && (window as any).TikTokEmbed && (window as any).TikTokEmbed.load) {
          try {
            (window as any).TikTokEmbed.load();
          } catch (error) {
            // TikTok処理エラー（ログ削除）
          }
        }
      }, SNS_EMBED_DELAY);

      return () => clearTimeout(timeoutId);
    }
  }, [description, rankingId]);

  // 安全なHTMLコンテンツを生成
  const safeHtmlContent = React.useMemo(() => {
    // 型チェック：descriptionが文字列であることを確認
    if (!description || typeof description !== 'string') {
      return '';
    }
    return description;
  }, [description]);

  return (
    <div
      ref={contentRef}
      className="ranking-content w-full pl-[16px] pt-[16px] pr-[16px] pb-[16px] text-[16px]"
      dangerouslySetInnerHTML={{ __html: safeHtmlContent }}
    />
  );
});

RankingContent.displayName = 'RankingContent';