'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/Header/Header';
import { Footer } from '@/components/Footer';
import { HeaderItems } from '@/components/HeaderItems';

export default function ChangeEmailPage() {
  const router = useRouter();
  const [currentEmail, setCurrentEmail] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [confirmEmail, setConfirmEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // メールアドレス更新処理
  const handleUpdateEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    // 入力検証
    if (!currentEmail || !newEmail || !confirmEmail) {
      setError('すべての項目を入力してください');
      return;
    }
    
    if (newEmail !== confirmEmail) {
      setError('新しいメールアドレスと確認用メールアドレスが一致しません');
      return;
    }
    
    // 簡易的なメールアドレス検証
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      setError('有効なメールアドレスを入力してください');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // ここにAPIリクエストを実装
      // 例: await updateEmail(currentEmail, newEmail);
      
      // 成功時の処理
      alert('メールアドレスが正常に更新されました');
      router.push('/settings');
    } catch (err) {
      console.error('メールアドレス更新エラー:', err);
      setError('メールアドレスの更新に失敗しました。もう一度お試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-[500px] mx-auto flex flex-col min-h-screen relative bg-white">
      <HeaderItems text="メールアドレスを変更" />

      {/* メインコンテンツ */}
      <main className="flex-1 px-10 py-6">
        <form onSubmit={handleUpdateEmail}>
          {/* 現在のメールアドレス */}
          <div className="mb-6">
            <label 
              htmlFor="current-email" 
              className="block text-[14px] text-[#313131] mb-2"
            >
              現在のメールアドレス
            </label>
            <input
              id="current-email"
              type="email"
              value={currentEmail}
              onChange={(e) => setCurrentEmail(e.target.value)}
              className="w-full p-3 border border-[#DDDDDD] rounded text-[#313131] bg-white"
              placeholder="<EMAIL>"
            />
          </div>

          {/* 新しいメールアドレス */}
          <div className="mb-6">
            <label 
              htmlFor="new-email" 
              className="block text-[14px] text-[#313131] mb-2"
            >
              新しいメールアドレス
            </label>
            <input
              id="new-email"
              type="email"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              className="w-full p-3 border border-[#DDDDDD] rounded text-[#313131] bg-white"
              placeholder="<EMAIL>"
            />
          </div>

          {/* メールアドレスを確認 */}
          <div className="">
            <label 
              htmlFor="confirm-email" 
              className="block text-[14px] text-[#313131] mb-2"
            >
              メールアドレスを確認
            </label>
            <input
              id="confirm-email"
              type="email"
              value={confirmEmail}
              onChange={(e) => setConfirmEmail(e.target.value)}
              className="w-full p-3 border border-[#DDDDDD] rounded text-[#313131] bg-white"
              placeholder="<EMAIL>"
            />
          </div>

          {/* エラーメッセージ */}
          {error && (
            <div className="mb-4 text-red-500 text-[14px]">
              {error}
            </div>
          )}

          {/* 更新ボタン */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-[#E63B5F] text-white py-3 rounded-full mt-14 hover:bg-[#DD0F2B]"
          >
            {isLoading ? '処理中...' : 'メールアドレスを更新'}
          </button>
        </form>
      </main>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
