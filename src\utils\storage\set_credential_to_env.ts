import fs from 'fs';
import path from 'path';

// マウントされたシークレットのファイルパス
const secretFilePath = path.join('/etc/secret', 'CLIENT_JSON');

// ファイルの存在を確認
if (fs.existsSync(secretFilePath)) {
  // GOOGLE_APPLICATION_CREDENTIALSという環境変数を設定する
  process.env.GOOGLE_APPLICATION_CREDENTIALS = secretFilePath;
    // ファイルの内容を読み込み
    const secretContent = fs.readFileSync(secretFilePath, 'utf8');
    
    // ここで secretContent を使用する
    // 例えば、JSONとしてパースする場合:
    const secretData = JSON.parse(secretContent);
} else {
    console.error('Secret file does not exist:', secretFilePath);
}
