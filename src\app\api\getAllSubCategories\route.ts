import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // URLパラメータからユーザーIDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');

    // ユーザーIDは必須
    if (!user_ID) {
      return NextResponse.json(
        { error: 'ユーザーIDは必須です' },
        { status: 400 }
      );
    }

    console.log(`すべてのサブカテゴリを取得します。ユーザーID: ${user_ID}`);

    // ユーザーに紐づくすべてのサブカテゴリを取得
    // parent_IDがnullでないものがサブカテゴリ
    const subCategories = await prisma.category.findMany({
      where: {
        user_ID: user_ID,
        NOT: {
          parent_ID: null
        }
      },
      select: {
        id: true,
        category_name: true,
        category_ID: true,
        parent_ID: true,
        created_at: true,
        order: true
      },
      orderBy: {
        order: 'asc'
      }
    });

    console.log(`取得したサブカテゴリ数: ${subCategories.length}`);
    
    return NextResponse.json(subCategories, { status: 200 });
  } catch (error) {
    console.error('すべてのサブカテゴリ取得エラー:', error);
    return NextResponse.json(
      { error: 'すべてのサブカテゴリの取得に失敗しました', details: error instanceof Error ? error.message : '不明なエラー' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
