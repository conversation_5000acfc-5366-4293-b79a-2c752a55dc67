/* eslint-disable @next/next/no-img-element */
import React, { useState } from "react";

interface Props {
  className: any;
  arrowImage?: string;
  onClick?: () => void;
  domain: string;
  text?: string;
  text1?: string;
  useButtonProfileEdit?: boolean;
}

export const TextUserUrlOpen = ({ className, domain, onClick, useButtonProfileEdit = false }: Props): JSX.Element => {
  const [isFlipped, setIsFlipped] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const handleClick = (e?: React.MouseEvent) => {
    // イベントが存在する場合、デフォルトの動作を防止
    if (e) {
      e.preventDefault();
      e.stopPropagation(); // イベントの伝播も停止
    }
    
    setIsFlipped(!isFlipped);

    // onClick が関数として存在する場合のみ実行
    if (onClick && typeof onClick === 'function') {
      onClick();
    }

    // スクロール位置復元を無効化（自動スクロールによる挙動不安定を防止）
  };

  const handleUrlClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowShareModal(true);
  };

  const handleCloseModal = () => {
    setShowShareModal(false);
  };

  const handleCopyUrl = () => {
    // domainがusernameとして使用されている場合の対応
    const url = `https://mypicks.best/${domain}`;
    navigator.clipboard.writeText(url)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch(() => {
        // コピーエラーは無視
      });
  };

  const handleShare = () => {
    // domainがusernameとして使用されている場合の対応
    const url = `https://mypicks.best/${domain}`;
    const title = "mypicks.bestを共有する";
    const text = "mypicks.bestのプロフィールを見てください！";

    if (navigator.share) {
      navigator.share({
        title,
        text,
        url
      })
      .catch(error => {
        console.error('共有に失敗しました:', error);
      });
    } else {
      handleCopyUrl();
    }
  };

  return (
    <div className={`flex flex-col ${className}`}>
      {domain && domain.trim() !== "" && (
        <div className="inline-flex items-center justify-center gap-[8px] pt-[8px] pb-0 px-0 relative h-[30px]">
          <a 
            href={`https://mypicks.best/${domain}`}
            onClick={handleUrlClick}
            className="relative w-fit font-normal text-white text-[11px] tracking-[0] leading-[14px] whitespace-nowrap hover:underline"
          >
            {`https://mypicks.best/${domain}`}
          </a>

          <button
            className="relative w-[24px] h-[24px] flex items-center justify-center"
            onClick={handleClick}
          >
            <img
              className={`relative w-[24px] h-[16px] object-cover transition-transform duration-300 ${isFlipped ? "scale-y-[-1]" : "scale-y-[1]"}`}
              alt="Arrow image"
              src="/static/img/arrowimage.png"
            />
          </button>
        </div>
      )}
      
      {/* プロフィール編集リンクは条件付きで表示 */}
      {useButtonProfileEdit && (
        <div className="mt-[24px] text-center text-white text-[12px]">
          <div className="p-3 bg-black/30 rounded-[2px]">
            <a
              href={`/${domain}/edit`}
              className="text-white"
            >
              プロフィール編集
            </a>
          </div>
        </div>
      )}

      {/* シェアモーダル */}
      {showShareModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">mypicks.bestを共有する</h3>
              <button 
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="flex justify-center mb-4">
              <div className="border p-2">
                <img 
                  src={`https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://mypicks.best/${domain}`} 
                  alt="QR Code" 
                  className="w-[150px] h-[150px]"
                />
              </div>
            </div>
            
            <div className="space-y-3">
              <button
                onClick={handleCopyUrl}
                className={`w-full py-3 rounded font-medium transition-colors duration-200 ${
                  isCopied ? 'bg-black text-white' : 'bg-[#FFD814]'
                }`}
              >
                {isCopied ? 'URLをコピーしました' : 'URLをコピーする'}
              </button>
              
              <button 
                onClick={handleShare}
                className="w-full py-3 bg-[#E94C6F] text-white rounded font-medium"
              >
                共有する
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
