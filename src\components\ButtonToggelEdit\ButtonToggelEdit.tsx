/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
}

export const ButtonToggelEdit = ({ className }: Props): JSX.Element => {
  return (
    <div className={`flex flex-col w-[390px] items-start pl-[16px] pr-[6px] pt-0 pb-[24px] relative ${className}`}>
      <div className="inline-flex flex-col items-start px-[10px] py-[12px] flex-[0_0_auto] bg-white relative rounded-[100px]">
        <div className="flex w-[180px] h-[28px] items-center bg-button-gray overflow-hidden relative rounded-[100px]">
          <div className="flex flex-col w-[90px] h-[28px] items-center justify-center relative bg-background rounded-[100px] shadow-toggle-button">
            <div className="text-white relative w-fit [font-family:'Roboto',Helvetica] font-normal text-[12px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
              編集
            </div>
          </div>
          <div className="flex flex-col w-[90px] h-[28px] items-center justify-center gap-[10px] relative rounded-[100px] shadow-toggle-button">
            <div className="text-black-1 relative w-fit [font-family:'Roboto',Helvetica] font-normal text-[12px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
              プレビュー
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
