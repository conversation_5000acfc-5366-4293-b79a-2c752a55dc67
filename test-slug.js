// テスト用スクリプト - Node.js環境で実行
const { convertToSlug } = require('./src/utils/slugUtils.ts');

async function testSlugConversion() {
  const testCases = [
    '横浜',
    '渋谷皮膚科',
    'カテゴリ1',
    'ひらがな',
    'カタカナ',
    '東京駅',
    'English',
    '123',
    'Mixed英語日本語',
    '新宿-渋谷',
    ''
  ];

  console.log('🧪 スラッグ変換テスト開始\n');

  for (const testCase of testCases) {
    try {
      const result = await convertToSlug(testCase);
      console.log(`入力: "${testCase}" → 出力: "${result}"`);
    } catch (error) {
      console.error(`❌ エラー: "${testCase}" →`, error.message);
    }
  }

  console.log('\n✅ テスト完了');
}

testSlugConversion().catch(console.error);
