import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('🔍 [saveRankingNew] GETリクエストを受信しました');
  return NextResponse.json({
    message: 'saveRankingNew API is working',
    timestamp: new Date().toISOString(),
    method: 'GET'
  });
}

export async function POST(request: NextRequest) {
  console.log('🔍 [saveRankingNew] POSTリクエストを受信しました');
  
  try {
    const data = await request.json();
    console.log('📋 [saveRankingNew] 受信データ:', data);
    
    return NextResponse.json({
      success: true,
      message: 'saveRankingNew POST is working',
      timestamp: new Date().toISOString(),
      receivedData: data
    });
  } catch (error) {
    console.error('❌ [saveRankingNew] エラー:', error);
    return NextResponse.json({
      error: 'saveRankingNew APIでエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  }
}
