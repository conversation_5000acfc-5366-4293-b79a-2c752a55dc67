import React, { useState } from 'react';
import { ToolbarButton, TextAlign } from '../types';
import { COLOR_OPTIONS } from '../constants';
import ColorPalette from './ColorPalette';

interface ToolbarProps {
  onToolbarClick: (command: string, value?: string) => void;
  onTextAlignCommand: () => void;
  onLinkCommand: () => void;
  textAlign: TextAlign;
}

const Toolbar: React.FC<ToolbarProps> = ({
  onToolbarClick,
  onTextAlignCommand,
  onLinkCommand,
  textAlign
}) => {
  const [isColorPaletteOpen, setIsColorPaletteOpen] = useState(false);

  // テキスト位置のアイコンを取得する関数
  const getAlignIcon = () => {
    switch (textAlign) {
      case 'left':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1px', fontSize: '8px', lineHeight: '1' }}>
            <div style={{ width: '12px', height: '1px', backgroundColor: 'currentColor' }}></div>
            <div style={{ width: '8px', height: '1px', backgroundColor: 'currentColor' }}></div>
            <div style={{ width: '10px', height: '1px', backgroundColor: 'currentColor' }}></div>
          </div>
        );
      case 'center':
        return '≡'; // 中央寄せアイコン
      case 'right':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1px', fontSize: '8px', lineHeight: '1', alignItems: 'flex-end' }}>
            <div style={{ width: '12px', height: '1px', backgroundColor: 'currentColor' }}></div>
            <div style={{ width: '8px', height: '1px', backgroundColor: 'currentColor' }}></div>
            <div style={{ width: '10px', height: '1px', backgroundColor: 'currentColor' }}></div>
          </div>
        );
      default:
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1px', fontSize: '8px', lineHeight: '1' }}>
            <div style={{ width: '12px', height: '1px', backgroundColor: 'currentColor' }}></div>
            <div style={{ width: '8px', height: '1px', backgroundColor: 'currentColor' }}></div>
            <div style={{ width: '10px', height: '1px', backgroundColor: 'currentColor' }}></div>
          </div>
        );
    }
  };

  // テキスト位置のタイトルを取得する関数
  const getAlignTitle = () => {
    switch (textAlign) {
      case 'left':
        return '左寄せ';
      case 'center':
        return '中央寄せ';
      case 'right':
        return '右寄せ';
      default:
        return '左寄せ';
    }
  };

  // ツールバーのボタン定義
  const toolbarButtons: ToolbarButton[] = [
    { command: 'bold', icon: 'B', title: '太字', style: { fontWeight: 'bold' } },
    { command: 'italic', icon: 'I', title: '斜体', style: { fontStyle: 'italic' } },
    { command: 'underline', icon: 'U', title: '下線', style: { textDecoration: 'underline' } },
    { command: 'textAlign', icon: getAlignIcon(), title: getAlignTitle(), style: { fontSize: '16px' } },
    { command: 'formatBlock', icon: 'H2', title: '見出し2', style: { fontWeight: 'bold' } },
    { command: 'formatBlock', icon: 'H3', title: '見出し3', style: { fontWeight: 'bold' } },
    { command: 'createLink', icon: '🔗', title: 'リンク追加/解除', style: { fontSize: '14px' } },
  ];

  return (
    <div className="flex flex-wrap items-center gap-1 p-2 bg-white sticky top-0 z-[999999] shadow-sm border-gray-200">
      {/* 書式ボタン */}
      {toolbarButtons.map((button, index) => (
        <button
          key={index}
          type="button"
          title={button.title}
          onClick={() => {
            if (button.command === 'formatBlock') {
              onToolbarClick(button.command, button.icon === 'H2' ? '<h2>' : '<h3>');
            } else if (button.command === 'textAlign') {
              onTextAlignCommand();
            } else if (button.command === 'createLink') {
              onLinkCommand();
            } else {
              onToolbarClick(button.command);
            }
          }}
          className="w-8 h-8 flex items-center justify-center rounded hover:bg-gray-100"
          style={button.style}
        >
          {button.icon}
        </button>
      ))}

      {/* 色選択ボタン - パレットアイコン */}
      <div className="relative inline-block">
        <button
          type="button"
          title="文字色"
          className="w-8 h-8 flex items-center justify-center rounded hover:bg-gray-100 border-none outline-none"
          onClick={() => setIsColorPaletteOpen(!isColorPaletteOpen)}
          aria-label="文字色を選択"
          style={{ appearance: 'none' }}
        >
          <span className="text-lg font-bold" style={{ background: 'linear-gradient(45deg, #E63B5F, #3B82F6)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>🎨</span>
        </button>
        
        {/* カラーパレット */}
        {isColorPaletteOpen && (
          <ColorPalette 
            colorOptions={COLOR_OPTIONS}
            onColorSelect={(color) => {
              onToolbarClick('foreColor', color);
              setIsColorPaletteOpen(false);
            }}
            onClose={() => setIsColorPaletteOpen(false)}
          />
        )}
      </div>
    </div>
  );
};

export default Toolbar;