import React, { useState, useCallback, useEffect } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useRouter } from 'next/navigation';
import { createPortal } from 'react-dom';

interface RankingType {
  id: string;
  ranking_ID: string;
  ranking_title: string;
  ranking_description?: string;
  ranking_url?: string;
  thumbnail_image: string[] | string;
  recommend_rate?: number;
  subCategory_ID?: string;
  subcategory_id?: string;
  userId: string;
  domain?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
}

interface DraggableRankingItemProps {
  ranking: RankingType;
  index: number;
  subcatId: string;
  isEditMode: boolean;
  userId: string;
  pageTransit: (userId: string, rankingId: string) => void;
  onShare?: (rankingId: string, title: string, description?: string) => void;
  isDraggable?: boolean;
  isOwnPage?: boolean; // ログイン状態の判定
  onEdit?: (rankingId: string) => void; // 編集機能
  onDelete?: (rankingId: string) => void; // 削除機能
}

export const DraggableRankingItem: React.FC<DraggableRankingItemProps> = ({
  ranking,
  index,
  subcatId,
  isEditMode,
  userId,
  pageTransit,
  onShare,
  isDraggable = true,
  isOwnPage = false,
  onEdit,
  onDelete
}) => {
  const router = useRouter();

  // アクティブ状態の管理
  const [isActive, setIsActive] = useState(false);
  // モーダル状態の管理
  const [showOptionsModal, setShowOptionsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: ranking.ranking_ID,
    disabled: !isDraggable,
    animateLayoutChanges: () => true,
    data: {
      type: 'ranking',
      ranking,
      subcatId,
    },
  });

  // マウス/タッチイベントハンドラー
  const handleMouseDown = useCallback(() => {
    if (!isDragging) {
      setIsActive(true);
    }
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsActive(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsActive(false);
  }, []);

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
    touchAction: isDraggable && isDragging ? "none" : "auto",
    position: "relative" as const,
    background: isDragging
      ? "rgba(240,240,240,0.8)"
      : isActive
        ? "#e5e7eb"
        : "transparent",
    borderRadius: "8px",
    boxShadow: isDragging ? "0 5px 15px rgba(0,0,0,.2)" : "none",
    cursor: isDraggable ? "grab" : "default",
  };

  // 三点リーダーボタンのクリック処理
  const handleOptionsClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    if (isOwnPage) {
      // ログイン中：モーダルを表示
      setShowOptionsModal(true);
    } else {
      // ログアウト中：直接共有機能を実行
      if (onShare) {
        onShare(ranking.ranking_ID, ranking.ranking_title, ranking.ranking_description);
      }
    }
  }, [isOwnPage, onShare, ranking.ranking_ID, ranking.ranking_title, ranking.ranking_description]);

  // 編集ボタンのクリック処理
  const handleEditClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // イベント伝播を防止
    setShowOptionsModal(false);
    if (onEdit) {
      onEdit(ranking.ranking_ID);
    }
  }, [onEdit, ranking.ranking_ID]);

  // 削除ボタンのクリック処理
  const handleDeleteClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // イベント伝播を防止
    setShowOptionsModal(false);
    setShowDeleteModal(true);
  }, []);

  // 削除確認処理
  const handleConfirmDelete = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // イベント伝播を防止
    setShowDeleteModal(false);
    if (onDelete) {
      onDelete(ranking.ranking_ID);
    }
  }, [onDelete, ranking.ranking_ID]);

  // 共有ボタンのクリック処理
  const handleShareClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // イベント伝播を防止
    setShowOptionsModal(false);
    if (onShare) {
      onShare(ranking.ranking_ID, ranking.ranking_title, ranking.ranking_description);
    }
  }, [onShare, ranking.ranking_ID, ranking.ranking_title, ranking.ranking_description]);

  // モーダル外クリック時の処理
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showOptionsModal || showDeleteModal) {
        const target = event.target as HTMLElement;
        if (!target.closest('.options-modal') && !target.closest('.delete-modal')) {
          setShowOptionsModal(false);
          setShowDeleteModal(false);
        }
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showOptionsModal, showDeleteModal]);

  // 星評価のレンダリング関数
  const renderStars = (rating?: number) => {
    const safe = rating ?? 0;
    const stars = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <span key={i} className={i < safe ? "text-yellow-500" : "text-gray-300"}>
          ★
        </span>
      );
    }
    return stars;
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...(isDraggable ? { ...attributes, ...listeners } : {})}
      className={`flex bg-white rounded-[10px] cursor-pointer relative transition-colors duration-150 w-full !mt-4 first:!mt-0 ${
        isDragging ? 'shadow-lg' : ''
      }`}
      data-sortable-item="true"
      onClick={() => {
        if (!isEditMode && typeof pageTransit === 'function') {
          pageTransit(userId, ranking.ranking_ID);
        }
      }}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleMouseDown}
      onTouchEnd={handleMouseUp}
    >

      {/* サムネイル画像 */}
      {ranking.thumbnail_image && (
        <div className="w-[120px] h-[120px] flex-shrink-0 mr-4">
          <img
            src={
              Array.isArray(ranking.thumbnail_image)
                ? ranking.thumbnail_image[0]
                : ranking.thumbnail_image
            }
            alt={ranking.ranking_title}
            className="w-full h-full object-cover rounded-[4px]"
          />
        </div>
      )}

      {/* ランキング情報 */}
      <div className="flex-1 min-w-0">
        {/* タイトルと共有ボタン */}
        <div className="flex items-start justify-between mb-2">
          <h2 className="text-sm font-bold flex-1 pr-2 line-clamp-2 mt-0.5">
            {ranking.ranking_title}
          </h2>
          {/* 三点リーダーボタン */}
          <button
            className="min-h-6 min-w-6 p-1 flex flex-col items-center justify-center rounded-[100px] active:bg-[#e5e7eb] transition-colors duration-150 flex-shrink-0"
            onClick={handleOptionsClick}
            onMouseDown={(e: React.MouseEvent) => {
              e.stopPropagation();
            }}
            onMouseUp={(e: React.MouseEvent) => {
              e.stopPropagation();
            }}
            onMouseLeave={(e: React.MouseEvent) => {
              e.stopPropagation();
            }}
            onTouchStart={(e: React.TouchEvent) => {
              e.stopPropagation();
            }}
            onTouchEnd={(e: React.TouchEvent) => {
              e.stopPropagation();
            }}
          >
            <div className="bg-black rounded-full" style={{ width: '3px', height: '3px', marginBottom: '2px' }}></div>
            <div className="bg-black rounded-full" style={{ width: '3px', height: '3px', marginBottom: '2px' }}></div>
            <div className="bg-black rounded-full" style={{ width: '3px', height: '3px' }}></div>
          </button>
        </div>

        <div className="flex items-center mb-2">
          {renderStars(ranking.recommend_rate)}
          <span className="ml-2 text-sm text-gray-600">
            {ranking.recommend_rate ?? 0}
          </span>
        </div>

        {ranking.ranking_description && (
          <p className="text-sm text-gray-700 line-clamp-2">
            {ranking.ranking_description
              .replace(/<div[^>]*class="[^"]*social-embed[^"]*"[^>]*>.*?<\/div>/gi, '') // SNS埋め込み要素を除去
              .replace(/<blockquote[^>]*class="[^"]*twitter-tweet[^"]*"[^>]*>.*?<\/blockquote>/gi, '') // Twitter埋め込みを除去
              .replace(/<iframe[^>]*src="[^"]*youtube[^"]*"[^>]*>.*?<\/iframe>/gi, '') // YouTube埋め込みを除去
              .replace(/<iframe[^>]*src="[^"]*tiktok[^"]*"[^>]*>.*?<\/iframe>/gi, '') // TikTok埋め込みを除去
              .replace(/<script[^>]*>.*?<\/script>/gi, '') // スクリプトタグを除去
              .replace(/<[^>]*>/g, '') // 全てのHTMLタグを除去
              .replace(/&nbsp;/g, ' ') // &nbsp;をスペースに変換
              .replace(/&lt;/g, '<') // HTMLエンティティをデコード
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .trim()
            }
          </p>
        )}
      </div>

      {/* オプションモーダル（ログイン状態のみ表示） */}
      {isOwnPage && showOptionsModal && (
        <div
          className="options-modal absolute bg-white shadow-md rounded-md z-10000"
          style={{
            right: '32px',
            top: '8px',
            minWidth: '120px'
          }}
        >
          <button
            onClick={handleEditClick}
            onMouseDown={(e: React.MouseEvent) => e.stopPropagation()}
            onMouseUp={(e: React.MouseEvent) => e.stopPropagation()}
            onTouchStart={(e: React.TouchEvent) => e.stopPropagation()}
            onTouchEnd={(e: React.TouchEvent) => e.stopPropagation()}
            className="block w-full text-left text-[14px] px-4 py-2 hover:bg-gray-100"
          >
            編集する
          </button>
          <button
            onClick={handleDeleteClick}
            onMouseDown={(e: React.MouseEvent) => e.stopPropagation()}
            onMouseUp={(e: React.MouseEvent) => e.stopPropagation()}
            onTouchStart={(e: React.TouchEvent) => e.stopPropagation()}
            onTouchEnd={(e: React.TouchEvent) => e.stopPropagation()}
            className="block w-full text-left text-[14px] px-4 py-2 hover:bg-gray-100"
          >
            削除する
          </button>
          <button
            onClick={handleShareClick}
            onMouseDown={(e: React.MouseEvent) => e.stopPropagation()}
            onMouseUp={(e: React.MouseEvent) => e.stopPropagation()}
            onTouchStart={(e: React.TouchEvent) => e.stopPropagation()}
            onTouchEnd={(e: React.TouchEvent) => e.stopPropagation()}
            className="block w-full text-left text-[14px] px-4 py-2 hover:bg-gray-100"
          >
            共有する
          </button>
        </div>
      )}

      {/* 削除確認モーダル */}
      {showDeleteModal && typeof window !== 'undefined' && createPortal(
        <div className="delete-modal fixed inset-0 flex items-center justify-center" style={{ zIndex: 999999 }}>
          <div className="absolute inset-0 bg-black opacity-50"></div>
          <div className="bg-white p-6 rounded-lg shadow-xl z-10 max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4">本当に削除しますか？</h3>
            <p className="mb-6 text-sm text-gray-600">
              削除すると、元に戻せません。
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  setShowDeleteModal(false);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm"
              >
                キャンセル
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-[#1f2937] text-white rounded-md text-sm"
              >
                削除する
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};
