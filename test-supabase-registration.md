# Supabase User Registration Test

## API Endpoint Test

### Manual Test
You can test the user upsert API manually by sending a POST request to:
```
POST http://localhost:3000/api/users/upsert
```

### Test Payload
```json
{
  "clerkUserId": "test_user_123",
  "email": "<EMAIL>",
  "firstName": "Test",
  "lastName": "User",
  "fullName": "Test User",
  "imageUrl": "https://example.com/avatar.jpg"
}
```

### Expected Response
```json
{
  "success": true,
  "user": {
    "id": "uuid-generated-id",
    "user_ID": "test_user_123",
    "email": "<EMAIL>",
    "name": "Test User",
    "username": "user-user_123",
    "profile_image": "https://example.com/avatar.jpg"
  }
}
```

## Database Verification

### Check User in Database
After OAuth authentication, verify the user exists in the database:

1. **Via Supabase Dashboard:**
   - Go to Supabase dashboard
   - Navigate to Table Editor
   - Check the `User` table
   - Look for the user with the Clerk user ID

2. **Via API:**
   ```
   GET http://localhost:3000/api/getUser?user_ID={clerkUserId}
   ```

### Expected Database Fields
- `id`: UUID (auto-generated)
- `user_ID`: Clerk user ID
- `username`: Temporary username (user-{clerkUserId})
- `account_type`: "google"
- `email`: User's email from Google
- `name`: User's full name
- `profile_image`: User's Google profile image
- `created_at`: Timestamp
- `updated_at`: Timestamp

## OAuth Flow Verification

### Console Logs to Watch For
1. **During OAuth:**
   - `🔄 [API] User upsert request:`
   - `📝 [API] Upserting user data:`
   - `✅ [API] User upsert successful:`

2. **Error Logs:**
   - `❌ [API] Missing required field:`
   - `❌ [API] User upsert error:`

### Success Criteria
- [ ] User record created in database
- [ ] All required fields populated
- [ ] No duplicate users created
- [ ] Proper error handling for missing data
- [ ] API responds within 2 seconds
