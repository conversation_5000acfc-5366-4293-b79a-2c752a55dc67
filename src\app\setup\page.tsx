'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { Footer } from '@/components/Footer/Footer';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';




export default function SetupPage() {
  const router = useRouter();
  const { user, isLoaded } = useUser();
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [usernameError, setUsernameError] = useState('');
  const [loading, setLoading] = useState(false);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [redirectExecuted, setRedirectExecuted] = useState(false);

  // OAuth認証完了の確認とアクセス制御
  useEffect(() => {
    const checkUserAccess = async () => {
      // リダイレクト済みの場合は何もしない
      if (redirectExecuted) {
        return;
      }

      if (isLoaded) {
        if (!user) {
          router.replace('/sign-up');
          return;
        }

        // ユーザーがデータベースに存在するかチェック
        try {
          const response = await fetch(`/api/getUser?user_ID=${user.id}`);

          if (response.ok) {
            // ユーザーが存在する場合
            const userData = await response.json();
            if (userData.username && userData.setup_completed) {
              // セットアップ完了済みの場合はリダイレクト
              setRedirectExecuted(true);
              window.location.replace(`/${userData.username}`);
              return;
            }
            // セットアップ未完了の場合はsetupページを継続表示
          } else if (response.status === 404) {
            // ユーザーが存在しない場合は異常事態
            console.error('ユーザーがデータベースに存在しません。Webhookが正常に動作していない可能性があります。');
          }
        } catch (error) {
          console.error('ユーザー確認エラー:', error);
        }
      }
    };

    checkUserAccess();
  }, [isLoaded, user?.id, redirectExecuted]);

  // Clerkからユーザー情報を取得して初期値を設定
  useEffect(() => {
    if (isLoaded && user) {
      // 名前の初期値を設定（firstName + lastName または fullName）
      const initialName = user.fullName ||
                          (user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` :
                           user.firstName || user.lastName || '');
      // 連続する空白を単一の空白に変換
      setName(initialName.replace(/\s+/g, ' ').trim());
    }
  }, [isLoaded, user]);

  // ユーザー名の重複チェック（デバウンス付き）
  useEffect(() => {
    if (!username || username.length < 3) {
      setUsernameError('');
      return;
    }

    const timeoutId = setTimeout(async () => {
      setIsCheckingUsername(true);
      try {
        const response = await fetch(`/api/checkUsername?username=${encodeURIComponent(username)}`);
        const data = await response.json();

        if (!data.available) {
          setUsernameError(data.error || 'このユーザー名は使用できません');
        } else {
          setUsernameError('');
        }
      } catch (error) {
        console.error('ユーザー名チェックエラー:', error);
        setUsernameError('ユーザー名の確認中にエラーが発生しました');
      } finally {
        setIsCheckingUsername(false);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [username]);

  // 名前の入力制限（連続する空白を単一の空白に変換、30文字まで）
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\s+/g, ' ').slice(0, 30);
    setName(value);
  };

  // ユーザー名の入力制限（英数字のみ、30文字まで）
  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toLowerCase().replace(/[^a-z0-9_-]/g, '').slice(0, 30);
    setUsername(value);
  };

  // フォーム送信処理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim() || !username.trim() || usernameError || isCheckingUsername) {
      return;
    }

    setLoading(true);

    try {
      // ユーザー情報更新API呼び出し（既存ユーザーの情報を更新）
      const response = await fetch('/api/setupUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: user?.id,
          name: name.trim(),
          username: username.trim(),
          setup_completed: true, // セットアップ完了フラグ
        }),
      });

      if (response.ok) {
        // 成功時はユーザー名ページにリダイレクト
        router.push(`/${username}`);
      } else {
        const errorData = await response.json();
        const errorMessage = errorData.error || 'セットアップに失敗しました。もう一度お試しください。';
        alert(errorMessage);
      }
    } catch (error) {
      alert('セットアップに失敗しました。もう一度お試しください。');
    } finally {
      setLoading(false);
    }
  };

  // ローディング中またはリダイレクト中
  if (!isLoaded || redirectExecuted) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-[500px] w-full mx-auto bg-white shadow-md p-8 min-h-screen flex flex-col items-center justify-center">
            <CatLoadingAnimation message={redirectExecuted ? "リダイレクト中..." : "Loading..."} />
          </div>
        </div>
      </div>
    );
  }

  // ボタンの有効性チェック
  const isFormValid = name.trim() && username.trim() && !usernameError && !isCheckingUsername;

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <div className="flex-1 flex items-center justify-center">
        <div className="max-w-[500px] w-full mx-auto bg-white shadow-md p-14 min-h-screen flex flex-col">
          <div className="flex-1 flex flex-col justify-center">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold mb-2">アカウントを作成</h1>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 名前入力 */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  名前を決める <span className="text-gray-500 text-xs">（変更可能）</span>
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={handleNameChange}
                  placeholder="名前を入力してください"
                  required
                  disabled={loading}
                  maxLength={30}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none"
                />
              </div>

              {/* URL入力 */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  自分のページURLを決める <span className="text-gray-500 text-xs">（変更不可）</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 text-sm">https://mypicks.best/</span>
                  </div>
                  <input
                    id="username"
                    type="text"
                    value={username}
                    onChange={handleUsernameChange}
                    placeholder="name"
                    required
                    disabled={loading}
                    maxLength={30}
                    className="w-full pl-[154px] pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none"
                  />
                </div>
                {/* エラーメッセージエリア（固定高さ） */}
                <div className="h-6 mt-1">
                  {usernameError && (
                    <div className="text-red-600 text-sm">
                      {usernameError}
                    </div>
                  )}
                  {isCheckingUsername && !usernameError && (
                    <div className="text-gray-500 text-sm">
                      確認中...
                    </div>
                  )}
                </div>
              </div>

              {/* 登録ボタン */}
              <button
                type="submit"
                disabled={loading || !isFormValid}
                className="w-full flex justify-center items-center border border-transparent shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed transition-colors mt-12"
                style={{
                  height: '44px',
                  paddingLeft: '12px',
                  paddingRight: '12px',
                  fontFamily: '"Google Sans", arial, sans-serif',
                  fontWeight: 500,
                  fontSize: '14px',
                  lineHeight: '20px',
                  backgroundColor: isFormValid ? '#E63B5F' : '#d1d5db',
                  borderRadius: '100px',
                  color: isFormValid ? '#FFFFFF' : '#6b7280',
                  marginTop: '48px'
                }}
                onMouseEnter={(e) => {
                  if (!loading && isFormValid) {
                    e.currentTarget.style.backgroundColor = '#DD0F2B';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading && isFormValid) {
                    e.currentTarget.style.backgroundColor = '#E63B5F';
                  }
                }}
              >
                {loading ? (
                  <div className="flex items-center">
                    処理中...
                  </div>
                ) : (
                  <span style={{
                    fontFamily: '"Google Sans", arial, sans-serif',
                    fontWeight: 500,
                    fontSize: '14px',
                    lineHeight: '20px'
                  }}>
                    登録する
                  </span>
                )}
              </button>
            </form>
          </div>

          {/* フッター */}
          <Footer />
        </div>
      </div>
    </div>
  );
}
