generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                String     @id @default(uuid())
  user_ID           String     @unique
  username          String     @unique
  account_type      AccounType
  email             String
  line              String?
  google            String?
  password          String?
  name              String
  profile_image     String?
  contact_url       String?
  contact_email     String?
  created_at        DateTime   @default(now())
  updated_at        DateTime   @updatedAt
  self_introduction String?
  background_image  String?
  setup_completed   Boolean    @default(false)
  Category          Category[]
  UserByRanking     Ranking[]  @relation("UserByRanking")
  SnsUser           SnsUser[]
}

model Category {
  id             String     @id @default(uuid())
  user_ID        String
  category_ID    String
  category_name  String
  category_slug  String?
  history_slug   String?    // 変更前のslugを1つだけ保存（リダイレクト用）
  parent_ID      String?
  created_at     DateTime   @default(now())
  updated_at     DateTime   @updatedAt
  order          Int        @default(0)
  Parent         Category?  @relation("CategoryToParent", fields: [parent_ID], references: [id])
  Children       Category[] @relation("CategoryToParent")
  CategoryUserID User       @relation(fields: [user_ID], references: [user_ID])

  @@index([user_ID])
  @@index([parent_ID])
  @@index([category_slug])
  @@index([history_slug])  // 旧slug検索用のインデックス
  @@unique([user_ID, category_slug])
}

model SnsMaster {
  id         String    @id @default(uuid())
  sns_ID     Int       @unique
  sns_name   String    @unique
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  snsUsers   SnsUser[]
}

model SnsUser {
  id            String    @id @default(uuid())
  user_ID       String
  sns_master_id String
  account_ID    String
  display_order Int       @default(0)
  created_at    DateTime  @default(now())
  updated_at    DateTime  @updatedAt
  snsMaster     SnsMaster @relation(fields: [sns_master_id], references: [id])
  user          User      @relation(fields: [user_ID], references: [user_ID])

  @@index([user_ID])
  @@index([sns_master_id])
}

model Ranking {
  id                  String        @id @default(uuid())
  ranking_ID          String
  user_ID             String
  thumbnail_image     String[]
  recommend_rate      Int?
  amazon_url          String?
  rakuten_url         String?
  yahoo_url           String?
  qoo10_url           String?
  official_url        String?
  ranking_title       String
  ranking_description String?
  subCategory_ID      String?
  order               Int           @default(0)
  status              RankingStatus @default(DRAFT)
  created_at          DateTime      @default(now())
  updated_at          DateTime      @updatedAt
  RankingUserID       User          @relation("UserByRanking", fields: [user_ID], references: [user_ID])

  @@index([user_ID])
  @@index([status])
}



enum AccounType {
  email
  line
  google
}

enum RankingStatus {
  DRAFT
  PUBLISHED
}
