import React from 'react';
import { ColorOption } from '../types';

interface ColorPaletteProps {
  colorOptions: ColorOption[];
  onColorSelect: (color: string) => void;
  onClose: () => void;
}

const ColorPalette: React.FC<ColorPaletteProps> = ({ colorOptions, onColorSelect, onClose }) => {
  return (
    <div className="fixed top-[50%] left-[50%] transform translate-x-[-50%] translate-y-[-50%] bg-white border border-gray-200 rounded-lg shadow-xl z-[9999999]" style={{ width: '300px' }}>
      <div className="p-4">
        <div className="flex justify-between items-center mb-3 border-b pb-2">
          <h3 className="text-lg font-semibold text-gray-700">文字色を選択</h3>
          <button 
            type="button" 
            className="text-gray-400 hover:text-gray-600" 
            onClick={onClose}
            aria-label="閉じる"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div className="grid grid-cols-5 gap-2 mb-3">
          {/* 基本色 */}
          {colorOptions.slice(0, 15).map((option, index) => (
            <button
              key={index}
              type="button"
              title={option.name}
              onClick={() => onColorSelect(option.color)}
              className={`w-10 h-10 rounded ${option.border ? 'border border-gray-300' : ''} flex items-center justify-center hover:scale-110 transition-transform duration-100`}
              style={{ backgroundColor: option.color }}
              aria-label={`色: ${option.name}`}
            ></button>
          ))}
        </div>
        
        <div className="grid grid-cols-5 gap-2 mb-3">
          {/* 追加色 */}
          {colorOptions.slice(15, 30).map((option, index) => (
            <button
              key={index + 15}
              type="button"
              title={option.name}
              onClick={() => onColorSelect(option.color)}
              className={`w-10 h-10 rounded ${option.border ? 'border border-gray-300' : ''} flex items-center justify-center hover:scale-110 transition-transform duration-100`}
              style={{ backgroundColor: option.color }}
              aria-label={`色: ${option.name}`}
            ></button>
          ))}
        </div>
        
        <div className="grid grid-cols-5 gap-2">
          {/* 残りの色 */}
          {colorOptions.slice(30).map((option, index) => (
            <button
              key={index + 30}
              type="button"
              title={option.name}
              onClick={() => onColorSelect(option.color)}
              className={`w-10 h-10 rounded ${option.border ? 'border border-gray-300' : ''} flex items-center justify-center hover:scale-110 transition-transform duration-100`}
              style={{ backgroundColor: option.color }}
              aria-label={`色: ${option.name}`}
            ></button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ColorPalette;