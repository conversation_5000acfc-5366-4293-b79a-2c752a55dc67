'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function DraftListPage({ params }: { params: { user_ID?: string } }) {
  const router = useRouter();

  // URLパラメータからユーザーIDを取得
  const userId = params.user_ID || '';

  // 新しいURL形式にリダイレクト
  useEffect(() => {
    const redirectToNewFormat = async () => {
      if (userId) {
        try {
          // ユーザーIDからユーザー名を取得
          const response = await fetch(`/api/getUser?user_ID=${userId}`);
          if (response.ok) {
            const userData = await response.json();
            const username = userData.username;
            if (username) {
              // 新しいURL形式にリダイレクト
              router.replace(`/${username}/draftList`);
              return;
            }
          }
        } catch (error) {
          console.error('ユーザー情報取得エラー:', error);
        }
      }

      // フォールバック: ホームページにリダイレクト
      router.replace('/');
    };

    redirectToNewFormat();
  }, [userId, router]);
  // リダイレクト中のローディング表示
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center justify-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-[#8B4513]/80 w-full" style={{ height: '200%' }}></div>

      {/* ヘッダー */}
      <div className="flex justify-between max-w-[500px] h-[52px] items-center relative bg-white shadow-[0px_0px_4px_#00000040] w-full">
        <div className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative bg-white">
          <p className="relative flex-[0_0_auto]">下書き一覧</p>
        </div>
      </div>

      {/* メインコンテンツ */}
      <div className="w-full bg-white" style={{ height: 'auto', flex: '1 1 0%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        <div className="flex-1 flex items-center justify-center">
        </div>
      </div>
    </div>
  );
}


