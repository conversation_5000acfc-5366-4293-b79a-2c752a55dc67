"use client"

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { HeaderItems } from '@/components/HeaderItems/HeaderItems';

// リファクタリングで作成したカスタムフック
import { useUserIdentification } from '../../hooks/useUserIdentification';
import { useImageUpload } from '../../hooks/useImageUpload';
import { useRankingForm } from '../../hooks/useRankingForm';

// リファクタリングで作成したコンポーネント
import { ImageUploadSection } from './components/ImageUploadSection';
import { TitleSection } from './components/TitleSection';
import { RecommendSection } from './components/RecommendSection';
import { RatingSection } from './components/RatingSection';
import { CategorySection } from './components/CategorySection';
import { LinkSection } from './components/LinkSection';
import { FooterButtons } from './components/FooterButtons';
import { SavedModal } from './components/SavedModal';

/**
 * ランキング登録ページコンポーネント
 */
export default function RankingRegister({ params }: { params: { user_ID?: string } }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // URLパラメータからランキングIDとカテゴリIDを取得
  const rankingIdParam = searchParams.get('ranking_id');
  const categoryIdParam = searchParams.get('categoryId') || searchParams.get('category_id');
  const categoryNameParam = searchParams.get('categoryName');
  
  // ユーザー識別
  const { userId, isLoading: isUserLoading } = useUserIdentification(params);
  
  // 画像アップロード処理
  const { 
    images, 
    setImages, 
    handleImageChange, 
    handleRemoveImage, 
    uploadImages 
  } = useImageUpload(userId);
  
  // フォーム処理
  const { 
    // フォーム状態
    title,
    setTitle,
    recommendText,
    setRecommendText,
    category,
    setCategory,
    categoryId,
    setCategoryId,
    subCategory,
    setSubCategory,
    rating,
    setRating,
    hoverRating,
    setHoverRating,
    amazonUrl,
    setAmazonUrl,
    rakutenUrl,
    setRakutenUrl,
    yahooUrl,
    setYahooUrl,
    qoo10Url,
    setQoo10Url,
    officialUrl,
    setOfficialUrl,
    
    // 編集モード関連
    isEditMode,
    setIsEditMode,
    rankingId,
    setRankingId,
    fetchRankingData,
    
    // UI状態
    validationErrors,
    showErrors,
    showSavedModal,
    errorMessage,
    isSubmitting,
    
    // 処理関数
    submitRanking,
    isFormValid
  } = useRankingForm(userId, uploadImages);
  
  // 初期化処理
  useEffect(() => {
    // URLパラメータからランキングIDがあれば編集モード
    if (rankingIdParam) {
      setIsEditMode(true);
      setRankingId(rankingIdParam);
      fetchRankingData(rankingIdParam);
    }
    
    // URLパラメータからカテゴリIDとカテゴリ名があれば設定
    if (categoryIdParam) {
      setCategoryId(categoryIdParam);
      
      if (categoryNameParam) {
        setCategory(decodeURIComponent(categoryNameParam));
      }
    }
  }, [rankingIdParam, categoryIdParam, categoryNameParam, fetchRankingData, setIsEditMode, setRankingId, setCategoryId, setCategory]);
  
  // ローディング中の表示
  if (isUserLoading) {
    return <div className="min-h-screen bg-white flex items-center justify-center">
      <p className="text-gray-500">Loading...</p>
    </div>;
  }
  
  return (
    <div className="max-w-[500px] mx-auto flex-col min-h-screen items-center relative bg-white">
      {/* ヘッダー */}
      <HeaderItems 
        text="ランキングを追加する" 
        backButton={true} 
        onBackButtonClick={() => router.back()} 
      />
      
      {/* 画像アップロードセクション */}
      <ImageUploadSection 
        images={images} 
        handleImageChange={handleImageChange} 
        handleRemoveImage={handleRemoveImage} 
      />
      
      <div className="flex-1 flex flex-col space-y-4 pb-24">
        {/* タイトルセクション */}
        <TitleSection 
          title={title} 
          setTitle={setTitle} 
          validationErrors={validationErrors} 
          showErrors={showErrors} 
        />
        
        {/* おすすめの理由セクション */}
        <RecommendSection 
          recommendText={recommendText} 
          setRecommendText={setRecommendText} 
        />
        
        {/* おすすめ度セクション */}
        <RatingSection 
          rating={rating} 
          setRating={setRating} 
          hoverRating={hoverRating} 
          setHoverRating={setHoverRating} 
        />
        
        {/* カテゴリセクション */}
        <CategorySection 
          category={category} 
          subCategory={subCategory} 
          setCategory={setCategory} 
          setSubCategory={setSubCategory} 
          categoryId={categoryId} 
          validationErrors={validationErrors} 
          showErrors={showErrors} 
        />
        
        {/* 商品リンクセクション */}
        <LinkSection 
          amazonUrl={amazonUrl} 
          setAmazonUrl={setAmazonUrl} 
          rakutenUrl={rakutenUrl} 
          setRakutenUrl={setRakutenUrl} 
          yahooUrl={yahooUrl} 
          setYahooUrl={setYahooUrl} 
          qoo10Url={qoo10Url} 
          setQoo10Url={setQoo10Url} 
          officialUrl={officialUrl} 
          setOfficialUrl={setOfficialUrl} 
        />
      </div>
      
      {/* フッターボタン */}
      <FooterButtons 
        isFormValid={isFormValid} 
        submitRanking={submitRanking} 
        isEditMode={isEditMode} 
      />
      
      {/* 保存成功モーダル */}
      <SavedModal show={showSavedModal} />
      
      {/* エラーメッセージ */}
      {errorMessage && (
        <div className="fixed bottom-20 left-0 right-0 mx-auto w-4/5 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {errorMessage}
        </div>
      )}
    </div>
  );
}
