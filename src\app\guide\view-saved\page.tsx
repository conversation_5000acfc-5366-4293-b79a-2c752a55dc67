'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function ViewSavedGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">保存したランキングを閲覧する方法</h1>
          <div className="pt-6 text-[14px] text-[#313131]">
            <p>1. マイページにアクセスします。</p>
            <p>2. 「保存済みランキング」セクションをタップします。</p>
            <p>3. 保存したすべてのランキングが一覧表示されます。</p>
            <p>4. 閲覧したいランキングをタップすると、詳細ページが表示されます。</p>
          </div>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
