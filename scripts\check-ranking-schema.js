const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkRankingSchema() {
  console.log('Checking ranking schema and data...');
  
  try {
    // ランキングテーブルの構造を確認するため、1つのレコードを取得
    const sampleRanking = await prisma.ranking.findFirst({
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        created_at: true,
        updated_at: true
      }
    });

    if (sampleRanking) {
      console.log('✅ Schema migration successful!');
      console.log('Sample ranking data:');
      console.log(JSON.stringify(sampleRanking, null, 2));
    } else {
      console.log('ℹ️  No ranking data found in database');
    }

    // 全ランキング数を確認
    const totalRankings = await prisma.ranking.count();
    console.log(`\nTotal rankings in database: ${totalRankings}`);

    // 各URLフィールドに値があるランキングの数を確認
    const urlStats = await Promise.all([
      prisma.ranking.count({ where: { amazon_url: { not: null, not: '' } } }),
      prisma.ranking.count({ where: { rakuten_url: { not: null, not: '' } } }),
      prisma.ranking.count({ where: { yahoo_url: { not: null, not: '' } } }),
      prisma.ranking.count({ where: { qoo10_url: { not: null, not: '' } } }),
      prisma.ranking.count({ where: { official_url: { not: null, not: '' } } })
    ]);

    console.log('\nURL field statistics:');
    console.log(`Amazon URLs: ${urlStats[0]}`);
    console.log(`Rakuten URLs: ${urlStats[1]}`);
    console.log(`Yahoo URLs: ${urlStats[2]}`);
    console.log(`Qoo10 URLs: ${urlStats[3]}`);
    console.log(`Official URLs: ${urlStats[4]}`);

  } catch (error) {
    console.error('❌ Error checking schema:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
checkRankingSchema();
