import { TextAlign } from '../types';

interface UseToolbarActionsProps {
  editorRef: React.RefObject<HTMLDivElement>;
  textAlign: TextAlign;
  setTextAlign: (align: TextAlign) => void;
  handleInput: () => void;
}

export const useToolbarActions = ({
  editorRef,
  textAlign,
  setTextAlign,
  handleInput
}: UseToolbarActionsProps) => {

  // ツールバーのボタンクリック処理
  const handleToolbarClick = (command: string, value?: string) => {
    const selection = window.getSelection();

    if (command === 'formatBlock') {
      if (!selection || selection.rangeCount === 0) {
        alert('カーソルを配置するかテキストを選択してください');
        return;
      }
    } else if (command !== 'createLink' && command !== 'textAlign') {
      if (!selection || selection.rangeCount === 0) {
        alert('テキストを選択してください');
        return;
      }

      const range = selection.getRangeAt(0);
      const selectedText = range.toString();
      if (!selectedText) {
        alert('テキストを選択してください');
        return;
      }
    }

    // コマンド実行
    if (command === 'formatBlock') {
      try {
        const range = selection!.getRangeAt(0);
        
        // 選択範囲内のH2/H3要素を検索
        const selectedNode = range.commonAncestorContainer;
        let targetHeading: HTMLElement | null = null;

        // 選択範囲の開始と終了ノードを取得
        const startContainer = range.startContainer;
        const endContainer = range.endContainer;

        // 選択範囲内または選択範囲を含むH2/H3要素を検索
        const findHeadingElement = (node: Node): HTMLElement | null => {
          if (node.nodeType === 1) {
            const element = node as HTMLElement;
            if (element.tagName === 'H2' || element.tagName === 'H3') {
              return element;
            }
          }

          // 親要素を確認
          let parent = node.parentElement;
          while (parent && parent !== editorRef.current) {
            if (parent.tagName === 'H2' || parent.tagName === 'H3') {
              return parent;
            }
            parent = parent.parentElement;
          }

          return null;
        };

        // 開始ノードから見出し要素を検索
        targetHeading = findHeadingElement(startContainer);

        // 開始ノードで見つからない場合は終了ノードから検索
        if (!targetHeading) {
          targetHeading = findHeadingElement(endContainer);
        }

        // 共通祖先からも検索
        if (!targetHeading) {
          targetHeading = findHeadingElement(selectedNode);
        }

        // 既存の見出しが見つかった場合は解除
        if (targetHeading) {
          const headingType = targetHeading.tagName;
          const requestedType = value === '<h2>' ? 'H2' : 'H3';

          // 同じタイプの見出しの場合は解除、異なる場合は変換
          if (headingType === requestedType) {
            // 見出しを解除してpタグに変換
            const textContent = targetHeading.textContent || '';
            const cleanText = textContent;

            // 新しいp要素を作成
            const pElement = document.createElement('p');
            pElement.textContent = cleanText;
            pElement.style.margin = '0';
            pElement.style.padding = '0';

            // 見出し要素をp要素に置き換え
            targetHeading.parentNode?.replaceChild(pElement, targetHeading);

            // 新しいp要素にカーソルを設定
            const newRange = document.createRange();
            newRange.selectNodeContents(pElement);
            const selection = window.getSelection();
            if (selection) {
              selection.removeAllRanges();
              selection.addRange(newRange);
            }

            return;
          } else {
            // 異なるタイプの見出しに変換
            const textContent = targetHeading.textContent || '';
            const cleanText = textContent;

            // 新しい見出し要素を作成
            const newHeading = document.createElement(requestedType.toLowerCase());
            newHeading.textContent = cleanText;

            // 見出し要素を置き換え
            targetHeading.parentNode?.replaceChild(newHeading, targetHeading);

            // 新しい見出しにスタイルを適用
            if (requestedType === 'H2') {
              newHeading.style.fontSize = '1.5em';
              newHeading.style.fontWeight = 'bold';
              newHeading.style.margin = '0.8em 0';
              newHeading.style.padding = '0.3em 0.5em';
              newHeading.style.borderLeft = '4px solid #FFD814';
              newHeading.style.color = '#333';
              newHeading.style.lineHeight = '1.4';
              newHeading.setAttribute('data-styled', 'true');
            } else {
              newHeading.style.fontSize = '1.25em';
              newHeading.style.fontWeight = 'bold';
              newHeading.style.margin = '0.7em 0';
              newHeading.style.padding = '0.2em 0';
              newHeading.style.position = 'relative';
              newHeading.style.color = '#444';
              newHeading.style.lineHeight = '1.4';
              newHeading.style.display = 'block';
              newHeading.style.wordBreak = 'break-word';
              newHeading.style.whiteSpace = 'normal';
              newHeading.style.textIndent = '0';
              newHeading.style.borderBottom = '3px solid #E63B5F';
              newHeading.style.paddingBottom = '0.3em';
              newHeading.textContent = cleanText;
              newHeading.setAttribute('data-styled', 'true');
            }

            // 新しい見出しにカーソルを設定
            const newRange = document.createRange();
            newRange.selectNodeContents(newHeading);
            const selection = window.getSelection();
            if (selection) {
              selection.removeAllRanges();
              selection.addRange(newRange);
            }

            return;
          }
        }

        // 新しい見出しを作成する場合
        if (value === '<h2>') {
          document.execCommand('formatBlock', false, '<h2>');

          // スタイルを適用
          setTimeout(() => {
            const h2Elements = editorRef.current?.querySelectorAll('h2:not([data-styled])') || [];
            h2Elements.forEach((h2: HTMLElement) => {
              h2.style.fontSize = '1.5em';
              h2.style.fontWeight = 'bold';
              h2.style.margin = '0.8em 0';
              h2.style.padding = '0.3em 0.5em';
              h2.style.borderLeft = '4px solid #FFD814';
              h2.style.color = '#333';
              h2.style.lineHeight = '1.4';
              h2.setAttribute('data-styled', 'true');
            });
          }, 10);
        } else {
          document.execCommand('formatBlock', false, '<h3>');

          // スタイルを適用
          setTimeout(() => {
            const h3Elements = editorRef.current?.querySelectorAll('h3:not([data-styled])') || [];
            h3Elements.forEach((h3: HTMLElement) => {
              h3.style.fontSize = '1.25em';
              h3.style.fontWeight = 'bold';
              h3.style.margin = '0.7em 0';
              h3.style.padding = '0.2em 0';
              h3.style.position = 'relative';
              h3.style.color = '#444';
              h3.style.lineHeight = '1.4';
              h3.style.display = 'block';
              h3.style.wordBreak = 'break-word';
              h3.style.whiteSpace = 'normal';
              h3.style.textIndent = '0';
              h3.style.borderBottom = '3px solid #FFD814';
              h3.style.paddingBottom = '0.3em';
              h3.setAttribute('data-styled', 'true');
            });
          }, 10);
        }
      } catch (error) {
        console.error('H2/H3タグの適用中にエラーが発生しました:', error);
        document.execCommand('formatBlock', false, value || '<h2>');
      }
    } else {
      document.execCommand(command, false, value);
    }

    if (editorRef.current) {
      editorRef.current.focus();
    }

    handleInput();
  };

  // テキスト位置の処理
  const handleTextAlignCommand = () => {
    const nextAlign = textAlign === 'left' ? 'center' : textAlign === 'center' ? 'right' : 'left';
    setTextAlign(nextAlign);

    const justifyCommand = nextAlign === 'left' ? 'justifyLeft' :
                          nextAlign === 'center' ? 'justifyCenter' : 'justifyRight';

    document.execCommand(justifyCommand, false);
    handleInput();

    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  return {
    handleToolbarClick,
    handleTextAlignCommand
  };
};