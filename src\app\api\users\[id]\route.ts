import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { auth } from '@clerk/nextjs/server';

// Prismaクライアントのインスタンスを作成
const prisma = new PrismaClient();

// ユーザー情報を取得するAPI
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 認証チェック
    const { userId: authUserId } = await auth();
    
    if (!authUserId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // リクエストされたユーザーIDを取得
    const { id } = params;
    
    // 自分自身のデータのみアクセス可能（セキュリティ対策）
    if (authUserId !== id) {
      return NextResponse.json(
        { error: 'Forbidden: You can only access your own data' },
        { status: 403 }
      );
    }
    
    // データベースからユーザー情報を取得
    const user = await prisma.user.findUnique({
      where: { user_ID: id },
    });
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // ユーザー情報を返す
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user data:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
