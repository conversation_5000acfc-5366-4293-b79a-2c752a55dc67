import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { Webhook } from 'svix';
import { prisma } from '@/lib/prisma';
import { AccounType } from '@prisma/client';

// Clerkからのwebhookを処理するPOSTハンドラ
export async function POST(request: NextRequest) {

  try {
    // リクエストボディを取得
    const payload = await request.json();
    // リクエストヘッダーを取得
    const headersList = await headers();
    const svix_id = headersList.get('svix-id') || '';
    const svix_timestamp = headersList.get('svix-timestamp') || '';
    const svix_signature = headersList.get('svix-signature') || '';

    // 開発環境では署名検証を完全にスキップ
    console.log('🔍 [Webhook] Processing webhook request');
    console.log('🔍 [Webhook] Event type:', payload.type);
    console.warn('⚠️ [Webhook] Skipping signature verification (development mode)');

    // イベントタイプに基づいて処理を分岐
    const eventType = payload.type || 'unknown';

    switch (eventType) {
      case 'test':
        return NextResponse.json({
          success: true,
          message: 'Test webhook processed successfully',
          received_data: payload
        });

      case 'user.created':
        // 新規ユーザー作成時の処理
        console.log('🔄 [Webhook] Processing user.created event');
        try {
          const userData = payload.data;
          console.log('🔍 [Webhook] User data received:', {
            id: userData.id,
            email_addresses: userData.email_addresses?.length || 0,
            first_name: userData.first_name,
            last_name: userData.last_name,
            username: userData.username
          });

          // メールアドレスの取得
          const primaryEmail = userData.email_addresses.find(
            (email: any) => email.id === userData.primary_email_address_id
          )?.email_address || '';

          // ユーザー名の生成
          const userName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || 'User';
          
          // ユーザー名（username）の生成
          let username = userData.username;
          if (!username) {
            // usernameが設定されていない場合は、user_IDの末尾8文字を使用
            username = `user-${userData.id.slice(-8)}`;
          }

          // 既存ユーザーの確認
          const existingUser = await prisma.user.findUnique({
            where: { user_ID: userData.id }
          });

          // 認証方法の情報を取得
          const externalAccounts = userData.external_accounts || [];
          const googleAccount = externalAccounts.find((account: any) => account.provider === 'google');
          const lineAccount = externalAccounts.find((account: any) => account.provider === 'line');
          
          const googleId = googleAccount?.provider_user_id || null;
          const lineId = lineAccount?.provider_user_id || null;
          const passwordEnabled = userData.password_enabled || false;

          // メインの認証方法を判断（ログ表示用）
          let primaryAuthMethod = 'email';
          if (googleId) primaryAuthMethod = 'google';
          else if (lineId) primaryAuthMethod = 'line';

          // メインの認証方法に基づいてaccount_typeを設定
          let accountType: AccounType = AccounType.email;
          if (googleId) accountType = AccounType.google;
          else if (lineId) accountType = AccounType.line;

          // ユーザー作成データの準備
          const userToCreate = {
            user_ID: userData.id,
            name: userName,
            email: primaryEmail,
            username: username,
            account_type: accountType,
            // 各認証方法のIDを設定
            google: googleId,
            line: lineId,
            password: passwordEnabled ? 'enabled' : null,
            profile_image: '/static/img/profile-default.png', // デフォルト画像を使用
            contact_url: '',
            setup_completed: false, // セットアップ未完了状態
          };

          console.log('🔍 [Webhook] User to create:', userToCreate);

          if (existingUser) {
            // 既存ユーザーの更新（認証方法を追加）
            console.log('🔄 [Webhook] Updating existing user:', existingUser.user_ID);
            const updateData: any = {};

            // 新しい認証方法の情報を追加
            if (googleId && !existingUser.google) {
              updateData.google = googleId;
            }

            if (lineId && !existingUser.line) {
              updateData.line = lineId;
            }

            if (passwordEnabled && !existingUser.password) {
              updateData.password = 'enabled';
            }

            // プロフィール情報の更新（必要に応じて）
            if (userName && userName !== existingUser.name) {
              updateData.name = userName;
            }

            if (primaryEmail && primaryEmail !== existingUser.email) {
              updateData.email = primaryEmail;
            }

            // プロフィール画像は自動更新しない（ユーザーが手動で変更した画像を保持）
            // const profileImage = userData.image_url || userData.profile_image_url;
            // if (profileImage && profileImage !== existingUser.profile_image) {
            //   updateData.profile_image = profileImage;
            // }

            // 更新するデータがある場合のみ更新を実行
            if (Object.keys(updateData).length > 0) {
              console.log('🔄 [Webhook] Updating user with data:', updateData);
              await prisma.user.update({
                where: { user_ID: userData.id },
                data: updateData
              });
              console.log('✅ [Webhook] User updated successfully');
            } else {
              console.log('ℹ️ [Webhook] No updates needed for existing user');
            }
          } else {
            // 新規ユーザーの作成
            console.log('🔄 [Webhook] Creating new user');
            const newUser = await prisma.user.create({
              data: userToCreate
            });
            console.log('✅ [Webhook] New user created:', newUser.user_ID);

            // 新規ユーザーに自動でデフォルトカテゴリ3つを作成
            try {
              console.log('🔄 [Webhook] Creating default categories for new user');
              const defaultCategories = [
                { name: 'カテゴリ1', slug: 'tab1', order: 1 },
                { name: 'カテゴリ2', slug: 'tab2', order: 2 },
                { name: 'カテゴリ3', slug: 'tab3', order: 3 }
              ];

              for (const category of defaultCategories) {
                await prisma.category.create({
                  data: {
                    category_name: category.name,
                    user_ID: userData.id,
                    category_ID: `category_${userData.id}_${category.order}`,
                    category_slug: category.slug,
                    parent_ID: null, // トップレベルカテゴリ
                    order: category.order,
                  }
                });
              }
              console.log('✅ [Webhook] Default categories created successfully');
            } catch (categoryError) {
              console.error('❌ [Webhook] Failed to create default categories:', categoryError);
              // カテゴリ作成に失敗してもユーザー作成は成功とする
            }
          }
        } catch (error) {
          console.error('Error saving user to database:', error);
        }
        break;

      case 'user.updated':
        // ユーザー情報が更新された場合の処理
        try {
          const userData = payload.data;

          // メールアドレスの取得
          const primaryEmail = userData.email_addresses.find(
            (email: any) => email.id === userData.primary_email_address_id
          )?.email_address || '';

          // 更新するユーザー情報
          const userName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || 'User';

          const userToUpdate = {
            name: userName,
            email: primaryEmail,
            profile_image: userData.image_url || userData.profile_image_url,
          };

          // ユーザー情報の更新
          await prisma.user.update({
            where: { user_ID: userData.id },
            data: userToUpdate
          });

        } catch (error) {
          console.error('Error updating user in database:', error);
        }
        break;

      case 'user.deleted':
        // ユーザー削除時の処理
        try {
          const userData = payload.data;

          // ユーザーの削除（関連データも自動的に削除される）
          await prisma.user.delete({
            where: { user_ID: userData.id }
          });

        } catch (error) {
          console.error('Error deleting user from database:', error);
        }
        break;

      default:
        return NextResponse.json({
          success: true,
          message: `Webhook received but not processed for event: ${eventType}`,
          event_type: eventType
        });
    }

    // 成功レスポンスを返す
    return NextResponse.json({
      success: true,
      message: `Webhook processed successfully for event: ${eventType}`
    });

  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
