import React, { useState, useEffect } from "react";
import { useUser } from "@/contexts/UserContext";

interface CategorySelectProps {
  className?: string;
  category?: string;
  subCategory: string;
  // 親カテゴリIDを受け取るためのprop
  parentCategoryId?: string;
  // 親にカテゴリを反映するためのハンドラ
  changeCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
  changeSubCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface SubCategory {
  id: string;
  category_name: string;
  category_ID: string;
}

export const CategorySelect: React.FC<CategorySelectProps> = ({
  className,
  category = "",
  subCategory,
  parentCategoryId,
  changeCategory,
  changeSubCategory,
}) => {
  // UserContextからユーザーIDを取得
  const { userId } = useUser();
  // ▼ モーダルの開閉管理
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ▼ 既存カテゴリのリスト（最初に「未分類（今は設定しない）」を用意）
  const [categories, setCategories] = useState<string[]>([
    "未分類（今は設定しない）",
  ]);
  
  // サブカテゴリの一覧を保持する状態
  const [subCategories, setSubCategories] = useState<SubCategory[]>([]);

  // ▼ 新しく追加するカテゴリ名
  const [newCategory, setNewCategory] = useState("");

  // ▼ 編集対象のカテゴリ
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedCategory, setEditedCategory] = useState("");

  // ▼ 「追加」ボタンの活性化判定
  const isAddButtonActive = newCategory.trim() !== "";

  // 親カテゴリIDが変更されたときにサブカテゴリを取得
  useEffect(() => {
    const fetchSubCategories = async () => {
      if (!userId || !parentCategoryId) return;
      
      try {
        setIsLoading(true);
        const response = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`);
        
        if (!response.ok) {
          throw new Error('サブカテゴリの取得に失敗しました');
        }
        
        const data = await response.json();
        console.log('取得したサブカテゴリ:', data);
        
        // サブカテゴリをセット
        setSubCategories(data);
        
        // カテゴリ名のリストを更新（「未分類」は常に先頭に）
        const categoryNames = ["未分類（今は設定しない）", ...data.map((item: SubCategory) => item.category_name)];
        setCategories(categoryNames);
      } catch (err) {
        console.error('サブカテゴリ取得エラー:', err);
        setError(err instanceof Error ? err.message : '不明なエラー');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSubCategories();
  }, [userId, parentCategoryId]);

  // ▼ カテゴリを追加
  const handleAddCategory = async () => {
    if (newCategory.trim() === "") return;
    
    console.log("新しいサブカテゴリを追加:", newCategory);
    setIsLoading(true);
    setError(null);
    
    try {
      // データベースにサブカテゴリを保存
      if (userId && parentCategoryId) {
        const newSubcategoryId = `new_subcategory_${Date.now()}`;
        console.log(`サブカテゴリ保存リクエスト: ID=${newSubcategoryId}, 名前=${newCategory}, ユーザーID=${userId}, 親ID=${parentCategoryId}`);
        
        // updateCategoryエンドポイントを使用
        const response = await fetch('/api/updateCategory', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: newSubcategoryId, // 一時的なIDを生成（必須パラメータ）
            category_name: newCategory,
            user_ID: userId,
            parent_ID: parentCategoryId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'サブカテゴリの保存に失敗しました');
        }

        const result = await response.json();
        console.log('サブカテゴリの保存結果:', result);
        
        // サブカテゴリリストを再取得
        const fetchResponse = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`);
        if (fetchResponse.ok) {
          const data = await fetchResponse.json();
          setSubCategories(data);
          
          // カテゴリ名のリストを更新（「未分類」は常に先頭に）
          const categoryNames = ["未分類（今は設定しない）", ...data.map((item: SubCategory) => item.category_name)];
          setCategories(categoryNames);
        } else {
          // UIを更新（APIが失敗した場合のフォールバック）
          setCategories((prev) => [...prev, newCategory]);
        }
      } else {
        // UIを更新
        setCategories((prev) => [...prev, newCategory]);
      }
      
      // 追加したカテゴリを選択状態にする
      const syntheticEvent = {
        target: { value: newCategory },
      } as unknown as React.ChangeEvent<HTMLInputElement>;
      
      changeCategory(syntheticEvent);
      changeSubCategory(syntheticEvent);
      
      setNewCategory("");
      setIsModalOpen(false);
    } catch (err: any) {
      console.error('サブカテゴリの保存エラー:', err);
      setError(err.message || 'サブカテゴリの保存中にエラーが発生しました');
    } finally {
      setIsLoading(false);
    }
  };

  // ▼ カテゴリを編集
  const handleEditCategory = (idx: number, cat: string) => {
    setEditingIndex(idx);
    setEditedCategory(cat);
  };

  // ▼ 編集を保存
  const handleSaveEdit = () => {
    if (editingIndex === null || editedCategory.trim() === "") return;

    setCategories((prev) => {
      const newCategories = [...prev];
      newCategories[editingIndex] = editedCategory;
      return newCategories;
    });

    setEditingIndex(null);
    setEditedCategory("");
  };

  // ▼ 編集をキャンセル
  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditedCategory("");
  };

  // ▼ カテゴリを削除
  const handleDeleteCategory = (cat: string) => {
    setDeleteTarget(cat);
    setIsDeleteModalOpen(true);
  };

  // ▼ 削除を確定
  const confirmDeleteCategory = () => {
    if (!deleteTarget) return;

    setCategories((prev) => prev.filter((cat) => cat !== deleteTarget));
    setIsDeleteModalOpen(false);
    setDeleteTarget(null);
  };

  return (
    <div className={className}>
      <div className="mb-4">
        <label className="block text-gray-700 text-sm font-bold mb-2">
          サブカテゴリ
        </label>
        <div className="flex items-center">
          <input
            type="text"
            value={subCategory}
            onChange={changeSubCategory}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="サブカテゴリを選択または入力"
            onClick={() => setIsModalOpen(true)}
            readOnly
          />
          <button
            type="button"
            onClick={() => setIsModalOpen(true)}
            className="ml-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded"
          >
            選択
          </button>
        </div>
      </div>

      {/* カテゴリ選択モーダル */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-[90%] max-w-[300px] p-4 shadow-lg relative">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">サブカテゴリを選択</h3>
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>

            {/* カテゴリリスト */}
            <div className="mb-4 max-h-[200px] overflow-y-auto">
              {isLoading && categories.length <= 1 ? (
                <p className="text-center text-gray-500">読み込み中...</p>
              ) : (
                categories.map((cat, idx) => (
                  <div
                    key={idx}
                    className="flex justify-between items-center border-b border-gray-100 py-2"
                  >
                    {editingIndex === idx ? (
                      <input
                        type="text"
                        value={editedCategory}
                        onChange={(e) => setEditedCategory(e.target.value)}
                        className="flex-1 bg-[#F6F7F8] px-3 py-2 text-sm outline-none"
                      />
                    ) : (
                      <button
                        type="button"
                        onClick={() => {
                          const syntheticEvent = {
                            target: { value: cat },
                          } as unknown as React.ChangeEvent<HTMLInputElement>;
                          changeCategory(syntheticEvent);
                          changeSubCategory(syntheticEvent);
                          setIsModalOpen(false);
                        }}
                        className="flex-1 text-left text-sm"
                      >
                        {cat}
                      </button>
                    )}

                    {idx !== 0 && (
                      <div className="flex">
                        {editingIndex === idx ? (
                          <div className="flex">
                            <button
                              type="button"
                              onClick={handleSaveEdit}
                              className="px-2 py-1 text-sm text-white bg-[#007CBA]"
                            >
                              保存
                            </button>
                            <button
                              type="button"
                              onClick={handleCancelEdit}
                              className="px-2 py-1 text-sm text-white bg-gray-500 ml-1"
                            >
                              キャンセル
                            </button>
                          </div>
                        ) : (
                          <div className="flex">
                            <button
                              type="button"
                              onClick={() => handleEditCategory(idx, cat)}
                              className="px-2 py-1 text-sm text-gray-600 border border-gray-300"
                            >
                              編集
                            </button>
                            <button
                              type="button"
                              onClick={() => handleDeleteCategory(cat)}
                              className="px-2 py-1 text-sm  text-white bg-black"
                            >
                              削除
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>

            {/* 新しくカテゴリを追加するエリア */}
            <div className="mt-0">
              <p className="text-xs text-gray-400 mt-4 mb-16">
                ※サブカテゴリは自由に「追加・変更・削除」できます
              </p>

              {error && (
                <p className="text-red-500 text-xs mb-2">{error}</p>
              )}

              <div className="flex items-center">
                <input
                  type="text"
                  placeholder="新しくサブカテゴリを追加する"
                  value={newCategory}
                  onFocus={() => {
                    // ① タップした時点で編集モード解除
                    setEditingIndex(null);
                  }}
                  onChange={(e) => setNewCategory(e.target.value)}
                  className="flex-1 bg-[#F6F7F8] px-3 py-3 text-sm outline-none"
                />
                <button
                  type="button"
                  onClick={handleAddCategory}
                  className={`px-3 py-3 text-sm text-white ${
                    isAddButtonActive && !isLoading ? "bg-[#E63B5F]" : "bg-gray-300"
                  }`}
                  disabled={!isAddButtonActive || isLoading}
                >
                  {isLoading ? "保存中..." : "追加"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 削除確認モーダル */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-[90%] max-w-[300px] p-4 shadow-lg relative">
            <p className="text-sm">削除しますか？</p>
            <div className="flex justify-end gap-4 mt-4">
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                className="text-[#007CBA] text-sm"
              >
                キャンセルする
              </button>
              <button
                type="button"
                onClick={confirmDeleteCategory}
                className="text-[#007CBA] text-sm"
              >
                削除する
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
