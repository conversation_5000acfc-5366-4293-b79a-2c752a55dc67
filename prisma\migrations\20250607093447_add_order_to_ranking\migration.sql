/*
  Warnings:

  - You are about to drop the column `account_type` on the `SnsMaster` table. All the data in the column will be lost.
  - You are about to drop the column `sns_image` on the `SnsMaster` table. All the data in the column will be lost.
  - You are about to drop the column `user_ID` on the `SnsMaster` table. All the data in the column will be lost.
  - You are about to drop the column `account_type` on the `SnsUser` table. All the data in the column will be lost.
  - You are about to drop the column `sns_ID` on the `SnsUser` table. All the data in the column will be lost.
  - You are about to drop the column `sns_image` on the `SnsUser` table. All the data in the column will be lost.
  - You are about to drop the column `sns_name` on the `SnsUser` table. All the data in the column will be lost.
  - You are about to drop the column `contact_phone` on the `User` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[sns_ID]` on the table `SnsMaster` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `sns_master_id` to the `SnsUser` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "SnsMaster" DROP CONSTRAINT "SnsMaster_user_ID_fkey";

-- DropForeignKey
ALTER TABLE "SnsUser" DROP CONSTRAINT "SnsUser_sns_image_fkey";

-- DropForeignKey
ALTER TABLE "SnsUser" DROP CONSTRAINT "SnsUser_sns_name_fkey";

-- DropIndex
DROP INDEX "SnsMaster_sns_image_key";

-- DropIndex
DROP INDEX "SnsMaster_user_ID_idx";

-- DropIndex
DROP INDEX "SnsUser_sns_image_idx";

-- DropIndex
DROP INDEX "SnsUser_sns_name_idx";

-- AlterTable
ALTER TABLE "Ranking" ADD COLUMN     "order" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "SnsMaster" DROP COLUMN "account_type",
DROP COLUMN "sns_image",
DROP COLUMN "user_ID";

-- AlterTable
ALTER TABLE "SnsUser" DROP COLUMN "account_type",
DROP COLUMN "sns_ID",
DROP COLUMN "sns_image",
DROP COLUMN "sns_name",
ADD COLUMN     "display_order" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "sns_master_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "User" DROP COLUMN "contact_phone",
ADD COLUMN     "background_image" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "SnsMaster_sns_ID_key" ON "SnsMaster"("sns_ID");

-- CreateIndex
CREATE INDEX "SnsUser_sns_master_id_idx" ON "SnsUser"("sns_master_id");

-- AddForeignKey
ALTER TABLE "SnsUser" ADD CONSTRAINT "SnsUser_sns_master_id_fkey" FOREIGN KEY ("sns_master_id") REFERENCES "SnsMaster"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
