'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function SaveRankingGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">ランキングを保存する方法</h1>
          <div className="pt-6 text-[14px] text-[#313131]">
              <p>1. 保存したいランキングの詳細ページにアクセスします。</p>
              <p>2. 「保存」ボタン（ブックマークアイコン）をタップします。</p>
              <p>3. 保存が完了すると、ボタンの色が変わり、保存済みであることを示します。</p>
              <p>4. 保存したランキングは「保存済みランキング」セクションで確認できます。</p>
          </div>
        </div>      
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
