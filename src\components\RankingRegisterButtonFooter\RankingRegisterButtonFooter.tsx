/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useUser } from '@clerk/nextjs';



interface RankingRegisterButtonFooterProps {
    images: string[],
    description: string,
    submitRanking?: () => void;
    userId: string;
    title?: string;
    isEditMode?: boolean; // 編集モードかどうかを示すフラグ
    // 追加プロパティ
    category?: string;
    categoryId?: string;
    subCategory?: string;
    rating?: number;
    amazonUrl?: string;
    rakutenUrl?: string;
    yahooUrl?: string;
    qoo10Url?: string;
    officialUrl?: string;
    // 下書き編集用
    draftId?: string;
    // 下書き機能用のコールバック
    onSaveDraft?: () => void;
    onDraftList?: () => void;
}

export const RankingRegisterButtonFooter: React.FC<RankingRegisterButtonFooterProps> = ({
  submitRanking,
  images,
  description,
  userId,
  title = "",
  isEditMode = false, // デフォルトは新規登録モード
  // 追加プロパティ
  category = "",
  categoryId = "",
  subCategory = "",
  rating = 1,
  amazonUrl = "",
  rakutenUrl = "",
  yahooUrl = "",
  qoo10Url = "",
  officialUrl = "",
  // 下書き編集用
  draftId = "",
  // 下書き機能用のコールバック
  onSaveDraft,
  onDraftList
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [username, setUsername] = useState<string>("");
  const router = useRouter();
  const { user } = useUser();

  // 必須項目のバリデーション
  const isValidUrlInput = (url: string): boolean => {
    const trimmedUrl = url.trim();
    return trimmedUrl.startsWith('https://') && trimmedUrl.length > 8;
  };

  const isFormValid = (): boolean => {
    return (
      (images && images.length > 0 && images.some(img => img && img.trim() !== '')) && // 画像設定
      title.trim() !== '' && // タイトル入力
      rating > 0 && // おすすめ度選択
      subCategory.trim() !== '' && // サブカテゴリ選択
      (isValidUrlInput(amazonUrl) || isValidUrlInput(rakutenUrl) || isValidUrlInput(yahooUrl) || isValidUrlInput(qoo10Url) || isValidUrlInput(officialUrl)) // 商品リンク1つ以上
    );
  };

  // ユーザー名を取得
  useEffect(() => {
    const fetchUsername = async () => {
      if (user) {
        try {
          const response = await fetch(`/api/getUser?user_ID=${user.id}`);
          if (response.ok) {
            const userData = await response.json();
            setUsername(userData.username || "");
          }
        } catch (error) {
          // ユーザー名取得エラー
        }
      }
    };

    fetchUsername();
  }, [user]);
  
  // フォームの有効性をチェック
  const formValid = isFormValid();

  // ホバー時と押下時のスタイルを動的に変更
  const buttonStyle = {
    backgroundColor: formValid
      ? (isHovered || isPressed ? '#DD0F2B' : '#E63B5F')
      : '#CCCCCC', // 無効時はグレー
    transition: 'background-color 0.2s ease-in-out',
    cursor: formValid ? 'pointer' : 'not-allowed'
  };

  // 下書き保存処理
  const handleSaveDraft = async () => {
    if (onSaveDraft) {
      onSaveDraft();
    }
  };

  // 下書き一覧ページへの遷移
  const handleDraftList = () => {
    if (onDraftList) {
      onDraftList();
    } else if (username) {
      router.push(`/${username}/draftList`);
    }
  };

  // 編集モードまたは下書きボタンが不要な場合は登録ボタンのみ表示
  const showDraftButtons = !isEditMode && (onSaveDraft || onDraftList);

  // ボタンクリック処理（バリデーション付き）
  const handleSubmit = () => {
    if (formValid && submitRanking) {
      submitRanking();
    }
  };

  return (
    <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-[500px] h-14 flex items-center justify-center px-4 py-0 border-t border-solid border-[var(--line-color)] shadow-[0_-0px_6px_0_rgba(0,0,0,0.1)] bg-white">
      {showDraftButtons ? (
        // 3つのボタンを横並びで表示（追加モード）
        <div className="flex w-full gap-2">
          {/* 下書き保存ボタン */}
          <button
            className="border border-[#1f2937] w-20 h-10 bg-gray-100 hover:bg-gray-200 rounded-[100px] flex items-center justify-center transition-colors"
            onClick={handleSaveDraft}
          >
            <span className="text-[#1f2937] text-xs font-normal">下書き保存</span>
          </button>

          {/* 下書き一覧ボタン */}
          <button
            className="border border-[#1f2937] w-20 h-10 bg-gray-100 hover:bg-gray-200 rounded-[100px] flex items-center justify-center transition-colors"
            onClick={handleDraftList}
          >
            <span className=" text-[#1f2937] text-xs font-normal">下書き一覧</span>
          </button>

          {/* 登録ボタン（長め） */}
          <button
            className="flex-1 h-10 rounded-[37px] overflow-hidden flex items-center justify-center"
            style={buttonStyle}
            onClick={handleSubmit}
            disabled={!formValid}
            onMouseEnter={() => formValid && setIsHovered(true)}
            onMouseLeave={() => {
              setIsHovered(false);
              setIsPressed(false);
            }}
            onMouseDown={() => formValid && setIsPressed(true)}
            onMouseUp={() => setIsPressed(false)}
            onTouchStart={() => formValid && setIsPressed(true)}
            onTouchEnd={() => setIsPressed(false)}
          >
            <span className={`text-sm font-normal text-center tracking-[0] leading-[normal] ${formValid ? 'text-white' : 'text-gray-500'}`}>
              追加する
            </span>
          </button>
        </div>
      ) : (
        // 登録ボタンのみ表示（編集モード）
        <button
          className="w-full max-w-[400px] h-10 rounded-[37px] overflow-hidden flex items-center justify-center"
          style={buttonStyle}
          onClick={handleSubmit}
          disabled={!formValid}
          onMouseEnter={() => formValid && setIsHovered(true)}
          onMouseLeave={() => {
            setIsHovered(false);
            setIsPressed(false);
          }}
          onMouseDown={() => formValid && setIsPressed(true)}
          onMouseUp={() => setIsPressed(false)}
          onTouchStart={() => formValid && setIsPressed(true)}
          onTouchEnd={() => setIsPressed(false)}
        >
          <span className={`text-sm font-normal text-center tracking-[0] leading-[normal] ${formValid ? 'text-white' : 'text-gray-500'}`}>
            {isEditMode ? '更新する' : '追加する'}
          </span>
        </button>
      )}

      <div className="absolute right-2 bottom-0">
        <img
          src="https://c.animaapp.com/GmJTluXx/img/<EMAIL>"
          alt=""
          className="w-[50px] h-auto opacity-60"
        />
      </div>
    </div>
  );
};
