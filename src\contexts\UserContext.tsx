'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { setCookie, getCookie, deleteCookie } from 'cookies-next';
// import { useAuth, useUser as useClerkUser } from '@clerk/nextjs'; // 一時的に無効化

// コンテキストの型定義
interface UserContextType {
  userId: string;
  login: (userId: string) => void;
  logout: () => void;
}

// デフォルト値の設定
const defaultUserContext: UserContextType = {
  userId: '', // デフォルト値を元に戻す（APIが対応しているユーザーID）
  login: () => {},
  logout: () => {},
};

// コンテキストの作成
const UserContext = createContext<UserContextType>(defaultUserContext);

// UserProviderの型定義
interface UserProviderProps {
  children: ReactNode;
}

// UserProviderコンポーネント
export const UserProvider = ({ children }: UserProviderProps): JSX.Element => {
  const [userId, setUserId] = useState<string>(defaultUserContext.userId);

  // 一時的にClerkを無効化
  const isAuthLoaded = true;
  const isUserLoaded = true;
  const clerkUserId = null;

  // Clerkの認証情報が読み込まれたらユーザーIDを設定
  useEffect(() => {
    if (isAuthLoaded && isUserLoaded) {
      if (clerkUserId) {
        // ClerkからユーザーIDを取得
        console.log('Clerk user authenticated:', clerkUserId);
        setUserId(clerkUserId);

        // LocalStorageとCookieに保存
        if (typeof window !== 'undefined') {
          localStorage.setItem('userId', clerkUserId);
        }

        setCookie('userId', clerkUserId, {
          maxAge: 30 * 24 * 60 * 60, // 30日間
          path: '/',
        } as any);
      } else {
        // 未ログインの場合はローカルストレージやCookieから確認
        const storedUserId = typeof window !== 'undefined'
          ? localStorage.getItem('userId')
          : null;

        const cookieUserId = getCookie('userId') as string | undefined;

        if (storedUserId) {
          setUserId(storedUserId);
        } else if (cookieUserId) {
          setUserId(cookieUserId);
          // LocalStorageに同期
          if (typeof window !== 'undefined') {
            localStorage.setItem('userId', cookieUserId);
          }
        }
      }
    }
  }, [isAuthLoaded, isUserLoaded, clerkUserId]);

  // ログイン処理
  const login = (newUserId: string) => {
    setUserId(newUserId);

    // LocalStorageに保存
    if (typeof window !== 'undefined') {
      localStorage.setItem('userId', newUserId);
    }

    // Cookieに保存（サーバーサイドレンダリング時にも利用可能）
    setCookie('userId', newUserId, {
      maxAge: 30 * 24 * 60 * 60, // 30日間
      path: '/'
    } as any);
  };

  // ログアウト処理
  const logout = () => {
    setUserId(defaultUserContext.userId);

    // LocalStorageから削除
    if (typeof window !== 'undefined') {
      localStorage.removeItem('userId');
    }

    // Cookieから削除
    deleteCookie('userId', { path: '/' } as any);

    // 注意: Clerkのログアウトはここでは行わない
    // Clerkのログアウトは別途、SignOutButtonコンポーネントなどで行う
  };

  // 型定義の問題を回避するために、型アサーションを使用
  return (
    <UserContext.Provider value={{ userId, login, logout }}>
      {children}
    </UserContext.Provider>
  ) as JSX.Element;
};

// useUserフック
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// getUserIdユーティリティ関数
export const getUserId = (): string => {
  // クライアントサイド
  if (typeof window !== 'undefined') {
    const storedUserId = localStorage.getItem('userId');
    if (storedUserId) return storedUserId;
  }

  // サーバーサイド
  const cookieUserId = getCookie('userId') as string | undefined;
  return cookieUserId || defaultUserContext.userId;
};

// データベースからユーザー情報を取得する関数
export const fetchUserData = async (userId: string) => {
  try {
    // APIルートを使用してデータベースからユーザー情報を取得
    const response = await fetch(`/api/getUser?user_ID=${userId}`);

    if (!response.ok) {
      throw new Error('Failed to fetch user data');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching user data:', error);
    return null;
  }
};
