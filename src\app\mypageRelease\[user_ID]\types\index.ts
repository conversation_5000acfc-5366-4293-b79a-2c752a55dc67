import React from "react";

export interface SideMenuProps {
  isModalOpen: boolean;
  handleCloseModal: () => void;
  isLoggedIn: boolean;
  handleTouchStart: (e: React.TouchEvent) => void;
  handleTouchMove: (e: React.TouchEvent) => void;
  handleTouchEnd: () => void;
}

export interface SortableSnsIconProps {
  id: string;
  type: string;
  url: string;
  iconSrc: string;
  disabled?: boolean;
}

export interface PageProps {
  params: {
    userId: string;
  };
}

export interface CategoryType {
  id: string;
  category_ID: string;
  category_name: string;
  category_slug?: string;
  userId: string;
  domain: string;
  parent_ID: string;
  order: number;
  subcategories?: CategoryType[];
}

export interface RankingType {
  id: string;
  ranking_ID: string;
  ranking_title: string;
  ranking_description?: string;
  ranking_url?: string;
  thumbnail_image: string[] | string;
  recommend_rate?: number;
  subCategory_ID?: string;
  subcategory_id?: string;
  userId: string;
  domain?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  /* 互換用フィールド --------------------------------------------- */
  title?: string;
  description?: string;
  category_id?: string;
  user_id?: string;
  items?: Array<{
    id: string;
    title: string;
    description: string;
    image_url: string;
    ranking: number;
  }>;
}

export interface UserInfo {
  userId: string;
  user_name: string;
  profile_image: string;
  website: string;
  email: string;
  selfIntroduction: string;
  /** 任意キーの SNS リンク */
  snsLinks: Record<string, string>;
  snsOrder?: string[];
  domain?: string;
  background_image?: string;
}