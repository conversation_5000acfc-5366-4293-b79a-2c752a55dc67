import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { generateUniqueRankingId } from '@/lib/rankingIdGenerator';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();


    
    // 必須フィールドのバリデーション（下書きなので緩い条件）
    if (!data.user_ID) {
      return NextResponse.json(
        { success: false, error: 'ユーザーIDが必要です' },
        { status: 400 }
      );
    }

    // タイトルが空の場合はデフォルトタイトルを設定
    if (!data.title || data.title.trim() === '') {
      data.title = '無題のランキング';
    }

    // 下書きなので、カテゴリやサブカテゴリが未設定でも保存可能
    
    // 編集モードか新規作成モードかを判定
    const isEditMode = data.ranking_ID ? true : false;
    console.log(`処理モード: ${isEditMode ? '編集モード' : '新規作成モード'}`);

    let result;

    if (isEditMode) {
      // 既存の下書きを更新
      console.log(`下書き更新処理を開始します: ランキングID=${data.ranking_ID}`);

      result = await prisma.ranking.update({
        where: {
          ranking_ID: data.ranking_ID,
          user_ID: data.user_ID, // セキュリティのため、ユーザーIDも条件に含める
          status: 'DRAFT', // 下書きのもののみ更新可能
        },
        data: {
          ranking_title: data.title,
          ranking_description: data.description || '',
          thumbnail_image: data.images || [],
          recommend_rate: data.rating || 1,
          amazon_url: data.amazonUrl || '',
          rakuten_url: data.rakutenUrl || '',
          yahoo_url: data.yahooUrl || '',
          qoo10_url: data.qoo10Url || '',
          official_url: data.officialUrl || '',
          subCategory_ID: data.subCategoryId || '',
          status: 'DRAFT', // 下書きとして保存
          updated_at: new Date(),
        },
      });


    } else {
      // 新規下書きを作成
      console.log('新規下書き作成処理を開始します');

      // ユーザーのusernameを取得
      const user = await prisma.user.findUnique({
        where: { user_ID: data.user_ID },
        select: { username: true },
      });

      if (!user) {
        return NextResponse.json(
          { success: false, error: 'ユーザーが見つかりません' },
          { status: 404 }
        );
      }

      // ranking_IDを生成（新しい短い形式）
      const rankingId = await generateUniqueRankingId();

      result = await prisma.ranking.create({
        data: {
          ranking_ID: rankingId,
          user_ID: data.user_ID,
          ranking_title: data.title,
          ranking_description: data.description || '',
          thumbnail_image: data.images || [],
          recommend_rate: data.rating || 1,
          amazon_url: data.amazonUrl || '',
          rakuten_url: data.rakutenUrl || '',
          yahoo_url: data.yahooUrl || '',
          qoo10_url: data.qoo10Url || '',
          official_url: data.officialUrl || '',
          subCategory_ID: data.subCategoryId || '',
          status: 'DRAFT', // 下書きとして保存
          order: 0,
        },
      });


    }
    
    // 成功した場合のレスポンスを返す
    return NextResponse.json(
      { 
        success: true, 
        data: result,
        message: isEditMode ? '下書きを更新しました' : '下書きを保存しました'
      },
      {
        status: isEditMode ? 200 : 201,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (error) {
    console.error('下書き保存中にエラーが発生しました:', error);
    return NextResponse.json(
      { success: false, error: '下書きの保存に失敗しました' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  // プリフライトリクエストに対するレスポンス
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}
