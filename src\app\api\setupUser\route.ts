import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(req: NextRequest) {
  try {
    // リクエストボディのJSONデータを取得
    const postData = await req.json();

    // 必須フィールドのチェック
    if (!postData.user_ID || !postData.username || !postData.name) {
      return NextResponse.json(
        { error: '必須フィールドが不足しています' },
        { status: 400 }
      );
    }

    // 既存ユーザーの確認
    const existingUser = await prisma.user.findUnique({
      where: { user_ID: postData.user_ID }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'ユーザーが見つかりません' },
        { status: 404 }
      );
    }

    // ユーザー名の重複チェック（自分以外のユーザーで同じユーザー名がないかチェック）
    const duplicateUser = await prisma.user.findFirst({
      where: { 
        username: postData.username,
        user_ID: { not: postData.user_ID }
      }
    });

    if (duplicateUser) {
      return NextResponse.json(
        { error: 'このユーザー名は既に使用されています' },
        { status: 409 }
      );
    }

    // ユーザー情報の更新
    const updatedUser = await prisma.user.update({
      where: { user_ID: postData.user_ID },
      data: {
        name: postData.name,
        username: postData.username,
        setup_completed: postData.setup_completed || true,
      },
    });

    console.log('setupUser API - ユーザー更新成功:', updatedUser.id);

    // デフォルトカテゴリが存在しない場合は作成
    try {
      const existingCategories = await prisma.category.findMany({
        where: {
          user_ID: postData.user_ID,
          parent_ID: null // トップレベルカテゴリのみ
        }
      });

      if (existingCategories.length === 0) {
        const defaultCategories = [
          { name: 'カテゴリ1', slug: 'tab1', order: 1 },
          { name: 'カテゴリ2', slug: 'tab2', order: 2 },
          { name: 'カテゴリ3', slug: 'tab3', order: 3 }
        ];

        for (const category of defaultCategories) {
          await prisma.category.create({
            data: {
              category_name: category.name,
              user_ID: postData.user_ID,
              category_ID: `category_${postData.user_ID}_${category.order}`,
              category_slug: category.slug,
              parent_ID: null, // トップレベルカテゴリ
              order: category.order,
            }
          });
        }

      }
    } catch (categoryError) {
      console.error('🔧 [setupUser] カテゴリ作成エラー:', categoryError);
      // カテゴリ作成に失敗してもユーザー更新は成功とする
    }

    return NextResponse.json({
      success: true,
      user: {
        id: updatedUser.id,
        user_ID: updatedUser.user_ID,
        username: updatedUser.username,
        name: updatedUser.name,
        email: updatedUser.email,
        setup_completed: updatedUser.setup_completed
      }
    });
  } catch (error) {
    console.error('setupUser API - エラー:', error);

    // Prismaエラーの詳細ログ
    if (error instanceof Error) {
      console.error('setupUser API - エラーメッセージ:', error.message);
      console.error('setupUser API - エラースタック:', error.stack);
    }

    // Prismaエラーの場合の詳細情報
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('setupUser API - Prismaエラーコード:', (error as any).code);
      console.error('setupUser API - Prismaエラーメタ:', (error as any).meta);
    }

    return NextResponse.json(
      { 
        error: 'ユーザー情報の更新に失敗しました',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
