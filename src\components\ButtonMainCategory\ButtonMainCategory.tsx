"use client";

import PropTypes from "prop-types";
import React, { useState, useEffect, useRef } from "react";
import { useUser, getUserId } from "@/contexts/UserContext"; // UserContextをインポート
import { useSearchParams, useRouter } from "next/navigation"; // URLクエリパラメータを取得するために追加
import { useAuth } from "@clerk/nextjs"; // Clerk認証フックを追加
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable";
import {
  restrictToHorizontalAxis,
  restrictToParentElement,
} from "@dnd-kit/modifiers";
import { SortableCategoryTab } from "../SortableCategoryTab/SortableCategoryTab";

// カテゴリ型定義
interface Category {
  id: string;
  category_name: string;
}

// Props インターフェースに userID を追加
interface Props {
  userID: string;
  categories?: Category[];
  selectedIndex?: number;
  onCategorySelect: (id: string, category_name?: string) => void;
  onCategoriesReorder?: (newCategories: Category[]) => void;
  onCategoryUpdated?: (action?: string, categoryId?: string, categoryName?: string) => void;
  showAddButton?: boolean;
  isEditMode?: boolean;
  isModalOpen?: boolean;
  onModalClose?: () => void;
  isOwnPage?: boolean;
}

// タブコンポーネント
interface CategoryTabProps {
  category: Category;
  index: number;
  selectedIndex: number;
  editingIndex: number | null;
  editedName: string;
  onCategoryClick: (index: number, id: string, event?: React.MouseEvent) => void;
  onOptionsClick: (e: React.MouseEvent, index: number) => void;
  onInputChange: (index: number, value: string) => void;
  onInputBlur: (index: number) => void;
  onKeyDown: (index: number, e: React.KeyboardEvent<HTMLInputElement>) => void;
  isModalOpen?: boolean;
  onModalClose?: () => void;
  optionsIndex?: number | null;
  onCloseOptionsModal?: () => void;
  isOwnPage?: boolean;
  onDirectShare?: (index: number) => void;
}

const CategoryTab = ({
  category,
  index,
  selectedIndex,
  editingIndex,
  editedName,
  onCategoryClick,
  onOptionsClick,
  onInputChange,
  onInputBlur,
  onKeyDown,
  isModalOpen = false,
  onModalClose,
  optionsIndex = null,
  onCloseOptionsModal,
  isOwnPage = true,
  onDirectShare
}: CategoryTabProps) => (
  <li
    key={`${category.id}_${index}`}
    onClick={(event) => {
      // サイドメニューモーダルが開いている場合は、モーダルを閉じるだけでカテゴリ切り替えは行わない
      if (isModalOpen && onModalClose) {
        event.preventDefault();
        event.stopPropagation();
        onModalClose();
        return;
      }

      // オプションモーダルが開いている場合は、モーダルを閉じるだけでカテゴリ切り替えは行わない
      if (optionsIndex !== null && onCloseOptionsModal) {
        event.preventDefault();
        event.stopPropagation();
        onCloseOptionsModal();
        return;
      }

      onCategoryClick(index, category.id, event);
    }}
    className={`category-tab flex-none w-auto pt-4 pb-4 pl-[20px] pr-0 cursor-pointer flex items-center ${
      selectedIndex === index ? "bg-white" : ""
    }`}
  >
    {editingIndex === index ? (
      <input
        type="text"
        data-index={index}
        value={editedName}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => onInputChange(index, e.target.value)}
        onBlur={() => onInputBlur(index)}
        onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => onKeyDown(index, e)}
        autoFocus
        className="text-black-1 text-[14px] focus:outline-none"
      />
    ) : (
      <div className="text-black-1 text-[14px]">{editedName}</div>
    )}
    {/* すべてのカテゴリに三点リーダーボタンを表示 */}
    <button
      onClick={(e: React.MouseEvent) => {
        e.stopPropagation();

        // サイドメニューモーダルが開いている場合は、モーダルを閉じるだけで他の処理は行わない
        if (isModalOpen && onModalClose) {
          onModalClose();
          return;
        }

        // ログアウト状態の場合は選択中のカテゴリのみ共有機能を実行
        if (!isOwnPage && onDirectShare) {
          if (index === selectedIndex) {
            onDirectShare(index);
          }
          return;
        }

        // ログイン状態での三点リーダーボタンのクリックを処理
        if (index === selectedIndex) {
          // 選択中のカテゴリの場合はモーダルを開く
          onOptionsClick(e, index);
        } else {
          // 選択中でない場合は、まずカテゴリを選択する
          onCategoryClick(index, category.id);
        }
      }}
      className="border-r border-[#DDDDDD] pt-1 pb-1 flex flex-col justify-start items-center w-[42px] focus:outline-none cursor-pointer"
      style={{transform: 'translateZ(0)', backfaceVisibility: 'hidden'}}
    >
      <span className="w-[2px] h-[2px] bg-black rounded-full mb-[2px]" style={{transform: 'translateZ(0)'}}></span>
      <span className="w-[2px] h-[2px] bg-black rounded-full mb-[2px]" style={{transform: 'translateZ(0)'}}></span>
      <span className="w-[2px] h-[2px] bg-black rounded-full" style={{transform: 'translateZ(0)'}}></span>
    </button>
  </li>
);

export const ButtonMainCategory = ({
  userID,
  categories,
  selectedIndex: propSelectedIndex,
  onCategorySelect,
  onCategoriesReorder,
  onCategoryUpdated,
  showAddButton,
  isEditMode,
  isModalOpen = false,
  onModalClose,
  isOwnPage = true
}: Props): JSX.Element | null => {
  // URLからクエリパラメータを取得
  const searchParams = useSearchParams();
  const router = useRouter();
  const categoryFromUrl = searchParams.get('category');
  
  // 現在選択中のタブのインデックス
  const [selectedIndex, setSelectedIndex] = useState<number>(propSelectedIndex || 0);
  // 編集中（インライン編集）のタブのインデックス
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  // ローカルで管理するカテゴリ配列
  const [localCategories, setLocalCategories] = useState<Category[]>(categories || []);
  // 各タブの名前（インライン編集用）
  const [editedNames, setEditedNames] = useState<string[]>(categories?.map(c => c.category_name) || []);
  // 追加処理中フラグ
  const [isAdding, setIsAdding] = useState<boolean>(false);
  // オプションモーダルを表示中のタブのインデックス
  const [optionsIndex, setOptionsIndex] = useState<number | null>(null);
  // 削除確認モーダルを表示中のタブのインデックス
  const [confirmDeleteIndex, setConfirmDeleteIndex] = useState<number | null>(null);
  // 編集前の名前（空文字対策用）
  const [originalName, setOriginalName] = useState<string>("");
  // オプションモーダルの表示位置
  const [optionsModalPosition, setOptionsModalPosition] = useState<{ left: number; top: number } | null>(null);
  // 新しいカテゴリフラグ
  const [isNewCategory, setIsNewCategory] = useState<boolean>(false);
  // 削除成功モーダル用 state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  // 削除処理中フラグ
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  // 保存が必要かどうかを示すフラグ
  const [needsSave, setNeedsSave] = useState<boolean>(false);
  const [saveIndex, setSaveIndex] = useState<number | null>(null);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  // UserContextからユーザーIDを取得
  const { userId } = useUser();
  // Clerk認証フックを使用
  const { getToken } = useAuth();
  // 実際に使用するユーザーID（propsから渡されたものを優先）
  const currentUserId = userID || userId || getUserId();



  // モーダル外クリック検知用Ref
  const containerRef = useRef<HTMLDivElement | null>(null);

  // ドラッグ&ドロップ用のsensors（Chromeタブと同じ挙動）
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        delay: 200, // 200ms長押しでドラッグ開始（クリックとの明確な分離）
        tolerance: 8, // 8pxの許容範囲内での移動は無視
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // ドラッグ&ドロップ用の状態
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isDragInProgress, setIsDragInProgress] = useState(false);

  // ドラッグ開始ハンドラー
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
    setIsDragInProgress(true);
    // カテゴリドラッグ開始ログを削除
  };

  // ドラッグ終了ハンドラー
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);
    setIsDragInProgress(false);

    if (!over || active.id === over.id) {
      // カテゴリドラッグキャンセルログを削除
      return;
    }

    const oldIndex = localCategories.findIndex(cat => cat.id === active.id);
    const newIndex = localCategories.findIndex(cat => cat.id === over.id);

    if (oldIndex !== -1 && newIndex !== -1) {
      // 更新中フラグを設定してuseEffectによる上書きを防ぐ
      setIsUpdating(true);

      // 現在選択中のカテゴリIDを保存
      const currentSelectedCategoryId = localCategories[selectedIndex]?.id;

      // 配列の順序を変更
      const newCategories = arrayMove(localCategories, oldIndex, newIndex);
      const newNames = arrayMove(editedNames, oldIndex, newIndex);

      // ローカル状態を更新
      setLocalCategories(newCategories);
      setEditedNames(newNames);

      // 親コンポーネントに通知（orderedCategoriesを即座に更新）
      if (onCategoriesReorder) {
        onCategoriesReorder(newCategories);
      }

      // 選択中のカテゴリIDに基づいて新しいインデックスを計算
      const newSelectedIndex = newCategories.findIndex(cat => cat.id === currentSelectedCategoryId);
      if (newSelectedIndex !== -1) {
        setSelectedIndex(newSelectedIndex);

        // 親コンポーネントに選択中のカテゴリが変更されたことを通知
        const selectedCategory = newCategories[newSelectedIndex];
        if (selectedCategory) {
          // 選択中のカテゴリを再通知して、サブカテゴリとランキングを更新
          // 選択中のカテゴリが移動した場合のみ実行
          if (selectedIndex !== newSelectedIndex) {
            setTimeout(() => {
              onCategorySelect(selectedCategory.id, selectedCategory.category_name);
            }, 0);
          }
        }
      }

      // サーバーに順序を保存（非同期）
      saveCategoryOrder(newCategories)
        .then(() => {
          // 成功時に更新フラグを解除
          setTimeout(() => {
            setIsUpdating(false);
          }, 100); // 少し遅延させて確実に処理を完了させる
        })
        .catch((error) => {
          console.error('カテゴリ順序保存エラー:', error);
          // エラーが発生した場合は元の順序に戻す
          setLocalCategories(localCategories);
          setEditedNames(editedNames);

          // 選択状態も元に戻す
          const originalSelectedIndex = localCategories.findIndex(cat => cat.id === currentSelectedCategoryId);
          if (originalSelectedIndex !== -1) {
            setSelectedIndex(originalSelectedIndex);
          }

          // エラー時も更新フラグを解除
          setIsUpdating(false);
        });
    }
  };

  // カテゴリ順序をサーバーに保存する関数
  const saveCategoryOrder = async (categories: Category[]) => {
    try {
      const requestData = {
        user_ID: currentUserId,
        categories: categories.map((cat, index) => ({
          id: cat.id,
          order: index
        }))
      };

      const response = await fetch('/api/updateCategoryOrder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`カテゴリ順序の保存に失敗しました: ${response.status} ${response.statusText}`);
      }

      await response.json();

    } catch (error) {
      throw error;
    }
  };

  useEffect(() => {
    function handleClickOutside(e: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        // 編集中のカテゴリがある場合は、保存フラグを設定
        if (editingIndex !== null) {
          setNeedsSave(true);
          setSaveIndex(editingIndex);
        }
        
        setOptionsIndex(null);
        setConfirmDeleteIndex(null);
        setOptionsModalPosition(null);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editingIndex]);

  // 保存フラグが設定されたときに保存処理を実行（無限ループ修正）
  useEffect(() => {
    if (needsSave && saveIndex !== null && !isUpdating) {
      // 非同期で実行して無限ループを防ぐ
      const timeoutId = setTimeout(async () => {
        try {
          await handleInputBlur(saveIndex);
        } catch (error) {
          console.error('useEffect内でのカテゴリ名更新エラー:', error);
        } finally {
          setNeedsSave(false);
          setSaveIndex(null);
        }
      }, 0);

      return () => clearTimeout(timeoutId);
    }
  }, [needsSave, saveIndex, isUpdating]);

  // propsのselectedIndexが変更された場合、内部のselectedIndexを更新
  useEffect(() => {
    if (propSelectedIndex !== undefined) {
      setSelectedIndex(propSelectedIndex);
    }
  }, [propSelectedIndex]);

  // 初期化が完了したかどうかを追跡するフラグ
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // URLのクエリパラメータからカテゴリを取得して初期表示に反映（無限ループ修正済み）
  const initializationRef = useRef<{ categories: Category[], categoryFromUrl: string | null }>({ categories: [], categoryFromUrl: null });

  useEffect(() => {
    // 初期化済みの場合は処理をスキップ
    if (isInitialized) {
      return;
    }

    // カテゴリがない場合は処理をスキップ
    if (!categories || categories.length === 0) {
      return;
    }

    // 前回と同じ条件の場合はスキップ（無限ループ防止）
    if (initializationRef.current.categories === categories &&
        initializationRef.current.categoryFromUrl === categoryFromUrl) {
      return;
    }

    initializationRef.current = { categories, categoryFromUrl };

    if (categoryFromUrl) {
      // URLからカテゴリIDが指定されている場合は、そのカテゴリを選択
      const categoryIndex = categories.findIndex(c => c.id === categoryFromUrl);
      if (categoryIndex !== -1) {
        // URLからカテゴリを選択
        setSelectedIndex(categoryIndex);
        // onCategorySelectを安全に呼び出し（依存配列に含めない）
        const selectedCategory = categories[categoryIndex];
        setTimeout(() => {
          onCategorySelect(selectedCategory.id, selectedCategory.category_name);
        }, 0);
      }
    }

    // 初期化完了フラグを設定
    setIsInitialized(true);
  }, [categories, categoryFromUrl, isInitialized]);

  // カテゴリが更新されたときにローカル状態を更新
  const categoriesRef = useRef<Category[]>([]);

  useEffect(() => {
    if (categories && categories.length > 0 && !isUpdating) {
      // 深い比較でカテゴリが実際に変更されたかチェック
      const categoriesChanged =
        categoriesRef.current.length !== categories.length ||
        categoriesRef.current.some((cat, index) =>
          cat.id !== categories[index]?.id ||
          cat.category_name !== categories[index]?.category_name
        );

      if (categoriesChanged) {
        categoriesRef.current = categories;
        setLocalCategories(categories);
        setEditedNames(categories.map(c => c.category_name));
      }
    }
  }, [categories, isUpdating]);

  // タブクリックハンドラ
  const handleTabClick = (index: number, id: string, event?: React.MouseEvent) => {
    // クリックイベントが渡された場合、デフォルト動作とイベント伝播を防止
    if (event) {
      event.preventDefault();
      event.stopPropagation();
      // カテゴリタブクリック時のデフォルト動作を防止
    }

    // サイドメニューモーダルが開いている場合は、モーダルを閉じるだけでカテゴリ切り替えは行わない
    if (isModalOpen && onModalClose) {
      onModalClose();
      return;
    }

    // オプションモーダルが開いている場合は、まずモーダルを閉じる
    if (optionsIndex !== null) {
      setOptionsIndex(null);
      setOptionsModalPosition(null);
      // オプションモーダルが開いている場合は、モーダルを閉じるだけでカテゴリ切り替えは行わない
      return;
    }

    setSelectedIndex(index);
    // onCategorySelectを安全に呼び出し
    const categoryName = localCategories[index].category_name;
    setTimeout(() => {
      onCategorySelect(id, categoryName);
    }, 0);
  };

  // カテゴリ追加ハンドラ
  const handleAddCategory = async () => {
    if (isAdding) return;
    
    setIsAdding(true);
    
    try {
      // APIを呼び出して新しいカテゴリを作成
      const response = await fetch("/api/createCategory", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          category_name: "新しいカテゴリ",
          user_ID: currentUserId,
        }),
      });
      
      if (!response.ok) {
        throw new Error('カテゴリの作成に失敗しました');
      }
      
      const newCategory = await response.json();
      
      // ローカル状態を更新
      const updatedCategories = [...localCategories, newCategory];
      setLocalCategories(updatedCategories);
      
      const updatedNames = [...editedNames, newCategory.category_name];
      setEditedNames(updatedNames);
      
      // 新しいカテゴリを選択
      const newIndex = updatedCategories.length - 1;
      setSelectedIndex(newIndex);
      setTimeout(() => {
        onCategorySelect(newCategory.id, newCategory.category_name);
      }, 0);
      
      // 新しいカテゴリの名前を編集モードにする
      setEditingIndex(newIndex);
      setOriginalName(newCategory.category_name);
      setIsNewCategory(true);
      
      // 親コンポーネントに通知（追加アクションとカテゴリIDを渡す）
      if (onCategoryUpdated) {
        onCategoryUpdated('add', newCategory.id, newCategory.category_name);
      }
    } catch (error) {
      // カテゴリ作成エラーの処理
      console.error('カテゴリの作成中にエラーが発生しました:', error);
    } finally {
      setIsAdding(false);
    }
  };

  // オプションモーダルを閉じる関数
  const handleCloseOptionsModal = () => {
    setOptionsIndex(null);
    setOptionsModalPosition(null);
  };

  // オプションボタンクリックハンドラ
  const handleOptionsClick = (e: React.MouseEvent, index: number) => {
    e.stopPropagation();

    // クリックした位置を基準にモーダルの表示位置を計算
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const containerRect = containerRef.current?.getBoundingClientRect();

    if (!containerRect) return;

    // モーダルの幅を150pxと仮定して計算
    const modalWidth = 150;
    const modalHeight = 120; // 3つのボタンの高さを仮定

    // 白背景内に収まるように位置を調整
    let left = rect.left;
    let top = rect.bottom + window.scrollY;

    // 右端がコンテナからはみ出る場合は左に調整
    if (left + modalWidth > containerRect.right) {
      left = containerRect.right - modalWidth - 10; // 10pxのマージン
    }

    // 左端がコンテナからはみ出る場合は右に調整
    if (left < containerRect.left) {
      left = containerRect.left + 10; // 10pxのマージン
    }

    // 下端がビューポートからはみ出る場合は上に表示
    if (top + modalHeight > window.innerHeight + window.scrollY) {
      top = rect.top + window.scrollY - modalHeight - 5; // 5pxのマージン
    }

    const modalPosition = { left, top };

    // 既に同じインデックスのオプションが表示されている場合は閉じる
    if (optionsIndex === index) {
      handleCloseOptionsModal();
    } else {
      setOptionsIndex(index);
      setOptionsModalPosition(modalPosition);
    }
  };

  // 編集ボタンクリックハンドラ
  const handleEditClick = (index: number) => {
    setEditingIndex(index);
    setOriginalName(editedNames[index]);
    setOptionsIndex(null);
    setOptionsModalPosition(null);
  };

  // 削除ボタンクリックハンドラ
  const handleDeleteClick = (index: number) => {
    setConfirmDeleteIndex(index);
    setOptionsIndex(null);
    setOptionsModalPosition(null);
  };

  // 削除確認ハンドラ
  const handleConfirmDelete = async (index: number) => {
    setIsDeleting(true);
    try {
      const categoryToDelete = localCategories[index];

      // APIを呼び出してカテゴリを削除
      const response = await fetch(`/api/deleteCategory?id=${categoryToDelete.id}&user_ID=${currentUserId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('カテゴリの削除に失敗しました');
      }
      
      // ローカル状態を更新
      const updatedCategories = [...localCategories];
      updatedCategories.splice(index, 1);
      setLocalCategories(updatedCategories);
      
      const updatedNames = [...editedNames];
      updatedNames.splice(index, 1);
      setEditedNames(updatedNames);
      
      // 選択中のカテゴリが削除された場合、選択を最初のカテゴリに戻す
      if (selectedIndex === index) {
        if (updatedCategories.length > 0) {
          setSelectedIndex(0);
          setTimeout(() => {
            onCategorySelect(updatedCategories[0].id, updatedCategories[0].category_name);
          }, 0);
        } else {
          setSelectedIndex(-1);
        }
      } else if (selectedIndex > index) {
        // 削除されたカテゴリより後ろを選択していた場合、インデックスを1つ前にずらす
        setSelectedIndex(selectedIndex - 1);
      }
      
      // モーダルを閉じる
      setConfirmDeleteIndex(null);
      
      // 削除成功モーダルを表示
      setShowDeleteModal(true);
      setTimeout(() => {
        setShowDeleteModal(false);
      }, 2000);
      
      // 親コンポーネントに通知（削除アクションとカテゴリIDを渡す）
      if (onCategoryUpdated) {
        onCategoryUpdated('delete', categoryToDelete.id);
      }
    } catch (error) {
      // カテゴリ削除エラーの処理
      console.error('カテゴリの削除中にエラーが発生しました:', error);
    } finally {
      setIsDeleting(false);
      setConfirmDeleteIndex(null);
    }
  };

  // 入力変更ハンドラ
  const handleInputChange = (index: number, value: string) => {
    // 更新中の場合は入力を無視
    if (isUpdating) {
      return;
    }

    const newEditedNames = [...editedNames];
    newEditedNames[index] = value;
    setEditedNames(newEditedNames);
  };

  // 入力フィールドからフォーカスが外れたときのハンドラ
  const handleInputBlur = async (index: number) => {
    // 既に更新処理中の場合は実行しない
    if (isUpdating) {
      return;
    }

    // 新しいカテゴリの場合は、空の入力を許可しない
    if (isNewCategory && !editedNames[index].trim()) {
      alert("カテゴリ名を入力してください");
      setEditedNames((prev) => {
        const updated = [...prev];
        updated[index] = originalName;
        return updated;
      });
      setEditingIndex(null);
      setIsNewCategory(false);
      return;
    }

    // 編集モードを終了
    setEditingIndex(null);

    // 名前が変更されていない場合は何もしない
    if (editedNames[index] === originalName) {
      setIsNewCategory(false);
      return;
    }

    // 空の入力の場合は元の名前に戻す
    if (!editedNames[index].trim()) {
      const newEditedNames = [...editedNames];
      newEditedNames[index] = originalName;
      setEditedNames(newEditedNames);
      return;
    }

    // カテゴリが存在しない場合は処理を中断
    if (!localCategories[index]) {
      console.error('カテゴリが見つかりません:', index);
      return;
    }

    setIsUpdating(true);

    const categoryToUpdate = localCategories[index];
    const newCategoryName = editedNames[index];

    // 重複実行防止：同じカテゴリ名への更新は無視
    if (categoryToUpdate.category_name === newCategoryName) {
      setIsUpdating(false);
      return;
    }

    // 楽観的更新：即座にローカル状態を更新
    const updatedCategories = [...localCategories];
    updatedCategories[index] = {
      ...updatedCategories[index],
      category_name: newCategoryName,
    };
    setLocalCategories(updatedCategories);

    // 親コンポーネントに即座に通知（楽観的更新）
    if (onCategoryUpdated) {
      onCategoryUpdated('edit', categoryToUpdate.id, newCategoryName);
    }

    try {
      console.log('🔧 [ButtonMainCategory] カテゴリ更新開始:', {
        index,
        categoryToUpdate,
        editedName: newCategoryName,
        currentUserId,
        timestamp: new Date().toISOString()
      });

      const requestData = {
        id: categoryToUpdate.id,
        category_name: newCategoryName,
        user_ID: currentUserId,
      };

      console.log('🔧 [ButtonMainCategory] 送信データ:', JSON.stringify(requestData, null, 2));

      // APIを呼び出してカテゴリ名を更新
      const response = await fetch("/api/updateCategory", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestData),
      });

      console.log('🔧 [ButtonMainCategory] レスポンス受信:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        timestamp: new Date().toISOString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔧 [ButtonMainCategory] エラーレスポンス:', errorText);
        throw new Error(`カテゴリ名の更新に失敗しました: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log('🔧 [ButtonMainCategory] 成功レスポンス:', responseData);

      // デバッグ情報を表示
      if (responseData.debug) {
        console.log('🔧 [ButtonMainCategory] サーバーサイドデバッグ情報:', responseData.debug);
      }

      // API成功時：正確なcategory_slugで親コンポーネントに再通知
      if (responseData.success && responseData.category && onCategoryUpdated) {
        const updatedCategory = responseData.category;

        // ローカル状態も正確な情報で更新
        const finalUpdatedCategories = [...localCategories];
        finalUpdatedCategories[index] = {
          ...finalUpdatedCategories[index],
          category_name: updatedCategory.category_name,
        };
        setLocalCategories(finalUpdatedCategories);

        // 親コンポーネントに正確な情報で再通知（API成功後）
        onCategoryUpdated('edit_success', updatedCategory.id, updatedCategory.category_name);
      }

    } catch (error) {
      // カテゴリ名更新エラーの処理
      console.error('カテゴリ名の更新中にエラーが発生しました:', error);

      // エラー時は元の名前に戻す（楽観的更新をロールバック）
      const rollbackCategories = [...localCategories];
      rollbackCategories[index] = {
        ...rollbackCategories[index],
        category_name: originalName,
      };
      setLocalCategories(rollbackCategories);

      setEditedNames((prev) => {
        const updated = [...prev];
        updated[index] = originalName;
        return updated;
      });

      // 親コンポーネントにロールバックを通知
      if (onCategoryUpdated) {
        onCategoryUpdated('edit', categoryToUpdate.id, originalName);
      }

    } finally {
      setIsUpdating(false);
      setIsNewCategory(false);
    }
  };

  // カテゴリ共有（ネイティブ共有機能を使用）
  const handleShareCategory = async (index: number) => {
    if (typeof window === 'undefined') return;

    const title = "カテゴリを共有する";
    const text = `「${localCategories[index].category_name}」のランキングを見てください！`;

    // 現在のURLから適切な共有URLを生成（即座に実行）
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.split('?')[0]; // クエリパラメータを除去
    const url = `${baseUrl}?category=${localCategories[index].id}`;

    if (navigator.share) {
      // 即座に共有画面を開く
      navigator.share({
        title,
        text,
        url
      })
      .catch(error => {
        console.error('共有に失敗しました:', error);
      });
    } else {
      // ネイティブ共有が対応していない場合はURLをコピー
      handleCopyToClipboard(url);
    }

    // バックグラウンドでユーザー名ベースのURLに更新（共有後の処理として）
    try {
      const response = await fetch(`/api/getUser?user_ID=${currentUserId}`);
      if (response.ok) {
        const userData = await response.json();
        if (userData.username) {
          // ユーザー名ベースのURLが取得できた場合、次回のために記録
          // （現在の共有には影響しない）
        }
      }
    } catch (error) {
      // エラーは無視（共有機能には影響しない）
    }
  };

  // クリップボードにコピー
  const handleCopyToClipboard = (text: string) => {
    if (typeof window === 'undefined') return;
    
    navigator.clipboard.writeText(text)
      .then(() => {
        alert("URLをコピーしました");
      })
      .catch((err) => {
        // クリップボードへのコピーに失敗した場合の処理
        alert("URLのコピーに失敗しました");
      });
  };

  // キーボードイベントハンドラ（Enterキーで編集を確定）
  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleInputBlur(index);
    }
  };

  // カテゴリが存在しない場合は何も表示しない
  if (!localCategories || localCategories.length === 0) {
    return null;
  }

  return (
    <div
      className="relative"
      ref={containerRef}
      onClick={(event: React.MouseEvent) => {
        const target = event.target as HTMLElement;
        const currentTarget = event.currentTarget as HTMLElement;

        // サイドメニューモーダルが開いている場合は、モーダルを閉じる
        if (isModalOpen && onModalClose) {
          // クリックされた要素が最外側のdivまたはその直接の子要素（カテゴリタブ以外）の場合
          if (target === currentTarget || target.closest('.category-tab') === null) {
            event.preventDefault();
            event.stopPropagation();
            onModalClose();
            return;
          }
        }

        // オプションモーダルが開いている場合は、モーダルを閉じる
        if (optionsIndex !== null) {
          // オプションモーダル自体をクリックした場合は閉じない
          if (!target.closest('.options-modal')) {
            event.preventDefault();
            event.stopPropagation();
            handleCloseOptionsModal();
            return;
          }
        }
      }}
    >
      <div className="w-full overflow-x-auto overflow-y-hidden bg-[#F6F7F8] rounded-t-md" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none', WebkitScrollbar: { display: 'none' } }}>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToHorizontalAxis, restrictToParentElement]}
        >
          <ul className="flex flex-row w-max overflow-y-hidden" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            {/* 追加ボタン - 一番左に配置 */}
            {showAddButton && (
              <li className="flex-none w-auto pt-4 pb-4 pl-[20px] cursor-pointer">
                <button
                  onClick={handleAddCategory}
                  className="text-black-1 text-[14px] focus:outline-none pr-[20px] border-r border-[#DDDDDD]"
                  disabled={isAdding}
                >
                  ＋
                </button>
              </li>
            )}

            <SortableContext
              items={localCategories.map(cat => cat.id)}
              strategy={horizontalListSortingStrategy}
            >
              {localCategories.map((category, index) => (
                <SortableCategoryTab
                  key={category.id}
                  category={category}
                  index={index}
                  selectedIndex={selectedIndex}
                  editingIndex={editingIndex}
                  editedName={editedNames[index]}
                  onCategoryClick={handleTabClick}
                  onOptionsClick={handleOptionsClick}
                  onInputChange={handleInputChange}
                  onInputBlur={handleInputBlur}
                  onKeyDown={handleKeyDown}
                  isModalOpen={isModalOpen}
                  onModalClose={onModalClose}
                  optionsIndex={optionsIndex}
                  onCloseOptionsModal={handleCloseOptionsModal}
                  isOwnPage={isOwnPage}
                  onDirectShare={handleShareCategory}
                  isDraggable={isOwnPage}
                />
              ))}
            </SortableContext>
          </ul>
        </DndContext>
      </div>
      
      {/* オプションモーダル（ログイン状態のみ表示） */}
      {isOwnPage && optionsIndex !== null && optionsModalPosition && (
        <div
          className="options-modal fixed bg-white shadow-md rounded-md z-10000"
          style={{
            left: optionsModalPosition.left,
            top: optionsModalPosition.top,
            minWidth: '150px'
          }}
        >
          <button
            onClick={() => handleEditClick(optionsIndex)}
            className="block w-full text-left text-[14px] px-4 py-2 hover:bg-gray-100"
          >
            カテゴリ名を変更
          </button>
          <button
            onClick={() => handleDeleteClick(optionsIndex)}
            className="block w-full text-left text-[14px] px-4 py-2 hover:bg-gray-100"
          >
            カテゴリを削除
          </button>
          <button
            onClick={() => {
              handleShareCategory(optionsIndex);
              setOptionsIndex(null);
            }}
            className="block w-full text-left text-[14px] px-4 py-2 hover:bg-gray-100"
          >
            共有
          </button>
        </div>
      )}
      
      {/* 削除確認モーダル */}
      {confirmDeleteIndex !== null && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0 bg-black opacity-50"></div>
          <div className="bg-white p-6 rounded-lg shadow-xl z-10 max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4">本当に削除しますか？</h3>
            <p className="mb-6 text-sm text-gray-600">
              このカテゴリを削除すると、関連するすべてのおすすめも削除されます。この操作は元に戻せません。
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setConfirmDeleteIndex(null)}
                disabled={isDeleting}
                className={`px-4 py-2 border border-gray-300 rounded-md text-sm ${
                  isDeleting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                }`}
              >
                キャンセル
              </button>
              <button
                onClick={() => handleConfirmDelete(confirmDeleteIndex)}
                disabled={isDeleting}
                className={`px-4 py-2 rounded-md text-sm flex items-center justify-center min-w-[80px] ${
                  isDeleting
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-[#1f2937] text-white hover:bg-[#374151]'
                }`}
              >
                {isDeleting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    削除中...
                  </div>
                ) : (
                  '削除する'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      

      
      {/* 削除成功モーダル */}
      {showDeleteModal && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="bg-[#FFFBE6] border border-[#FFE58F] rounded-md px-6 py-4 flex items-center shadow-md">
            <div className="bg-[#FADB14] rounded-full p-1 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-[#5F5F5F] font-medium">カテゴリを削除しました</span>
          </div>
        </div>
      )}
    </div>
  );
};

ButtonMainCategory.propTypes = {
  userID: PropTypes.string.isRequired,
  categories: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      category_name: PropTypes.string.isRequired,
    })
  ),
  selectedIndex: PropTypes.number,
  onCategorySelect: PropTypes.func.isRequired,
  onCategoriesReorder: PropTypes.func,
  onCategoryUpdated: PropTypes.func,
  showAddButton: PropTypes.bool,
  isEditMode: PropTypes.bool,
};
