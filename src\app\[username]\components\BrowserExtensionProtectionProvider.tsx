'use client';

import React, { useEffect } from 'react';
import { initializeBrowserExtensionProtection } from '../../../utils/browserExtensionProtection';

interface BrowserExtensionProtectionProviderProps {
  children: React.ReactNode;
}

export const BrowserExtensionProtectionProvider: React.FC<BrowserExtensionProtectionProviderProps> = ({ children }) => {
  useEffect(() => {
    // ブラウザ拡張機能による干渉を防ぐための初期化
    initializeBrowserExtensionProtection();
  }, []);

  return <>{children}</>;
};
