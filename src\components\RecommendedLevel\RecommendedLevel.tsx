/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
  divClassName: any;
}

export const RecommendedLevel = ({ className, divClassName }: Props): JSX.Element => {
  return (
    <div
      className={`inline-flex items-center gap-[4px] pl-[16px] pr-0 pt-[16px] pb-0 relative ${className}`}
    >
      <div
        className={`relative w-fit [font-family:'Roboto',Helvetica] font-normal text-black text-[12px] tracking-[0] leading-[normal] whitespace-nowrap ${divClassName}`}
      >
        おすすめ度
      </div>
      <div className="inline-flex items-center justify-center gap-[2px] relative flex-[0_0_auto]">
        <p className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-transparent text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
          <span className="text-[#ffc600]">★★★★</span>
          <span className="text-[#aaaaaa]">★</span>
        </p>
        <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-button-red text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
          4
        </div>
      </div>
      <img
        className="relative w-[18px] h-[18px] object-cover"
        alt="Image help"
        src="https://c.animaapp.com/ZUwMMAhl/img/<EMAIL>"
      />
    </div>
  );
};
