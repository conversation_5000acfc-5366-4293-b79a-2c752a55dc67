# OAuth Redirect Flow Validation

## Complete Flow Test

### 1. Initial State
- User is not authenticated
- Starting at `/sign-up` page
- Clear browser data (cookies, localStorage, sessionStorage)

### 2. OAuth Initiation
- Click Google OAuth button
- Verify redirect to Google authentication
- Complete Google authentication

### 3. Expected Redirect Sequence

#### Option A: Direct SSO Callback (Preferred)
```
/sign-up → Google Auth → /sso-callback?from=sign-up → /setup
```

#### Option B: Continue Hash (Fallback)
```
/sign-up → Google Auth → /sign-up#/continue → /setup
```

### 4. Redirect Validation Points

#### At `/sso-callback`
- **Expected Logs:**
  - `🔍 [SSO-Callback] ページ初期化`
  - `🔍 [SSO-Callback] Googleからのリダイレクト検出`
  - `✅ [AuthSuccess] 認証成功処理開始`
  - `🔄 [AuthSuccess] setupページにリダイレクト`

#### At `/sign-up#/continue`
- **Expected Logs:**
  - `🔍 [SignUp] OAuth認証完了ハッシュ検出`
  - `🔄 [SignUp] Clerk認証状態チェック`
  - `✅ [SignUp] OAuth認証完了検出`
  - `🔄 [SignUp] setupページにリダイレクト`

#### At `/setup`
- **Expected Behavior:**
  - Page loads successfully
  - User information is pre-populated
  - User can complete account setup

### 5. Timing Expectations
- **Google Auth:** 2-5 seconds
- **Callback Processing:** 1-3 seconds
- **Supabase Registration:** 1-2 seconds
- **Setup Redirect:** Immediate
- **Total Time:** < 10 seconds

### 6. Error Scenarios

#### Timeout Handling
- If no authentication detected after 15 seconds
- Should redirect back to `/sign-up?error=auth_timeout`

#### Authentication Failure
- If Google auth fails or is cancelled
- Should redirect to `/sign-up?error=auth_failed`

#### API Errors
- If Supabase registration fails
- Should still proceed to `/setup` (authentication succeeded)
- Log error for debugging

### 7. Success Criteria
- [ ] User successfully reaches `/setup` page
- [ ] User data is saved in database
- [ ] No infinite redirect loops
- [ ] Proper error handling for edge cases
- [ ] Consistent behavior across browser refreshes
- [ ] Works in incognito/private mode

### 8. Browser Compatibility
Test in:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### 9. Network Conditions
Test with:
- [ ] Fast connection
- [ ] Slow connection (throttled)
- [ ] Intermittent connection

### 10. Debugging Tools
- Browser Developer Tools (Console, Network, Application)
- Clerk Dashboard (User sessions)
- Supabase Dashboard (User table)
- Application logs
