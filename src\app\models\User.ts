// src/types/prisma.d.ts
import { Prisma } from "@prisma/client";

export type User = {
  id: string; // Prisma's ObjectId
  user_ID: string; // Unique user ID
  username: string; // Unique username
  account_type: AccounType; // Enum for account type
  email: string; // Email address
  line?: string | null; // Optional LINE account ID
  google?: string | null; // Optional Google account ID
  password?: string | null; // Optional hashed password
  name: string; // User's name
  profile_image?: string | null; // Optional profile image URL
  contact_url?: string | null; // Optional contact URL
  contact_phone?: number | null; // Optional contact phone number
  contact_email?: string | null; // Optional contact email
  created_at: Date; // Date of creation
  updated_at: Date; // Date of last update
  setup_completed: boolean; // Setup completion status

  // Relations
  Category: Category[]; // List of related categories
  SnsUser: SnsUser[]; // List of related SNS user records
  UserByRanking: Ranking[]; // List of related rankings where the user is involved
};

// Additional type definitions for related models
export type AccounType = "email" | "line" | "google"; // Replace with your enum values
export type Category = {
  id: string;
  user_ID: string;
  category_ID: string;
  category_name: string;
  category_slug?: string | null;
  parent_ID?: string | null;
  created_at: Date;
  updated_at: Date;
  order: number;
};

export type SnsMaster = {
  id: string;
  sns_ID: number;
  sns_name: string;
  created_at: Date;
  updated_at: Date;
};

export type SnsUser = {
  id: string;
  user_ID: string;
  sns_master_id: string;
  account_ID: string;
  display_order: number;
  created_at: Date;
  updated_at: Date;
  snsMaster: SnsMaster; // SNSマスターへの参照
};

export type Ranking = {
  id: string;
  ranking_ID: string;
  user_ID: string;
  thumbnail_image: string[];
  recommend_rate?: number | null;
  ranking_url?: string | null;
  ranking_title: string;
  ranking_description?: string | null;
  subCategory_ID?: string | null;
  order: number;
  created_at: Date;
  updated_at: Date;
};
