.social-embed-container {
  width: 90%;
  margin: 20px auto;
  padding: 0;
  display: block;
  text-align: center;
  clear: both;
  position: relative;
  z-index: 1;
  height: auto;
  max-height: none;
  overflow: visible;
}

.social-embed-content {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: visible;
  border-radius: 0;
  display: block;
  text-align: center;
  height: auto;
  max-height: none;
  border: none;
  box-shadow: none;
}

/* 全SNSプラットフォーム統一スタイル */
.social-embed-instagram,
.social-embed-twitter,
.social-embed-x,
.social-embed-tiktok,
.social-embed-youtube {
  width: 100%;
  margin: 0;
  padding: 0;
  display: block;
  text-align: center;
  height: auto;
  max-height: none;
  overflow: visible;
  clear: both;
}

/* 全SNS埋め込み要素の統一スタイル */
.social-embed-twitter iframe,
.social-embed-twitter blockquote,
.social-embed-x iframe,
.social-embed-x blockquote,
.social-embed-tiktok iframe,
.social-embed-tiktok blockquote,
.social-embed-youtube iframe {
  width: 100% !important;
  max-height: none !important;
  min-height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
  overflow: visible !important;
  display: block !important;
  text-align: center !important;
  box-sizing: border-box !important;
  contain: none !important;
  resize: none !important;
}

/* Instagramのスタイル - border と border-radius なし */
.social-embed-instagram iframe,
.social-embed-instagram blockquote {
  width: 100% !important;
  max-height: none !important;
  min-height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
  overflow: visible !important;
  display: block !important;
  text-align: center !important;
  box-sizing: border-box !important;
  contain: none !important;
  resize: none !important;
}

/* 全SNS埋め込み内の要素の高さ制限解除 */
.social-embed-instagram *,
.social-embed-twitter *,
.social-embed-x *,
.social-embed-tiktok *,
.social-embed-youtube * {
  max-height: none !important;
  min-height: auto !important;
  overflow: visible !important;
  contain: none !important;
  resize: none !important;
}

/* 最強のセレクタ - すべてのSNS関連要素の高さ制限を強制解除 */
*[class*="social-embed"] *,
*[class*="twitter"] *,
*[class*="instagram"] *,
*[class*="tiktok"] *,
*[data-platform] *,
*[data-instgrm-permalink] *,
*[data-tweet-id] *,
*[data-video-id] *,
iframe[src*="twitter.com"],
iframe[src*="instagram.com"],
iframe[src*="tiktok.com"],
iframe[src*="youtube.com"],
iframe[src*="youtu.be"],
iframe[id*="twitter"],
iframe[id*="instagram"],
iframe[id*="tiktok"],
blockquote[class*="twitter"],
blockquote[class*="instagram"],
blockquote[class*="tiktok"] {
  max-height: none !important;
  min-height: auto !important;
  overflow: visible !important;
  contain: none !important;
  resize: none !important;
  box-sizing: border-box !important;
}


