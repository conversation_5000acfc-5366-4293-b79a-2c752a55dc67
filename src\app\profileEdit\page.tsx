"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { getUserId } from "../../contexts/UserContext";
import LoadingAnimation from "../../components/LoadingAnimation/LoadingAnimation";

/**
 * 旧プロフィール編集ページ - 新URLにリダイレクト
 * 旧URL: /profileEdit
 * 新URL: /[username]/edit
 */
const ProfileEditRedirect = (): JSX.Element => {
  const router = useRouter();

  useEffect(() => {
    const redirectToNewUrl = async () => {
      try {
        // 現在のユーザーのユーザー名を取得
        const currentUserId = getUserId();

        if (!currentUserId) {
          // ログインしていない場合はホームページにリダイレクト
          router.push('/');
          return;
        }

        const response = await fetch(`/api/getUser?user_ID=${currentUserId}`);

        if (response.ok) {
          const userData = await response.json();
          const username = userData.username;

          if (username) {
            // 新しいURL形式でプロフィール編集ページに遷移
            router.push(`/${username}/edit`);
          } else {
            // ユーザー名が取得できない場合はホームページにリダイレクト
            router.push('/');
          }
        } else {
          // ユーザー情報が取得できない場合はホームページにリダイレクト
          router.push('/');
        }
      } catch (error) {
        console.error("ProfileEditRedirect - プロフィール編集ページリダイレクトエラー:", error);
        // エラーの場合はホームページにリダイレクト
        router.push('/');
      }
    };

    redirectToNewUrl();
  }, [router]);

  // リダイレクト中はローディング画面を表示
  return <LoadingAnimation />;
};

export default ProfileEditRedirect;