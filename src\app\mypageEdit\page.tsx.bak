      
      {/* プロフィール編集ボタン - 編集ページでは表示 */}
      <ButtonProfileEdit 
        className="!flex-[0_0_auto]" 
        useButtonProfileEdit={true} 
        userId={user_ID}
      />
      
      {/* プロフィール詳細情報（アコーディオンで表示/非表示） */}
            {slideIn && (
              <div className="w-full max-w-[500px] px-8 mt-14 text-white">
                {/* 上部の区切り線 */}
                <div className="w-full h-px bg-white mb-4"></div>
                
                {/* 自己紹介 */}
                {user.selfIntroduction && (
                  <div className="mb-6">
                    <p className="text-sm whitespace-pre-wrap">{user.selfIntroduction}</p>
                  </div>
                )}
                
                {/* Webサイト */}
                {user.website && (
                  <div className="mb-3 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9" />
                    </svg>
                    <a 
                      href={user.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-[#1D9BF0]"
                    >
                      {user.website}
                    </a>
                  </div>
                )}
                
                {/* メールアドレス */}
                {user.email && (
                  <div className="mb-6 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <a 
                      href={`mailto:${user.email}`}
                      className="text-sm text-[#1D9BF0]"
                    >
                      {user.email}
                    </a>
                  </div>
                )}
                
                {/* SNSリンク */}
                {user.snsLinks && (
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragStart={handleSnsDragStart}
                    onDragEnd={handleSnsDragEnd}
                  >
                    <div className="mb-6">
                      <div className="grid grid-cols-4 gap-4">
                        <SortableContext
                          items={user.snsOrder || Object.keys(user.snsLinks)}
                          strategy={rectSortingStrategy}
                        >
                          {(user.snsOrder || Object.keys(user.snsLinks)).map((type: string) => {
                            const url = user.snsLinks[type];
                            if (!url || typeof url !== 'string') return null;
                            
                            const iconSrc = getSnsIconSrc(type);
                            // SNS名を正しく表示するための変換
                            const displayName = getSnsDisplayName(type);
                            
                            return (
                              <SortableSnsIcon
                                key={type}
                                id={type}
                                type={displayName}
                                url={url}
                                iconSrc={iconSrc}
                              />
                            );
                          })}
                        </SortableContext>
                      </div>
                    </div>
                  </DndContext>
                )}
                
                {/* 下部の区切り線 */}
                <div className="w-full h-px bg-white"></div>
                
                {/* モーダルを閉じるボタン - 区切り線の下に直接配置 */}
                <div className="flex justify-center mt-[24px] mb-4" style={{ marginTop: '24px' }}>
                  <button 
                    onClick={() => {
                      setSlideIn(false);
                    }}
                    className="w-6 h-6 rounded-full bg-white flex items-center justify-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            {/* 白いモーダル + フッターをまとめたラッパ */}
            <div
              className={`mt-[36px] w-full bg-white rounded-t-[10px] ${containerClass}`}
              style={{ 
                height: slideIn ? '0' : 'auto',
                flex: slideIn ? '0' : '1',
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden'
              }}
            >
              {!slideIn && (
                <>
                  {/* カテゴリ */}
                  <div className="w-full bg-white rounded-[10px_10px_0_0]">
                    <ButtonMainCategory
                      categories={(categories || []).filter(cat => {
                        // カテゴリ名が空または0の場合は除外
                        if (!cat.category_name || cat.category_name === "0") {
                          return false;
                        }
                        
                        // 除外リスト
                        const excludedNames = ["文字2", "1125", "ttttttt"];
                        if (excludedNames.includes(cat.category_name)) {
                          return false;
                        }
                        
                        // 新しく追加されたカテゴリ（category_IDが"new_category_"で始まる）は
                        // ローカルのカテゴリリストにも追加する
                        if (cat.category_ID.startsWith("new_category_")) {
                          return true;
                        }
                        
                        // デフォルトカテゴリは常に表示
                        if (cat.category_ID.startsWith("default_category_")) {
                          return true;
                        }
                        
                        // このカテゴリに関連するランキングデータがあるかチェック
                        const hasRankings = rankings && rankings.some(
                          ranking => ranking.category_id === cat.category_ID
                        );
                        
                        return hasRankings;
                      })}
                      onCategorySelect={handleCategorySelect}
                      onCategoriesReorder={handleCategoriesReorder}
                      isEditMode={true}
                    />
                  </div>
                  
      
                  {/* サブカテゴリ */}
                  {categoryID && !isEditMode && subCategories && subCategories.length > 0 && (
                    <div className="flex w-full flex-[0_0_auto] bg-white rounded-[10px_10px_0_0] overflow-visible">
                      {/* ここで ButtonSubCategory を使って表示 */}
                      <ButtonSubCategory
                        subcategories={subCategories.filter(subcat => {
                          // ランキング表示部分と同じ除外リストを使用
                          const excludedNames = ["文字2", "1125", "ttttttt"];
                          
                          // 空のカテゴリ名を持つサブカテゴリを除外
                          if (!subcat.category_name || excludedNames.includes(subcat.category_name)) {
                            return false;
                          }
                          
                          // このサブカテゴリに関連するランキングデータがあるかチェック
                          const hasRankings = rankingsBySubcategory && 
                                             rankingsBySubcategory[subcat.category_ID] && 
                                             rankingsBySubcategory[subcat.category_ID].length > 0;
                          
                          return hasRankings;
                        })}
                        onSubCategoySelect={handleSubCategorySelect}
                        onResetCategoryState={resetCategoryState}
                        selectedSubCategoryID={subCategory}
                      />
                    </div>
                  )}
      
                  {/* ランキング一覧 */}
                  <div
                    ref={rankingContainerRef}
                    className={`flex flex-col items-center ${isEditMode ? "" : "gap-4"} ${
                      isEditMode ? "pt-[16px]" : "p-[16px]"
                    } w-full flex-1 overflow-y-auto`}
                  >
                    {categoryID ? (
                      <div>
                        {/* サブカテゴリごとにグループ化して表示 */}
                        {subcategoryOrder.map(subcatId => {
                          const subcat = subCategories.find(sc => sc.category_ID === subcatId);
                          if (!subcat) return null;
                          
                          // 特定のカテゴリ名を持つサブカテゴリを除外
                          const excludedNames = ["文字2", "1125", "ttttttt"];
                          if (excludedNames.includes(subcat.category_name)) return null;
                          
                          const subcatRankings = rankingsBySubcategory[subcatId] || [];
                          const isExpanded = expandedSubcategories[subcatId];
                          
                          return (
                            <div key={`subcat-container-${subcatId}`} className="w-full mb-4" id={`subcat-section-${subcatId}`}>
                              {/* 編集モード時は折りたたみ可能なヘッダーを表示 */}
                              {isEditMode ? (
                                <SubcategoryHeader 
                                  subcatId={subcatId}
                                  subcatName={subcat.category_name}
                                  isExpanded={expandedSubcategories[subcatId]}
                                  onToggle={() => toggleSubcategory(subcatId)}
                                  isActive={activeId === `subcat_${subcatId}`}
                                />
                              ) : null}
                              
                              {/* サブカテゴリ内のランキングアイテム */}
                              {(!isEditMode || (isEditMode && expandedSubcategories[subcatId])) && (
                                <div className="space-y-4 mt-2">
                                  {subcatRankings.map((ranking, idx) => (
                                    <div
                                      key={`${ranking.ranking_ID}_${idx}_${subcatId}`}
                                      className="flex flex-col p-4 bg-white rounded-[10px] shadow-md cursor-pointer"
                                      onClick={() => {
                                        if (!isEditMode) {
                                          pageTransit(user_ID, ranking.ranking_ID);
                                        }
                                      }}
                                    >
                                      {ranking.thumbnail_image && (
                                        <img
                                          src={ranking.thumbnail_image}
                                          alt={ranking.ranking_title}
                                          className="w-full h-40 object-cover rounded-t-[10px]"
                                        />
                                      )}
                                      <h2 className="text-lg font-bold mt-2">{ranking.ranking_title}</h2>
                                      {ranking.ranking_description && (
                                        <p className="text-sm text-gray-500">{ranking.ranking_description}</p>
                                      )}
                                      <div className="flex items-center mt-2">
                                        {renderStars(ranking.recommend_rate)}
                                      </div>
                                    </div>
                                  ))}
                                  
                                  {subcatRankings.length === 0 && (
                                    <button 
                                      className="border border-black flex justify-between items-center w-full text-black px-4 py-5 rounded-[10px] font-medium bg-[#FFD814] hover:bg-[#E1BC03] transition-colors duration-200"
                                      onClick={() => router.push(`/pages/rankingRegister/${user_ID}`)}
                                    >
                                      <span>{subcat.category_name}にランキングを追加する</span>
                                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" className="w-5 h-5 ml-2">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                                      </svg>
                                    </button>
                                  )}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="w-full text-center py-8">
                        {/* 「カテゴリを選択してください」テキストを削除 */}
                      </div>
                    )}
                  </div>
      
                  {/* フッター */}
                  <Footer
                    className="!flex-[0_0_auto] mt-auto w-full"
                    logoImageMyrank={rankings[0]?.logo_image_myrank}
                  />
                  
                  {/* ランキング追加ボタン - 公開ページでは非表示 */}
                  <ButtonFooter user_ID={user_ID} showAddButton={false} />
                </>
              )}
            </div>
          </div>
        );
      }
      
      interface SubcategoryHeaderProps {
        subcatId: string;
        subcatName: string;
        isExpanded: boolean;
        onToggle: () => void;
        isActive: boolean;
      }
      
      const SubcategoryHeader = ({ subcatId, subcatName, isExpanded, onToggle, isActive }: SubcategoryHeaderProps) => {
        const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ 
          id: `subcat_${subcatId}`,
        });
      
        const style = {
          // X軸の移動を無効化して、Y軸のみの移動を許可
          transform: transform ? `translate3d(0px, ${transform.y}px, 0)` : undefined,
          transition: transition || 'transform 200ms cubic-bezier(0.25, 1, 0.5, 1)', // トランジションを最適化
          zIndex: isActive ? 10 : 1,
          opacity: isActive ? 0.8 : 1,
          touchAction: 'none',
          position: 'relative' as const,
          // ドラッグ中に要素の背景色を変更して視覚的フィードバックを強化
          background: isActive ? 'rgba(240, 240, 240, 0.8)' : 'transparent',
          borderRadius: '8px',
          // ドラッグ中の要素に影を追加
          boxShadow: isActive ? '0 5px 15px rgba(0, 0, 0, 0.2)' : 'none',
          width: '100%', // 幅を100%に固定
        };
      
        return (
          <div 
            ref={setNodeRef}
            style={style}
            className={`w-full ${isActive ? 'shadow-lg' : ''}`}
            data-id={`subcat_${subcatId}`}
            {...attributes}
            {...listeners}
          >
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onToggle();
              }}
              className="w-full flex justify-between items-center p-3 bg-gray-100"
            >
              <span className="font-bold">{subcatName}</span>
              <span className="p-1">
                {isExpanded ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                )}
              </span>
            </button>
          </div>
        );
      };
      
      // サイドメニューコンポーネント
      const SideMenu = ({ isModalOpen, handleCloseModal }: { isModalOpen: boolean, handleCloseModal: () => void }) => {
        // ログイン状態に応じてメニュー項目を変更
        const isLoggedIn = !!user; // userオブジェクトの有無でログイン状態を判定
{{ ... }}