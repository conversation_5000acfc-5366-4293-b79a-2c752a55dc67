// デバッグログ関数（ログ出力は削除）
export const debugLog = (message: string, ...args: any[]) => {
  // デバッグログは削除されました
};

// 埋め込み要素の状態追跡（ログ出力は削除）
export const trackEmbedState = (action: string, embedId?: string, details?: any) => {
  // 埋め込み状態追跡ログは削除されました
};

// 各プラットフォームのスクリプトを読み込む関数
export const loadEmbedScripts = (loadedScripts: { instagram: boolean; twitter: boolean; tiktok: boolean }) => {
  // 🔧 修正: Instagramのスクリプト読み込み（安全な実装）
  const hasInstagramEmbed = document.querySelector('[data-platform="instagram"]') ||
                           document.querySelector('.instagram-media') ||
                           document.querySelector('blockquote[class*="instagram"]');

  if (hasInstagramEmbed) {
    // 新しい安全なInstagram埋め込み処理を使用
    import('../../../utils/instagramEmbedUtils').then(({ processInstagramEmbeds }) => {
      processInstagramEmbeds();
      loadedScripts.instagram = true;
    }).catch(error => {
      if (process.env.NODE_ENV === 'development') {
        console.error('[RichTextEditor] Instagram埋め込み処理エラー:', error);
      }
    });
  }
  
  // 🔧 修正: Twitterのスクリプト読み込み（Twitter埋め込みが実際に存在する場合のみ）
  const hasTwitterEmbed = document.querySelector('[data-platform="twitter"]') ||
                         document.querySelector('[data-platform="x"]') ||
                         document.querySelector('.twitter-tweet') ||
                         document.querySelector('blockquote[class*="twitter"]');

  if (hasTwitterEmbed &&
      !loadedScripts.twitter &&
      !document.querySelector('script[src*="platform.twitter.com/widgets.js"]')) {
    if (process.env.NODE_ENV === 'development') {
      console.log('[RichTextEditor] Twitterスクリプトを読み込みます');
    }
    const twitterScript = document.createElement('script');
    twitterScript.src = 'https://platform.twitter.com/widgets.js';
    twitterScript.async = true;
    twitterScript.id = 'twitter-embed-script';
    twitterScript.onload = () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[RichTextEditor] Twitterスクリプトの読み込みが完了しました');
      }
      loadedScripts.twitter = true;
      setTimeout(() => {
        if (window.twttr && typeof window.twttr.widgets?.load === 'function') {
          if (process.env.NODE_ENV === 'development') {
            console.log('[RichTextEditor] Xウィジェット読み込み実行');
          }
          // 🔧 修正: 新規追加前に既存要素を保護してから処理
          const allXEmbeds = document.querySelectorAll('[data-platform="x"], [data-platform="twitter"]');
          const unprocessedEmbeds = document.querySelectorAll('[data-platform="x"]:not([data-embed-stable]), [data-platform="twitter"]:not([data-embed-stable])');

          // 🔧 既存の安定化されていない埋め込みを事前保護（高さも保護）
          allXEmbeds.forEach((embed) => {
            if (embed instanceof HTMLElement) {
              const iframe = embed.querySelector('iframe[id^="twitter-widget"]');
              const embedId = embed.getAttribute('data-embed-id');
              if (iframe && embedId) {
                // 🔧 現在の高さを保護
                const currentHeight = embed.offsetHeight;
                if (currentHeight > 200) {
                  embed.style.height = `${currentHeight}px`;
                  embed.setAttribute('data-height-locked', 'true');
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`[RichTextEditor] グローバル処理前に既存埋め込み ${embedId} の高さを保護: ${currentHeight}px`);
                  }
                }
                // 安定化マークも設定
                if (!embed.hasAttribute('data-embed-stable')) {
                  embed.setAttribute('data-embed-stable', 'true');
                  if (process.env.NODE_ENV === 'development') {
                    console.log('[RichTextEditor] グローバル処理前に既存埋め込みを保護しました');
                  }
                }
              }
            }
          });

          if (unprocessedEmbeds.length > 0) {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[RichTextEditor] 未処理の埋め込み要素を処理: ${unprocessedEmbeds.length}個`);
            }
            unprocessedEmbeds.forEach((embed) => {
              if (embed instanceof HTMLElement) {
                try {
                  window.twttr.widgets.load(embed);
                } catch (error) {
                  if (process.env.NODE_ENV === 'development') {
                    console.error('[RichTextEditor] Twitter widgets.load エラー:', error);
                  }
                }
              }
            });
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.log('[RichTextEditor] 処理対象の未処理埋め込み要素がありません');
            }
          }
        }
      }, 300);
    };
    document.body.appendChild(twitterScript);
  } else if (loadedScripts.twitter && window.twttr && typeof window.twttr.widgets?.load === 'function' && hasTwitterEmbed) {
    // 🔧 修正: 引数なしのwidgets.load()は全要素再生成の原因となるため完全削除
    // 個別要素指定の処理のみを使用（useSocialEmbed.tsで実装済み）
    if (process.env.NODE_ENV === 'development') {
      console.log('[RichTextEditor] Twitter widgets.jsは既に読み込み済みです（個別要素処理を使用）');
    }
  }

  // 🔧 修正: TikTokのスクリプト読み込み（TikTok埋め込みが実際に存在する場合のみ）
  const hasTikTokEmbed = document.querySelector('[data-platform="tiktok"]') ||
                        document.querySelector('.tiktok-embed') ||
                        document.querySelector('blockquote[class*="tiktok"]');

  if (hasTikTokEmbed &&
      !loadedScripts.tiktok &&
      !document.querySelector('script[src*="tiktok.com/embed.js"]')) {
    if (process.env.NODE_ENV === 'development') {
      console.log('[RichTextEditor] TikTokスクリプトを読み込みます');
    }
    const tiktokScript = document.createElement('script');
    tiktokScript.src = 'https://www.tiktok.com/embed.js';
    tiktokScript.async = true;
    tiktokScript.id = 'tiktok-embed-script';
    tiktokScript.onload = () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[RichTextEditor] TikTokスクリプトの読み込みが完了しました');
      }
      loadedScripts.tiktok = true;
      if (window.TikTokEmbed && typeof window.TikTokEmbed.load === 'function') {
        window.TikTokEmbed.load();
      }
    };
    document.body.appendChild(tiktokScript);
  } else if (loadedScripts.tiktok && window.TikTokEmbed && typeof window.TikTokEmbed.load === 'function' && hasTikTokEmbed) {
    window.TikTokEmbed.load();
  }
};

// エラーメッセージを表示する関数
export const showEmbedError = (element: Element, message: string) => {
  // 既存のエラーメッセージがあれば削除
  const existingError = element.querySelector('.social-embed-error');
  if (existingError) {
    existingError.remove();
  }
  
  // エラーメッセージを表示
  const errorBox = document.createElement('div');
  errorBox.className = 'social-embed-error';
  errorBox.textContent = message;
  errorBox.style.cssText = 'color: #d00; font-size: 0.875rem; padding: 8px; background-color: #fff0f0; border-radius: 4px; margin: 8px 0;';
  element.appendChild(errorBox);
};

// 既存のリンクにtarget="_blank"とrel="noopener noreferrer"を追加し、クリックイベントを設定する関数
export const processExistingLinks = (editorRef: React.RefObject<HTMLDivElement>) => {
  if (!editorRef.current) return;

  const links = editorRef.current.querySelectorAll('a');
  links.forEach(link => {
    if (!link.getAttribute('target')) {
      link.setAttribute('target', '_blank');
    }
    if (!link.getAttribute('rel')) {
      link.setAttribute('rel', 'noopener noreferrer');
    }

    // クリックイベントリスナーを追加（重複を避けるため、既存のリスナーをチェック）
    if (!link.hasAttribute('data-click-handler-added')) {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const url = link.getAttribute('href');
        if (url) {
          window.open(url, '_blank', 'noopener,noreferrer');
        }
      });
      link.setAttribute('data-click-handler-added', 'true');
    }
  });
};