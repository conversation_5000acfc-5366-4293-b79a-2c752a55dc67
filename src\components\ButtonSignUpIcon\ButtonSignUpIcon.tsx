/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import PropTypes from "prop-types";
import React from "react";

interface Props {
  className: any;
  divClassName: any;
  text: string;
  divClassNameOverride: any;
  text1: string;
  hasMailEntry: boolean;
}

export const ButtonSignUpIcon = ({
  className,
  divClassName,
  text = "LINEで登録する",
  divClassNameOverride,
  text1 = "Googleで登録する",
  hasMailEntry = true,
}: Props): JSX.Element => {
  return (
    <div className={`inline-flex flex-col items-center gap-[8px] relative ${className}`}>
      <div className="relative w-[260px] h-[40px] bg-white border border-solid border-line-color">
        <div className="inline-flex flex-col h-[40px] items-start gap-[10px] px-[16px] py-[10px] absolute top-0 left-0">
          <img
            className="relative w-[20.97px] h-[19.95px] object-cover"
            alt="Line icon pic"
            src="/static/img/lineiconpic.png"
          />
        </div>
        <div
          className={`absolute h-[18px] top-[10px] left-[87px] [font-family:'Roboto',Helvetica] font-normal text-black text-[12px] text-center tracking-[0] leading-[18px] whitespace-nowrap ${divClassName}`}
        >
          {text}
        </div>
      </div>
      <div className="relative w-[260px] h-[40px] bg-white border border-solid border-line-color">
        <div className="inline-flex flex-col h-[40px] items-start gap-[10px] px-[16px] py-[10px] absolute top-0 left-0">
          <img className="relative w-[20px] h-[20px] object-cover" alt="Google icon pic" src="/static/img/googleiconpic.png" />
        </div>
        <div
          className={`absolute h-[18px] top-[10px] left-[81px] [font-family:'Roboto',Helvetica] font-normal text-black text-[12px] text-center tracking-[0] leading-[18px] whitespace-nowrap ${divClassNameOverride}`}
        >
          {text1}
        </div>
      </div>
      {hasMailEntry && (
        <div className="relative w-[260px] h-[40px] bg-white border border-solid border-line-color">
          <div className="inline-flex flex-col h-[40px] items-start gap-[10px] px-[16px] py-[10px] absolute top-0 left-0">
            <img
              className="relative w-[20.97px] h-[20px] object-cover"
              alt="Mail icon pic"
              src="/static/img/mailiconpic.png"
            />
          </div>
          <div className="absolute h-[18px] top-[10px] left-[89px] [font-family:'Roboto',Helvetica] font-normal text-black text-[12px] text-center tracking-[0] leading-[18px] whitespace-nowrap">
            Mailで登録する
          </div>
        </div>
      )}
    </div>
  );
};

ButtonSignUpIcon.propTypes = {
  text: PropTypes.string,
  text1: PropTypes.string,
  hasMailEntry: PropTypes.bool,
};
