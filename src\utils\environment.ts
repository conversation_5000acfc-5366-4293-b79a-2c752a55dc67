/**
 * 環境判定用のユーティリティ関数
 */

/**
 * 現在の実行環境がローカル開発環境かどうかを判定する
 * @returns ローカル開発環境の場合はtrue、それ以外はfalse
 */
export function isLocalDevelopment(): boolean {
  // Next.jsの環境変数を使用して判定
  return process.env.NODE_ENV === 'development';
}

/**
 * 現在の実行環境が本番環境かどうかを判定する
 * @returns 本番環境の場合はtrue、それ以外はfalse
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * 現在の実行環境がテスト環境かどうかを判定する
 * @returns テスト環境の場合はtrue、それ以外はfalse
 */
export function isTest(): boolean {
  return process.env.NODE_ENV === 'test';
}
