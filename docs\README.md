# mypicks.best プロジェクト概要

## プロジェクトについて

mypicks.best(マイピックスベスト)は、自分がおすすめしたい商品やサービスをキュレーションして・共有できるWebアプリケーションです。ユーザーは様々なカテゴリやサブカテゴリでおすすめを作成し、他のユーザーと共有することができます。

## 主な機能

- ユーザー認証（Clerk認証システム使用）
- ランキングの作成・編集・削除
- カテゴリ・サブカテゴリ管理
- ランキングの保存・下書き機能
- ユーザープロフィール管理
- SNSリンク管理
- ランキング共有機能

## 技術スタック

- **フロントエンド**: Next.js, React, TypeScript, Tailwind CSS
- **バックエンド**: Next.js API Routes
- **データベース**: PostgreSQL (Supabase)
- **ORM**: Prisma
- **認証**: Clerk
- **デプロイ**: Docker

## ドキュメント構成

- [アーキテクチャ](./architecture/README.md) - システム全体の構造と設計
- [API](./api/README.md) - APIエンドポイントの仕様と使用方法
- [コンポーネント](./components/README.md) - 再利用可能なUIコンポーネント
- [機能](./features/README.md) - 主要機能の詳細説明
- [データベース](./database/README.md) - データベース設計とスキーマ
- [開発](./development/README.md) - 開発環境のセットアップと貢献ガイド

## 環境変数

プロジェクトには以下の環境変数が必要です：

```
DATABASE_URL=postgresql://...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=...
CLERK_SECRET_KEY=...
```

詳細な環境変数の設定については[開発ガイド](./development/setup.md)を参照してください。

## ライセンス

このプロジェクトは独自のライセンスの下で提供されています。詳細はプロジェクト管理者にお問い合わせください。

## 最終更新日

2025年4月25日
