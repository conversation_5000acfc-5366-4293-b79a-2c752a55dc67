import { NextRequest, NextResponse } from 'next/server';

// 🔧 修正: APIレベルでの重複リクエスト防止
const activeRequests = new Map<string, Promise<any>>();

/**
 * SNS投稿の埋め込みHTMLを取得するAPIルート
 * Instagram, TikTok, X(Twitter), YouTubeの埋め込みに対応
 */
export async function POST(request: NextRequest) {
  try {
    // リクエストボディを取得
    const body = await request.json();
    const { postUrl, platform } = body;

    if (!postUrl) {
      return NextResponse.json({ error: '投稿URLが必要です' }, { status: 400 });
    }

    if (!platform) {
      return NextResponse.json({ error: 'プラットフォーム情報が必要です' }, { status: 400 });
    }

    // 🔧 修正: 重複リクエストキーを生成
    const requestKey = `${platform.toLowerCase()}-${postUrl}`;

    // 🔧 修正: 既に同じリクエストが処理中の場合は待機
    if (activeRequests.has(requestKey)) {
      console.log(`${platform}埋め込み処理: 重複リクエストを検出、既存の処理を待機中:`, postUrl);
      const cachedResult = await activeRequests.get(requestKey)!;
      // 🔧 修正: キャッシュされた結果から新しいレスポンスを作成
      return NextResponse.json(cachedResult.data, { status: cachedResult.status });
    }

    console.log(`${platform}埋め込み処理開始:`, postUrl);

    // 🔧 修正: リクエスト処理をPromiseでラップして重複防止
    const requestPromise = (async (): Promise<{data: any, status: number}> => {
      try {
        // プラットフォームに応じた処理
        let response: NextResponse;
        switch (platform.toLowerCase()) {
          case 'instagram':
            response = await getInstagramEmbed(postUrl);
            break;
          case 'tiktok':
            response = await getTikTokEmbed(postUrl);
            break;
          case 'twitter':
          case 'x':
            response = await getTwitterEmbed(postUrl);
            break;
          case 'youtube':
            response = await getYoutubeEmbed(postUrl);
            break;
          default:
            response = NextResponse.json({ error: 'サポートされていないプラットフォームです' }, { status: 400 });
        }

        // 🔧 修正: レスポンスデータを抽出してキャッシュ用に保存
        const responseData = await response.json();
        return { data: responseData, status: response.status };
      } catch (error) {
        console.error('🚨 SNS埋め込み処理エラー:', error);
        console.error('🚨 エラー詳細:');
        console.error('- URL:', postUrl);
        console.error('- Platform:', platform);
        console.error('- Error Type:', error instanceof Error ? error.constructor.name : typeof error);
        console.error('- Error Message:', error instanceof Error ? error.message : String(error));
        console.error('- Error Stack:', error instanceof Error ? error.stack : 'スタックトレースなし');

        const errorData = {
          error: '処理中にエラーが発生しました',
          details: error instanceof Error ? error.message : '不明なエラー',
          platform: platform,
          url: postUrl,
          timestamp: new Date().toISOString()
        };
        return { data: errorData, status: 500 };
      } finally {
        // 🔧 修正: 処理完了後にキャッシュから削除
        activeRequests.delete(requestKey);
      }
    })();

    // 🔧 修正: アクティブリクエストマップに追加
    activeRequests.set(requestKey, requestPromise);

    const result = await requestPromise;
    return NextResponse.json(result.data, { status: result.status });
  } catch (error) {
    console.error('🚨 SNS埋め込み処理エラー (POST - 外側):', error);
    console.error('🚨 エラー詳細 (POST - 外側):');
    console.error('- Error Type:', error instanceof Error ? error.constructor.name : typeof error);
    console.error('- Error Message:', error instanceof Error ? error.message : String(error));
    console.error('- Error Stack:', error instanceof Error ? error.stack : 'スタックトレースなし');

    return NextResponse.json({
      error: '処理中にエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Instagram投稿の埋め込みHTMLを取得（簡易版）
 */
async function getInstagramEmbed(postUrl: string) {
  try {
    console.log('Instagram埋め込み処理開始:', postUrl);

    // URLからpost IDを抽出
    const postIdMatch = postUrl.match(/\/(?:p|reel)\/([a-zA-Z0-9_-]+)/);
    if (!postIdMatch) {
      return NextResponse.json({
        error: '無効なInstagram URLです'
      }, { status: 400 });
    }

    // 簡易的な埋め込みHTMLを生成（Instagram公式の埋め込み形式）
    const embedHtml = `<blockquote class="instagram-media" data-instgrm-permalink="${postUrl}" data-instgrm-version="14" style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px);"><div style="padding:16px;"><a href="${postUrl}" style="background:#FFFFFF; line-height:0; padding:0 0; text-align:center; text-decoration:none; width:100%;" target="_blank"><div style="display: flex; flex-direction: row; align-items: center;"><div style="background-color: #F4F4F4; border-radius: 50%; flex-grow: 0; height: 40px; margin-right: 14px; width: 40px;"></div><div style="display: flex; flex-direction: column; flex-grow: 1; justify-content: center;"><div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; margin-bottom: 6px; width: 100px;"></div><div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; width: 60px;"></div></div></div><div style="padding: 19% 0;"></div><div style="display:block; height:50px; margin:0 auto 12px; width:50px;"><svg width="50px" height="50px" viewBox="0 0 60 60" version="1.1" xmlns="https://www.w3.org/2000/svg"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-511.000000, -20.000000)" fill="#000000"><g><path d="M556.869,30.41 C554.814,30.41 553.148,32.076 553.148,34.131 C553.148,36.186 554.814,37.852 556.869,37.852 C558.924,37.852 560.59,36.186 560.59,34.131 C560.59,32.076 558.924,30.41 556.869,30.41 M541,60.657 C535.114,60.657 530.342,55.887 530.342,50 C530.342,44.114 535.114,39.342 541,39.342 C546.887,39.342 551.658,44.114 551.658,50 C551.658,55.887 546.887,60.657 541,60.657 M541,33.886 C532.1,33.886 524.886,41.1 524.886,50 C524.886,58.899 532.1,66.113 541,66.113 C549.9,66.113 557.115,58.899 557.115,50 C557.115,41.1 549.9,33.886 541,33.886 M565.378,62.101 C565.244,65.022 564.756,66.606 564.346,67.663 C563.803,69.06 563.154,70.057 562.106,71.106 C561.058,72.155 560.06,72.803 558.662,73.347 C557.607,73.757 556.021,74.244 553.102,74.378 C549.944,74.521 548.997,74.552 541,74.552 C533.003,74.552 532.056,74.521 528.898,74.378 C525.979,74.244 524.393,73.757 523.338,73.347 C521.94,72.803 520.942,72.155 519.894,71.106 C518.846,70.057 518.197,69.06 517.654,67.663 C517.244,66.606 516.755,65.022 516.623,62.101 C516.479,58.943 516.448,57.996 516.448,50 C516.448,42.003 516.479,41.056 516.623,37.899 C516.755,34.978 517.244,33.391 517.654,32.338 C518.197,30.938 518.846,29.942 519.894,28.894 C520.942,27.846 521.94,27.196 523.338,26.654 C524.393,26.244 525.979,25.756 528.898,25.623 C532.057,25.479 533.004,25.448 541,25.448 C548.997,25.448 549.943,25.479 553.102,25.623 C556.021,25.756 557.607,26.244 558.662,26.654 C560.06,27.196 561.058,27.846 562.106,28.894 C563.154,29.942 563.803,30.938 564.346,32.338 C564.756,33.391 565.244,34.978 565.378,37.899 C565.522,41.056 565.552,42.003 565.552,50 C565.552,57.996 565.522,58.943 565.378,62.101 M570.82,37.631 C570.674,34.438 570.167,32.258 569.425,30.349 C568.659,28.377 567.633,26.702 565.965,25.035 C564.297,23.368 562.623,22.342 560.652,21.575 C558.743,20.834 556.562,20.326 553.369,20.18 C550.169,20.033 549.148,20 541,20 C532.853,20 531.831,20.033 528.631,20.18 C525.438,20.326 523.257,20.834 521.349,21.575 C519.376,22.342 517.703,23.368 516.035,25.035 C514.368,26.702 513.342,28.377 512.574,30.349 C511.834,32.258 511.326,34.438 511.181,37.631 C511.035,40.831 511,41.851 511,50 C511,58.147 511.035,59.17 511.181,62.369 C511.326,65.562 511.834,67.743 512.574,69.651 C513.342,71.625 514.368,73.296 516.035,74.965 C517.703,76.634 519.376,77.658 521.349,78.425 C523.257,79.167 525.438,79.673 528.631,79.82 C531.831,79.965 532.853,80.001 541,80.001 C549.148,80.001 550.169,79.965 553.369,79.82 C556.562,79.673 558.743,79.167 560.652,78.425 C562.623,77.658 564.297,76.634 565.965,74.965 C567.633,73.296 568.659,71.625 569.425,69.651 C570.167,67.743 570.674,65.562 570.82,62.369 C570.966,59.17 571,58.147 571,50 C571,41.851 570.966,40.831 570.82,37.631"></path></g></g></g></svg></div><div style="padding-top: 8px;"><div style="color:#3897f0; font-family:Arial,sans-serif; font-size:14px; font-style:normal; font-weight:550; line-height:18px;">Instagramで見る</div></div></a></div></blockquote>`;

    console.log('Instagram埋め込みHTML生成成功');

    return NextResponse.json({ html: embedHtml, platform: 'instagram' });
  } catch (error) {
    console.error('Instagram埋め込み処理エラー:', error);
    return NextResponse.json({
      error: 'Instagram埋め込み処理中にエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  }
}

/**
 * TikTok投稿の埋め込みHTMLを取得
 */
async function getTikTokEmbed(postUrl: string) {
  try {
    console.log('TikTok埋め込み処理開始:', postUrl);
    
    // 短縮URLを展開する処理
    let finalUrl = postUrl;
    
    // 短縮URL（vt.tiktok.comなど）かどうかを確認
    if (postUrl.includes('vt.tiktok.com') || !postUrl.includes('/video/')) {
      console.log('TikTok短縮URLを検出しました。展開を試みます...');
      
      try {
        // リダイレクトをたどって最終的なURLを取得
        const response = await fetch(postUrl, {
          method: 'HEAD',
          redirect: 'follow',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
          },
        });
        
        // 最終的なURLを取得
        if (response.url) {
          finalUrl = response.url;
          console.log('TikTok短縮URLを展開しました:', finalUrl);
        }
      } catch (redirectError) {
        console.error('TikTok短縮URL展開エラー:', redirectError);
        // エラーが発生しても元のURLで続行
      }
    }
    
    // TikTokのoEmbed APIを呼び出し
    const oEmbedUrl = `https://www.tiktok.com/oembed?url=${encodeURIComponent(finalUrl)}&format=json`;
    console.log('TikTok oEmbed APIリクエスト:', oEmbedUrl);

    const res = await fetch(oEmbedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'Accept': 'application/json',
      },
    });

    if (!res.ok) {
      const err = await res.text();
      console.error('TikTok oEmbed APIエラー:', res.status, err);
      
      // APIエラーの場合、代替の埋め込みHTMLを生成
      if (finalUrl.includes('/video/')) {
        // 正規URLからビデオIDを抽出
        const match = finalUrl.match(/\/video\/(\d+)/);
        if (match && match[1]) {
          const videoId = match[1];
          console.log('TikTok ビデオID抽出成功:', videoId);
          
          // iframeを使用した埋め込みHTMLを生成
          const alternativeHtml = `<blockquote class="tiktok-embed" cite="${finalUrl}" data-video-id="${videoId}" style="max-width: 605px; min-width: 325px;">
            <section></section>
          </blockquote>`;
          
          return NextResponse.json({ html: alternativeHtml, platform: 'tiktok' });
        }
      }
      
      // 代替手段も失敗した場合はエラーを返す
      return NextResponse.json(
        { error: 'TikTok埋め込み取得失敗', details: err },
        { status: 500 },
      );
    }

    const data = await res.json();
    console.log('TikTok oEmbed APIレスポンス成功');

    // <script> タグを削除（Reactでは無視されるため）
    const html = (data.html as string).replace(/<script[^>]*><\/script>/gi, '').trim();

    return NextResponse.json({ html, platform: 'tiktok' });
  } catch (e) {
    console.error('🚨 TikTok埋め込み処理エラー:', e);
    console.error('🚨 TikTokエラー詳細:');
    console.error('- URL:', postUrl);
    console.error('- Error Type:', e instanceof Error ? e.constructor.name : typeof e);
    console.error('- Error Message:', e instanceof Error ? e.message : String(e));
    console.error('- Error Stack:', e instanceof Error ? e.stack : 'スタックトレースなし');

    return NextResponse.json(
      {
        error: 'TikTok埋め込み処理エラー',
        details: e instanceof Error ? e.message : String(e),
        url: postUrl,
        timestamp: new Date().toISOString()
      },
      { status: 500 },
    );
  }
}

/**
 * Twitter/X投稿の埋め込みHTMLを取得
 */
async function getTwitterEmbed(postUrl: string) {
  try {
    // 🔧 修正: x.com URLをtwitter.com URLに正規化（2024年のウィジェット仕様変更対応）
    const normalizedUrl = postUrl.replace(/^https?:\/\/x\.com\//, 'https://twitter.com/');
    console.log('Twitter URL正規化:', postUrl, '->', normalizedUrl);

    // Twitter oEmbed APIエンドポイント
    const oEmbedUrl = `https://publish.twitter.com/oembed?url=${encodeURIComponent(normalizedUrl)}&omit_script=true`;

    console.log('Twitter oEmbed APIリクエスト:', oEmbedUrl);

    const response = await fetch(oEmbedUrl);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Twitter oEmbed APIエラー:', response.status, errorText);
      return NextResponse.json({ 
        error: 'Twitter埋め込みの取得に失敗しました', 
        details: errorText 
      }, { status: 500 });
    }

    const data = await response.json();
    
    if (!data.html) {
      console.error('Twitter oEmbed APIレスポンスにHTMLが含まれていません:', data);
      return NextResponse.json({ 
        error: '埋め込みHTMLが取得できませんでした' 
      }, { status: 500 });
    }

    console.log('Twitter埋め込みHTML取得成功');

    // 🔧 修正: 生成されたHTMLのURLもtwitter.comに正規化
    const normalizedHtml = data.html.replace(/https?:\/\/x\.com\//g, 'https://twitter.com/');

    return NextResponse.json({ html: normalizedHtml, platform: 'twitter' });
  } catch (error) {
    console.error('Twitter埋め込み処理エラー:', error);
    return NextResponse.json({ 
      error: 'Twitter埋め込み処理中にエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  }
}

/**
 * YouTube動画の埋め込みHTMLを取得
 */
async function getYoutubeEmbed(postUrl: string) {
  try {
    // YouTube oEmbed APIエンドポイント
    const oEmbedUrl = `https://www.youtube.com/oembed?url=${encodeURIComponent(postUrl)}&format=json`;
    
    console.log('YouTube oEmbed APIリクエスト:', oEmbedUrl);

    const response = await fetch(oEmbedUrl);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('YouTube oEmbed APIエラー:', response.status, errorText);
      return NextResponse.json({ 
        error: 'YouTube埋め込みの取得に失敗しました', 
        details: errorText 
      }, { status: 500 });
    }

    const data = await response.json();
    
    if (!data.html) {
      console.error('YouTube oEmbed APIレスポンスにHTMLが含まれていません:', data);
      return NextResponse.json({ 
        error: '埋め込みHTMLが取得できませんでした' 
      }, { status: 500 });
    }

    console.log('YouTube埋め込みHTML取得成功');
    
    return NextResponse.json({ html: data.html, platform: 'youtube' });
  } catch (error) {
    console.error('YouTube埋め込み処理エラー:', error);
    return NextResponse.json({ 
      error: 'YouTube埋め込み処理中にエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  }
}

/**
 * GETメソッドでSNS埋め込みHTMLを取得する関数
 * クエリパラメータからplatformとurlを取得して処理する
 */
export async function GET(request: NextRequest) {
  try {
    // URLからクエリパラメータを取得
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');
    const url = searchParams.get('url');

    if (!url) {
      return NextResponse.json({ error: '投稿URLが必要です' }, { status: 400 });
    }

    if (!platform) {
      return NextResponse.json({ error: 'プラットフォーム情報が必要です' }, { status: 400 });
    }

    // 🔧 修正: 重複リクエストキーを生成
    const requestKey = `${platform.toLowerCase()}-${url}`;

    // 🔧 修正: 既に同じリクエストが処理中の場合は待機
    if (activeRequests.has(requestKey)) {
      console.log(`GET: ${platform}埋め込み処理: 重複リクエストを検出、既存の処理を待機中:`, url);
      const cachedResult = await activeRequests.get(requestKey)!;
      // 🔧 修正: キャッシュされた結果から新しいレスポンスを作成
      return NextResponse.json(cachedResult.data, { status: cachedResult.status });
    }

    console.log(`GET: ${platform}埋め込み処理開始:`, url);

    // 🔧 修正: リクエスト処理をPromiseでラップして重複防止
    const requestPromise = (async (): Promise<{data: any, status: number}> => {
      try {
        // プラットフォームに応じた処理
        let response: NextResponse;
        switch (platform.toLowerCase()) {
          case 'instagram':
            response = await getInstagramEmbed(url);
            break;
          case 'tiktok':
            response = await getTikTokEmbed(url);
            break;
          case 'twitter':
          case 'x':
            response = await getTwitterEmbed(url);
            break;
          case 'youtube':
            response = await getYoutubeEmbed(url);
            break;
          default:
            response = NextResponse.json({ error: 'サポートされていないプラットフォームです' }, { status: 400 });
        }

        // 🔧 修正: レスポンスデータを抽出してキャッシュ用に保存
        const responseData = await response.json();
        return { data: responseData, status: response.status };
      } catch (error) {
        console.error('🚨 SNS埋め込み処理エラー (GET):', error);
        console.error('🚨 エラー詳細 (GET):');
        console.error('- URL:', url);
        console.error('- Platform:', platform);
        console.error('- Error Type:', error instanceof Error ? error.constructor.name : typeof error);
        console.error('- Error Message:', error instanceof Error ? error.message : String(error));
        console.error('- Error Stack:', error instanceof Error ? error.stack : 'スタックトレースなし');

        const errorData = {
          error: '処理中にエラーが発生しました',
          details: error instanceof Error ? error.message : '不明なエラー',
          platform: platform,
          url: url,
          timestamp: new Date().toISOString()
        };
        return { data: errorData, status: 500 };
      } finally {
        // 🔧 修正: 処理完了後にキャッシュから削除
        activeRequests.delete(requestKey);
      }
    })();

    // 🔧 修正: アクティブリクエストマップに追加
    activeRequests.set(requestKey, requestPromise);

    const result = await requestPromise;
    return NextResponse.json(result.data, { status: result.status });
  } catch (error) {
    console.error('🚨 SNS埋め込み処理エラー (GET - 外側):', error);
    console.error('🚨 エラー詳細 (GET - 外側):');
    console.error('- Error Type:', error instanceof Error ? error.constructor.name : typeof error);
    console.error('- Error Message:', error instanceof Error ? error.message : String(error));
    console.error('- Error Stack:', error instanceof Error ? error.stack : 'スタックトレースなし');

    return NextResponse.json({
      error: '処理中にエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
