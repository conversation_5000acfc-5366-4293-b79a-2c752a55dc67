# ランキング保存機能

## 概要

ランキング保存機能は、ユーザーが他のユーザーが作成したランキングをブックマークとして保存し、後で参照できるようにする機能です。保存したランキングは専用のページで一覧表示され、管理することができます。

## 機能一覧

- ランキングの保存（ブックマーク）
- 保存したランキングの一覧表示
- 保存したランキングの削除
- 保存したランキングの詳細表示

## ランキング保存機能

### 機能概要

ユーザーはランキング詳細ページでブックマークアイコンをクリックすることで、そのランキングを保存できます。既に保存されているランキングの場合は、再度クリックすることで保存を解除できます。

### 実装の詳細

ランキング保存機能は以下のように実装されています：

1. MypageReleaseButtonFooterコンポーネントにブックマークアイコンを追加
2. ブックマークアイコンをクリックすると、ランキングデータがローカルストレージに保存される
3. 既に保存されているランキングの場合は、再度クリックすると削除される
4. ブックマークアイコンの表示状態が保存状態に応じて変化する

### 実装上のポイント

- 保存したランキングデータはローカルストレージに保存されます
- 将来的にはサーバーサイドでの保存に変更することも検討されています
- ブックマークアイコンの状態は、ローカルストレージ内のデータに基づいて動的に変化します

### コード例

```javascript
// ランキングの保存
const saveRanking = (ranking) => {
  const savedRankings = JSON.parse(localStorage.getItem('savedRankings') || '[]');
  const isAlreadySaved = savedRankings.some(item => item.ranking_ID === ranking.ranking_ID);
  
  if (isAlreadySaved) {
    // 既に保存されている場合は削除
    const updatedRankings = savedRankings.filter(item => item.ranking_ID !== ranking.ranking_ID);
    localStorage.setItem('savedRankings', JSON.stringify(updatedRankings));
    return false; // 保存解除
  } else {
    // 保存されていない場合は追加
    savedRankings.push(ranking);
    localStorage.setItem('savedRankings', JSON.stringify(savedRankings));
    return true; // 保存完了
  }
};
```

## 保存したランキング一覧表示機能

### 機能概要

ユーザーは保存したランキングを一覧で表示できます。各ランキングにはタイトル、サムネイル画像、星評価、説明文が表示されます。

### 実装の詳細

保存したランキング一覧表示機能は以下のように実装されています：

1. SavedRankingsPageコンポーネントで保存したランキングを一覧表示
2. ローカルストレージから保存したランキングデータを取得
3. 各ランキングの情報（タイトル、サムネイル画像、星評価、説明文）を表示
4. 保存したランキングがない場合は「保存したランキングがありません」と表示

### 三点リーダーメニューの追加

保存したランキングページ（SavedRankingsPage）のUIが改善され、以下の機能が追加されました：

1. 各ランキングアイテムの右上（タイトルの右側）に縦並びの三点リーダーを追加
2. 三点リーダーをクリックするとドロップダウンメニューが表示される
3. メニューには「削除する」ボタンがあり、クリックすると確認なしでランキングを削除できる
4. 画面の他の場所をクリックするとメニューが自動的に閉じる

三点リーダーの位置調整も行われ、タイトルの高さに合わせて表示するよう位置が調整されています。絶対位置指定を使用してタイトルの右上に正確に配置されています。

### ローディング表示の改善

保存したランキングページのローディング表示も改善されました：

1. 既存のLoadingAnimationコンポーネントを使用してローディング表示を実装
2. ページ読み込み時に星マークとmypicks.bestテキストを表示するアニメーションを表示
3. データ取得後に1秒間のタイムアウトを設定して、ローディングアニメーションが表示される時間を確保
4. ローディング中は画面全体をLoadingAnimationコンポーネントで置き換える条件付きレンダリングを実装

## 保存したランキングの編集・削除機能

### 機能概要

ユーザーは保存したランキングを編集モードで管理し、不要なランキングを削除できます。

### 実装の詳細

編集・削除機能は以下のように実装されています：

1. 右下の「編集」ボタンをクリックすると編集モードに切り替わる
2. 編集モード中は各ランキングアイテムに削除ボタンが表示される
3. 削除ボタンをクリックすると確認モーダルが表示され、削除を実行できる
4. 編集モード中は「編集」ボタンが「完了」に変わる

### コード例

```javascript
// 保存したランキングの削除
const deleteRanking = (rankingId) => {
  const savedRankings = JSON.parse(localStorage.getItem('savedRankings') || '[]');
  const updatedRankings = savedRankings.filter(item => item.ranking_ID !== rankingId);
  localStorage.setItem('savedRankings', JSON.stringify(updatedRankings));
  setSavedRankings(updatedRankings);
};
```

## 保存したランキングの詳細表示

### 機能概要

ユーザーは保存したランキングをクリックすることで、そのランキングの詳細を表示できます。

### 実装の詳細

詳細表示機能は以下のように実装されています：

1. 保存したランキングをクリックすると詳細ページに遷移する
2. 詳細ページではローカルストレージから保存したデータを使用して表示
3. APIからのデータ取得に失敗した場合のフォールバック処理も実装

## 関連コンポーネント

- [MypageReleaseButtonFooter](../components/buttons.md#mypagereleasebuttonfooter)
- [LoadingAnimation](../components/others.md#loadinganimation)

## 最終更新日

2025年4月25日
