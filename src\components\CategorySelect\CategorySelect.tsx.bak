import React, { useState, useEffect } from "react";
import { useUser } from "@/contexts/UserContext";

interface CategorySelectProps {
  className?: string;
  category?: string;
  subCategory: string;
  // 親カテゴリIDを受け取るためのprop
  parentCategoryId?: string;
  // 親にカテゴリを反映するためのハンドラ
  changeCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
  changeSubCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface SubCategory {
  id: string;
  category_name: string;
  category_ID: string;
}

export const CategorySelect: React.FC<CategorySelectProps> = ({
  className,
  category = "",
  subCategory,
  parentCategoryId,
  changeCategory,
  changeSubCategory,
}) => {
  // UserContextからユーザーIDを取得
  const { userId } = useUser();
  // ▼ モーダルの開閉管理
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ▼ 既存カテゴリのリスト（最初に「未分類（今は設定しない）」を用意）
  const [categories, setCategories] = useState<string[]>([
    "未分類（今は設定しない）",
  ]);
  
  // サブカテゴリの一覧を保持する状態
  const [subCategories, setSubCategories] = useState<SubCategory[]>([]);

  // ▼ 新しく追加するカテゴリ名
  const [newCategory, setNewCategory] = useState("");

  // ▼ 編集対象のカテゴリ
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedCategory, setEditedCategory] = useState("");

  // ▼ 「追加」ボタンの活性化判定
  const isAddButtonActive = newCategory.trim() !== "";

  // 親カテゴリIDが変更されたときにサブカテゴリを取得
  useEffect(() => {
    const fetchSubCategories = async () => {
      if (!userId || !parentCategoryId) return;
      
      try {
        setIsLoading(true);
        const response = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`);
        
        if (!response.ok) {
          throw new Error('サブカテゴリの取得に失敗しました');
        }
        
        const data = await response.json();
        console.log('取得したサブカテゴリ:', data);
        
        // サブカテゴリをセット
        setSubCategories(data);
        
        // カテゴリ名のリストを更新（「未分類」は常に先頭に）
        const categoryNames = ["未分類（今は設定しない）", ...data.map((item: SubCategory) => item.category_name)];
        setCategories(categoryNames);
      } catch (err) {
        console.error('サブカテゴリ取得エラー:', err);
        setError(err instanceof Error ? err.message : '不明なエラー');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSubCategories();
  }, [userId, parentCategoryId]);

  // ▼ カテゴリを追加
  const handleAddCategory = async () => {
    if (newCategory.trim() === "") return;
    
    console.log("新しいサブカテゴリを追加:", newCategory);
    setIsLoading(true);
    setError(null);
    
    try {
      // データベースにサブカテゴリを保存
      if (userId && parentCategoryId) {
        const newSubcategoryId = `new_subcategory_${Date.now()}`;
        console.log(`サブカテゴリ保存リクエスト: ID=${newSubcategoryId}, 名前=${newCategory}, ユーザーID=${userId}, 親ID=${parentCategoryId}`);
        
        // updateCategoryエンドポイントを使用
        const response = await fetch('/api/updateCategory', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: newSubcategoryId, // 一時的なIDを生成（必須パラメータ）
            category_name: newCategory,
            user_ID: userId,
            parent_ID: parentCategoryId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'サブカテゴリの保存に失敗しました');
        }

        const result = await response.json();
        console.log('サブカテゴリの保存結果:', result);
        
        // サブカテゴリリストを再取得
        const fetchResponse = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`);
        if (fetchResponse.ok) {
          const data = await fetchResponse.json();
          setSubCategories(data);
          
          // カテゴリ名のリストを更新（「未分類」は常に先頭に）
          const categoryNames = ["未分類（今は設定しない）", ...data.map((item: SubCategory) => item.category_name)];
          setCategories(categoryNames);
        }
      }
      
      // 追加したカテゴリを選択状態にする
      const syntheticEvent = {
        target: { value: newCategory },
      } as unknown as React.ChangeEvent<HTMLInputElement>;
      
      changeCategory(syntheticEvent);
      changeSubCategory(syntheticEvent);
      
      setNewCategory("");
      setIsModalOpen(false);
    } catch (err: any) {
      console.error('サブカテゴリの保存エラー:', err);
      setError(err.message || 'サブカテゴリの保存中にエラーが発生しました');
    } finally {
      setIsLoading(false);
    }
  };

  // ▼ カテゴリを削除
  const handleDeleteCategory = (index: number) => {
    setDeleteTarget(categories[index]);
    setIsDeleteModalOpen(true);
  };

  // ▼ 削除を確定
  const confirmDeleteCategory = () => {
    if (deleteTarget) {
      const newCategories = categories.filter((cat) => cat !== deleteTarget);
      setCategories(newCategories);
      setIsDeleteModalOpen(false);
      setDeleteTarget(null);
    }
  };

  // ▼ 編集モードに入る
  const handleEditCategory = (index: number) => {
    setEditingIndex(index);
    setEditedCategory(categories[index]);
  };

  // ▼ 編集を保存
  const handleSaveEdit = () => {
    if (editingIndex !== null && editedCategory.trim() !== "") {
      const newCategories = [...categories];
      newCategories[editingIndex] = editedCategory;
      setCategories(newCategories);
      setEditingIndex(null);
      setEditedCategory("");
    }
  };

  // ▼ 編集をキャンセル
  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditedCategory("");
  };

  return (
    <div className={`flex flex-col ${className}`}>
      {/* カテゴリ選択フィールド */}
      <div className="relative mb-4">
        <label className="block text-gray-700 text-sm font-bold mb-2">
          サブカテゴリ
        </label>
        <div className="flex items-center">
          <input
            type="text"
            value={subCategory}
            onChange={changeSubCategory}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="サブカテゴリを選択または入力"
            onClick={() => setIsModalOpen(true)}
            readOnly
          />
          <button
            type="button"
            onClick={() => setIsModalOpen(true)}
            className="ml-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded"
          >
            選択
          </button>
        </div>
      </div>

      {/* カテゴリ選択モーダル */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-full max-w-md max-h-[80vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">サブカテゴリを選択</h2>
            
            {/* エラーメッセージ */}
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}
            
            {/* 新規カテゴリ追加フォーム */}
            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                新しいサブカテゴリを追加
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  placeholder="新しいサブカテゴリ名"
                />
                <button
                  type="button"
                  onClick={handleAddCategory}
                  disabled={!isAddButtonActive || isLoading}
                  className={`ml-2 ${
                    isAddButtonActive && !isLoading
                      ? "bg-blue-500 hover:bg-blue-700"
                      : "bg-gray-300"
                  } text-white font-bold py-2 px-4 rounded`}
                >
                  {isLoading ? "追加中..." : "追加"}
                </button>
              </div>
            </div>

            {/* カテゴリリスト */}
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">既存のサブカテゴリ</h3>
              {isLoading && categories.length <= 1 ? (
                <div className="text-center py-4">
                  <p>読み込み中...</p>
                </div>
              ) : (
                <ul className="divide-y divide-gray-200">
                  {categories.map((cat, index) => (
                    <li key={index} className="py-2">
                      {editingIndex === index ? (
                        <div className="flex">
                          <input
                            type="text"
                            value={editedCategory}
                            onChange={(e) => setEditedCategory(e.target.value)}
                            className="shadow appearance-none border rounded w-full py-1 px-2 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                          />
                          <button
                            onClick={handleSaveEdit}
                            className="ml-2 bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-sm"
                          >
                            保存
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="ml-1 bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded text-sm"
                          >
                            キャンセル
                          </button>
                        </div>
                      ) : (
                        <div className="flex items-center justify-between">
                          <button
                            type="button"
                            onClick={() => {
                              const syntheticEvent = {
                                target: { value: cat },
                              } as unknown as React.ChangeEvent<HTMLInputElement>;
                              changeSubCategory(syntheticEvent);
                              setIsModalOpen(false);
                            }}
                            className="text-left w-full py-1 px-2 hover:bg-gray-100 rounded"
                          >
                            {cat}
                          </button>
                          {index !== 0 && ( // 「未分類」は編集・削除不可
                            <div className="flex">
                              <button
                                onClick={() => handleEditCategory(index)}
                                className="text-blue-500 hover:text-blue-700 mx-1"
                              >
                                編集
                              </button>
                              <button
                                onClick={() => handleDeleteCategory(index)}
                                className="text-red-500 hover:text-red-700 mx-1"
                              >
                                削除
                              </button>
                            </div>
                          )}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* 閉じるボタン */}
            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
              >
                閉じる
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 削除確認モーダル */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">削除の確認</h2>
            <p className="mb-6">
              「{deleteTarget}」を削除してもよろしいですか？
            </p>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
              >
                キャンセル
              </button>
              <button
                type="button"
                onClick={confirmDeleteCategory}
                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
              >
                削除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
