/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className?: any;
  logoImageMyrank?: any;
}

export const Footer = ({ className }: Props): JSX.Element => {
  return (
    <div
      className={`bg-white flex flex-col w-full items-center justify-center gap-[6px] pt-[60px] pb-[40px] px-0 relative mt-auto ${className}`}
    >
      <img
        className="relative w-[120px] h-[30px] object-cover"
        alt="Logo image mypicks.best"
        src="\static\img\mypicks.best-logo.png"
      />
      <div className="relative w-fit text-font-gray text-[12px] text-center tracking-[0] leading-[normal] underline whitespace-nowrap">
        Created by mypicks.best
      </div>
      <div className="relative w-fit text-font-gray text-[10px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
        ©︎mypicks.best All Rights Reserved
      </div>
    </div>
  );
};

