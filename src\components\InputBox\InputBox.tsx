/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";
interface InputBoxProps {
    className: string;
    value: string;
    onChange: any;
  }

export const InputBox: React.FC<InputBoxProps> = ({ className, value, onChange }) => {
  return (
    <div className={`inline-flex w-full flex-col items-start gap-1.5 px-4 py-0 relative ${className}`}>
      <div className="flex w-full h-12 items-center relative bg-[rgba(246,247,248,1)] rounded-sm overflow-hidden border border-solid border-[#DDDDDD]">
        <div className="flex h-12 items-center gap-2.5 pl-4 pr-0 py-0 relative flex-1 grow">
          <input
            type="text"
            className="flex h-12 items-center gap-2.5 pr-0 py-0 flex-1 bg-transparent border-none outline-none text-sm text-[#313131]"
            placeholder="タイトルを入力"
            maxLength={26}
            value={value} 
            onChange={onChange}
          />
        </div>
      </div>
      <div className="flex w-full items-center justify-end relative flex-[0_0_auto]">
        <div className="relative w-fit mt-[-1.00px] font-normal text-[#AAAAAA] text-xs text-right tracking-[0] leading-[normal] whitespace-nowrap">
          {value.length}/26
        </div>
      </div>
    </div>
  );
};
