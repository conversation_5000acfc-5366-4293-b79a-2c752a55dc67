import React from 'react';

interface SavedModalProps {
  show: boolean;
}

/**
 * 保存成功モーダルコンポーネント
 */
export const SavedModal: React.FC<SavedModalProps> = ({ show }) => {
  if (!show) return null;
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="bg-[rgba(0,0,0,0.5)] absolute inset-0"></div>
      <div className="bg-[#FFFCE1] rounded-lg p-4 flex items-center max-w-[90%] relative z-10 shadow-lg border border-yellow-400">
        <div className="bg-yellow-400 rounded-full p-2 mr-3 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <span className="text-[16px] font-medium">保存しました</span>
      </div>
    </div>
  );
};
