'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function ChangeEmailGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">メールアドレスを変更する方法</h1>
            <div className="pt-6 text-[14px] text-[#313131]">
              <p>1. マイページから「設定」アイコンをタップします。</p>
              <p>2. 「アカウント設定」を選択します。</p>
              <p>3. 「メールアドレス変更」をタップします。</p>
              <p>4. 現在のパスワードを入力します（セキュリティ確認のため）。</p>
              <p>5. 新しいメールアドレスを入力します。</p>
              <p>6. 「変更を申請」ボタンをタップします。</p>
              <p>7. 新しいメールアドレスに確認メールが送信されます。</p>
              <p>8. 確認メール内のリンクをタップして、メールアドレスの変更を完了します。</p>
            </div>
        </div>        
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
