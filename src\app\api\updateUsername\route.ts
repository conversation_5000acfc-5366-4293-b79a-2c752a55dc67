import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { getAuth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    // 認証チェック
    const { userId: clerkUserId } = getAuth(request);
    
    if (!clerkUserId) {
      return NextResponse.json(
        { error: '認証が必要です' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { newUsername } = body;

    if (!newUsername) {
      return NextResponse.json(
        { error: '新しいユーザー名が必要です' },
        { status: 400 }
      );
    }

    // ユーザー名の形式チェック
    const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/;
    if (!usernameRegex.test(newUsername)) {
      return NextResponse.json({
        error: 'ユーザー名は3-30文字の英数字、アンダースコア、ハイフンのみ使用可能です'
      }, { status: 400 });
    }

    // 予約語チェック
    const reservedUsernames = [
      'admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'root',
      'support', 'help', 'info', 'contact', 'about', 'terms',
      'privacy', 'login', 'signup', 'register', 'dashboard',
      'profile', 'settings', 'account', 'user', 'users',
      'mypageRelease', 'ranking', 'category', 'draft', 'public'
    ];

    if (reservedUsernames.includes(newUsername.toLowerCase())) {
      return NextResponse.json({
        error: 'このユーザー名は予約されています'
      }, { status: 400 });
    }

    // 現在のユーザー情報を取得
    const currentUser = await prisma.user.findUnique({
      where: { user_ID: clerkUserId },
      select: { id: true, username: true }
    });

    if (!currentUser) {
      return NextResponse.json(
        { error: 'ユーザーが見つかりません' },
        { status: 404 }
      );
    }

    // 新しいユーザー名が既に使用されているかチェック（自分以外）
    const existingUser = await prisma.user.findFirst({
      where: {
        username: newUsername,
        NOT: { user_ID: clerkUserId }
      }
    });

    if (existingUser) {
      return NextResponse.json({
        error: 'このユーザー名は既に使用されています'
      }, { status: 409 });
    }

    // トランザクションでユーザー名を更新
    const result = await prisma.$transaction(async (tx) => {
      // ユーザーテーブルを更新
      const updatedUser = await tx.user.update({
        where: { user_ID: clerkUserId },
        data: { username: newUsername },
        select: {
          id: true,
          user_ID: true,
          username: true,
          name: true,
          email: true
        }
      });

      // カテゴリテーブルとランキングテーブルの更新は不要（usernameフィールドを削除したため）

      return updatedUser;
    });

    return NextResponse.json({
      success: true,
      message: 'ユーザー名を更新しました',
      user: result
    });

  } catch (error) {
    console.error('ユーザー名更新エラー:', error);
    return NextResponse.json(
      { error: 'ユーザー名の更新中にエラーが発生しました' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
