const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSnsTestRanking() {
  console.log('Creating test ranking with SNS embed content...');
  
  try {
    // 1. 既存のユーザーを確認
    console.log('\n1. Checking existing users...');
    const users = await prisma.user.findMany({
      select: {
        user_ID: true,
        domain: true,
        name: true
      },
      take: 3
    });
    
    if (users.length === 0) {
      console.log('❌ No users found. Please create a user first.');
      return;
    }
    
    const testUser = users[0];
    console.log(`Using user: ${testUser.name} (ID: ${testUser.user_ID})`);

    // 2. 既存のカテゴリを確認
    console.log('\n2. Checking existing categories...');
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        category_ID: true,
        category_name: true,
        user_ID: true,
        parent_ID: true
      },
      take: 5
    });
    
    if (categories.length === 0) {
      console.log('❌ No categories found. Please create a category first.');
      return;
    }
    
    const testCategory = categories.find(cat => cat.user_ID === testUser.user_ID) || categories[0];
    console.log(`Using category: ${testCategory.category_name} (ID: ${testCategory.id})`);

    // 3. SNS埋め込みを含むHTML文字列を作成
    console.log('\n3. Creating test ranking with SNS embed content...');
    
    // SNS埋め込みを含むテストデータ
    const snsEmbedContent = `
<p>この商品について、実際に使ってみた感想をSNSでも投稿しました！</p>

<h3 style="color: red; text-decoration: underline;">Twitter/Xでの投稿</h3>
<div class="social-embed social-embed-twitter" data-platform="twitter" data-url="https://twitter.com/example/status/1234567890123456789" data-loaded="false">
  <blockquote class="twitter-tweet">
    <p lang="ja" dir="ltr">この商品、本当におすすめです！使ってみて感動しました✨ <a href="https://t.co/example">pic.twitter.com/example</a></p>
    &mdash; テストユーザー (@testuser) <a href="https://twitter.com/testuser/status/1234567890123456789">January 1, 2024</a>
  </blockquote>
</div>

<h3 style="color: red; text-decoration: underline;">Instagramでの投稿</h3>
<div class="social-embed social-embed-instagram" data-platform="instagram" data-url="https://www.instagram.com/p/ABC123DEF456/" data-loaded="false">
  <blockquote class="instagram-media" data-instgrm-captioned data-instgrm-permalink="https://www.instagram.com/p/ABC123DEF456/" data-instgrm-version="14">
    <div>
      <a href="https://www.instagram.com/p/ABC123DEF456/" target="_blank">
        <div>
          <div></div>
          <div>
            <div></div>
            <div>
              <div></div>
              <div>
                <div></div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
        <p>実際に使ってみた様子をInstagramにも投稿しました！ #おすすめ商品 #レビュー</p>
      </a>
    </div>
  </blockquote>
</div>

<h3 style="color: red; text-decoration: underline;">YouTubeでの紹介動画</h3>
<div class="social-embed social-embed-youtube" data-platform="youtube" data-url="https://www.youtube.com/watch?v=dQw4w9WgXcQ" data-loaded="false">
  <iframe width="560" height="315" src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
</div>

<p><strong>まとめ</strong>：SNSでも話題になっているこの商品、本当におすすめです！</p>
    `.trim();

    const testRankingData = {
      ranking_ID: `sns_test_rank_${Date.now()}`,
      user_ID: testUser.user_ID,
      domain: testUser.domain,
      ranking_title: 'SNS埋め込みテスト用ランキング',
      ranking_description: snsEmbedContent,
      amazon_url: 'https://amazon.co.jp/test-sns-product',
      rakuten_url: 'https://rakuten.co.jp/test-sns-product',
      yahoo_url: 'https://shopping.yahoo.co.jp/test-sns-product',
      qoo10_url: 'https://qoo10.jp/test-sns-product',
      official_url: 'https://example.com/test-sns-product',
      recommend_rate: 4,
      thumbnail_image: ['https://via.placeholder.com/300x300/4ECDC4/FFFFFF?text=SNS+Test'],
      subCategory_ID: testCategory.id,
      order: 2
    };

    console.log('Creating test ranking with SNS embed data:', {
      ...testRankingData,
      ranking_description: `[${testRankingData.ranking_description.length} characters of HTML with SNS embeds]`
    });
    
    const createdRanking = await prisma.ranking.create({
      data: testRankingData
    });
    
    console.log('✅ SNS embed test ranking created successfully!');
    console.log('Ranking ID:', createdRanking.ranking_ID);
    console.log('User ID:', createdRanking.user_ID);
    console.log('Access URL:', `http://localhost:3001/mypageReleaseDetail/${createdRanking.user_ID}/${createdRanking.ranking_ID}`);
    
    // 4. 作成されたランキングを確認
    console.log('\n4. Verifying created ranking...');
    const verifyRanking = await prisma.ranking.findFirst({
      where: {
        ranking_ID: createdRanking.ranking_ID
      },
      select: {
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        user_ID: true
      }
    });
    
    if (verifyRanking) {
      console.log('✅ Ranking verification successful');
      console.log('SNS embed content length:', verifyRanking.ranking_description.length);
      console.log('Contains Twitter embed:', verifyRanking.ranking_description.includes('twitter-tweet'));
      console.log('Contains Instagram embed:', verifyRanking.ranking_description.includes('instagram-media'));
      console.log('Contains YouTube embed:', verifyRanking.ranking_description.includes('youtube.com/embed'));
    } else {
      console.log('❌ Ranking verification failed');
    }

  } catch (error) {
    console.error('❌ Error during SNS test ranking creation:', error);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.meta) {
      console.error('Error meta:', error.meta);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
createSnsTestRanking();
