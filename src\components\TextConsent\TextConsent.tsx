/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
}

export const TextConsent = ({ className }: Props): JSX.Element => {
  return (
    <div className={`inline-flex items-center justify-center pt-[24px] pb-0 px-0 relative ${className}`}>
      <p className="relative w-[300px] mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-transparent text-[12px] text-center tracking-[0] leading-[normal]">
        <span className="text-[#313131]">上記の「登録する」ボタンを押すと</span>
        <span className="text-[#e63b5f] underline">個人情報</span>
        <span className="text-[#313131]">と</span>
        <span className="text-[#e63b5f] underline">利用規約</span>
        <span className="text-[#313131]">について同意の上、登録されたこととなります。</span>
      </p>
    </div>
  );
};
