import React from 'react';

// グローバル型定義の拡張
declare global {
  interface Window {
    instgrm?: {
      Embeds: {
        process: () => void;
      };
    };
    twttr?: {
      widgets: {
        load: (element?: HTMLElement) => void;
      };
      events: {
        bind: (event: string, callback: (e: any) => void) => void;
      };
    };
    TikTok?: {
      embed: {
        reload: () => void;
      };
    };
  }
}

// リッチテキストエディタのプロパティ
export interface RichTextEditorProps {
  value?: string | { target: { value: string } } | any; // 初期値として使用（オプショナル）
  initialHtml?: string; // 初期HTML（一度きり）
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  isFullScreen?: boolean;
  showToolbar?: boolean; // ツールバーの表示/非表示を制御するプロパティ
  onGetCurrentContent?: (getCurrentContentFn: () => string) => void; // 現在のDOM内容を取得する関数を受け取るコールバック
  onInsertEmbed?: (insertEmbedFn: (html: string) => void) => void; // 埋め込み挿入関数を受け取るコールバック
  instanceId?: string; // インスタンス識別用ID
}

// ツールバーのボタン定義
export interface ToolbarButton {
  command: string;
  icon: string | React.ReactElement;
  title: string;
  style?: React.CSSProperties;
}

// 色選択オプション
export interface ColorOption {
  color: string;
  name: string;
  border?: boolean;
}

// テキストアライメントの型
export type TextAlign = 'left' | 'center' | 'right';

// 埋め込みプラットフォームの型
export type EmbedPlatform = 'instagram' | 'tiktok' | 'twitter' | 'x' | 'youtube';