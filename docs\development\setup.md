# 開発環境のセットアップ

## 概要

このドキュメントでは、mypicks.bestアプリケーションの開発環境をセットアップする方法について説明します。開発を始める前に、必要な依存関係をインストールし、環境変数を設定する必要があります。

## 前提条件

開発を始める前に、以下のソフトウェアがインストールされていることを確認してください：

- Node.js（バージョン18以上）
- npm（バージョン9以上）
- PostgreSQL（バージョン14以上）または Supabase アカウント
- Git

## リポジトリのクローン

まず、GitHubリポジトリをクローンします：

```bash
git clone <リポジトリURL>
cd myrank
```

## 依存関係のインストール

プロジェクトの依存関係をインストールします：

```bash
npm install
```

## 環境変数の設定

`.env.local`ファイルをプロジェクトのルートディレクトリに作成し、以下の環境変数を設定します：

```
# データベース
DATABASE_URL=<データベースの接続URL>
DIRECT_URL=<直接接続用URL>

# Clerk認証
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=<Clerkの公開キー>
CLERK_SECRET_KEY=<Clerkのシークレットキー>

# API URL
NEXT_PUBLIC_API_URL=<APIのベースURL> # APIリクエストのベースURL

# ドメイン
DOMAIN=<アプリケーションのドメイン> # アプリケーションのドメイン名
```

## データベースのセットアップ

Prismaを使用してデータベースをセットアップします：

```bash
# Prismaクライアントの生成
npx prisma generate

# 開発環境でのデータベースマイグレーション
npx prisma migrate dev

# シードデータの投入
npx prisma db seed
```

## Prismaの使用

mypicks.bestアプリケーションでは、Prisma ORMを使用してデータベースアクセスを管理しています。Prismaに関連する主要な操作は以下の通りです。

### Prismaクライアントの生成

Prismaスキーマを変更した後、または初めてプロジェクトをセットアップする際には、Prismaクライアントを生成する必要があります：

```bash
npx prisma generate
```

このコマンドは、`prisma/schema.prisma`ファイルに基づいてTypeScriptの型定義を含むPrismaクライアントを生成します。

### データベースマイグレーション

データベーススキーマを変更する場合は、マイグレーションを作成して適用する必要があります：

```bash
# 新しいマイグレーションを作成
npx prisma migrate dev --name <変更の説明>

# 本番環境でマイグレーションを適用
npx prisma migrate deploy
```

### シードデータの投入

開発環境でテストデータを作成するには、シードスクリプトを実行します：

```bash
npx prisma db seed
```

シードスクリプトは`prisma/seed.ts`ファイルに定義されており、テストユーザー、カテゴリ、ランキング、SNSマスターデータなどを作成します。

### Prismaスタジオの使用

データベースの内容を視覚的に確認・編集するには、Prisma Studioを使用できます：

```bash
npx prisma studio
```

このコマンドを実行すると、ブラウザで`http://localhost:5555`が開き、データベースの内容を確認・編集できるインターフェースが表示されます。

### Prismaの開発フロー

1. `schema.prisma`ファイルでモデルを定義または変更する
2. マイグレーションを作成する: `npx prisma migrate dev --name <変更の説明>`
3. 必要に応じてシードデータを更新する
4. アプリケーションコードでPrismaクライアントを使用してデータベースにアクセスする

### 注意点

- マイグレーションを作成する前に、変更内容が正しいことを確認してください
- 本番環境でマイグレーションを適用する際は、データ損失のリスクを考慮してください
- 環境変数`DATABASE_URL`と`DIRECT_URL`が正しく設定されていることを確認してください

## データベース操作

### 基本的なCRUD操作

Prismaクライアントを使用した基本的なCRUD（Create, Read, Update, Delete）操作の例：

```typescript
// ユーザーの作成
const createUser = async (userData) => {
  return await prisma.user.create({
    data: userData
  });
};

// ユーザーの取得
const getUser = async (userId) => {
  if (!userId) {
    return null;
  }
  return await prisma.user.findUnique({
    where: { user_ID: userId }
  });
};

// ユーザーの更新
const updateUser = async (userId, userData) => {
  return await prisma.user.update({
    where: { user_ID: userId },
    data: userData
  });
};

// ユーザーの削除
const deleteUser = async (userId) => {
  return await prisma.user.delete({
    where: { user_ID: userId }
  });
};
```

### リレーションを含むクエリ

リレーションを含むデータを取得する例：

```typescript
// ユーザーとそのランキングを取得
const getUserWithRankings = async (userId) => {
  if (!userId) {
    return null;
  }
  return await prisma.user.findUnique({
    where: { user_ID: userId },
    include: { UserByRanking: true }
  });
};

// カテゴリとそのサブカテゴリを取得
const getCategoryWithSubcategories = async (categoryId) => {
  return await prisma.category.findUnique({
    where: { id: categoryId },
    include: { Children: true }
  });
};
```

### 実装上の注意点

Prismaスキーマとコードの不一致に関する問題が過去に発生しました。以下の点に注意してください：

1. **フィールド名の一致**: Prismaスキーマで定義されているフィールド名とコード内で使用するフィールド名が一致していることを確認してください。例えば、以下のような問題がありました：
   - Prismaスキーマでは`id`フィールドが主キーとして定義されていましたが、コード内で`category_ID`フィールドが使用されていたため、制約違反エラーが発生しました。
   - この問題を解決するために、`updateCategory`、`postRank`、`postCategory`などのAPIエンドポイントを修正し、Prismaスキーマと一致するフィールド名を使用するようにしました。

2. **ダミーデータの使用禁止**: コード内にハードコードされたダミーデータ（例：`user123`）を使用しないでください。代わりに、UserContextから取得した実際のユーザーIDを使用するか、環境変数から適切な値を取得してください。

2. **リレーションの設定**: サブカテゴリ作成時などのリレーション設定には特に注意が必要です：
   - 親カテゴリを作成した後、その自動生成されたIDを取得
   - サブカテゴリの`parent_ID`として親カテゴリの自動生成IDを使用

3. **Nullableフィールドの扱い**: `parent_ID`のようなnullable（`String?`型）のフィールドには、空文字列ではなく`null`を設定してください。

これらの注意点を守ることで、「Invalid `prisma.category.create()` invocation: constraint violation on the fields: (`category_ID`)」のようなエラーを防ぐことができます。

## データベース操作とPrismaの使用に関する注意点

### Prismaの使用

Prismaを使用してデータベース操作を行う際は、以下の点に注意してください：

1. **スキーマ定義の確認**
   - 操作前に必ず`prisma/schema.prisma`ファイルでモデルの定義を確認してください
   - 特に主キーや外部キーの定義に注意が必要です

2. **フィールド名の一致**
   - Prismaスキーマで定義されたフィールド名とコード内で使用するフィールド名を一致させてください
   - 特に`create`操作では、主キーフィールドを明示的に指定する必要がある場合があります

3. **エラー処理**
   - Prisma特有のエラーを適切に処理してください
   - `PrismaClientKnownRequestError`のエラーコードに基づいて詳細なエラーメッセージを提供すると良いでしょう

### 開発フロー

1. スキーマの変更
   ```bash
   npx prisma migrate dev --name <変更の説明>
   ```

2. クライアントの生成
   ```bash
   npx prisma generate
   ```

3. データベースの確認
   ```bash
   npx prisma studio
   ```

### 実装上の注意点

#### Prismaスキーマとコードの不一致

mypicks.bestプロジェクトでは、Prismaスキーマとコードの間でフィールド名の不一致が発生することがあります。特に以下の点に注意してください：

1. **Categoryモデル**
   - Prismaスキーマでは主キーは`id`フィールドですが、一部のコードでは`category_ID`を使用している箇所があります
   - 新規カテゴリ作成時は、`id`フィールドを明示的に指定する必要があります

2. **Rankingモデル**
   - `subCategory_ID`フィールドを使用する際は、Prismaスキーマの定義に合わせてください

#### 修正例

```typescript
// 正しい実装例（updateCategoryエンドポイント）
const newCategory = await prisma.category.create({
  data: {
    id: id || undefined, // idを明示的に指定（undefinedの場合はPrismaが自動生成）
    category_name: uniqueName,
    user_ID,
    domain: process.env.DOMAIN || "default-domain.com",
    parent_ID: parent_ID || null,
  },
});
```

### データベース操作例

#### カテゴリの作成

```typescript
// 新規カテゴリの作成
const category = await prisma.category.create({
  data: {
    id: `new_category_${Date.now()}`, // カスタムIDを指定
    category_name: "新しいカテゴリ",
    user_ID: currentUserId, // UserContextから取得したユーザーID
    domain: process.env.DOMAIN || "default-domain.com",
  },
});
```

#### ランキングの作成

```typescript
// 新規ランキングの作成
const ranking = await prisma.ranking.create({
  data: {
    ranking_ID: `ranking_${Date.now()}`,
    user_ID: currentUserId, // UserContextから取得したユーザーID
    domain: process.env.DOMAIN || "default-domain.com",
    ranking_title: "新しいランキング",
    subCategory_ID: categoryId, // 既存のカテゴリID
    thumbnail_image: ["image1.jpg"],
  },
});
```

#### エラー処理

```typescript
try {
  // Prisma操作
} catch (error) {
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Prisma特有のエラー処理
    if (error.code === 'P2002') {
      // 一意性制約違反
    }
  }
  // その他のエラー処理
}
```

## 開発サーバーの起動

開発サーバーを起動します：

```bash
npm run dev
```

これにより、開発サーバーが起動し、`http://localhost:3000`でアプリケーションにアクセスできるようになります。

## Dockerを使用した開発

Dockerを使用して開発環境をセットアップすることもできます：

```bash
# Dockerイメージのビルド
docker build -t myrank .

# Dockerコンテナの起動
docker run -p 3000:3000 -e DATABASE_URL=<データベースURL> myrank
```

## テスト

テストを実行するには：

```bash
npm test
```

## コードの構造

プロジェクトのコード構造は以下の通りです：

```
mypicks.best/
├── src/                  # ソースコード
│   ├── app/              # Next.jsアプリケーション
│   │   ├── api/          # APIエンドポイント
│   │   ├── dao/          # データアクセスオブジェクト
│   │   ├── guide/        # ガイドページ
│   │   ├── models/       # データモデル
│   │   └── pages/        # ページコンポーネント
│   ├── components/       # 再利用可能なコンポーネント
│   ├── contexts/         # Reactコンテキスト
│   ├── lib/              # ユーティリティ関数
│   ├── state/            # 状態管理
│   └── utils/            # ユーティリティ関数
├── prisma/               # Prismaスキーマと関連ファイル
├── public/               # 静的ファイル
└── styles/               # グローバルスタイル
```

## 開発ワークフロー

1. 新しい機能やバグ修正に取り組む前に、最新のコードをプルします：
   ```bash
   git pull origin main
   ```

2. 新しいブランチを作成します：
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. コードを変更し、コミットします：
   ```bash
   git add .
   git commit -m "機能の説明"
   ```

4. 変更をプッシュします：
   ```bash
   git push origin feature/your-feature-name
   ```

5. プルリクエストを作成します。

## コーディング規約

- TypeScriptの型定義を適切に使用する
- コンポーネントはReactの関数コンポーネントとして実装する
- スタイリングにはTailwind CSSを使用する
- ファイル名はパスカルケース（例：`ButtonComponent.tsx`）を使用する
- 変数名とプロパティ名はキャメルケース（例：`userName`）を使用する

## 環境別の設定

### 開発環境

開発環境では、ローカルのPostgreSQLデータベースまたはSupabaseの開発環境を使用します。

### 本番環境

本番環境では、Supabaseの本番環境を使用します。デプロイはDockerコンテナを使用して行います。

## トラブルシューティング

### データベース接続エラー

データベース接続エラーが発生した場合は、以下を確認してください：

1. `.env.local`ファイルに正しいデータベースURLが設定されているか
2. PostgreSQLサーバーが実行されているか
3. ファイアウォールがデータベース接続を許可しているか

### Prismaエラー

Prismaに関連するエラーが発生した場合は、以下のコマンドを実行してみてください：

```bash
npx prisma generate
```

これにより、Prismaクライアントが再生成されます。

## 関連ドキュメント

- [アーキテクチャ概要](../architecture/README.md)
- [API仕様](../api/README.md)
- [データベース設計](../database/README.md)
- [ユーザー認証](../auth/README.md)
- [エラー処理](../error-handling/README.md)

## 最終更新日

2025年5月16日
