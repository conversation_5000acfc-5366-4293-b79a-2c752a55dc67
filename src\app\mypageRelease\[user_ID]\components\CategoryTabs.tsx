import React, { RefObject, useMemo } from "react";

// 空配列の定数（無限ループ防止）
const EMPTY_SUBCATEGORIES: any[] = [];
import { ButtonMainCategory } from "@/components/ButtonMainCategory";
import { ButtonSubCategory } from "@/components/ButtonSubCategory";
import type { CategoryType } from "../types";

interface CategoryTabsProps {
  tabsContainerRef: RefObject<HTMLDivElement>;
  categoryTabRef: RefObject<HTMLDivElement>;
  userId: string;
  pageUserId: string;
  username?: string;
  categories: CategoryType[];
  subCategories: CategoryType[];
  selectedCategoryIndex: number;
  categoryID: string;
  subCategory: string;
  isOwnPage: boolean;
  forceUpdate: number;
  handleCategorySelect: (id: string, category_name?: string) => void;
  handleSubCategorySelect: (subCategoryId: string) => void;
  resetCategoryState: () => void;
  handleSubcategoriesReorder: (newSubcategories: { id: string; category_name: string; rankingCount?: number }[]) => void;
  onCategoriesReorder: (newCategories: any[]) => void;
  onCategoryUpdated: (action: string, categoryId?: string, categoryName?: string) => Promise<void>;
  isEditMode?: boolean;
  isModalOpen?: boolean;
  onModalClose?: () => void;
  selectedCategoryName?: string;
  rankingsBySubcategory?: Record<string, any[]>;
  orderedSubcategories?: any[];
}

export const CategoryTabs: React.FC<CategoryTabsProps> = ({
  tabsContainerRef,
  categoryTabRef,
  userId,
  pageUserId,
  username,
  categories,
  subCategories,
  selectedCategoryIndex,
  categoryID,
  subCategory,
  isOwnPage,
  forceUpdate,
  handleCategorySelect,
  handleSubCategorySelect,
  resetCategoryState,
  handleSubcategoriesReorder,
  onCategoriesReorder,
  onCategoryUpdated,
  isEditMode,
  isModalOpen,
  onModalClose,
  selectedCategoryName,
  rankingsBySubcategory = {},
  orderedSubcategories = [],
}) => {
  // CategoryTabsログを削除

  // カテゴリ名を最適化して取得（即座に反映）
  const currentCategoryName = useMemo(() => {
    const category = categories.find(cat => cat.id === categoryID);
    return category ? category.category_name : (selectedCategoryName || 'このカテゴリ');
  }, [categories, categoryID, selectedCategoryName, forceUpdate]);

  return (
    <div
      ref={tabsContainerRef}
      data-tabs-container
      className="w-full sticky top-0 z-30 bg-white rounded-t-md overflow-hidden"
      style={{
        position: "sticky",
        top: 0,
        zIndex: 30,
        borderRadius: "0.375rem 0.375rem 0 0"
      }}
      data-fixed="false"
    >
      <div
        className="w-full bg-white shadow-md"
        style={{
          borderRadius: "inherit"
        }}
      >
        {/* カテゴリタブ */}
        <div ref={categoryTabRef}>
          <ButtonMainCategory
            userID={pageUserId}
            categories={useMemo(() => categories.map((c) => ({
              id: c.id,
              category_name: c.category_name,
            })), [categories])}
            selectedIndex={selectedCategoryIndex}
            onCategorySelect={handleCategorySelect}
            onCategoriesReorder={onCategoriesReorder}
            onCategoryUpdated={onCategoryUpdated}
            isEditMode={isEditMode}
            showAddButton={isOwnPage}
            isModalOpen={isModalOpen}
            onModalClose={onModalClose}
            isOwnPage={isOwnPage}
          />
        </div>

        {/* カテゴリ直下ボタン */}
        {categoryID && isOwnPage && (
          <div className="p-4 pb-0 bg-white">
            <button
              className="border border-black flex justify-between items-center w-full text-black px-4 py-5 rounded-[10px] font-medium bg-[#FFD814] hover:bg-[#E1BC03] transition-colors duration-200"
              onClick={() => {
                // 新しいURL形式でおすすめ追加ページに遷移する
                if (username) {
                  // 選択されたカテゴリのcategory_slugを取得（category_IDではなくcategory_slugを使用）
                  const selectedCategory = categories.find(cat => cat.id === categoryID);
                  const categorySlug = selectedCategory?.category_slug || selectedCategory?.category_ID || categoryID;

                  console.log('🔧 [CategoryTabs] おすすめ追加ボタンクリック:', {
                    categoryID,
                    selectedCategory: selectedCategory ? {
                      id: selectedCategory.id,
                      category_name: selectedCategory.category_name,
                      category_slug: selectedCategory.category_slug,
                      category_ID: selectedCategory.category_ID
                    } : null,
                    categorySlug,
                    targetUrl: `/${username}/add?category=${categorySlug}`
                  });

                  // router.pushを使用して即座に遷移（ローディングアニメーションなし）
                  window.location.href = `/${username}/add?category=${categorySlug}`;
                } else {
                  // usernameが取得できない場合はアラートを表示
                  alert('ユーザー名が取得できませんでした。ページを更新してください。');
                }
              }}
            >
              <span>
                {`「${currentCategoryName}」におすすめを追加する`}
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2}
                stroke="currentColor"
                className="w-5 h-5 ml-2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        )}

        {/* サブカテゴリタブ */}
        <div className="w-full bg-white px-4">
          <ButtonSubCategory
            key={`subcat-${categoryID}-${forceUpdate}`}
            subcategories={(() => {
              // ランキングデータが取得されているかチェック
              const hasRankingData = Object.keys(rankingsBySubcategory).length > 0;

              // ランキングデータが取得されていない場合は定数の空配列を返す（無限ループ防止）
              if (!hasRankingData) {
                return EMPTY_SUBCATEGORIES;
              }

              // orderedSubcategoriesが存在する場合はそれを使用、そうでなければsubCategoriesを使用
              const sourceSubcategories = orderedSubcategories.length > 0 ? orderedSubcategories : subCategories;

              const filtered = (sourceSubcategories || EMPTY_SUBCATEGORIES)
                .filter((s) => {
                  const rankings = rankingsBySubcategory[s.id] || EMPTY_SUBCATEGORIES;
                  const hasRankings = rankings.length > 0;
                  return hasRankings;
                })
                .map((s) => ({
                  id: s.id,
                  category_name: s.category_name,
                  rankingCount: (rankingsBySubcategory[s.id] || []).length,
                }));

              return filtered;
            })()}
            onSubCategorySelect={handleSubCategorySelect}
            onResetCategoryState={resetCategoryState}
            selectedSubCategoryID={subCategory}
            userId={pageUserId}
            isOwnPage={isOwnPage}
            onSubcategoriesReorder={handleSubcategoriesReorder}
          />
        </div>
      </div>


    </div>
  );
};