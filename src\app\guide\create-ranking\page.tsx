'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function CreateRankingGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">ランキングを作成する方法</h1>
          <div className="pt-6 text-[14px] text-[#313131]">
              <p>1. マイページの「ランキング作成」ボタンをタップします。</p>
              <p>2. ランキングのタイトルと説明を入力します。</p>
              <p>3. カテゴリを選択します。</p>
              <p>4. 「アイテム追加」ボタンをタップして、ランキングに含めるアイテムを追加します。</p>
              <p>5. 各アイテムの詳細情報（名前、画像、説明など）を入力します。</p>
              <p>6. アイテムの順序をドラッグ＆ドロップで調整します。</p>
              <p>7. 「公開」ボタンをタップしてランキングを公開します。</p>
            </div>
        </div>  
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
