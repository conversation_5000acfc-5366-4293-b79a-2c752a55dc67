import React from 'react';
import Image from 'next/image';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { AiFillStar } from 'react-icons/ai';

interface RankingListItemProps {
  id: string;
  title: string;
  description: string;
  thumbnailImage: string[] | string;
  recommendRate?: number | null;
  onClick?: () => void;
  onMenuClick?: (e: React.MouseEvent) => void;
}

export const RankingListItem: React.FC<RankingListItemProps> = ({
  id,
  title,
  description,
  thumbnailImage,
  recommendRate = 0,
  onClick,
  onMenuClick
}) => {
  // 説明文を短く切り詰める
  const truncateDescription = (text: string, maxLength: number = 100) => {
    // 型チェックを追加
    if (typeof text !== 'string') {
      text = String(text);
    }
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // サムネイル画像のURLを取得（最初の画像を使用）
  const getThumbnailUrl = () => {
    if (!thumbnailImage) return '/images/no-image.png';
    
    if (Array.isArray(thumbnailImage) && thumbnailImage.length > 0) {
      return thumbnailImage[0];
    }
    
    if (typeof thumbnailImage === 'string' && thumbnailImage.trim() !== '') {
      return thumbnailImage;
    }
    
    return '/images/no-image.png';
  };

  const thumbnailUrl = getThumbnailUrl();
  
  // 評価値の処理（nullや未定義の場合は0とする）
  const rating = recommendRate ? recommendRate : 0;

  return (
    <div 
      className="flex w-full cursor-pointer"
      onClick={onClick}
    >
      {/* サムネイル画像 */}
      <div className="w-24 h-24 rounded-lg overflow-hidden flex-shrink-0 mr-4 relative">
        <Image
          src={thumbnailUrl}
          alt={title}
          fill
          style={{ objectFit: 'cover' }}
          priority={true}
          quality={85}
          sizes="96px"
        />
      </div>

      {/* テキスト情報 */}
      <div className="flex-1 min-w-0">
        <h3 className="text-[16px] font-bold text-gray-900 truncate mb-1">{title}</h3>
        <p className="text-[14px] text-gray-600 line-clamp-2 mb-2">{truncateDescription(description)}</p>
        
        {/* 評価 */}
        <div className="flex items-center">
          <div className="flex">
            {[1, 2, 3, 4, 5].map((star) => (
              <svg 
                key={star} 
                className={`h-4 w-4 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
                fill="currentColor" 
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
          <span className="text-[14px] text-gray-600 ml-1">{rating}</span>
        </div>
      </div>

      {/* メニューボタン */}
      <div className="flex items-center ml-2">
        <button 
          onClick={(e) => {
            e.stopPropagation();
            onMenuClick && onMenuClick(e);
          }} 
          className="p-2 rounded-full hover:bg-gray-100"
          aria-label="メニュー"
        >
          <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      </div>
    </div>
  );
};
