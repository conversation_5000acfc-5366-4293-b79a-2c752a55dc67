# データベース設計

## 概要

mypicks.bestアプリケーションは、PostgreSQL（Supabase）をデータベースとして使用し、Prisma ORMを通じてデータアクセスを行っています。以前はMongoDBを使用していましたが、リレーショナルデータの管理とSupabaseとの統合のためにPostgreSQLに移行しました。

## データベース接続情報

データベース接続は以下のように設定されています：

- **旧接続（MongoDB）**: `mongodb+srv://admin:<EMAIL>/mydatabase?retryWrites=true&w=majority?tls=true`
- **新接続（PostgreSQL）**: `postgresql://postgres.hscloesvpbxhwpefjyfg:<EMAIL>:6543/postgres?pgbouncer=true`
- **直接接続（PostgreSQL）**: `postgresql://postgres.hscloesvpbxhwpefjyfg:<EMAIL>:5432/postgres`

## データモデル

mypicks.bestアプリケーションの主要なデータモデルは以下の通りです：

1. [ユーザーモデル](#ユーザーモデル)
2. [カテゴリモデル](#カテゴリモデル)
3. [ランキングモデル](#ランキングモデル)
4. [SNSモデル](#snsモデル)

### ユーザーモデル

ユーザーモデルはアプリケーションのユーザー情報を管理します。

```prisma
model User {
  id           String   @id @default(uuid())
  user_ID      String   @unique
  user_name    String?
  domain       String?  @unique
  profile_text String?
  profile_image String?
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  // リレーション
  rankings     Ranking[]
  categories   Category[]
  sns_links    SnsUser[]
}
```

### カテゴリモデル

カテゴリモデルはランキングのカテゴリとサブカテゴリを管理します。

```prisma
model Category {
  id         String   @id @default(uuid())
  name       String
  parent_ID  String?
  user_ID    String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // リレーション
  user       User     @relation(fields: [user_ID], references: [user_ID])
  rankings   Ranking[]
}
```

### ランキングモデル

ランキングモデルはユーザーが作成したランキングを管理します。

```prisma
model Ranking {
  id             String   @id @default(uuid())
  ranking_ID     String   @unique
  user_ID        String
  title          String
  description    String?
  category_ID    String?
  subCategory_ID String?
  thumbnail      String?
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt

  // リレーション
  user           User     @relation(fields: [user_ID], references: [user_ID])
  category       Category? @relation(fields: [category_ID], references: [id])
  items          RankingItem[]
}

model RankingItem {
  id              String   @id @default(uuid())
  ranking_ID      String
  item_name       String
  item_image      String?
  item_description String?
  item_score      Float?
  item_order      Int
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // リレーション
  ranking         Ranking  @relation(fields: [ranking_ID], references: [ranking_ID])
}
```

### SNSモデル

SNSモデルはSNSマスターデータとユーザーのSNSリンクを管理します。

```prisma
model SnsMaster {
  id         String   @id @default(uuid())
  sns_name   String   @unique
  sns_icon   String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // リレーション
  sns_users  SnsUser[]
}

model SnsUser {
  id         String   @id @default(uuid())
  user_ID    String
  sns_ID     String
  sns_url    String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // リレーション
  user       User     @relation(fields: [user_ID], references: [user_ID])
  sns        SnsMaster @relation(fields: [sns_ID], references: [id])
}
```

## Prismaディレクトリ構造

mypicks.bestアプリケーションでは、`prisma`ディレクトリでデータベース関連の設定とマイグレーションを管理しています。このディレクトリには以下のファイルとフォルダが含まれています：

```
prisma/
├── migrations/                # マイグレーションファイル
│   ├── 20250405015924_init/  # 初期マイグレーション
│   ├── 20250408152059_update_category_parent_id/
│   ├── 20250408180149_add_self_introduction/
│   ├── 20250417161711_add_category_order/
│   ├── 20250418015155_remove_category_fields/
│   └── migration_lock.toml   # マイグレーションロックファイル
├── schema.prisma             # Prismaスキーマ定義
└── seed.ts                   # シードデータスクリプト
```

### schema.prisma

`schema.prisma`ファイルには、データベースの接続情報とデータモデルが定義されています。主要なモデルには以下が含まれます：

- `User` - ユーザー情報
- `Category` - カテゴリとサブカテゴリ
- `Ranking` - ランキング情報
- `SnsMaster` - SNSマスターデータ
- `SnsUser` - ユーザーのSNSリンク

### マイグレーション

マイグレーションファイルは、データベーススキーマの変更履歴を管理します。主要なマイグレーションには以下が含まれます：

1. `20250405015924_init` - 初期データベーススキーマの作成
2. `20250408152059_update_category_parent_id` - カテゴリの親子関係の更新
3. `20250408180149_add_self_introduction` - 自己紹介フィールドの追加
4. `20250417161711_add_category_order` - カテゴリの表示順序フィールドの追加
5. `20250418015155_remove_category_fields` - 不要なカテゴリフィールドの削除

新しいマイグレーションを作成するには、以下のコマンドを使用します：

```bash
npx prisma migrate dev --name <マイグレーション名>
```

### seed.ts

`seed.ts`ファイルは、開発環境で使用するテストデータを生成するためのスクリプトです。このスクリプトは以下のデータを作成します：

- テストユーザー
- カテゴリとサブカテゴリ
- サンプルランキング
- SNSマスターデータ

シードスクリプトを実行するには、以下のコマンドを使用します：

```bash
npx prisma db seed
```

以前は固定値「user123」を使用していましたが、現在は環境変数からユーザーIDを取得するように修正されています：

- `process.env.DEFAULT_USER_ID`が設定されていればその値を使用
- 環境変数が設定されていない場合はエラーメッセージを表示して終了

## データベースとPrismaスキーマの不一致に関する注意点

mypicks.bestプロジェクトでは、データベースの実際のテーブル構造とPrismaスキーマの間に不一致が発生することがあります。特に以下の点に注意してください：

### 既知の問題と解決策

1. **Categoryテーブルの`category_ID`フィールド**

   - **問題**: データベースの`Category`テーブルには`category_ID`フィールドが存在しますが、Prismaスキーマには含まれていないことがあります。これにより、カテゴリ追加時に「Invalid `prisma.category.create()` invocation: constraint violation on the fields: (`category_ID`)」というエラーが発生します。
   
   - **解決策**: 
     - Prismaスキーマに`category_ID`フィールドを追加する
     - フロントエンドのAPIリクエストに`category_ID`フィールドを含める
     - バックエンドのPrisma操作で`category_ID`フィールドを設定する

   ```prisma
   // Prismaスキーマの正しい定義
   model Category {
     id             String     @id @default(uuid())
     user_ID        String
     domain         String
     category_ID    String     // このフィールドが必要
     category_name  String
     parent_ID      String?
     // 他のフィールド...
   }
   ```

   ```typescript
   // フロントエンドからのリクエスト例
   const res = await fetch("/api/updateCategory", {
     method: "POST",
     headers: { "Content-Type": "application/json" },
     body: JSON.stringify({
       id: newCategoryId,
       category_ID: newCategoryId, // このフィールドが必要
       category_name: placeholderName,
       user_ID: currentUserId,
       // 他のフィールド...
     }),
   });
   ```

2. **APIエンドポイントのURL問題**

   - **問題**: 開発サーバーが3001ポートで動作していても、相対パスを使用したAPIリクエストが3000ポートに送信されることがあります。
   
   - **解決策**: 
     - 絶対URLを使用して正しいポートを指定する
     - 環境変数を使用してAPIエンドポイントのベースURLを設定する

   ```typescript
   // 正しいURLを使用したリクエスト例
   const res = await fetch("http://localhost:3001/api/updateCategory", {
     // リクエスト詳細...
   });
   ```

### 今後の改善点

1. **データベースマイグレーションの整理**
   - 不要なフィールドを削除するマイグレーションを作成する
   - スキーマとデータベースの構造を完全に一致させる

2. **型安全性の向上**
   - Prismaスキーマと一致する型定義を使用する
   - TypeScriptの型チェックを活用してエラーを早期に発見する

3. **エラーハンドリングの強化**
   - より詳細なエラーメッセージを提供する
   - ユーザーにわかりやすいエラー表示を実装する

## データベース移行

### MongoDBからPostgreSQLへの移行

mypicks.bestアプリケーションは、以前はMongoDBを使用していましたが、以下の理由からPostgreSQLに移行しました：

1. リレーショナルデータの管理が容易になる
2. Supabaseとの統合によるホスティングと管理の簡素化
3. Prisma ORMの機能をより活用できる

### 移行時の注意点

移行時には以下の点に注意が必要でした：

1. スキーマの違いによるフィールド名の調整
   - 例：`category_ID`フィールドを`id`フィールドに変更
2. リレーションシップの設定
   - 外部キー制約の追加
3. データ型の変換
   - MongoDBの特定のデータ型からPostgreSQLのデータ型への変換

## Prismaの使用

### Prismaスキーマ

Prismaスキーマは`prisma/schema.prisma`ファイルで定義されています。このファイルには、データベースの接続情報とデータモデルが記述されています。

### Prismaクライアント

Prismaクライアントは`src/lib/prisma.ts`ファイルでインスタンス化され、アプリケーション全体で使用されています。

```typescript
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default prisma;
```

### マイグレーション

データベーススキーマの変更時には、以下のコマンドを使用してマイグレーションを実行します：

```bash
npx prisma migrate dev --name <マイグレーション名>
```

## 実装上の注意点

### フィールド名の変更

Prismaスキーマに合わせてコードを修正する際に、以下の変更が行われました：

1. **Categoryモデルの修正**
   - `category_ID`フィールドを削除（Prismaが自動的にUUIDを生成するため）
   - `parent_ID`を空文字列ではなく`null`に変更（スキーマでは`String?`型）

### 環境変数の設定

データベース接続情報は環境変数を使用して管理されています：

```
DATABASE_URL=postgresql://postgres.hscloesvpbxhwpefjyfg:<EMAIL>:6543/postgres?pgbouncer=true
```

dotenvパッケージを導入して、環境変数を適切に読み込むための設定が追加されました。

## 関連ファイル

- `prisma/schema.prisma` - Prismaスキーマ定義
- `src/lib/prisma.ts` - Prismaクライアントのインスタンス化
- `.env` - 環境変数の設定

## 最終更新日

2025年4月25日
