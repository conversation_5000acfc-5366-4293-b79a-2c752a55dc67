import { useCallback, useState } from "react";
import { useSearchParams } from "next/navigation";
import { getUser } from "../../../../lib/api/getUser";
import type { UserInfo, CategoryType, RankingType } from "../types";

interface UseUserDataProps {
  isDragInProgress: boolean;
  initializedFromUrl: boolean;
}

interface UseUserDataReturn {
  isLoading: boolean;
  loadingError: string | null;
  userData: UserInfo | null;
  user: UserInfo | null;
  userName: string;
  userImage: string;
  rankings: RankingType[];
  categories: CategoryType[];
  getUserInfo: (currentUserId: string) => Promise<void>;
  setUser: (user: UserInfo | null) => void;
  setUserData: (userData: UserInfo | null) => void;
  setUserName: (name: string) => void;
  setUserImage: (image: string) => void;
  setRankings: (rankings: RankingType[]) => void;
  setCategories: (categories: CategoryType[]) => void;
  setCategoryID: (id: string) => void;
  setSelectedCategoryName: (name: string) => void;
  setSelectedCategoryIndex: (index: number) => void;
  setSubCategories: (subcategories: CategoryType[]) => void;
  setSubcategoryOrder: (order: string[]) => void;
  setSubCategory: (id: string) => void;
}

export const useUserData = ({ 
  isDragInProgress, 
  initializedFromUrl 
}: UseUserDataProps): UseUserDataReturn => {
  const searchParams = useSearchParams();
  
  // State variables
  const [isLoading, setIsLoading] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [userData, setUserData] = useState<UserInfo | null>(null);
  const [user, setUser] = useState<UserInfo | null>(null);
  const [userName, setUserName] = useState("");
  const [userImage, setUserImage] = useState("");
  const [rankings, setRankings] = useState<RankingType[]>([]);
  const [categories, setCategories] = useState<CategoryType[]>([]);
  
  // These setters are exposed to allow parent component to manage state
  const [categoryID, setCategoryID] = useState("");
  const [selectedCategoryName, setSelectedCategoryName] = useState("");
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState(-1);
  const [subCategories, setSubCategories] = useState<CategoryType[]>([]);
  const [subcategoryOrder, setSubcategoryOrder] = useState<string[]>([]);
  const [subCategory, setSubCategory] = useState("");

  const getUserInfo = useCallback(async (currentUserId: string) => {
    if (!currentUserId) {
      // userIdが空の場合はAPIリクエストをスキップ
      return;
    }

    // ドラッグ中の場合はAPIリクエストをスキップ
    if (isDragInProgress) {
      return;
    }

    // APIリクエストを実行
    setIsLoading(true);
    setLoadingError(null);

    // ユーザーIDの検証
    if (!currentUserId || typeof currentUserId !== 'string' || currentUserId.trim().length === 0) {
      setLoadingError('ユーザーIDが正しくありません。ページを再読み込みしてください。');
      setIsLoading(false);
      return;
    }

    try {
      // 並列でデータを取得（パフォーマンス最適化）
      const [userData, profileResponse, rankingsResponse, categoriesResponse] = await Promise.allSettled([
        getUser(currentUserId),
        fetch(`/api/profile?user_ID=${currentUserId}`),
        fetch(`/api/getRanks?user_ID=${currentUserId}`),
        fetch(`/api/getCategories?user_ID=${currentUserId}&domain=default-domain.com`)
      ]);

      // ユーザーデータの処理
      if (userData.status === 'fulfilled') {
        const userInfo = userData.value;
        setUserData(userInfo);
        setUser(userInfo);
        setUserName(userInfo.user_name || '');
        setUserImage(userInfo.profile_image || '');
        setIsLoading(false); // 基本情報取得完了

        // プロフィール情報の処理
        let combinedData: UserInfo = { ...userInfo };
        if (profileResponse.status === 'fulfilled' && profileResponse.value.ok) {
          try {
            const profileData = await profileResponse.value.json();
            combinedData = {
              ...userInfo,
              ...profileData,
              userId: userInfo.userId || currentUserId,
              user_name: userInfo.name || "",
              profile_image: userInfo.profile_image || "",
              website: profileData.website || userInfo.contact_url || "",
              email: profileData.email || userInfo.contact_email || "",
              selfIntroduction: profileData.selfIntroduction || userInfo.self_introduction || "",
              snsLinks: profileData.snsLinks || {
                instagram: "", twitter: "", facebook: "", youtube: "", tiktok: "", line: "",
              },
              snsOrder: profileData.snsOrder && profileData.snsOrder.length > 0
                ? profileData.snsOrder
                : ["instagram", "twitter", "facebook", "youtube", "tiktok", "line"],
              domain: userInfo.domain || profileData.domain || "",
              background_image: userInfo.background_image || profileData.background_image || "",
            };
            setUser(combinedData);
          } catch (error) {
            // プロフィール情報の処理失敗時も処理を続行
          }
        }

        // ランキング情報の処理
        if (rankingsResponse.status === 'fulfilled' && rankingsResponse.value.ok) {
          try {
            const responseData = await rankingsResponse.value.json();
            const rankingData = Array.isArray(responseData)
              ? responseData
              : responseData.ranks && Array.isArray(responseData.ranks)
                ? responseData.ranks
                : [];
            setRankings(rankingData);
          } catch (error) {
            // ランキング情報の処理失敗時も処理を続行
          }
        }

        // カテゴリ情報の処理
        if (categoriesResponse.status === 'fulfilled' && categoriesResponse.value.ok) {
          try {
            const categoriesData = await categoriesResponse.value.json();
            setCategories(categoriesData);

            // URLからのパラメータがある場合や初期化済みの場合は初期化しない
            const categoryParam = searchParams.get("category");
            if (!initializedFromUrl && !categoryParam && categoriesData.length > 0) {
              // カテゴリ初期化: 最初のカテゴリを選択
              const firstCat = categoriesData[0];
              setCategoryID(firstCat.id);
              setSelectedCategoryName(firstCat.category_name);
              setSelectedCategoryIndex(0);
              const subcats = (firstCat.subcategories || []).sort((a: any, b: any) => (a.order || 0) - (b.order || 0));
              setSubCategories(subcats);
              setSubcategoryOrder(subcats.map((s: CategoryType) => s.id));
              if (subcats.length > 0) setSubCategory(subcats[0].id);
            }
          } catch (error) {
            // カテゴリ情報の処理失敗時も処理を続行
          }
        }
    } catch (err) {
      // データ取得エラーの処理
      setLoadingError(
        err instanceof Error ? err.message : "データ取得に失敗しました"
      );
      setIsLoading(false);
    }
  }, [isDragInProgress, searchParams, initializedFromUrl]);

  return {
    isLoading,
    loadingError,
    userData,
    user,
    userName,
    userImage,
    rankings,
    categories,
    getUserInfo,
    setUser,
    setUserData,
    setUserName,
    setUserImage,
    setRankings,
    setCategories,
    setCategoryID,
    setSelectedCategoryName,
    setSelectedCategoryIndex,
    setSubCategories,
    setSubcategoryOrder,
    setSubCategory,
  };
};