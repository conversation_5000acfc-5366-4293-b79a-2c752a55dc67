'use client';

/*
We're constantly improving the code you see.
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React, { useEffect, useState, useRef } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { getUserId } from "../../contexts/UserContext";

interface HeaderItemsReviewProps {
  userID: string;
  userName?: string;
  profileImage?: string;
  rankingID?: string;
  categoryName?: string;
  categoryId?: string;
  subCategoryName?: string;
  subCategoryId?: string;
}

export const HeaderItemsReview = ({
  userID,
  userName = "ユーザーネーム",
  profileImage,
  rankingID,
  categoryName,
  categoryId,
  subCategoryName,
  subCategoryId
}: HeaderItemsReviewProps): JSX.Element => {
  const router = useRouter();
  const [imgError, setImgError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const [username, setUsername] = useState<string | null>(null);
  
  // デフォルトのプロフィール画像
  const defaultImage = "/static/img/defaultUserIcon.png";
  
  // フォールバック用のSVGデータ URL
  const fallbackImage = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ccc'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z'/%3E%3C/svg%3E";
  
  // 現在のユーザーID（propsから受け取るか、ログイン中のユーザーIDを使用）
  const currentUserId = userID || getUserId();

  // プロフィール画像の優先順位: props > API取得 > デフォルト
  const shouldFetchFromAPI = !profileImage || profileImage.trim() === "";

  // ユーザー名を取得するuseEffect
  useEffect(() => {
    const fetchUsername = async () => {
      if (userID) {
        try {
          const response = await fetch(`/api/getUser?user_ID=${userID}`);
          if (response.ok) {
            const userData = await response.json();
            setUsername(userData.username);
          }
        } catch (error) {
          console.error('ユーザー名取得エラー:', error);
        }
      }
    };

    fetchUsername();
  }, [userID]);

  // プロフィール画像のURLが変更されたときに更新（画像切り替えによる不要な実行を防ぐ）
  useEffect(() => {
    // 認証トークンを取得する関数
    const getAuthToken = async () => {
      try {
        // Clerkからトークンを取得する処理
        // 注: 実際のClerkの実装に合わせて調整する必要があります
        const token = localStorage.getItem('clerkToken') || 'dummy-auth-token';
        return token;
      } catch (error) {
        return 'dummy-auth-token';
      }
    };
    
    const fetchProfileImage = async () => {
      try {
        // プロフィール画像がpropsから渡された場合は優先的に使用
        if (profileImage && profileImage.trim() !== "") {
          setImageSrc(profileImage);
          return;
        }

        // APIからプロフィール画像を取得する場合
        if (shouldFetchFromAPI && currentUserId && currentUserId.trim() !== "") {

          // 認証トークンを取得
          const token = await getAuthToken();

          // Authorizationヘッダーを追加してリクエストを送信
          const response = await fetch(`/api/profile?user_ID=${currentUserId}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.ok) {
            const data = await response.json();

            if (data.profileImage && data.profileImage.trim() !== "") {
              setImageSrc(data.profileImage);
              return;
            }
          } else {
            console.error("HeaderItemsReview - APIリクエスト失敗:", response.status, response.statusText);
            const errorText = await response.text();
            console.error("HeaderItemsReview - エラー詳細:", errorText);
          }
        }

        // フォールバック: デフォルト画像を使用
        setImageSrc(defaultImage);
      } catch (error) {
        console.error("HeaderItemsReview - エラー発生:", error);
        setImageSrc(defaultImage);
      }
    };
    
    fetchProfileImage();
  }, [currentUserId, profileImage, shouldFetchFromAPI]); // profileImageの変更も監視
  
  // 画像読み込みエラー時の処理
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error("Image loading error, falling back to default image");
    // 現在の画像ソースがデフォルト画像の場合は、SVGフォールバックを使用
    if (imageSrc === defaultImage) {
      console.warn("Default image also failed to load, using SVG fallback");
      setImageSrc(fallbackImage);
    } else {
      setImageSrc(defaultImage);
    }
    setImgError(true);
  };
  
  // メニューの外側をクリックしたときにメニューを閉じる
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside as EventListener);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside as EventListener);
    };
  }, []);
  
  // 編集ページに遷移する関数
  const handleEdit = async () => {
    if (rankingID && userID) {
      try {
        // ユーザー名を取得
        const response = await fetch(`/api/getUser?user_ID=${userID}`);
        if (response.ok) {
          const userData = await response.json();
          const username = userData.username;

          if (username) {
            // 新しいURL形式で遷移
            const params = new URLSearchParams();
            params.append('ranking_id', rankingID);

            // categoryIdがcategory_ID形式（tab1, tab2など）かチェック
            if (categoryId) {
              // categoryIdが数値やUUIDの場合は、category_IDに変換する必要がある
              // ここでは一旦そのまま使用し、ページ側で変換処理を行う
              params.append('category', categoryId);
            }

            router.push(`/${username}/add?${params.toString()}`);
          } else {
            // フォールバック: 旧URL形式
            const params = new URLSearchParams();
            params.append('ranking_id', rankingID);

            if (categoryId) params.append('categoryId', categoryId);
            if (categoryName) params.append('categoryName', encodeURIComponent(categoryName));
            if (subCategoryId) params.append('subCategoryId', subCategoryId);
            if (subCategoryName) params.append('subCategory', encodeURIComponent(subCategoryName));

            router.push(`/rankingRegister/${userID}?${params.toString()}`);
          }
        } else {
          // フォールバック: 旧URL形式
          const params = new URLSearchParams();
          params.append('ranking_id', rankingID);

          if (categoryId) params.append('categoryId', categoryId);
          if (categoryName) params.append('categoryName', encodeURIComponent(categoryName));
          if (subCategoryId) params.append('subCategoryId', subCategoryId);
          if (subCategoryName) params.append('subCategory', encodeURIComponent(subCategoryName));

          router.push(`/rankingRegister/${userID}?${params.toString()}`);
        }
      } catch (error) {
        console.error("ユーザー名取得エラー:", error);
        // フォールバック: 旧URL形式
        const params = new URLSearchParams();
        params.append('ranking_id', rankingID);

        if (categoryId) params.append('categoryId', categoryId);
        if (categoryName) params.append('categoryName', encodeURIComponent(categoryName));
        if (subCategoryId) params.append('subCategoryId', subCategoryId);
        if (subCategoryName) params.append('subCategory', encodeURIComponent(subCategoryName));

        router.push(`/rankingRegister/${userID}?${params.toString()}`);
      }
    }
    setShowMenu(false);
  };

  return (
    <div className="flex justify-between w-full h-[52px] pl-[16px] pr-[16px] items-center relative bg-white shadow-[0px_0px_6px_#00000040]">
      <div className="flex w-[80%] items-center gap-[8px]  py-0 relative">
        <Link href={username ? `/${username}` : `/mypageRelease/${userID}`}>
          <img
            className="relative w-[30px] h-[30px] object-cover"
            alt="Image left arrow"
            src="https://c.animaapp.com/ZUwMMAhl/img/<EMAIL>"
          />
        </Link>
        
        {imageSrc !== null && imageSrc !== "" ? (
          <img
            className="relative h-[30px] w-[30px] object-cover rounded-full"
            alt="User profile"
            src={imageSrc}
            onError={handleImageError}
          />
        ) : (
          <img
            className="relative h-[30px] w-[30px] object-cover rounded-full"
            alt="User profile"
            src={defaultImage}
            onError={handleImageError}
          />
        )}
        <p className="relative w-fit [font-family:'Roboto',Helvetica] font-normal text-black text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
          <span className="[font-family:'Roboto',Helvetica] font-normal text-black text-[12px] tracking-[0]">
            {userName}{" "}
          </span>
          <span className="text-[10px]">のおすすめ</span>
        </p>
      </div>
      <div className="inline-flex items-start pl-0 py-0 relative flex-[0_0_auto]">
        <div className="relative">
          <img
            className="relative w-[20px] h-[20px] object-cover cursor-pointer"
            alt="Image menu"
            src="https://c.animaapp.com/ZUwMMAhl/img/<EMAIL>"
            onClick={() => setShowMenu(!showMenu)}
          />
          {showMenu && (
            <div 
              ref={menuRef}
              className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 border border-gray-200"
            >
              <div className="py-1">
                <button
                  onClick={handleEdit}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  編集する
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
