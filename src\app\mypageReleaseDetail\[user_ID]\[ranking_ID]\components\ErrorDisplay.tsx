"use client";

import React from "react";
import { useRouter } from "next/navigation";

interface ErrorDisplayProps {
  error: string;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => {
  const router = useRouter();

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <div className="flex-1 flex items-center justify-center">
        <div className="max-w-[500px] w-full mx-auto bg-white shadow-md min-h-screen flex items-center justify-center">
          <div className="text-center w-full px-4">
            <h1 className="text-[20px] font-semibold text-[#313131] mb-4">
              エラーが発生しました
            </h1>
            <p className="text-[#676767] mb-8">
              {error}
            </p>
            <button
              onClick={() => router.push('/')}
              className="inline-block w-[340px] h-[40px] bg-[#FFD814] text-[#313131] font-medium rounded-[100px] hover:bg-[#E1BC03] transition-colors leading-[40px]"
            >
              ホームに戻る
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};