'use client';

import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { UserProvider } from '../contexts/UserContext';
import { jaJP } from '@clerk/localizations';

// Clerkのローカライゼーション設定
const base = jaJP as any;
const localization = {
  ...base,
  formFieldLabel__emailAddress: 'メールアドレス',
  formFieldInputPlaceholder__emailAddress: '<EMAIL>',
  formFieldLabel__password: 'パスワード',
  formFieldLabel__confirmPassword: 'パスワード（確認）',
  formFieldInputPlaceholder__password: 'パスワードを入力してください',
  formFieldInputPlaceholder__confirmPassword: 'パスワードを再入力してください',
  formButtonPrimary: '続ける',
  signUp: {
    ...base.signUp,
    start: {
      ...base.signUp?.start,
      title: '{{applicationName}}へようこそ',
      subtitle: '{{applicationName}}へログインする',
      actionText: 'すでにアカウントを持っている方はこちら',
      actionLink: 'ログイン',
    },
    emailCode: {
      ...base.signUp?.emailCode,
      subtitle: '{{applicationName}}に進む',
    },
    continue: {
      ...base.signUp?.continue,
      actionText: '次に進む',
      subtitle: '{{applicationName}}へログイン',
    }
  },
  signIn: {
    ...base.signIn,
    start: {
      ...base.signIn?.start,
      titleCombined: '{{applicationName}}へログイン',
    },
  }
};

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="h-screen w-full bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <ClerkProvider
        publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
        localization={localization}
        appearance={{
          elements: {
            formButtonPrimary:
              'bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5]',
            card: 'bg-white shadow-lg rounded-lg',
          },
        }}
        signInUrl="/sign-in"
        signUpUrl="/sign-up"
        signOutUrl="/sign-in"
      >
        {/* --- Auth Buttons / UserMenu -------------- */}
        {/* <SignedInHeader /> */}

        {/* --- Context Provider -------------------- */}
        <UserProvider>{children}</UserProvider>
      </ClerkProvider>
    </div>
  );
}
