/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";

interface Props {
  className: any;
  useButtonProfileEdit: boolean;
  onClick?: () => void;
  userId?: string;
}

export const ButtonProfileEdit = ({ className, useButtonProfileEdit, onClick, userId }: Props): JSX.Element | null => {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);
  const { user: clerkUser, isLoaded } = useUser();

  // ページ遷移の監視
  useEffect(() => {
    const handleRouteChange = () => {
      console.log('🔍 [ButtonProfileEdit] ルート変更検出:', {
        pathname: window.location.pathname,
        timestamp: new Date().toISOString()
      });
    };

    // popstate イベントでブラウザの戻る/進むを監視
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // useButtonProfileEdit が false の場合は null を返して何も表示しない
  if (!useButtonProfileEdit) {
    return null;
  }

  // 本番環境対応: 事前にページを準備
  const prefetchPage = async () => {
    if (userId && process.env.NODE_ENV === 'production') {
      const targetUrl = `/${userId}/edit`;
      try {
        // プリフェッチを実行
        router.prefetch(targetUrl);
      } catch (error) {
        // エラーは無視
      }
    }
  };

  const handleClick = async () => {
    if (onClick) {
      onClick();
    } else {
      try {
        setIsNavigating(true);

        // userIdプロパティ（実際にはユーザー名）が渡されている場合は直接遷移
        if (userId) {
          const targetUrl = `/${userId}/edit`;

          // 本番環境では最適化された遷移、開発環境では直接遷移
          if (process.env.NODE_ENV === 'production') {
            // 本番環境: Next.jsの最適化を活用
            router.push(targetUrl);
          } else {
            // 開発環境: 直接遷移で高速化
            window.location.href = targetUrl;
          }
          return;
        }

        // Clerkが読み込み完了していない場合は待機
        if (!isLoaded) {
          setIsNavigating(false);
          return;
        }

        // ログインしていない場合はサインインページにリダイレクト
        if (!clerkUser) {
          router.push('/sign-in');
          return;
        }

        // ClerkからユーザーIDを取得してAPIを呼び出し（userIdが渡されていない場合のみ）
        const currentUserId = clerkUser.id;
        const response = await fetch(`/api/getUser?user_ID=${currentUserId}`);

        if (response.ok) {
          const userData = await response.json();
          const username = userData.username;

          if (username) {
            // 新しいURL形式でプロフィール編集ページに遷移
            router.push(`/${username}/edit`);
          } else {
            // ユーザー名が取得できない場合はセットアップページにリダイレクト
            router.push('/setup');
          }
        } else {
          // ユーザー情報が取得できない場合はセットアップページにリダイレクト
          router.push('/setup');
        }
      } catch (error) {
        console.error("❌ [ButtonProfileEdit] プロフィール編集ページ遷移エラー:", error);
        // エラーの場合はホームページにリダイレクト
        router.push('/');
      } finally {
        setIsNavigating(false);
      }
    }
  };
  
  // デバッグログは削除（パフォーマンス改善）

  return (
    <div className={`inline-flex flex-col items-start gap-[10px] pt-[20px] pb-0 px-0 relative ${className}`}>
      <div
        className={`inline-flex items-center justify-center gap-[10px] px-[16px] py-[12px] relative flex-[0_0_auto] rounded-[2px] cursor-pointer transition-colors ${
          isNavigating
            ? 'bg-[#000000] opacity-70'
            : 'bg-[#000000b3] hover:bg-[#000000] active:bg-[#000000]'
        }`}
        onClick={isNavigating ? undefined : handleClick}
        onMouseEnter={prefetchPage}
        onTouchStart={prefetchPage}
      >
        <div className="relative w-fit mt-[-1.00px] font-normal text-white text-[12px] text-center tracking-[0] leading-[14px] whitespace-nowrap">
          {isNavigating ? '遷移中...' : 'プロフィール編集'}
        </div>
      </div>
    </div>
  );
};
