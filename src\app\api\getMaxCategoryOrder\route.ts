import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("user_ID");

    if (!userId) {
      return NextResponse.json(
        { error: "user_IDは必須パラメータです" },
        { status: 400 }
      );
    }

    // 同じuser_IDを持ち、parent_IDがnullのカテゴリの中で最大のorder値を取得
    const maxOrderResult = await prisma.category.aggregate({
      where: {
        user_ID: userId,
        parent_ID: null, // parent_IDがnullのカテゴリ（親カテゴリ）
      },
      _max: {
        order: true,
      },
    });

    const maxOrder = maxOrderResult._max.order || 0;

    return NextResponse.json({ maxOrder });
  } catch (error) {
    return NextResponse.json(
      { error: "カテゴリの最大order値の取得中にエラーが発生しました" },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
