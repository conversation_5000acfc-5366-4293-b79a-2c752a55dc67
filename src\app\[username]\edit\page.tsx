'use client';

import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import ProfileSaveLoadingAnimation from "../../../components/ProfileSaveLoadingAnimation";
import CatLoadingAnimation from "../../../components/CatLoadingAnimation/CatLoadingAnimation";
import { RedButtonFooter } from "@/components/RedButtonFooter";
import { DivWrapper } from "@/components/DivWrapper";
import { Footer } from "@/components/Footer";
import { HeaderItems } from "@/components/HeaderItems";
import { InputList } from "@/components/InputList";
import { ItemName } from "@/components/ItemName";
import { OtherSns } from "@/components/OtherSns";
import { SelfIntroduction } from "@/components/SelfIntroduction";
import { SnsLink } from "@/components/SnsLink";
import { ProfileProvider, useProfile } from "../../../contexts/ProfileContext";
import { useUser } from "@clerk/nextjs";
import { getUserId } from "../../../contexts/UserContext";

/**
 * This component renders the profile edit page.
 * The page contains:
 * - A header with the title " "
 * - A section for basic information
 * - A section for self introduction
 * - A section for adding SNS links
 * - A footer with a red button to save the profile
 */
/**
 * ProfileEditPage is a React component that renders the profile edit page.
 * The page is divided into sections for basic information, self introduction, and adding SNS links.
 * It also contains a footer with a red button to save the profile.
 */
const ProfileEditContent = (): JSX.Element => {
  const {
    profileData,
    updateWebsite,
    updateEmail,
    updateSelfIntroduction,
    updateProfileImage,
    updateBackgroundImage,
    updateSnsLink,
    saveProfile,
    updateUserName
  } = useProfile();
  const { user: clerkUser, isLoaded } = useUser();

  const [localProfileImage, setLocalProfileImage] = useState<string>("");
  const [localBackgroundImage, setLocalBackgroundImage] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false); // 初期状態をfalseに変更
  const [isNameEmpty, setIsNameEmpty] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isAuthChecking, setIsAuthChecking] = useState(true); // 認証チェック状態を追加
  
  const params = useParams();
  const router = useRouter();
  const username = params.username as string;

  // プロフィールデータが読み込まれたら画像を設定
  useEffect(() => {
    if (profileData && Object.keys(profileData).length > 0) {
      // プロフィール画像の設定（デフォルト画像の場合も適切に処理）
      const profileImageToSet = profileData.profileImage || "/static/img/defaultUserIcon.png";
      const backgroundImageToSet = profileData.backgroundImage || "";

      setLocalProfileImage(profileImageToSet);
      setLocalBackgroundImage(backgroundImageToSet);

      // 名前が空かどうかをチェック
      setIsNameEmpty(!profileData.userName || profileData.userName.trim() === "");
    }
  }, [profileData]);

  // ユーザー認証チェック（超最適化版）
  useEffect(() => {
    // Clerkが読み込み完了していない場合は早期リターン
    if (!isLoaded) {
      console.log('🔍 [ProfileEdit] Clerk未読み込み - 待機中');
      return;
    }

    let authCheckExecuted = false; // 重複実行防止フラグ

    const checkUserAuth = async () => {
      try {
        // 重複実行防止
        if (authCheckExecuted) {
          return;
        }
        authCheckExecuted = true;

        // ログインしていない場合はサインインページにリダイレクト
        if (!clerkUser) {
          router.push('/sign-in');
          return;
        }

        // 認証チェック完了（ページは即座に表示）
        setIsAuthChecking(false);

        // バックグラウンドでユーザー名検証（ページ表示をブロックしない）
        setTimeout(async () => {
          try {
            const currentUserId = clerkUser.id;
            const response = await fetch(`/api/getUser?user_ID=${currentUserId}`);
            if (response.ok) {
              const userData = await response.json();
              const currentUsername = userData.username;

              // URLのユーザー名と現在のユーザーのユーザー名が一致しない場合のみリダイレクト
              if (currentUsername && currentUsername !== username) {
                router.push(`/${currentUsername}/edit`);
                return;
              }

              // ユーザー名が取得できない場合はセットアップページにリダイレクト
              if (!currentUsername) {
                router.push('/setup');
                return;
              }
            } else {
              router.push('/setup');
            }
          } catch (error) {
            console.error("❌ [ProfileEdit] バックグラウンド認証チェックエラー:", error);
          }
        }, 10);

      } catch (error) {
        console.error("❌ [ProfileEdit] ユーザー認証チェックエラー:", error);
        setIsAuthChecking(false);
        authCheckExecuted = false; // エラー時はリセット
      }
    };

    if (username) {
      checkUserAuth();
    }

    // クリーンアップ
    return () => {
      authCheckExecuted = false;
    };
  }, [username, router, clerkUser, isLoaded]);

  const handleSaveProfile = async () => {
    try {
      setIsSaving(true);
      await saveProfile();
      // ローディングアニメーションが完了するまでモーダル表示を遅延
      // モーダル表示はhandleSaveCompleteで行う
    } catch (error) {
      console.error("ProfileEditContent - プロフィール保存エラー:", error);
      setIsSaving(false);
      alert("プロフィールの保存に失敗しました");
    }
  };

  // 保存完了時のコールバック
  const handleSaveComplete = () => {
    setIsSaving(false);
  };

  // 画像変更時の処理
  const handleImageChange = async (imageUrl: string) => {
    // 無効な画像URLの場合はデフォルト画像を使用
    const validImageUrl = imageUrl && imageUrl.trim() !== "" ? imageUrl : "/static/img/defaultUserIcon.png";

    setLocalProfileImage(validImageUrl); // ローカルの状態を即時更新

    try {
      await updateProfileImage(validImageUrl);
    } catch (error) {
      console.error("ProfileEditContent - 画像更新エラー:", error);
      // エラーの場合はデフォルト画像に戻す
      setLocalProfileImage("/static/img/defaultUserIcon.png");
    }
  };

  // 背景画像変更時の処理
  const handleBackgroundImageChange = async (imageUrl: string) => {
    setLocalBackgroundImage(imageUrl); // ローカルの状態を即時更新

    try {
      // 背景画像の更新（ProfileContextのupdateBackgroundImage関数内でAPIを呼び出して保存される）
      await updateBackgroundImage(imageUrl);
      // バックアップ保存処理は削除（二重更新防止）
    } catch (error) {
      console.error("ProfileEditContent - 背景画像更新エラー:", error);
    }
  };

  const handleNameChange = async (value: string) => {
    try {
      await updateUserName(value);
      // 名前が空かどうかをチェック
      setIsNameEmpty(!value || value.trim() === "");
    } catch (error) {
      console.error("ProfileEditContent - 名前更新エラー:", error);
    }
  };

  // 認証チェック中の場合のみローディング表示
  if (isAuthChecking) {
    return (
      <div className="max-w-[500px] mx-auto flex flex-col min-h-screen bg-white overflow-y-auto">
        <HeaderItems text="プロフィール編集" />
        <div className="flex-1 flex items-center justify-center">
          <CatLoadingAnimation />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-[500px] mx-auto flex flex-col min-h-screen bg-white overflow-y-auto">
      <HeaderItems text="プロフィール編集" />
      <ItemName text = "基本情報"/>
      <InputList
        profileImage={localProfileImage}
        backgroundImage={localBackgroundImage}
        onImageChange={handleImageChange}
        onBackgroundImageChange={handleBackgroundImageChange}
        user_ID={clerkUser?.id} // ClerkのユーザーIDを直接渡す
      />
      <DivWrapper
        className="!flex-[0_0_auto]"
        text="名前"
        text1="名前を入力"
        initialValue={profileData?.userName || ""}
        onSave={handleNameChange}
      />
      <DivWrapper
        className="!flex-[0_0_auto]"
        text="Webサイト"
        text1="URLを入力"
        initialValue={profileData?.website || ""}
        onSave={updateWebsite}
      />
      <DivWrapper
        className="!flex-[0_0_auto]"
        text="メールアドレス"
        text1="メールアドレスを入力"
        initialValue={profileData?.email || ""}
        onSave={updateEmail}
      />
      <ItemName text="自己紹介" />
      <SelfIntroduction
        initialValue={profileData?.selfIntroduction || ""}
        onSave={updateSelfIntroduction}
      />
      <ItemName text="SNSのリンクをプロフィールに追加する" />
      <SnsLink
        className="!flex-[0_0_auto]"
        snsIcon="/static/img/instagram.png"
        text="Instagram"
        snsIconClassName="![object-fit:unset]"
        initialValue={profileData?.snsLinks?.["Instagram"] || ""}
        onSave={updateSnsLink}
      />
      <SnsLink
        className="!flex-[0_0_auto]"
        snsIcon="/static/img/tiktok.png"
        text="TikTok"
        snsIconClassName="![object-fit:unset]"
        initialValue={profileData?.snsLinks?.["TikTok"] || ""}
        onSave={updateSnsLink}
      />
      <SnsLink
        className="!flex-[0_0_auto]"
        snsIcon="/static/img/x.png"
        snsIconClassName="![object-fit:unset]"
        text="X"
        initialValue={profileData?.snsLinks?.["X"] || ""}
        onSave={updateSnsLink}
      />
      <SnsLink
        className="!flex-[0_0_auto]"
        snsIcon="/static/img/youtube.png"
        text="YouTube"
        snsIconClassName="![object-fit:unset]"
        initialValue={profileData?.snsLinks?.["YouTube"] || ""}
        onSave={updateSnsLink}
      />
      <OtherSns className="!flex-[0_0_auto]" />
      <Footer className="!flex-[0_0_auto] !bg-white pb-[80px]" />
      <RedButtonFooter
        buttonYellowClassName={isNameEmpty || isSaving ? "!justify-center !bg-button-red !bg-opacity-50 hover:!bg-[#DD0F2B] active:!bg-[#DD0F2B]" : "!justify-center !bg-button-red hover:!bg-[#DD0F2B] active:!bg-[#DD0F2B]"}
        divClassName="!text-white"
        hasFrameLeft={false}
        hasFrameRight={false}
        text={isSaving ? "保存中..." : "プロフィールを保存する"}
        onClick={isNameEmpty || isSaving ? undefined : handleSaveProfile}
      />
      
      {/* 保存中のローディングアニメーション */}
      <ProfileSaveLoadingAnimation
        isVisible={isSaving}
        onComplete={handleSaveComplete}
      />
    </div>
  );
};

const ProfileEditPage = (): JSX.Element => {
  return (
    <ProfileProvider>
      <ProfileEditContent />
    </ProfileProvider>
  );
};

export default ProfileEditPage;
