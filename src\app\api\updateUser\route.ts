import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// 共通のPrismaクライアントを使用

// POSTリクエスト - ユーザー情報の更新
export async function POST(request: NextRequest) {
  try {
    // リクエストボディからデータを取得
    const data = await request.json();
    const { 
      user_ID, 
      name, 
      website, 
      email, 
      selfIntroduction, 
      profileImage,
      backgroundImage,
      background_image // 後方互換性のために残す
    } = data;

    console.log("API: updateUser - リクエスト受信:", { 
      user_ID, 
      name, 
      website, 
      email, 
      selfIntroduction,
      profileImage: profileImage ? "存在します" : "存在しません",
      backgroundImage: backgroundImage ? "存在します" : "存在しません",
      background_image: background_image ? "存在します" : "存在しません"
    });

    // ユーザーIDが提供されていない場合はエラー
    if (!user_ID) {
      console.error("API: updateUser - ユーザーIDが提供されていません");
      return NextResponse.json({ error: 'ユーザーIDが必要です' }, { status: 400 });
    }

    // 更新するデータを準備
    const updateData: any = {};

    // 各フィールドが提供されている場合のみ更新データに追加（undefinedやnullは除外）
    if (name !== undefined && name !== null) updateData.name = name;
    if (website !== undefined && website !== null) updateData.contact_url = website;
    if (email !== undefined && email !== null) updateData.contact_email = email;
    if (selfIntroduction !== undefined && selfIntroduction !== null) updateData.self_introduction = selfIntroduction;
    if (profileImage !== undefined && profileImage !== null) updateData.profile_image = profileImage;
    
    // 背景画像の処理（変数名を統一）
    // backgroundImageを優先し、なければbackground_imageを使用
    const finalBackgroundImage = backgroundImage !== undefined ? backgroundImage : background_image;
    
    if (finalBackgroundImage !== undefined && finalBackgroundImage !== null) {
      console.log("API: updateUser - 背景画像を使用:", typeof finalBackgroundImage, finalBackgroundImage.substring ? finalBackgroundImage.substring(0, 50) + "..." : finalBackgroundImage);
      // Prismaの型定義エラーを回避するためにanyを使用
      (updateData as any).background_image = finalBackgroundImage;
    }

    console.log("API: updateUser - 更新データ:", updateData);

    try {
      // データベースのユーザー情報を更新
      const updatedUser = await prisma.user.update({
        where: { user_ID: String(user_ID) },
        data: updateData,
      }) as any; // 型エラーを回避するためにany型を使用

      console.log("API: updateUser - ユーザー情報更新成功:", {
        id: updatedUser.id,
        user_ID: updatedUser.user_ID,
        name: updatedUser.name,
        profile_image: updatedUser.profile_image ? "存在します" : "存在しません",
        background_image: updatedUser.background_image ? "存在します" : "存在しません",
        contact_url: updatedUser.contact_url as string | null,
        contact_email: updatedUser.contact_email as string | null,
        self_introduction: updatedUser.self_introduction as string | null
      });

      // 更新されたユーザー情報を返す
      return NextResponse.json({
        success: true,
        user: {
          id: updatedUser.id,
          user_ID: updatedUser.user_ID,
          name: updatedUser.name,
          profileImage: updatedUser.profile_image as string | null,
          backgroundImage: updatedUser.background_image as string | null,
          website: updatedUser.contact_url as string | null,
          email: updatedUser.contact_email as string | null,
          selfIntroduction: updatedUser.self_introduction as string | null
        }
      });
    } catch (dbError: any) {
      console.error("API: updateUser - データベース更新エラー:", dbError);
      // Prismaエラーの詳細をログ出力
      if (dbError.code) {
        console.error("API: updateUser - Prismaエラーコード:", dbError.code);
      }
      if (dbError.meta) {
        console.error("API: updateUser - Prismaエラーメタ情報:", dbError.meta);
      }
      return NextResponse.json({ 
        error: 'データベース更新エラー', 
        message: dbError.message,
        code: dbError.code
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error("API: updateUser - リクエスト処理エラー:", error);
    return NextResponse.json({ 
      error: 'ユーザー情報の更新に失敗しました',
      message: error.message 
    }, { status: 500 });
  }
}
