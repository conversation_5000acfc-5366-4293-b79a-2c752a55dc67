'use client';

import React, { useEffect, useState } from "react";
import { useUser, fetchUserData } from "../../contexts/UserContext";
import { usePathname } from 'next/navigation';

interface Props {
  title: string;
  pageUserId?: string; // ページのユーザーIDを受け取るプロパティを追加
}

export const TextUserName = ({ title, pageUserId }: Props): JSX.Element => {
  const { userId } = useUser(); // ログイン中のユーザーID
  const pathname = usePathname();
  const [name, setName] = useState<string>(''); // ユーザー名用ステート

  useEffect(() => {
    const fetchName = async () => {
      // ページのユーザーIDが指定されていればそれを使用
      // 指定がなければログインユーザーIDを使用
      // それもなければURLからユーザーIDを抽出
      let targetUserId = pageUserId || userId;
      
      // どちらもない場合は、URLからユーザーIDを抽出する
      if (!targetUserId && pathname) {
        // /mypageRelease/[user_ID] 形式のURLからユーザーIDを抽出
        const match = pathname.match(/\/mypageRelease\/([^\/]+)/);
        if (match && match[1]) {
          targetUserId = match[1];
        }
      }

      if (!targetUserId) {
        // ユーザーIDが取得できなかった場合の処理
        return;
      }

      // ユーザー名を取得中
      const userData = await fetchUserData(targetUserId);
      if (userData && userData.user_name) {
        setName(userData.user_name);
      } else if (userData && userData.name) {
        // 後方互換性のためにnameプロパティもチェック
        setName(userData.name);
      }
    };

    fetchName();
  }, [userId, pageUserId, pathname]);

  return (
    <div className={'inline-flex gap-[6px]'}>
      <div className="font-bold text-[18px] w-fit mt-[-1.00px] text-white leading-[14px]">
        {name}
      </div>
      <div className="font-normal text-[14px] relative w-fit mt-[-1.00px] text-white leading-[14px]">
        のおすすめ
      </div>
    </div>
  );
};
