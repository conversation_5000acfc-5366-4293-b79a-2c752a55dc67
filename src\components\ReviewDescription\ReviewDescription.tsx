/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
  text: any;
}

export const ReviewDescription = ({
  className,
  text = "あああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああああ",
}: Props): JSX.Element => {
  return (
    <div className={`flex flex-col w-[390px] items-start gap-[10px] pt-[16px] pb-0 px-[16px] relative ${className}`}>
      <div className="relative w-[358px] mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-black text-[14px] tracking-[0] leading-[16.8px]">
        {text}
      </div>
    </div>
  );
};
