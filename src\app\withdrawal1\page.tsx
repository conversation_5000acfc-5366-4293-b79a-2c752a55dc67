'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Footer } from '../../components/Footer/Footer';
import { ImageUserFrame } from '../../components/ImageUserFrame/ImageUserFrame';
import { TextUserUrlSimple } from '../../components/TextUserUrlSimple/TextUserUrlSimple';

export default function WithdrawalStep1Page() {
  const router = useRouter();
  const [password, setPassword] = useState('');
  const [userDomain, setUserDomain] = useState('');
  const [profileImage, setProfileImage] = useState('/static/img/profile-default.png');
  
  // ユーザー情報を取得
  useEffect(() => {
    // APIからユーザー情報を取得する
    // 実際の実装では、ここで適切なAPIエンドポイントを呼び出す
    const fetchUserData = async () => {
      try {
        // 例: const response = await fetch('/api/user/profile');
        // const data = await response.json();
        // setUserDomain(data.customUrl);
        // setProfileImage(data.profileImage || '/static/img/default-avatar.png');
        
        // 実際のAPIが実装されるまでは、ローカルストレージやセッションから取得するなどの方法も考えられる
        // 例: const domain = localStorage.getItem('userDomain') || '';
        // setUserDomain(domain);
      } catch (error) {
        console.error('Failed to fetch user data:', error);
      }
    };
    
    fetchUserData();
  }, []);
  
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // パスワード検証後、次のステップに進む処理
    // 実際の実装ではAPIとの連携などが必要
    router.push('/withdrawal2');
  };

  const handleBack = () => {
    window.history.back();
  };

  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden" data-component-name="WithdrawalStep1Page">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <div className="flex justify-between max-w-[500px] h-[52px] items-center relative shadow-[0px_0px_4px_#00000040] w-full">
        <div 
          className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative cursor-pointer"
          onClick={handleBack}
        >
          <Image 
            alt="Image left arrow" 
            src="/static/img/imageleftarrow.png" 
            width={30}
            height={30}
            className="relative w-[30px] h-[30px] object-cover"
          />
          <p className="relative flex-[0_0_auto]">退会</p>
        </div>
        <div className="flex w-[52px] h-[52px] items-center justify-end pl-0 pr-[16px] py-0 relative">
          <Image 
            alt="Image question" 
            src="/static/img/imagequestion.png" 
            width={20}
            height={20}
            className="relative w-[20px] h-[20px] object-cover"
          />
        </div>
      </div>

      {/* メインコンテンツ */}
      <div className="w-full flex-1 flex flex-col items-center p-6">
        {/* ユーザー情報 */}
        <div className="mb-8 text-center">
          <div className="flex flex-col items-center">
            <ImageUserFrame profile_image={profileImage} />
            <div className="mt-2">
              <TextUserUrlSimple domain={userDomain} textColor="text-gray-600" />
            </div>
          </div>
        </div>
        
        {/* フォーム */}
        <form onSubmit={handleSubmit} className="w-full max-w-md">
          {/* パスワード入力 */}
          <div className="w-full mb-10">
            <div className="flex max-w-[500px] h-[46px] items-center pr-0 py-0 relative">
              <p className="relative w-fit text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
                <span className="text-[12px] tracking-[0]">パスワードを入力してください</span>
              </p>
            </div>
            <input
              type="password"
              value={password}
              onChange={handlePasswordChange}
              className="w-full border border-gray-300 bg-white/90 rounded p-3 focus:outline-none focus:ring-2 focus:ring-yellow-300"
              required
            />
          </div>
          
          {/* 次へ進むボタン */}
          <div className="flex justify-center">
            <button
              type="submit"
              className="w-full max-w-xs bg-yellow-400 text-black font-medium py-3 rounded-full mb-4 hover:bg-yellow-500 transition duration-200"
            >
              次へ進む
            </button>
          </div>
          
          {/* パスワードを忘れた場合 */}
          <div className="text-center">
            <Link href="/forgot-password" className="text-xs text-gray-400">
              パスワードを忘れた場合はこちら
            </Link>
          </div>
        </form>
      </div>

      {/* フッター */}
      <Footer />
    </div>
  );
}
