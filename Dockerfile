# ベースイメージ
FROM node:18 AS base

# 依存関係をインストールするステージ
FROM base AS deps
WORKDIR /app

# 必要なライブラリをインストール
# OpenSSL 3.0.xをインストール
RUN apt-get update && apt-get install -y openssl

# 依存関係をインストール
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn install --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm install --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# アプリケーションコードをビルドするステージ
FROM base AS builder
WORKDIR /app

# 環境変数をビルド時に設定
ARG DATABASE_URL
ENV DATABASE_URL $DATABASE_URL
ARG GOOGLE_PROJECT_ID
ENV GOOGLE_PROJECT_ID $GOOGLE_PROJECT_ID
ARG GOOGLE_CLIENT_EMAIL
ENV GOOGLE_CLIENT_EMAIL $GOOGLE_CLIENT_EMAIL
ARG GOOGLE_PRIVATE_KEY
ENV GOOGLE_PRIVATE_KEY $GOOGLE_PRIVATE_KEY

# 依存関係とソースコードをコピー
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# テレメトリを無効化したい場合は以下の行をコメント解除
# ENV NEXT_TELEMETRY_DISABLED 1

# Prismaのマイグレーションと生成
RUN npx prisma generate

# Next.jsのビルド
RUN yarn build

# 本番環境のイメージ
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Next.js用のユーザーとグループを作成
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# ビルド成果物をコピー
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next

# Next.jsのスタンドアロンモードで必要なファイルをコピー
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# プリレンダーキャッシュ用のディレクトリを作成し、権限を設定
RUN mkdir -p .next/cache && chown nextjs:nodejs .next/cache

# Next.jsユーザーに切り替え
USER nextjs

# ポートを公開
EXPOSE 3000

# ホスト名を設定
ENV HOSTNAME "0.0.0.0"

# アプリケーションを起動
CMD ["node", "server.js"]
