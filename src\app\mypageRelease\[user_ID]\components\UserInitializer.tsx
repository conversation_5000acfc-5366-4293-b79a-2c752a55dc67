"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useUser } from "../../../../contexts/UserContext";
import { useUserData } from "../hooks/useUserData";
import { useUrlInitialization } from "../hooks/useUrlInitialization";
import type { UserInfo, CategoryType, RankingType } from "../types";

interface UserInitializerProps {
  pageUserId: string;
  onUserDataReady: (data: {
    user: UserInfo | null;
    categories: CategoryType[];
    isOwnPage: boolean;
    domain: string;
  }) => void;
  onError: (error: string) => void;
}

interface UserInitializerReturn {
  isLoading: boolean;
  error: string | null;
}

export const UserInitializer: React.FC<UserInitializerProps> = ({
  pageUserId,
  onUserDataReady,
  onError,
}) => {
  const { userId } = useUser();
  const searchParams = useSearchParams();
  
  // 初期化関連のフラグ
  const [initializedFromUrl, setInitializedFromUrl] = useState(false);
  const [isDragInProgress, setIsDragInProgress] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isOwnPage, setIsOwnPage] = useState(false);
  const [isDev, setIsDev] = useState(false);

  // クライアントサイドでのみ環境変数を設定（Hydration mismatch防止）
  useEffect(() => {
    setIsClient(true);
    setIsDev(process.env.NODE_ENV === "development");
  }, []);

  // Use the useUserData hook to manage user-related state
  const {
    isLoading,
    loadingError,
    user,
    categories,
    getUserInfo,
    setUser,
    setCategories,
    setCategoryID: setCategoryIDFromHook,
    setSelectedCategoryName: setSelectedCategoryNameFromHook,
    setSelectedCategoryIndex: setSelectedCategoryIndexFromHook,
    setSubCategories: setSubCategoriesFromHook,
    setSubcategoryOrder: setSubcategoryOrderFromHook,
    setSubCategory: setSubCategoryFromHook,
  } = useUserData({ isDragInProgress, initializedFromUrl });

  // Wrapper functions to update hook state
  const updateCategoryID = React.useCallback((id: string) => {
    setCategoryIDFromHook(id);
  }, [setCategoryIDFromHook]);

  const updateSelectedCategoryName = React.useCallback((name: string) => {
    setSelectedCategoryNameFromHook(name);
  }, [setSelectedCategoryNameFromHook]);

  const updateSelectedCategoryIndex = React.useCallback((index: number) => {
    setSelectedCategoryIndexFromHook(index);
  }, [setSelectedCategoryIndexFromHook]);

  const updateSubCategories = React.useCallback((subcats: CategoryType[]) => {
    setSubCategoriesFromHook(subcats);
  }, [setSubCategoriesFromHook]);

  const updateSubcategoryOrder = React.useCallback((order: string[]) => {
    setSubcategoryOrderFromHook(order);
  }, [setSubcategoryOrderFromHook]);

  const updateSubCategory = React.useCallback((id: string, fromScroll = false) => {
    setSubCategoryFromHook(id);

    // スクロールからの呼び出しの場合は、サブカテゴリボタンの状態のみ更新
    // （スムーススクロールは実行しない）
  }, [setSubCategoryFromHook]);

  // Use the URL initialization hook
  const { isInitializing, initializationError } = useUrlInitialization({
    categories,
    searchParams,
    initializedFromUrl,
    userId,
    pageUserId,
    updateCategoryID,
    updateSelectedCategoryName,
    updateSelectedCategoryIndex,
    updateSubCategories,
    updateSubcategoryOrder,
    updateSubCategory,
    setRankingsBySubcategory: () => {}, // Will be handled by parent
    setIgnoreNextCategorySelect: () => {}, // Will be handled by parent
    setInitializedFromUrl,
  });

  // クライアントサイドでのみデータ読み込みを実行（Hydration mismatch防止）
  useEffect(() => {
    // クライアントサイドでのみ実行
    if (!isClient) return;

    // URLパラメータから取得したユーザーIDを優先的に使用
    let currentUserId = pageUserId || userId;

    if (currentUserId) {
      // 有効なユーザーIDが取得できたため、APIリクエストを実行
      getUserInfo(currentUserId);
    } else {
      // 有効なユーザーIDが取得できないため、APIリクエストを保留
      onError('有効なユーザーIDが取得できません');
    }
  }, [pageUserId, isClient, userId, getUserInfo, onError]);

  // 開発環境でのログイン状態の初期化
  useEffect(() => {
    if (isDev) {
      const savedIsOwnPage = localStorage.getItem('isOwnPage');
      if (savedIsOwnPage !== null) {
        setIsOwnPage(savedIsOwnPage === 'true');
      }
    }
  }, [isDev]);

  // ユーザーデータが準備できたら親コンポーネントに通知
  useEffect(() => {
    if (user && categories.length > 0 && !isLoading && !isInitializing) {
      const domain = user?.domain || "";
      onUserDataReady({
        user,
        categories,
        isOwnPage,
        domain,
      });
    }
  }, [user, categories, isLoading, isInitializing, isOwnPage, onUserDataReady]);

  // エラーハンドリング
  useEffect(() => {
    if (loadingError) {
      onError(loadingError);
    }
    if (initializationError) {
      onError(initializationError);
    }
  }, [loadingError, initializationError, onError]);

  // エラー状態の場合
  if (loadingError || initializationError) {
    return null; // エラーは親コンポーネントで処理
  }

  // データが準備できている場合は何も表示しない（親コンポーネントが表示を担当）
  return null;
};
