import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // URLパラメータからユーザーIDと親カテゴリIDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');
    const parent_ID = searchParams.get('parent_ID');

    // ユーザーIDと親カテゴリIDは必須
    if (!user_ID || !parent_ID) {
      return NextResponse.json(
        { error: 'ユーザーIDと親カテゴリIDは必須です' },
        { status: 400 }
      );
    }

    // 親カテゴリに紐づくサブカテゴリを取得
    const subCategories = await prisma.category.findMany({
      where: {
        user_ID: user_ID,
        parent_ID: parent_ID
      },
      select: {
        id: true,
        category_name: true,
        category_ID: true,
        created_at: true
      },
      orderBy: {
        order: 'asc'
      }
    });

    // 各サブカテゴリのランキング数を取得
    const subCategoriesWithRankingCount = await Promise.all(
      subCategories.map(async (subCategory) => {
        const rankingCount = await prisma.ranking.count({
          where: {
            user_ID: user_ID,
            subCategory_ID: subCategory.id
          }
        });

        return {
          ...subCategory,
          rankingCount
        };
      })
    );

    // ランキングが存在するサブカテゴリのみをフィルタリング
    const subCategoriesWithRankings = subCategoriesWithRankingCount.filter(
      (subCategory) => subCategory.rankingCount > 0
    );

    return NextResponse.json(subCategoriesWithRankings);
  } catch (error) {
    // サブカテゴリ取得エラーの処理
    return NextResponse.json(
      { error: 'サブカテゴリの取得に失敗しました' },
      { status: 500 }
    );
  }
}
