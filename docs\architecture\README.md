# mypicks.best アーキテクチャ概要

## システム構成

mypicks.bestは、Next.jsを使用したフルスタックWebアプリケーションで、フロントエンドとバックエンドの両方の機能を提供しています。

### 全体構造

```
mypicks.best/
├── src/                  # ソースコード
│   ├── app/              # Next.jsアプリケーション
│   │   ├── api/          # APIエンドポイント
│   │   ├── dao/          # データアクセスオブジェクト
│   │   ├── guide/        # ガイドページ
│   │   ├── models/       # データモデル
│   │   └── pages/        # ページコンポーネント
│   ├── components/       # 再利用可能なコンポーネント
│   ├── contexts/         # Reactコンテキスト
│   ├── lib/              # ユーティリティ関数
│   ├── state/            # 状態管理
│   └── utils/            # ユーティリティ関数
├── prisma/               # Prismaスキーマと関連ファイル
├── public/               # 静的ファイル
├── styles/               # グローバルスタイル
└── backend_api/          # バックエンドAPI仕様
```

## アーキテクチャの特徴

### フロントエンド

- **ページベースのルーティング**: Next.jsのApp Routerを使用
- **コンポーネント指向設計**: 再利用可能なコンポーネントを多用
- **コンテキストベースの状態管理**: UserContextとProfileContextを使用
- **レスポンシブデザイン**: Tailwind CSSによるモバイルファーストの設計

### バックエンド

- **API Routes**: Next.jsのAPI Routesを使用したサーバーサイド機能
- **データアクセス層**: Prisma ORMを使用したデータベースアクセス
- **認証**: Clerkを使用した認証システム
- **ファイル管理**: 画像アップロード・管理機能

### データフロー

1. ユーザーがブラウザでアクションを実行
2. フロントエンドコンポーネントがAPIリクエストを送信
3. Next.js API Routesがリクエストを処理
4. Prisma ORMを通じてデータベースにアクセス
5. レスポンスがフロントエンドに返され、UIが更新される

## 主要なモジュール

### ユーザー管理

- UserContextを使用したユーザー認証状態の管理
- Clerkとの統合による認証機能
- ユーザープロフィール管理

### ランキング管理

- ランキングの作成・編集・削除機能
- カテゴリとサブカテゴリの管理
- ランキングの保存と下書き機能

### SNS連携

- SNSリンクの管理
- SNSマスターデータの管理

## 技術的な意思決定

- **PostgreSQLの採用**: 以前はMongoDBを使用していましたが、リレーショナルデータの管理とSupabaseとの統合のためにPostgreSQLに移行しました。
- **Prisma ORM**: タイプセーフなデータベースアクセスとスキーマ管理のために採用。
- **Clerk認証**: セキュアで柔軟な認証システムを提供するために採用。
- **Tailwind CSS**: 迅速なUIの開発と一貫したデザインシステムのために採用。

## 将来の拡張性

- マイクロサービスアーキテクチャへの移行の可能性
- APIのバージョニング
- パフォーマンス最適化のためのキャッシュ層の導入

## 関連ドキュメント

- [API仕様](../api/README.md)
- [データベース設計](../database/README.md)
- [コンポーネント設計](../components/README.md)

## 最終更新日

2025年4月25日
