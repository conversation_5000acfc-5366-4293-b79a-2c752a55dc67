# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
/.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

*.json
# clerk configuration (can include secrets)
/.clerk/

# Added by <PERSON> Task Master
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Task files
tasks.json
tasks/ 