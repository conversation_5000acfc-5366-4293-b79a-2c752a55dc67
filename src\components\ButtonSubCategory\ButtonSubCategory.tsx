import PropTypes from 'prop-types';
import React, { useEffect, useRef, useState, useMemo } from "react";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable";
import {
  restrictToHorizontalAxis,
  restrictToParentElement,
} from "@dnd-kit/modifiers";
import { SortableSubCategoryTab } from "../SortableSubCategoryTab/SortableSubCategoryTab";


interface SubCategory {
  id: string;
  category_name: string;
  rankingCount?: number;
}

interface Ranking {
  id: string;
  ranking_ID: string;
  ranking_title: string;
  ranking_description?: string;
  thumbnail_image: string[] | string;
  recommend_rate?: number;
  subCategory_ID?: string;
  subcategory_id?: string;
  userId: string;
  domain?: string;
  created_at?: string;
  updated_at?: string;
}

interface Props {
  className?: string;
  onSubCategorySelect: (subCategoryID: string, subCategoryName?: string) => void;
  subcategories?: SubCategory[];
  onResetCategoryState: () => void;
  selectedSubCategoryID?: string;
  userId?: string;
  isOwnPage?: boolean;
  onSubcategoriesReorder?: (newSubcategories: SubCategory[]) => void;
}

export const ButtonSubCategory = ({
  subcategories,
  onSubCategorySelect,
  onResetCategoryState,
  selectedSubCategoryID = "",
  userId = "",
  isOwnPage = false,
  onSubcategoriesReorder
}: Props): JSX.Element => {

  const [localSubcategories, setLocalSubcategories] = useState<SubCategory[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isDragInProgress, setIsDragInProgress] = useState(false);

  // 選択されたサブカテゴリのインデックス（propsから計算）
  const selectedIndex = useMemo(() => {
    if (!selectedSubCategoryID || !localSubcategories.length) return -1;
    const index = localSubcategories.findIndex(sub => sub.id === selectedSubCategoryID);
    return index >= 0 ? index : -1;
  }, [selectedSubCategoryID, localSubcategories]);

  // スクロール用のref
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const tabWrapperRef = useRef<HTMLUListElement | null>(null);
  const tabRefs = useRef<(HTMLLIElement | null)[]>([]);

  // ドラッグ&ドロップ用のセンサー設定
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 重複するuseEffectを削除（下の統合版を使用）
  
  
  /** 選択中のボタンを表示位置に調整する関数 */
  // 左に自動でスライドする挙動をコメントアウト
  /*
  const alignTabToStart = (idx: number) => {
    // インデックスが無効な場合は処理しない
    if (idx < 0 || idx >= filteredSubcategories.length) {
      return;
    }
    
    const wrapper = scrollContainerRef.current;
    if (!wrapper) {
      return;
    }
    
    // 選択されたボタンの位置を取得
    const selectedBtn = tabRefs.current[idx];
    if (!selectedBtn) {
      return;
    }
    
    // 最初のボタンの位置を取得
    const firstBtn = tabRefs.current[0];
    if (!firstBtn) {
      return;
    }
    
    // 選択されたボタンが最初のボタンの位置に来るようにスクロール位置を計算
    const scrollPosition = selectedBtn.offsetLeft - firstBtn.offsetLeft;
    
    // スクロール位置を設定
    wrapper.scrollTo({
      left: scrollPosition,
      behavior: "smooth"
    });
  };
  */

  // DOM操作を削除（メインページのスクロール処理と競合するため）
  // 見出し要素のIDは直接HTMLで設定されるため、ここでの操作は不要

  // 親からのsubcategoriesプロパティの変更を即座に反映（無限ループ完全修正）
  const subcategoriesRef = useRef<any[]>([]);
  const hasInitializedRef = useRef(false);

  // 空配列の無限ループを防ぐため、長さが0の場合は初回のみ実行
  useEffect(() => {
    if (subcategories && subcategories.length > 0) {
      // 深い比較でサブカテゴリが実際に変更されたかチェック
      const subcategoriesChanged =
        subcategoriesRef.current.length !== subcategories.length ||
        subcategoriesRef.current.some((subcat, index) =>
          subcat.id !== subcategories[index]?.id ||
          subcat.category_name !== subcategories[index]?.category_name
        );

      if (subcategoriesChanged) {
        subcategoriesRef.current = subcategories;
        setLocalSubcategories(subcategories);
        // tabRefsの配列を初期化
        tabRefs.current = Array(subcategories.length).fill(null);
        hasInitializedRef.current = true;
      }
    } else if (subcategories && subcategories.length === 0 && !hasInitializedRef.current) {
      // 空配列の場合：初回のみ実行
      subcategoriesRef.current = [];
      setLocalSubcategories([]);
      tabRefs.current = [];
      hasInitializedRef.current = true;
    }
  }, [subcategories]);




  
  // サブカテゴリをクリックしたときの処理（スクロール無効化）
  const handleSubCategoryClick = (
    id: string,
    name: string,
    index: number,
    event?: React.MouseEvent
  ) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }



    // ユーザーの明示的なクリックの場合のみスクロールを実行
    if (event) {
      const targetSection = document.getElementById(`subcat-section-${id}`);
      if (targetSection) {
        const offset = 120; // ナビゲーション高さ分のオフセット
        const top = targetSection.getBoundingClientRect().top + window.pageYOffset - offset;

        // プログラムによるスクロール開始を通知
        (window as any).__isProgrammaticScroll = true;

        window.scrollTo({
          top: Math.max(0, top),
          behavior: 'smooth'
        });

        // スクロール完了後にフラグをリセット
        setTimeout(() => {
          (window as any).__isProgrammaticScroll = false;
        }, 1000); // スムーススクロールの完了を待つ
      }
    } else {
      // スクロールマネージャーからの呼び出しの場合はスクロールしない
    }

    // サブカテゴリ選択状態の更新（親コンポーネントに通知）
    onSubCategorySelect(id);
  };

// ドラッグ開始ハンドラー
const handleDragStart = (event: DragStartEvent) => {
  setActiveId(event.active.id as string);
  setIsDragInProgress(true);
};

// ドラッグ終了ハンドラー
const handleDragEnd = (event: DragEndEvent) => {
  const { active, over } = event;
  setActiveId(null);

  if (!over || active.id === over.id) {
    setIsDragInProgress(false);
    return;
  }

  const oldIndex = localSubcategories.findIndex(subcat => subcat.id === active.id);
  const newIndex = localSubcategories.findIndex(subcat => subcat.id === over.id);

  if (oldIndex !== -1 && newIndex !== -1) {
    // 配列の順序を変更
    const newSubcategories = arrayMove(localSubcategories, oldIndex, newIndex);

    // ローカル状態を更新
    setLocalSubcategories(newSubcategories);

    // ドラッグ状態を即座にリセット
    setIsDragInProgress(false);

    // 親コンポーネントに変更を通知（ランキング表示順序の更新のため）
    // 即座に実行してリアルタイム更新を実現
    if (onSubcategoriesReorder) {
      onSubcategoriesReorder(newSubcategories);
    }

    // サーバーに順序を保存
    saveSubcategoryOrder(newSubcategories);
  } else {
    // インデックスが見つからない場合もドラッグ状態をリセット
    setIsDragInProgress(false);
  }
};

// サブカテゴリ順序をサーバーに保存する関数
const saveSubcategoryOrder = async (subcategories: SubCategory[]) => {
  try {
    const requestData = {
      user_ID: userId,
      categories: subcategories.map((subcat, index) => ({
        id: subcat.id,
        order: index
      }))
    };



    const response = await fetch('/api/updateCategoryOrder', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`サブカテゴリ順序の保存に失敗しました: ${response.status} ${response.statusText}`);
    }

    await response.json();
  } catch (error) {

  }
};
  
  
  




  return (
    <>
      {/* サブカテゴリボタンエリア */}
      <div className="w-full bg-white sticky top-0 z-30" style={{ pointerEvents: 'auto', position: 'relative' }}>
        {/* スクロールはこの DIV で制御 - コンテナの幅は維持しつつ、内部のスクロールを可能に */}
        <div 
          className="max-w-[500px] mx-auto relative overflow-hidden"
        >
          <div
            ref={scrollContainerRef}
            className="overflow-x-auto w-full"
            style={{
              WebkitOverflowScrolling: 'touch',
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              position: 'relative',
              left: '0',
              right: '0'
            }}
          >
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              modifiers={[restrictToHorizontalAxis, restrictToParentElement]}
            >
              <ul
                ref={tabWrapperRef}
                className="flex gap-2 py-4 scrollbar-hide snap-x"
                style={{
                  scrollSnapType: 'x mandatory',
                  width: '9999px', // 非常に広い幅を設定して実質的に無限にする
                  paddingLeft: '0px', // 左側の余白を削除
                  paddingRight: '150px'  // 右側の余白は維持
                }}
              >
                <SortableContext
                  items={localSubcategories.map(subcat => subcat.id)}
                  strategy={horizontalListSortingStrategy}
                >
                  {localSubcategories.map((subCategory, index) => (
                    <SortableSubCategoryTab
                      key={subCategory.id}
                      subCategory={subCategory}
                      index={index}
                      selectedIndex={selectedIndex}
                      onSubCategoryClick={handleSubCategoryClick}
                      isDraggable={isOwnPage}
                    />
                  ))}
                </SortableContext>
              </ul>
            </DndContext>
          </div>
        </div>
      </div>

      {/* ランキングが存在するサブカテゴリのコンテンツのみを表示 */}
      {/* h2タイトルとランキングリストの表示部分を削除 */}
    </>
  );
};

// PropTypes
ButtonSubCategory.propTypes = {
  className: PropTypes.string,
  onSubCategorySelect: PropTypes.func.isRequired,
  subcategories: PropTypes.array,
  onResetCategoryState: PropTypes.func.isRequired,
  selectedSubCategoryID: PropTypes.string,
  userId: PropTypes.string,
  isOwnPage: PropTypes.bool,
  onSubcategoriesReorder: PropTypes.func
};
