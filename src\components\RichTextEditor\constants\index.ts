import { ColorOption } from '../types';

// 色選択のオプション
export const COLOR_OPTIONS: ColorOption[] = [
  // 基本色
  { color: '#000000', name: '黒' },
  { color: '#FFFFFF', name: '白', border: true },
  { color: '#DC2626', name: '赤' },
  { color: '#EA580C', name: '深いオレンジ' },
  { color: '#F59E0B', name: 'オレンジ' },
  { color: '#FBBF24', name: '黄色' },
  { color: '#84CC16', name: '黄緑' },
  { color: '#10B981', name: '緑' },
  { color: '#06B6D4', name: 'ターコイズ' },
  { color: '#0EA5E9', name: '水色' },
  { color: '#3B82F6', name: '青' },
  { color: '#6366F1', name: 'インディゴ' },
  { color: '#8B5CF6', name: 'ライトパープル' },
  { color: '#6D28D9', name: '紫' },
  { color: '#A855F7', name: 'パープル' },
  { color: '#D946EF', name: 'フクシア' },
  { color: '#EC4899', name: 'ホットピンク' },
  { color: '#E63B5F', name: 'ピンク' },
  
  // グレー系
  { color: '#171717', name: '濃い黒' },
  { color: '#404040', name: '濃いグレー' },
  { color: '#4B5563', name: 'グレー' },
  { color: '#6B7280', name: '中間グレー' },
  { color: '#9CA3AF', name: 'ライトグレー' },
  { color: '#E5E7EB', name: '薄いグレー' },
  
  // 特殊色
  { color: '#92400E', name: 'ブラウン' },
  { color: '#78350F', name: '濃い茶色' },
  { color: '#7C2D12', name: '茶色' },
  { color: '#4D7C0F', name: '深い緑' },
  { color: '#065F46', name: '濃い緑' },
];

// サポートされるSNSプラットフォーム
export const SUPPORTED_PLATFORMS = ['instagram', 'tiktok', 'twitter', 'x', 'youtube'] as const;

// 🔧 緊急修正: デバウンスタイマーの設定（無限ループ防止）
export const DEBOUNCE_DELAY = 2000; // 🔧 緊急修正: 大幅に延長
export const EMBED_PROCESS_DELAY = 1000; // 🔧 緊急修正: 大幅に延長
export const SCRIPT_LOAD_DELAY = 1000;
export const EMBED_RELOAD_DELAY = 100;

// 🔧 修正: 重複実行防止の設定（カーソル移動最適化）
export const MIN_EXECUTION_INTERVAL = 500;
export const MIN_EMBED_PROCESS_INTERVAL = 2000; // 🔧 さらに延長してカーソル移動への影響を最小化
export const FLAG_RESET_DELAY = 1500;
export const EMBED_FLAG_RESET_DELAY = 1000; // 🔧 短縮してレスポンス向上

// TikTok埋め込みのサイズ設定
export const TIKTOK_EMBED_WIDTH = '325';
export const TIKTOK_EMBED_HEIGHT = '750';

// API関連の設定
export const API_CACHE_CLEANUP_DELAY = 5000;