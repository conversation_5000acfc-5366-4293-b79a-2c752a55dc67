/**
 * 画像処理のためのユーティリティ関数
 */

/**
 * Base64形式の画像データをBlobに変換する
 * @param base64Data Base64形式の画像データ
 * @returns Blob形式の画像データ
 */
export const base64ToBlob = (base64Data: string): Blob => {
  // Base64のヘッダー部分を削除
  const base64WithoutPrefix = base64Data.includes('base64,')
    ? base64Data.split('base64,')[1]
    : base64Data;

  // Base64をバイナリに変換
  const byteCharacters = atob(base64WithoutPrefix);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);
    const byteNumbers = new Array(slice.length);
    
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  // MIMEタイプを判定
  let contentType = 'image/jpeg';
  if (base64Data.startsWith('data:image/png')) {
    contentType = 'image/png';
  } else if (base64Data.startsWith('data:image/gif')) {
    contentType = 'image/gif';
  }

  return new Blob(byteArrays, { type: contentType });
};

/**
 * 画像パスの正規化（Next.jsの静的ファイル配信の仕組みに合わせる）
 * @param imagePath 画像パス
 * @returns 正規化された画像パス
 */
export const normalizeImagePath = (imagePath: string): string => {
  if (!imagePath || imagePath.trim() === '') {
    return '/static/img/profile-default.png';
  }

  // Blob URLの場合はデフォルト画像を返す
  if (imagePath.startsWith('blob:')) {
    return '/static/img/profile-default.png';
  }

  // Base64データの場合はそのまま返す
  if (imagePath.startsWith('data:image')) {
    return imagePath;
  }

  // パスの正規化（Next.jsの静的ファイル配信の仕組みに合わせる）
  let normalizedPath = imagePath;

  // /static/ から始まる場合はそのまま使用（/public/static/ へのアクセス）
  if (imagePath.startsWith('/static/')) {
    normalizedPath = imagePath;
  }
  // /img/ から始まる場合は /static/img/ に変換
  else if (imagePath.startsWith('/img/')) {
    normalizedPath = `/static${imagePath}`;
  }
  // /uploads/ から始まる場合は /static/uploads/ に変換
  else if (imagePath.startsWith('/uploads/')) {
    normalizedPath = `/static${imagePath}`;
  }
  // http:// または https:// から始まる場合はそのまま使用
  else if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    normalizedPath = imagePath;
  }
  // その他の場合はパスをそのまま使用
  else {
    normalizedPath = imagePath;
  }

  return normalizedPath;
};

/**
 * 画像ファイルをBase64形式に変換する
 * @param file 画像ファイル
 * @returns Promise<string> Base64形式の画像データ
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      resolve(reader.result as string);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * 画像サイズを取得する
 * @param imageUrl 画像URL（Base64形式も可）
 * @returns Promise<{width: number, height: number}> 画像サイズ
 */
export const getImageSize = (imageUrl: string): Promise<{width: number, height: number}> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = reject;
    img.src = imageUrl;
  });
};
