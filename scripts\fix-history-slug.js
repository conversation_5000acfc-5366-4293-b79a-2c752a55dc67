const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixHistorySlug() {
  try {
    const userId = 'user_2zfDuk3M6mp7DkZl27rbEgKJQcD';
    
    // 現在のカテゴリ状況を確認
    const categories = await prisma.category.findMany({
      where: {
        user_ID: userId,
        parent_ID: null
      },
      orderBy: { order: 'asc' }
    });

    console.log('現在のカテゴリ状況:');
    categories.forEach((cat, index) => {
      console.log(`[${index}] ${cat.category_name}`);
      console.log(`    ID: ${cat.id}`);
      console.log(`    category_ID: ${cat.category_ID}`);
      console.log(`    category_slug: ${cat.category_slug}`);
      console.log(`    history_slug: ${cat.history_slug}`);
      console.log(`    order: ${cat.order}`);
      console.log('    ---');
    });

    // 適切なhistory_slugを設定
    // order: 1 → tab1のhistory_slug
    // order: 2 → tab2のhistory_slug
    // order: 3 → tab3のhistory_slug

    for (const category of categories) {
      let expectedHistorySlug = null;

      if (category.order === 1) {
        expectedHistorySlug = 'tab1';
      } else if (category.order === 2) {
        expectedHistorySlug = 'tab2';
      } else if (category.order === 3) {
        expectedHistorySlug = 'tab3';
      }

      if (expectedHistorySlug && category.history_slug !== expectedHistorySlug) {
        console.log(`\n"${category.category_name}" (order: ${category.order}) のhistory_slugを "${expectedHistorySlug}" に設定します...`);

        const updated = await prisma.category.update({
          where: { id: category.id },
          data: { history_slug: expectedHistorySlug }
        });

        console.log('更新完了:', {
          name: updated.category_name,
          order: category.order,
          category_slug: updated.category_slug,
          history_slug: updated.history_slug
        });
      }
    }

    // 最終確認
    console.log('\n=== 最終確認 ===');
    const finalCategories = await prisma.category.findMany({
      where: {
        user_ID: userId,
        parent_ID: null
      },
      orderBy: { order: 'asc' }
    });

    finalCategories.forEach((cat, index) => {
      console.log(`[${index}] ${cat.category_name} (order: ${cat.order})`);
      console.log(`    category_slug: ${cat.category_slug}`);
      console.log(`    history_slug: ${cat.history_slug}`);
      console.log('    ---');
    });

  } catch (error) {
    console.error('エラー:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixHistorySlug();
