/* animations.module.css */
.slideIn {
  animation: slideIn 1s forwards;
}

.slideOut {
  animation: slideOut 1s forwards;
}

.fadeInBackground {
  animation: fadeInBackground 1s forwards;
}

.fadeOutBackground {
  animation: fadeOutBackground 1s forwards;
}

@keyframes slideIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes fadeInBackground {
  from {
    background-color: transparent;
  }
  to {
    background-color: black;
  }
}

@keyframes fadeOutBackground {
  from {
    background-color: black;
  }
  to {
    background-color: transparent;
  }
}
