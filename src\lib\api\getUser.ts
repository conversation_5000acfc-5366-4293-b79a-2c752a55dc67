export async function getUser(userId: string) {
  // ローカル開発環境では常にlocalhost:3000を使用
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';
  const apiUrl = `${baseUrl}/api/getUser?user_ID=${userId}`;

  const response = await fetch(apiUrl);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'ユーザー情報の取得に失敗しました');
  }

  const data = await response.json();
  return data;
}

/**
 * ユーザーのセットアップ完了状態を判定する関数
 * @param userData ユーザーデータオブジェクト
 * @returns セットアップが完了している場合はtrue
 */
export function isUserSetupCompleted(userData: any): boolean {
  if (!userData) {
    return false;
  }

  // セットアップ完了の条件を変更:
  // nameとusernameが両方設定されていれば、setup_completedの値に関係なくセットアップ完了とみなす
  const hasName = userData.name && userData.name.trim() !== '';
  const hasUsername = userData.username && userData.username.trim() !== '';

  // 名前とユーザー名が両方設定されていればセットアップ完了
  const isCompleted = hasName && hasUsername;

  return isCompleted;
}

/**
 * ユーザーのセットアップ状態を取得し、完了状態を判定する関数
 * @param userId ユーザーID
 * @returns セットアップ完了状態とユーザーデータ
 */
export async function checkUserSetupStatus(userId: string): Promise<{
  isCompleted: boolean;
  userData: any;
  username?: string;
}> {
  try {
    const userData = await getUser(userId);
    const isCompleted = isUserSetupCompleted(userData);

    return {
      isCompleted,
      userData,
      username: userData.username
    };
  } catch (error) {
    return {
      isCompleted: false,
      userData: null
    };
  }
}
