# ランキング登録ページのテキスト入力エリア全画面表示機能

## 要件
- テキスト入力エリア（ReviewInputBox）をクリックすると全画面表示になる
- 全画面表示時に「テンプレートを使う」と「SNSの投稿を追加」ボタンは画面最上部に固定表示する
- 全画面表示時に画面右下に「閉じる」ボタンを表示する
- 「閉じる」ボタンをクリックすると元の画面に戻る
- 入力内容は保持されたままにする

## 技術的な実装ポイント
1. ReviewInputBoxコンポーネントに全画面表示モードを追加
   - 全画面表示の状態を管理するためのstateを追加
   - プレースホルダーをクリックしたときに全画面表示に切り替える処理を追加

2. 全画面表示時のレイアウトを実装
   - 全画面表示時のスタイリングを追加
   - 上部ボタンを固定表示するスタイルを追加
   - 右下に閉じるボタンを追加

3. 全画面表示の切り替え機能を実装
   - 閉じるボタンをクリックしたときに元の表示に戻る処理を追加
   - 入力内容を保持したまま表示を切り替える処理を実装

4. RichTextEditorコンポーネントの修正
   - 全画面表示モードに対応するためのプロパティを追加
   - 全画面表示時のスタイリングを調整
