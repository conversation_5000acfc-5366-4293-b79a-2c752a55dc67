/**
 * SNSプラットフォームのスクリプトを動的に読み込むユーティリティ
 */

// スクリプトの読み込み状態を追跡
interface ScriptStatus {
  loaded: boolean;
  callbacks: Array<() => void>;
}

// 読み込み済みスクリプトを追跡するオブジェクト
const loadedScripts: Record<string, ScriptStatus> = {
  instagram: { loaded: false, callbacks: [] },
  twitter: { loaded: false, callbacks: [] },
  tiktok: { loaded: false, callbacks: [] },
};

/**
 * スクリプトを動的に読み込む関数
 * @param src スクリプトのURL
 * @param id スクリプトのID
 * @param callback 読み込み完了時のコールバック
 */
const loadScript = (src: string, id: string, callback: () => void): void => {
  // 既に読み込み済みの場合はコールバックを実行
  if (loadedScripts[id]?.loaded) {
    callback();
    return;
  }

  // 読み込み中の場合はコールバックを追加
  if (loadedScripts[id]) {
    loadedScripts[id].callbacks.push(callback);
    return;
  }

  // 初めての読み込みの場合
  loadedScripts[id] = { loaded: false, callbacks: [callback] };

  // スクリプト要素を作成
  const script = document.createElement('script');
  script.src = src;
  script.async = true;
  script.id = `sns-script-${id}`;

  // 読み込み完了時の処理
  script.onload = () => {
    loadedScripts[id].loaded = true;
    // 全てのコールバックを実行
    loadedScripts[id].callbacks.forEach(cb => cb());
    // コールバックをクリア
    loadedScripts[id].callbacks = [];
  };

  // エラー時の処理
  script.onerror = (error) => {
    console.error(`Error loading ${id} script:`, error);
    // エラーが発生してもコールバックを実行
    loadedScripts[id].callbacks.forEach(cb => cb());
    loadedScripts[id].callbacks = [];
  };

  // DOMに追加
  document.body.appendChild(script);
};

/**
 * Instagramのスクリプトを読み込む
 * @param callback 読み込み完了時のコールバック
 */
export const loadInstagramScript = (callback: () => void = () => {}): void => {
  loadScript('//www.instagram.com/embed.js', 'instagram', () => {
    if (window.instgrm) {
      window.instgrm.Embeds.process();
    }
    callback();
  });
};

/**
 * Twitter(X)のスクリプトを読み込む
 * @param callback 読み込み完了時のコールバック
 */
export const loadTwitterScript = (callback: () => void = () => {}): void => {
  // 既存のスクリプトを削除して再読み込みを確実にする
  const existingScripts = document.querySelectorAll('script[src*="platform.twitter.com/widgets.js"]');
  existingScripts.forEach(script => script.remove());
  
  // スクリプトが読み込まれたか確認する関数
  const checkTwitterScriptLoaded = (attempt = 1, maxAttempts = 10) => {
    if (window.twttr && typeof window.twttr.widgets?.load === 'function') {
      if (process.env.NODE_ENV === 'development') {
        console.log('[X] widgets.load 実行 (試行回数: ' + attempt + ')');
      }

      // 🔧 修正: 個別の埋め込み要素を優先的に処理
      try {
        // XとTwitterの両方のプラットフォーム名をサポート
        const xEmbeds = document.querySelectorAll('[data-platform="x"], [data-platform="twitter"]');
        if (process.env.NODE_ENV === 'development') {
          console.log(`[X] ${xEmbeds.length}個の埋め込み要素を処理します`);
        }

        if (xEmbeds.length > 0) {
          xEmbeds.forEach((embed, index) => {
            if (embed instanceof HTMLElement) {
              if (process.env.NODE_ENV === 'development') {
                console.log(`[X] 埋め込み要素 ${index + 1}/${xEmbeds.length} を処理します:`, embed.getAttribute('data-url'));
              }
              try {
                window.twttr.widgets.load(embed);
              } catch (e) {
                if (process.env.NODE_ENV === 'development') {
                  console.error(`[X] 埋め込み要素 ${index + 1} の処理中にエラーが発生しました:`, e);
                }
              }
            }
          });
        } else {
          // 埋め込み要素がない場合は全体を処理
          try {
            window.twttr.widgets.load();
          } catch (e) {
            if (process.env.NODE_ENV === 'development') {
              console.error('[X] widgets.load 実行エラー:', e);
            }
          }
        }
      } catch (e) {
        if (process.env.NODE_ENV === 'development') {
          console.error('[X] 埋め込み要素の処理中にエラーが発生しました:', e);
        }
      }

      // コールバックを実行
      callback();
    } else if (attempt < maxAttempts) {
      // まだ読み込まれていない場合は再試行
      if (process.env.NODE_ENV === 'development') {
        console.log(`[X] widgets.loadがまだ利用できません。${300 * attempt}ms後に再試行します (${attempt}/${maxAttempts})`);
      }
      setTimeout(() => checkTwitterScriptLoaded(attempt + 1, maxAttempts), 300 * attempt);
    } else {
      // 最大試行回数に達した場合
      if (process.env.NODE_ENV === 'development') {
        console.warn('[X] window.twttr.widgets.load が利用できませんでした。最大試行回数に達しました。');
      }
      callback();
    }
  };
  
  loadScript('https://platform.twitter.com/widgets.js', 'twitter', () => {
    // スクリプト読み込み後、少し待ってからwidgets.loadを確認
    setTimeout(() => checkTwitterScriptLoaded(), 500); // 0.5秒待機してから確認開始
  });
};



/**
 * TikTokのスクリプトを読み込む
 * @param callback 読み込み完了時のコールバック
 */
export const loadTikTokScript = (callback: () => void = () => {}): void => {
  loadScript('https://www.tiktok.com/embed.js', 'tiktok', () => {
    if ((window as any).tiktokEmbedder?.load) {
      (window as any).tiktokEmbedder.load();
    }   
    callback();
  });
};

/**
 * 条件付きで必要なSNSスクリプトのみを読み込む
 */
export const loadRequiredSnsScripts = (): void => {
  // Instagram埋め込みが存在する場合のみ読み込み
  if (document.querySelector('.instagram-media') ||
      document.querySelector('[data-platform="instagram"]') ||
      document.querySelector('blockquote[class*="instagram"]')) {
    loadInstagramScript();
  }

  // Twitter埋め込みが存在する場合のみ読み込み
  if (document.querySelector('.twitter-tweet') ||
      document.querySelector('[data-platform="twitter"]') ||
      document.querySelector('[data-platform="x"]') ||
      document.querySelector('blockquote[class*="twitter"]')) {
    loadTwitterScript();
  }

  // TikTok埋め込みが存在する場合のみ読み込み
  if (document.querySelector('.tiktok-embed') ||
      document.querySelector('[data-platform="tiktok"]') ||
      document.querySelector('blockquote[class*="tiktok"]')) {
    loadTikTokScript();
  }
};

/**
 * 全てのSNSスクリプトを読み込む（後方互換性のため残す）
 * @deprecated loadRequiredSnsScripts() を使用してください
 */
export const loadAllSnsScripts = (): void => {
  loadRequiredSnsScripts();
};

// グローバル型定義の拡張
declare global {
  interface Window {
    instgrm?: {
      Embeds: {
        process: () => void;
      };
    };
    twttr?: {
      widgets: {
        load: (element?: HTMLElement) => void;
      };
      events: {
        bind: (event: string, callback: (e: any) => void) => void;
      };
    };
    tiktokEmbedder?: {
      load: () => void;
    };   
  }
}
