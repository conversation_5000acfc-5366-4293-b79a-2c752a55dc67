# ユーザー関連API

## ユーザー情報取得 API

### エンドポイント

```
GET /api/getUser
```

### 説明

指定されたユーザーIDに基づいてユーザー情報を取得します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明                 |
|--------------|--------|------|----------------------|
| user_ID      | string | はい | 取得するユーザーのID |

### レスポンス

成功時（200 OK）:

```json
{
  "id": "uuid",
  "user_ID": "string",
  "user_name": "string",
  "domain": "string",
  "profile_text": "string",
  "profile_image": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（404 Not Found）:

```json
{
  "error": true,
  "message": "ユーザーが見つかりません"
}
```

### 実装の注意点

- ユーザーIDが存在しない場合は404エラーを返します
- データベースにユーザーIDが存在するにもかかわらず、APIが404エラーを返す問題が過去に発生しました。APIエンドポイントにはデバッグログを追加して問題の診断を容易にしています。

### 使用例

```javascript
// ユーザー情報の取得
const fetchUserData = async (userId) => {
  try {
    const response = await fetch(`/api/getUser?user_ID=${userId}`);
    
    if (!response.ok) {
      throw new Error('ユーザー情報の取得に失敗しました');
    }
    
    return await response.json();
  } catch (error) {
    console.error('エラー:', error);
    return null;
  }
};
```

## <a id="create"></a>ユーザー作成 API

### エンドポイント

```
POST /api/createUser
```

### 説明

新しいユーザーを作成します。

### リクエストボディ

```json
{
  "user_ID": "string",
  "user_name": "string",
  "domain": "string",
  "profile_text": "string",
  "profile_image": "string"
}
```

### レスポンス

成功時（201 Created）:

```json
{
  "id": "uuid",
  "user_ID": "string",
  "user_name": "string",
  "domain": "string",
  "profile_text": "string",
  "profile_image": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（400 Bad Request）:

```json
{
  "error": true,
  "message": "ユーザーの作成に失敗しました"
}
```

## <a id="update"></a>ユーザー更新 API

### エンドポイント

```
PUT /api/updateUser
```

### 説明

既存のユーザー情報を更新します。

### リクエストボディ

```json
{
  "user_ID": "string",
  "user_name": "string",
  "domain": "string",
  "profile_text": "string",
  "profile_image": "string"
}
```

### レスポンス

成功時（200 OK）:

```json
{
  "id": "uuid",
  "user_ID": "string",
  "user_name": "string",
  "domain": "string",
  "profile_text": "string",
  "profile_image": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（404 Not Found）:

```json
{
  "error": true,
  "message": "更新するユーザーが見つかりません"
}
```

## ユーザー一覧取得 API

### エンドポイント

```
GET /api/getUsers
```

### 説明

システム内のすべてのユーザーを取得します。

### パラメータ

なし

### レスポンス

成功時（200 OK）:

```json
[
  {
    "id": "uuid",
    "user_ID": "string",
    "user_name": "string",
    "domain": "string",
    "profile_text": "string",
    "profile_image": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  // ...
]
```

## 実装上の注意点

- 以前は固定値「user123」を使用していましたが、現在は動的にユーザーIDを取得する方法に変更されています
- ユーザーIDの取得は以下の優先順位で行われます:
  1. リクエストボディからユーザーIDを取得
  2. CookieからユーザーIDを取得
  3. UserContext.tsxからユーザーIDを取得

## 関連ファイル

- `src/app/api/getUser/route.ts` - ユーザー取得API
- `src/app/api/createUser/route.ts` - ユーザー作成API
- `src/app/api/updateUser/route.ts` - ユーザー更新API
- `src/app/api/getUsers/route.ts` - ユーザー一覧取得API
- `src/contexts/UserContext.tsx` - ユーザーコンテキスト
- `src/utils/db/getUsers.ts` - ユーザーデータ取得ユーティリティ

## 最終更新日

2025年4月25日
