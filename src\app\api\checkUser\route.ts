import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_ID');



    if (!userId) {
  
      return NextResponse.json(
        { error: 'user_IDは必須パラメータです' },
        { status: 400 }
      );
    }

    // ユーザーが存在するか確認
    const user = await prisma.user.findUnique({
      where: {
        user_ID: userId
      }
    });

    if (!user) {
      return NextResponse.json({ exists: false }, { status: 404 });
    }

    return NextResponse.json({
      exists: true,
      user_ID: user.user_ID
    });
  } catch (error) {

    return NextResponse.json(
      { error: 'ユーザー確認中にエラーが発生しました' },
      { status: 500 }
    );
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
    }
  }
}
