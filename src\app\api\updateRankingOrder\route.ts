import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

// 共通のPrismaクライアントを使用

// POSTリクエスト - ランキング順序の更新
export async function POST(request: NextRequest) {
  try {
    // リクエストボディからデータを取得
    const { rankings, user_ID, subCategory_ID } = await request.json();

    console.log('ランキング順序更新リクエスト:', { user_ID, subCategory_ID, rankings });

    // バリデーション
    if (!user_ID || !subCategory_ID || !Array.isArray(rankings)) {
      console.error('バリデーションエラー:', { user_ID, subCategory_ID, rankings: Array.isArray(rankings) });
      return NextResponse.json({
        error: '必須パラメータが不足しています',
        details: {
          user_ID: !user_ID,
          subCategory_ID: !subCategory_ID,
          rankings: !Array.isArray(rankings)
        }
      }, { status: 400 });
    }

    // ランキング配列の各要素にidフィールドがあることを確認
    const invalidRankings = rankings.filter(ranking => !ranking.id);
    if (invalidRankings.length > 0) {
      console.error('idフィールドが不足しているランキング:', invalidRankings);
      return NextResponse.json({
        error: 'ランキングデータにidフィールドが不足しています',
        invalidCount: invalidRankings.length
      }, { status: 400 });
    }

    // トランザクションで順序を更新
    const updatePromises = rankings.map((ranking, index) => {
      console.log(`更新対象ランキング: id=${ranking.id}, ranking_ID=${ranking.ranking_ID}, 新しい順序=${index + 1}`);

      return prisma.ranking.update({
        where: {
          id: ranking.id // プライマリーキーであるidを使用
        },
        data: {
          order: index + 1 // 1から始まる順序
        }
      });
    });

    const results = await Promise.all(updatePromises);

    console.log('ランキング順序を正常に更新しました:', {
      updatedCount: results.length,
      user_ID,
      subCategory_ID
    });

    return NextResponse.json({
      success: true,
      message: 'ランキング順序が正常に更新されました',
      updatedCount: results.length
    }, { status: 200 });
  } catch (error: any) {
    console.error('ランキング順序更新エラー:', error);

    // Prismaエラーの詳細を取得
    let errorMessage = 'ランキング順序の更新中にエラーが発生しました';
    let errorDetails = '';

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack || '';
    }

    return NextResponse.json({
      error: errorMessage,
      details: errorDetails,
      context: {
        user_ID,
        subCategory_ID,
        rankingCount: rankings?.length || 0
      }
    }, { status: 500 });
  }
}
