'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function ProfileEditGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">プロフィールを設定する方法</h1>
          <div className="pt-6 text-[14px] text-[#313131]">
              <p className="mb-4">プロフィールには、以下8項目を設定できます。</p>
              <p>1.プロフィール画像</p>
              <p>2.プロフィール背景画像</p>
              <p>3.名前</p>
              <p>4.自己紹介文</p>
              <p>5.WebサイトURL</p>
              <p>6.メールアドレス</p>
              <p>7.電話番号</p>
              <p>8.SNSリンク</p>
          </div>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
