"use client";

import React from "react";

interface StarRatingProps {
  rating: number;
}

export const StarRating: React.FC<StarRatingProps> = ({ rating }) => {
  const renderStars = () => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <svg
          key={i}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 26 26"
          fill={i <= rating ? "var(--star-color)" : "var(--star-background-color)"}
          className="w-4 h-4"
        >
          <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
        </svg>
      );
    }
    return stars;
  };

  return (
    <div className="flex pl-[16px] pt-[8px] w-full items-center gap-[0px]">
      <p className="text-[12px] pr-[4px]">おすすめ度</p>
      {renderStars()}
      <p className="pl-[4px] text-[#E63B5F]">{rating}</p>
    </div>
  );
};