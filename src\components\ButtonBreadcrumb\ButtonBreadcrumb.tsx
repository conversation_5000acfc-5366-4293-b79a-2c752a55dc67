/*
We're constantly improving the code you see.
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";

interface Props {
  className?: string;
  category?: string;
  subCategory?: string;
  userID?: string;
}

export const ButtonBreadcrumb = ({ className = "", category = "未分類", subCategory = "未分類", userID }: Props): JSX.Element => {
  const [username, setUsername] = useState<string | null>(null);

  // ユーザー名を取得するuseEffect
  useEffect(() => {
    const fetchUsername = async () => {
      if (userID) {
        try {
          const response = await fetch(`/api/getUser?user_ID=${userID}`);
          if (response.ok) {
            const userData = await response.json();
            setUsername(userData.username);
          }
        } catch (error) {
          console.error('ユーザー名取得エラー:', error);
        }
      }
    };

    fetchUsername();
  }, [userID]);

  const profileUrl = username ? `/${username}` : "/";

  return (
    <div className={`inline-flex items-start gap-[6px] pl-[16px] pr-0 pt-[16px] pb-0 relative ${className}`}>
      <Link href={profileUrl} className="relative w-fit mt-[-1.00px] font-normal text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap">
        TOP
      </Link>
      <div className="relative w-fit mt-[-1.00px] font-normal text-black-1 text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
        &gt;
      </div>
      <Link href={profileUrl} className="relative w-fit mt-[-1.00px] font-normal text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap">
        {category}
      </Link>
      <div className="relative w-fit mt-[-1.00px] font-normal text-black-1 text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
        &gt;
      </div>
      <Link href={profileUrl} className="relative w-fit mt-[-1.00px] font-normal text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap">
        {subCategory}
      </Link>
    </div>
  );
};
