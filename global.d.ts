// global.d.ts
import { PrismaClient } from '@prisma/client';

declare global {
  var prisma: PrismaClient | undefined;

  interface Window {
    instgrm?: {
      Embeds: {
        process: () => void;
      };
    };
    twttr?: {
      widgets: {
        load: (element?: HTMLElement) => void;
      };
      events: {
        bind: (event: string, callback: (e: any) => void) => void;
      };
    };
    TikTok?: {
      embed: {
        reload: () => void;
      };
    };
  }
}

