import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { cookies } from 'next/headers';

const prisma = new PrismaClient();

export async function GET(request: Request) {
  try {
    // URLからクエリパラメータを取得
    const { searchParams } = new URL(request.url);
    
    // リクエストからユーザーIDを取得
    let userId = searchParams.get('userId');
    
    // リクエストパラメータにユーザーIDがない場合はCookieから取得
    if (!userId) {
      const cookieStore = await cookies();
      const userIdCookie = cookieStore.get('userId');
      userId = userIdCookie ? userIdCookie.value : null;
    }
    
    // ユーザーIDが取得できない場合はエラーを返す
    if (!userId) {
      return NextResponse.json(
        { error: 'ユーザーIDが指定されていません。認証が必要です。' },
        { status: 401 }
      );
    }
    
    // カテゴリ名をクエリパラメータから取得
    const categoryParam = searchParams.get('categories');
    
    // カテゴリが指定されていない場合はエラーを返す
    if (!categoryParam) {
      return NextResponse.json(
        { error: 'カテゴリが指定されていません。' },
        { status: 400 }
      );
    }
    
    const categoryNames = categoryParam.split(',');
    
    // 既存のカテゴリを確認
    const existingCategories = await prisma.category.findMany({
      where: {
        user_ID: userId,
        parent_ID: null
      }
    });
    
    console.log(`既存のカテゴリ: ${existingCategories.length}件`);
    
    // 既存のカテゴリ名を取得
    const existingCategoryNames = existingCategories.map(cat => cat.category_name);
    
    // 作成したカテゴリを格納する配列
    const createdCategories = [];
    
    // カテゴリを作成
    for (const categoryName of categoryNames) {
      // 既に同じ名前のカテゴリが存在する場合はスキップ
      if (existingCategoryNames.includes(categoryName)) {
        console.log(`カテゴリ「${categoryName}」は既に存在します`);
        continue;
      }
      
      // ドメインをクエリパラメータから取得、なければデフォルト値
      const domain = searchParams.get('domain') || 'example.com';
      
      // カテゴリを作成
      const newCategory = await prisma.category.create({
        data: {
          user_ID: userId,
          category_ID: `cat_${Date.now()}`, // ユニークなカテゴリIDを生成
          category_name: categoryName,
          parent_ID: null,
          created_at: new Date(),
          updated_at: new Date()
        }
      });
      
      createdCategories.push(newCategory);
      console.log(`カテゴリ「${categoryName}」を作成しました`);
    }
    
    return NextResponse.json({
      message: `${createdCategories.length}件のカテゴリを作成しました`,
      categories: createdCategories
    });
    
  } catch (error) {
    console.error('カテゴリ作成エラー:', error);
    return NextResponse.json(
      { error: 'カテゴリの作成中にエラーが発生しました' },
      { status: 500 }
    );
  }
}
