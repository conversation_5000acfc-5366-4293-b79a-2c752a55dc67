// Type definitions for mypageReleaseDetail component

export interface SavedRankingItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  rating: number;
  savedAt?: string;
}

export interface ProductLinks {
  amazon?: string;
  rakuten?: string;
  yahoo?: string;
  qoo10?: string;
  official?: string;
}

export interface RankingData {
  ranking_ID: string;
  ranking_title: string;
  ranking_description: string;
  thumbnail_image: string | string[];
  images?: string[];
  recommend_rate: number;
  category_name: string;
  category_id: string;
  subcategory_name: string;
  subCategory_ID?: number;
  logo_image_myrank: string;
  amazon_url?: string;
  rakuten_url?: string;
  yahoo_url?: string;
  qoo10_url?: string;
  official_url?: string;
}

export interface UserData {
  name: string;
  profileImage?: string;
}

// SNS embed types are already declared globally in the component
// No need to redeclare them here

// Props for sub-components
export interface RankingContentProps {
  description: string;
  rankingId: string;
}

export interface RankingDetailState {
  ranking: RankingData[] | null;
  selectedThumbnail: string | null;
  currentImageIndex: number;
  showHelpPopup: boolean;
  snsEmbedProcessed: boolean;
  processedRankingId: string | null;
  userData: UserData | null;
  isMounted: boolean;
  error: string | null;
  isLoading: boolean;
  pathAnalyzed: boolean;
}