import React, { useState, useEffect } from "react";
import { useUser } from '@clerk/nextjs';
import type { SideMenuProps } from "../types";

export const SideMenu: React.FC<SideMenuProps> = ({
  isModalOpen,
  handleCloseModal,
  isLoggedIn,
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd,
}) => {
  const { user } = useUser();
  const [username, setUsername] = useState<string>("");

  // ユーザー名を取得
  useEffect(() => {
    const fetchUsername = async () => {
      if (user) {
        try {
          const response = await fetch(`/api/getUser?user_ID=${user.id}`);
          if (response.ok) {
            const userData = await response.json();
            setUsername(userData.username || "");
          }
        } catch (error) {
          console.error('ユーザー名取得エラー:', error);
        }
      }
    };

    fetchUsername();
  }, [user]);
  return (
    <div
      className={`fixed inset-0 z-modal-overlay flex ${
        isModalOpen ? "opacity-100" : "opacity-0 pointer-events-none"
      } transition-opacity duration-300`}
      style={{
        zIndex: 9999,
        position: 'fixed'
      }}
      onClick={handleCloseModal}
    >
      <div
        className={`w-[70%] max-w-[280px] bg-white h-full transform z-modal ${
          isModalOpen ? "translate-x-0" : "-translate-x-full"
        } transition-transform duration-300 ease-out`}
        style={{
          zIndex: 10000,
          position: 'relative'
        }}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className="p-6">
          <img
            src="/static/img/mypicks.best-logo.png"
            alt="mypicks.best"
            className="h-8 w-auto"
          />
        </div>
        <nav className="px-7">
          <ul className="space-y-6">
            {isLoggedIn ? (
              <>
                <li>
                  <a href="/mypageEdit" className="block text-[#313131]">
                    マイページ
                  </a>
                </li>
                <li>
                  <a href={username ? `/${username}/draftList` : "/draftList"} className="block text-[#313131]">
                    下書き一覧
                  </a>
                </li>
                <li>
                  <a href="/saveRank" className="block text-[#313131]">
                    保存した投稿
                  </a>
                </li>
                <li>
                  <a href="/guide" className="block text-[#313131]">
                    使い方ガイド
                  </a>
                </li>
                <li>
                  <a href="/settings" className="block text-[#313131]">
                    設定
                  </a>
                </li>
              </>
            ) : (
              <>
                <li>
                  <a href="/sign-in" className="block text-[#313131]">
                    ログイン
                  </a>
                </li>
                <li>
                  <a href="/sign-up" className="block text-[#313131]">
                    新規登録
                  </a>
                </li>
              </>
            )}
          </ul>
        </nav>
      </div>
      <div
        className="flex-1 bg-black/50 z-modal-overlay"
        style={{
          zIndex: 9999,
          position: 'relative'
        }}
      />
    </div>
  );
};