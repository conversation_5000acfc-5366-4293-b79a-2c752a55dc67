import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from "@prisma/client";

// Prismaクライアントのインスタンスを作成
const prisma = new PrismaClient();

// GETリクエスト - ユーザーのSNSリンクを取得
export async function GET(request: NextRequest) {
  try {
    // リクエストURLからuser_IDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get("user_ID");

    if (!user_ID) {
      return NextResponse.json({ error: 'ユーザーIDが必要です' }, { status: 400 });
    }

    // データベースからユーザーのSNSリンクを取得
    const snsLinks = await prisma.snsUser.findMany({
      where: { user_ID: String(user_ID) },
      include: {
        snsMasterByName: true
      }
    });

    // SNSリンクがない場合は空の配列を返す
    if (!snsLinks || snsLinks.length === 0) {
      return NextResponse.json({ snsLinks: [] }, { status: 200 });
    }

    // フロントエンドで使いやすい形式に変換
    const formattedLinks = snsLinks.map(link => ({
      id: link.id,
      sns_ID: link.sns_ID,
      sns_name: link.sns_name,
      sns_image: link.sns_image,
      account_ID: link.account_ID,
      account_type: link.account_type
    }));

    return NextResponse.json({ snsLinks: formattedLinks }, { status: 200 });
  } catch (error) {
    console.error("SNSリンクの取得中にエラーが発生しました:", error);
    return NextResponse.json({ error: 'SNSリンクの取得に失敗しました' }, { status: 500 });
  }
}

// POSTリクエスト - SNSリンクの作成または更新
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // バリデーション
    if (!data || !data.user_ID || !data.sns_name || !data.account_ID) {
      return NextResponse.json(
        { error: '必須フィールドが不足しています' },
        { status: 400 }
      );
    }
    
    // SNSマスターからSNS情報を取得
    const snsMaster = await prisma.snsMaster.findUnique({
      where: { sns_name: data.sns_name }
    });

    if (!snsMaster) {
      return NextResponse.json(
        { error: '指定されたSNSが見つかりません' },
        { status: 404 }
      );
    }

    // 既存のSNSリンクを確認
    const existingSnsLink = await prisma.snsUser.findFirst({
      where: {
        user_ID: data.user_ID,
        sns_name: data.sns_name
      }
    });

    let result;

    if (existingSnsLink) {
      // 既存のリンクを更新
      result = await prisma.snsUser.update({
        where: { id: existingSnsLink.id },
        data: {
          account_ID: data.account_ID,
          updated_at: new Date()
        }
      });
    } else {
      // 新しいリンクを作成
      result = await prisma.snsUser.create({
        data: {
          sns_ID: snsMaster.sns_ID,
          user_ID: data.user_ID,
          sns_name: data.sns_name,
          sns_image: snsMaster.sns_image,
          account_ID: data.account_ID,
          account_type: data.account_type || null
        }
      });
    }

    return NextResponse.json({ 
      success: true, 
      message: existingSnsLink ? 'SNSリンクを更新しました' : 'SNSリンクを作成しました',
      snsLink: result 
    }, { status: 200 });
  } catch (error) {
    console.error("SNSリンクの保存中にエラーが発生しました:", error);
    return NextResponse.json({ error: 'SNSリンクの保存に失敗しました' }, { status: 500 });
  }
}

// DELETEリクエスト - SNSリンクの削除
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const user_ID = searchParams.get("user_ID");

    if (!id || !user_ID) {
      return NextResponse.json({ error: 'IDとユーザーIDが必要です' }, { status: 400 });
    }

    // 削除前に存在確認とユーザー所有権の確認
    const snsLink = await prisma.snsUser.findUnique({
      where: { id: String(id) }
    });

    if (!snsLink) {
      return NextResponse.json({ error: 'SNSリンクが見つかりません' }, { status: 404 });
    }

    if (snsLink.user_ID !== user_ID) {
      return NextResponse.json({ error: '権限がありません' }, { status: 403 });
    }

    // SNSリンクを削除
    await prisma.snsUser.delete({
      where: { id: String(id) }
    });

    return NextResponse.json({ 
      success: true, 
      message: 'SNSリンクを削除しました' 
    }, { status: 200 });
  } catch (error) {
    console.error("SNSリンクの削除中にエラーが発生しました:", error);
    return NextResponse.json({ error: 'SNSリンクの削除に失敗しました' }, { status: 500 });
  }
}
