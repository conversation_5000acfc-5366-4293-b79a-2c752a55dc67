import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');

    if (!username) {
      return NextResponse.json(
        { error: 'usernameは必須パラメータです' },
        { status: 400 }
      );
    }

    // ユーザー名の形式チェック
    const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/;
    if (!usernameRegex.test(username)) {
      return NextResponse.json({
        available: false,
        error: 'ユーザー名は3-30文字の英数字、アンダースコア、ハイフンのみ使用可能です'
      }, { status: 400 });
    }

    // 予約語チェック
    const reservedUsernames = [
      'admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'root',
      'support', 'help', 'info', 'contact', 'about', 'terms',
      'privacy', 'login', 'signup', 'register', 'dashboard',
      'profile', 'settings', 'account', 'user', 'users',
      'mypageRelease', 'ranking', 'category', 'draft', 'public'
    ];

    if (reservedUsernames.includes(username.toLowerCase())) {
      return NextResponse.json({
        available: false,
        error: 'このユーザー名は予約されています'
      }, { status: 400 });
    }

    // ユーザー名が既に存在するかチェック
    const existingUser = await prisma.user.findUnique({
      where: {
        username: username
      },
      select: {
        username: true
      }
    });

    if (existingUser) {
      return NextResponse.json({
        available: false,
        error: 'このユーザー名は既に使用されています'
      });
    }

    return NextResponse.json({
      available: true,
      message: 'このユーザー名は使用可能です'
    });

  } catch (error) {
    console.error('ユーザー名チェックエラー:', error);
    return NextResponse.json(
      { error: 'ユーザー名確認中にエラーが発生しました' },
      { status: 500 }
    );
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('Prisma disconnect error:', disconnectError);
    }
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
