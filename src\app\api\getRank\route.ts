import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');
    const ranking_ID = searchParams.get('ranking_ID');

    if (!user_ID || !ranking_ID) {
      return NextResponse.json({ error: 'Missing user_ID or ranking_ID' }, { status: 400 });
    }

    // 公開済みランキングデータを取得
    const ranking = await prisma.ranking.findFirst({
      where: {
        user_ID: user_ID,
        ranking_ID: ranking_ID,
        status: 'PUBLISHED' // 公開済みのみ取得
      },
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        thumbnail_image: true,  // 画像データは配列として保存されている
        recommend_rate: true,
        subCategory_ID: true,
        user_ID: true,
        status: true, // ステータスも取得
        created_at: true,
        updated_at: true
      }
    });

    if (!ranking) {
      return NextResponse.json({ error: 'Ranking not found' }, { status: 404 });
    }

    // レスポンスデータを準備
    const responseData = {
      ...ranking,
      category_name: undefined as string | undefined,
      subcategory_name: undefined as string | undefined
    };

    // サブカテゴリIDが存在する場合、カテゴリ情報を取得
    if (ranking.subCategory_ID) {
      try {
        // サブカテゴリ情報を取得
        const subcategory = await prisma.category.findUnique({
          where: {
            id: ranking.subCategory_ID
          },
          select: {
            category_name: true,
            parent_ID: true
          }
        });

        if (subcategory) {
          // サブカテゴリ名をレスポンスに追加
          responseData.subcategory_name = subcategory.category_name;

          // 親カテゴリが存在する場合は親カテゴリ情報も取得
          if (subcategory.parent_ID) {
            const parentCategory = await prisma.category.findUnique({
              where: {
                id: subcategory.parent_ID
              },
              select: {
                category_name: true
              }
            });

            if (parentCategory) {
              // 親カテゴリ名をレスポンスに追加
              responseData.category_name = parentCategory.category_name;
            }
          }
        }
      } catch (categoryError) {
        console.error('Error fetching category information:', categoryError);
        // カテゴリ情報の取得に失敗してもランキングデータは返す
      }
    }

    return NextResponse.json(responseData, { status: 200 });
  } catch (error) {
    console.error('Error fetching ranking:', error);
    return NextResponse.json({ error: 'Failed to fetch ranking' }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
