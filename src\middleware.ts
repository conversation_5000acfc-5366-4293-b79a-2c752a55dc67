import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/sign-out(.*)',
  '/sso-callback(.*)',
  '/createAccount(.*)',
  '/terms(.*)',
  '/privacy(.*)',
  '/static/(.*)',
  '/api/webhook(.*)',
  '/api/webhooks/(.*)',
  '/api/getUserByUsername(.*)',
  '/api/getUser(.*)',
  '/api/getCategories(.*)',
  '/api/getRankings(.*)',
  '/api/profile(.*)',
  '/api/updateCategory(.*)',
  '/api/updateCategoryOrder(.*)',
]);

// Define protected routes that require authentication
const isProtectedRoute = createRouteMatcher([
  '/mypage(.*)',
  '/rankingRegister(.*)',
  '/mypageRelease(.*)',
  // setupページは除外 - ページレベルで認証チェックを行う
]);

export default clerkMiddleware(async (auth, request: NextRequest) => {
  const { pathname } = request.nextUrl;

  // OAuth認証コールバックは最優先で処理（他の処理をスキップ）
  if (pathname.startsWith('/sso-callback')) {
    return NextResponse.next();
  }

  // Skip processing for public routes
  if (isPublicRoute(request)) {
    return NextResponse.next();
  }

  // setupページの特別なアクセス制御
  if (pathname === '/setup') {
    // setupページは認証チェックをページレベルで行うため、middlewareでは常にアクセスを許可
    // OAuth認証フロー中やClerkの認証状態が不安定な場合でもアクセスできるようにする
    return NextResponse.next();
  }

  // 保護されたルートの場合のみ認証チェックを行う（setupページ以外）
  if (isProtectedRoute(request) && pathname !== '/setup') {
    try {
      const { userId: clerkUserId } = auth();

      if (!clerkUserId) {
        // 認証が必要なページで未ログインの場合はサインインページにリダイレクト
        return NextResponse.redirect(new URL('/sign-in', request.url));
      }
    } catch (error) {
      console.error('ミドルウェアでのClerk認証エラー:', error);
      return NextResponse.redirect(new URL('/sign-in', request.url));
    }
  }



  // ユーザー名ページ（/[username]）の場合は特別な処理
  // パスが /[username]/add や /[username]/edit の場合は認証が必要
  const usernamePageMatch = pathname.match(/^\/([^\/]+)(?:\/(.+))?$/);
  if (usernamePageMatch) {
    const [, username, subPath] = usernamePageMatch;

    // 除外するパス（静的ルート）
    const excludedPaths = ['admin', 'api', 'guide', 'legal', 'privacy', 'terms', 'settings', 'sign-in', 'sign-up', 'sign-out', 'createAccount', 'forgot-password', 'change-email', 'change-password', 'withdrawal1', 'withdrawal2', 'test', 'test_api'];

    if (excludedPaths.includes(username)) {
      return NextResponse.next();
    }

    // /[username]/add や /[username]/edit の場合は認証が必要
    // 一時的にミドルウェアでの認証チェックを無効化し、ページレベルで認証チェックを行う
    if (subPath && (subPath.startsWith('add') || subPath.startsWith('edit'))) {
      // 認証チェックをスキップしてページにアクセスを許可
      // 認証チェックはページコンポーネント内で行う
    }

    // その他のユーザー名ページ（/[username]、/[username]/post など）はパブリック
    return NextResponse.next();
  }

  // ルートページ（/）へのアクセス時のリダイレクト処理
  if (pathname === '/') {
    try {
      // Clerkの認証情報を取得
      const { userId: clerkUserId } = auth();

      if (clerkUserId) {
        // ログイン済みの場合、ユーザー名を取得して直接リダイレクト
        try {
          const baseUrl = request.nextUrl.origin;
          const response = await fetch(`${baseUrl}/api/getUser?user_ID=${clerkUserId}`, {
            headers: {
              'Cookie': request.headers.get('cookie') || '',
            },
          });

          if (response.ok) {
            const userData = await response.json();

            // setup_completedフィールドが存在しない場合はtrueとして扱う（既存ユーザー対応）
            const isSetupCompleted = userData.setup_completed !== undefined ? userData.setup_completed : true;

            if (!isSetupCompleted) {
              // セットアップ未完了の場合はsetupページにリダイレクト
              return NextResponse.redirect(new URL('/setup', request.url));
            } else if (userData.username) {
              // セットアップ完了済みでユーザー名が設定されている場合はユーザー名ページにリダイレクト
              return NextResponse.redirect(new URL(`/${userData.username}`, request.url));
            } else {
              // セットアップ完了済みだがユーザー名が設定されていない場合はsetupページにリダイレクト
              return NextResponse.redirect(new URL('/setup', request.url));
            }
          } else if (response.status === 404) {
            // ユーザーが存在しない場合（新規ユーザー）はsetupページにリダイレクト
            return NextResponse.redirect(new URL('/setup', request.url));
          } else {
            // その他のAPI呼び出しエラーの場合はsetupページにリダイレクト
            return NextResponse.redirect(new URL('/setup', request.url));
          }
        } catch (error) {
          console.error('ミドルウェアでのユーザー情報取得エラー:', error);
          // エラーの場合はsetupページにリダイレクト
          return NextResponse.redirect(new URL('/setup', request.url));
        }
      } else {
        // ログインしていない場合はサインインページにリダイレクト
        return NextResponse.redirect(new URL('/sign-in', request.url));
      }
    } catch (error) {
      console.error('ミドルウェアでのClerk認証エラー:', error);
      // Clerk認証エラーの場合はサインインページにリダイレクト
      return NextResponse.redirect(new URL('/sign-in', request.url));
    }
  }

  return NextResponse.next();
});

// Clerk v5 recommended matcher configuration
export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
