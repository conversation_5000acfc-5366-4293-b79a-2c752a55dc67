import { useState, useEffect, useRef } from 'react';
// import { useAuth } from '@clerk/nextjs'; // 一時的に無効化
import { getCookie } from 'cookies-next';
import React from 'react';

/**
 * ユーザー識別のためのカスタムフック
 * 優先順位:
 * 1. Clerkの認証情報
 * 2. <PERSON><PERSON>から取得
 * 3. URLパラメータ
 * 4. URLパスから取得
 */
export function useUserIdentification(params: Promise<{ user_ID?: string }> | { user_ID?: string }) {
  // ユーザーIDの状態
  const [userId, setUserId] = useState<string>('');
  const [userIdFromUrl, setUserIdFromUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userIdFromParams, setUserIdFromParams] = useState<string>('');

  // paramsを安全に処理
  useEffect(() => {
    const resolveParams = async () => {
      try {
        const resolvedParams = params instanceof Promise ? await params : params;
        setUserIdFromParams(resolvedParams?.user_ID || '');
      } catch (error) {
        console.error('パラメータの解決に失敗しました:', error);
        setUserIdFromParams('');
      }
    };

    resolveParams();
  }, [params]);
  
  // 検証済みのユーザーIDをメモリに保存
  const validatedUserIds = useRef<Set<string>>(new Set());
  
  // API呼び出しの状態を追跡
  const isValidatingRef = useRef<boolean>(false);
  
  // Clerkの認証情報を取得（一時的に無効化）
  // const { isLoaded: isAuthLoaded, userId: clerkUserId } = useAuth();
  const isAuthLoaded = true;
  const clerkUserId = null;
  
  // CookieからユーザーIDを取得（メモ化）
  const userIdFromCookie = useRef<string>('');
  useEffect(() => {
    if (typeof window !== 'undefined') {
      userIdFromCookie.current = getCookie('userId')?.toString() || '';
    }
  }, []);
  
  // URLから直接ユーザーIDを取得（初期化時に一度だけ実行）
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const pathSegments = window.location.pathname.split('/');
      const urlUserId = pathSegments[pathSegments.length - 1];
      setUserIdFromUrl(urlUserId);
    }
  }, []); // 空の依存配列で初回のみ実行

  // ユーザーIDの設定と検証
  useEffect(() => {
    // 既に検証中なら処理をスキップ
    if (isValidatingRef.current) return;
    
    const validateUserId = async () => {
      isValidatingRef.current = true;
      setIsLoading(true);
      
      // ユーザーIDの優先順位に従って設定
      const initialUserId = clerkUserId || userIdFromCookie.current || userIdFromParams || userIdFromUrl || '';
      
      // 前回と同じユーザーIDなら処理をスキップ
      if (initialUserId === userId && userId !== '') {
        setIsLoading(false);
        isValidatingRef.current = false;
        return;
      }
      
      // 既に検証済みのユーザーIDなら再検証しない
      if (validatedUserIds.current.has(initialUserId) && initialUserId !== '') {
        setUserId(initialUserId);
        setIsLoading(false);
        isValidatingRef.current = false;
        return;
      }
      
      // 一時的にユーザーIDを設定
      setUserId(initialUserId);
      
      // ユーザーIDが有効か確認する
      if (initialUserId) {
        try {
          // 絶対URLを使用してAPIを呼び出す
          let baseUrl = '';
          if (typeof window !== 'undefined') {
            baseUrl = window.location.origin;
          }
          const apiUrl = `${baseUrl}/api/checkUser?user_ID=${initialUserId}`;
          
          const response = await fetch(apiUrl, {
            // キャッシュを有効化
            cache: 'force-cache'
          });
          
          if (response.ok) {
            const data = await response.json();
            if (data.exists) {
              setUserId(data.user_ID); // データベースの正確なユーザーIDを設定
              validatedUserIds.current.add(data.user_ID); // 検証済みとしてマーク
            }
          }
        } catch (error) {
          console.error('ユーザー確認中にエラーが発生しました:', error);
        }
      }
      
      setIsLoading(false);
      isValidatingRef.current = false;
    };
    
    validateUserId();
    
    // コンポーネントのアンマウント時にフラグをリセット
    return () => {
      isValidatingRef.current = false;
    };
  }, [clerkUserId, userIdFromParams, userIdFromUrl, userId]);
  
  return { userId, isLoading };
}
