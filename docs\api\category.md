# カテゴリ関連API

## カテゴリ一覧取得 API

### エンドポイント

```
GET /api/getCategories
```

### 説明

指定されたユーザーIDに基づいて、そのユーザーが作成したすべてのカテゴリを取得します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明                             |
|--------------|--------|------|----------------------------------|
| user_ID      | string | はい | カテゴリを取得するユーザーのID   |

### レスポンス

成功時（200 OK）:

```json
[
  {
    "id": "uuid",
    "name": "string",
    "parent_ID": null,
    "user_ID": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  // ...
]
```

エラー時（404 Not Found）:

```json
{
  "error": true,
  "message": "カテゴリが見つかりません"
}
```

## <a id="create"></a>カテゴリ作成 API

### エンドポイント

```
POST /api/postCategory
```

### 説明

新しいカテゴリを作成します。親カテゴリIDが指定されている場合はサブカテゴリとして作成されます。

### リクエストボディ

```json
{
  "user_ID": "string",
  "name": "string",
  "parent_ID": "string" // サブカテゴリの場合のみ
}
```

### レスポンス

成功時（201 Created）:

```json
{
  "id": "uuid",
  "name": "string",
  "parent_ID": "string",
  "user_ID": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（400 Bad Request）:

```json
{
  "error": true,
  "message": "カテゴリの作成に失敗しました"
}
```

## <a id="update"></a>カテゴリ更新 API

### エンドポイント

```
PUT /api/updateCategory
```

### 説明

既存のカテゴリ情報を更新します。

### リクエストボディ

```json
{
  "id": "uuid",
  "name": "string",
  "user_ID": "string"
}
```

### レスポンス

成功時（200 OK）:

```json
{
  "id": "uuid",
  "name": "string",
  "parent_ID": "string",
  "user_ID": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（404 Not Found）:

```json
{
  "error": true,
  "message": "更新するカテゴリが見つかりません"
}
```

## <a id="delete"></a>カテゴリ削除 API

### エンドポイント

```
DELETE /api/deleteCategory
```

### 説明

指定されたカテゴリを削除します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明                 |
|--------------|--------|------|----------------------|
| id           | string | はい | 削除するカテゴリのID |

### レスポンス

成功時（200 OK）:

```json
{
  "success": true,
  "message": "カテゴリが正常に削除されました"
}
```

エラー時（404 Not Found）:

```json
{
  "error": true,
  "message": "削除するカテゴリが見つかりません"
}
```

## サブカテゴリ追加 API

### エンドポイント

```
POST /api/addSubcategory
```

### 説明

既存のカテゴリにサブカテゴリを追加します。

### リクエストボディ

```json
{
  "user_ID": "string",
  "parent_ID": "string",
  "name": "string"
}
```

### レスポンス

成功時（201 Created）:

```json
{
  "id": "uuid",
  "name": "string",
  "parent_ID": "string",
  "user_ID": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## サブカテゴリ一覧取得 API

### エンドポイント

```
GET /api/getSubCategories
```

### 説明

指定された親カテゴリIDに基づいて、そのカテゴリに属するすべてのサブカテゴリを取得します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明                                 |
|--------------|--------|------|--------------------------------------|
| parent_ID    | string | はい | サブカテゴリを取得する親カテゴリのID |
| user_ID      | string | はい | カテゴリを取得するユーザーのID       |

### レスポンス

成功時（200 OK）:

```json
[
  {
    "id": "uuid",
    "name": "string",
    "parent_ID": "string",
    "user_ID": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  // ...
]
```

## 実装上の注意点

- Prismaスキーマでは`id`フィールドが主キーとして定義されていますが、以前のコードでは`category_ID`フィールドが使用されていました
- `postCategory`エンドポイントを修正し、Prismaスキーマに存在しない`category_ID`フィールドを削除しました
- サブカテゴリ作成時の親カテゴリの参照方法を修正しました
  - 親カテゴリを作成した後、その自動生成されたIDを取得
  - サブカテゴリの`parent_ID`として親カテゴリの自動生成IDを使用
- `parent_ID`を空文字列ではなく`null`に変更しました（スキーマでは`String?`型）

## 関連ファイル

- `src/app/api/getCategories/route.ts` - カテゴリ一覧取得API
- `src/app/api/postCategory/route.ts` - カテゴリ作成API
- `src/app/api/updateCategory/route.ts` - カテゴリ更新API
- `src/app/api/deleteCategory/route.ts` - カテゴリ削除API
- `src/app/api/addSubcategory/route.ts` - サブカテゴリ追加API
- `src/app/api/getSubCategories/route.ts` - サブカテゴリ一覧取得API

## 最終更新日

2025年4月25日
