# ランキング管理機能

## 概要

ランキング管理機能は、mypicks.bestアプリケーションの中核機能です。ユーザーは様々なカテゴリやサブカテゴリでランキングを作成、編集、削除することができます。

## 機能一覧

- ランキングの作成
- ランキングの編集
- ランキングの削除
- ランキングの一覧表示
- カテゴリ別ランキング表示
- サブカテゴリ別ランキング表示
- ランキングの詳細表示

## ランキング作成機能

### 機能概要

ユーザーは新しいランキングを作成できます。ランキングにはタイトル、説明、カテゴリ、サブカテゴリ、サムネイル画像、ランキングアイテムなどの情報を含めることができます。

### 実装の詳細

ランキング作成時には、以下の処理が行われます：

1. ユーザーがランキング情報（タイトル、説明、カテゴリ、サブカテゴリなど）を入力
2. カテゴリとサブカテゴリの存在確認
   - 存在しない場合は自動的に作成
3. ランキングアイテムの登録
4. サムネイル画像のアップロード（必要に応じて）
5. ランキングデータの保存

### カテゴリ・サブカテゴリの自動登録

ランキング登録時に新しいカテゴリやサブカテゴリを自動的にCategoryテーブルに保存する機能があります：

1. 親カテゴリの存在確認と必要に応じた新規作成
2. サブカテゴリの存在確認と必要に応じた新規作成（parent_IDを設定）
3. 作成したカテゴリのIDをランキングデータに関連付け

### 実装上の注意点

- 以前はPrismaスキーマでは`id`フィールドが主キーとして定義されていましたが、コード内で`category_ID`フィールドが使用されていたため、制約違反エラーが発生していました
- この問題を解決するために、以下の修正が行われました：
  - `updateCategory`エンドポイントを修正し、`category_ID`フィールドを削除して`id`フィールドを使用するように変更
  - `postRank`エンドポイントを修正し、カテゴリ検索・作成時に`category_ID`フィールドを削除
  - ランキング作成時に`category_ID`や`category_name`、`subCategory_name`フィールドを適切に処理するように修正

## ランキング一覧表示機能

### 機能概要

ユーザーは自分が作成したランキングの一覧を表示できます。また、カテゴリやサブカテゴリごとにランキングをフィルタリングして表示することもできます。

### 実装の詳細

ランキング一覧表示では、以下の処理が行われます：

1. ユーザーIDに基づいてランキングデータを取得
2. カテゴリやサブカテゴリでフィルタリング（必要に応じて）
3. ランキングデータの表示（タイトル、サムネイル、カテゴリなど）

### サブカテゴリ表示の改善

サブカテゴリ表示に関して、以下の改善が行われています：

1. h2タイトルの修正
   - 共有ボタン付きのh2タイトルを復元（「未分類のおすすめ」などのテキストが表示される）
   - 各サブカテゴリセクションに見出しとして表示

2. サブカテゴリボタンの赤色表示
   - カテゴリ選択時に最初のサブカテゴリが自動的に選択されるよう修正
   - updateUrlWithCategory関数を拡張してサブカテゴリIDもURLに含めるように変更
   - 選択時のURLパラメータを更新して状態を保持

3. 未分類サブカテゴリの重複表示問題の解決
   - ButtonSubCategoryコンポーネントでサブカテゴリリストをマップする際に「未分類」という名前のサブカテゴリをスキップするように条件を追加

## ランキング詳細表示機能

### 機能概要

ユーザーはランキングの詳細を表示できます。詳細表示では、ランキングのタイトル、説明、カテゴリ、サブカテゴリ、ランキングアイテムなどの情報が表示されます。

### 実装の詳細

ランキング詳細表示では、以下の処理が行われます：

1. ランキングIDに基づいてランキングデータを取得
2. ランキングデータの表示（タイトル、説明、カテゴリ、サブカテゴリ、ランキングアイテムなど）
3. 共有ボタンの表示（Web Share APIを使用）

### サブカテゴリ表示とナビゲーション

mypageReleaseページでは、サブカテゴリを選択すると「サブカテゴリ名のおすすめ」という見出し（h2）を表示し、その横に共有ボタンを配置する実装が行われています：

1. サブカテゴリタブをクリックすると、そのサブカテゴリに関連するランキングを表示
2. 選択されたサブカテゴリ名に「のおすすめ」を追加した見出しを表示
3. 見出しの横に共有ボタンを配置し、Web Share APIを使用して共有機能を実装
4. サブカテゴリを選択しても親カテゴリの情報（「おすすめを追加する」ボタンに表示されるカテゴリ名）が保持されるように実装
5. カテゴリを切り替えたときに、サブカテゴリの選択状態がリセットされるように実装
6. サブカテゴリタブをクリックすると、そのタブが左端に表示されるようにスクロール機能を実装
7. Intersection Observerを使用して、スクロール時に現在表示されている見出しに応じてサブカテゴリボタンの色を変更

## ドラッグ＆ドロップによるサブカテゴリ並べ替え

### 機能概要

mypageReleaseページでは、編集モード時にサブカテゴリをドラッグ＆ドロップで並べ替えられる機能が実装されています。

### 実装の詳細

1. 「表示順を変更する」ボタンをクリックすると編集モードになり、サブカテゴリの並べ替えが可能になる
2. サブカテゴリヘッダーは上下方向のみドラッグ可能（左右のスクロールは無効）
3. 各サブカテゴリは折りたたみ可能で、開閉状態に応じて矢印の向きが変わる（開：上向き、閉：下向き）
4. ドラッグ中は視覚的フィードバック（影、背景色の変更）が表示される

### 実装上のポイント

- @dnd-kit/core, @dnd-kit/sortable, @dnd-kit/modifiersパッケージを使用
- restrictToVerticalAxisモディファイアで左右方向の移動を制限
- transform値のX座標を0に固定して水平方向の移動を防止
- クリックイベントとドラッグイベントを分離し、両方の機能が正しく動作するよう実装

## ローディングアニメーションの改善

### 機能概要

ランキングデータの読み込み中に表示されるローディングアニメーションが改善されました。

### 実装の詳細

1. データ取得フローの改善:
   - `isDataFetched` フラグを追加して、データ取得の完了状態を追跡
   - ローディング表示の条件を `isLoading && !isDataFetched` に変更し、データ取得完了後は一時的なローディング状態でもアニメーションを表示しないように修正
   - 個別のデータ取得関数からローディング状態の管理を一元化

2. ローディングアニメーションのCSSを改善:
   - `position: fixed` と `z-index: 9999` を追加して、ローディングアニメーションが画面全体を覆うように設定
   - `will-change` プロパティを追加して、アニメーションのパフォーマンスを向上
   - 幅と高さを 100% に設定して、画面全体をカバー

これらの変更により、ローディングアニメーションの点滅が解消され、データ取得中に一度だけローディングアニメーションが表示されるようになりました。

## 関連API

- [ランキング関連API](../api/ranking.md)
- [カテゴリ関連API](../api/category.md)

## 関連コンポーネント

- [ButtonMainCategory](../components/buttons.md#buttonmaincategory)
- [ButtonSubCategory](../components/buttons.md#buttonsubcategory)
- [CategorySelect](../components/inputs.md#categoryselect)
- [RichTextEditor](../components/inputs.md#richtexteditor)
- [MypageReleaseButtonFooter](../components/buttons.md#mypagereleasebuttonfooter)
- [RankingRegisterButtonFooter](../components/buttons.md#rankingregisterbuttonfooter)

## 最終更新日

2025年4月25日
