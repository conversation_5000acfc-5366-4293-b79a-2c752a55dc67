import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from "@prisma/client";

// Prismaクライアントのインスタンスを作成
const prisma = new PrismaClient();

// GETリクエスト - プロフィールデータの取得
export async function GET(request: NextRequest) {
  try {
    // Authorizationヘッダーのチェックを削除（クライアントからの直接呼び出しに対応）
    console.log("API: profile - リクエスト受信");
    
    // URLパラメータからユーザーIDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get("user_ID");
    console.log("API: profile - リクエスト受信 - ユーザーID:", user_ID);

    // ユーザーIDが提供されていない場合はエラー
    if (!user_ID) {
      console.error("API: profile - ユーザーIDが提供されていません");
      return NextResponse.json({ error: 'ユーザーIDが必要です' }, { status: 400 });
    }

    // データベースからユーザー情報を取得
    console.log("API: profile - ユーザー情報の取得開始:", user_ID);
    const user = await prisma.user.findUnique({
      where: { user_ID: String(user_ID) },
    });

    if (!user) {
      console.log(`API: profile - ユーザーが見つかりません: ${user_ID}`);
      return NextResponse.json({ 
        website: '',
        email: '',
        profileImage: '/static/img/profile-default.png',
        selfIntroduction: '',
        userName: '',
        snsLinks: {},
        snsOrder: []
      }, { status: 200 });
    }

    console.log("API: profile - ユーザー情報取得成功:", user);
    console.log("API: profile - ユーザーのプロフィール画像:", user.profile_image);

    // SNSリンク情報を取得
    let snsLinksMap: { [key: string]: string } = {};
    let snsOrder: string[] = [];
    
    try {
      // SNSリンク情報を取得（SnsMasterを含む）
      const snsLinks = await prisma.snsUser.findMany({
        where: { user_ID: String(user_ID) },
        include: {
          snsMaster: true
        },
        orderBy: {
          display_order: 'asc'  // display_orderでソート
        }
      });
      
      console.log("API: profile - 取得したSNSリンク:", snsLinks);
      
      // SNSリンクをマッピング
      snsLinks.forEach(link => {
        // snsMasterからsns_nameを取得
        const snsName = link.snsMaster?.sns_name || '';
        if (snsName) {
          snsLinksMap[snsName] = link.account_ID;
          if (!snsOrder.includes(snsName)) {
            snsOrder.push(snsName);
          }
        }
      });

      console.log("API: profile - 順序付けされたSNSリンク:", snsOrder);
    } catch (error) {
      console.error("API: profile - SNSリンク取得エラー:", error);
      // エラーが発生しても処理を続行
    }

    // データベースから取得したユーザー情報をマージ
    const userProfileData = {
      website: user.contact_url || '',
      email: user.contact_email || user.email || '',
      profileImage: user.profile_image || '/static/img/defaultUserIcon.png',
      selfIntroduction: (user as any).self_introduction || '',
      userName: user.name || '',
      snsLinks: snsLinksMap,
      snsOrder: snsOrder
    };

    console.log(`API: profile - ユーザー ${user_ID} のプロフィールデータを取得しました:`, userProfileData);
    console.log(`API: profile - ユーザー ${user_ID} のプロフィール画像パス:`, userProfileData.profileImage);
    return NextResponse.json(userProfileData, { status: 200 });
  } catch (error) {
    console.error("API: profile - プロフィールデータの取得中にエラーが発生しました:", error);
    return NextResponse.json({ 
      website: '',
      email: '',
      profileImage: '/static/img/profile-default.png',
      selfIntroduction: '',
      userName: '',
      snsLinks: {},
      snsOrder: []
    }, { status: 200 });
  }
}

// POSTリクエスト - プロフィールデータの保存
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log("受信したプロフィールデータ:", data);
    
    // バリデーション
    if (!data) {
      return NextResponse.json(
        { error: '無効なデータ形式です' },
        { status: 400 }
      );
    }
    
    // ユーザーIDを取得
    let user_ID = data.user_ID;
    if (!user_ID) {
      // URLパラメータからユーザーIDを取得
      const { searchParams } = new URL(request.url);
      user_ID = searchParams.get("user_ID");
    }

    // ユーザーIDが提供されていない場合はエラー
    if (!user_ID) {
      return NextResponse.json({ error: 'ユーザーIDが必要です' }, { status: 400 });
    }
      
    // ユーザーデータを更新
    const updateData: any = {};
    
    if (data.profileImage) {
      updateData.profile_image = data.profileImage;
    }
    
    if (data.website) {
      updateData.contact_url = data.website;
    }
    
    if (data.email) {
      updateData.contact_email = data.email;
    }
    
    if (data.selfIntroduction) {
      updateData.self_introduction = data.selfIntroduction;
    }
    
    // 更新するデータがある場合のみデータベース更新を実行
    if (Object.keys(updateData).length > 0) {
      await prisma.user.update({
        where: { user_ID: user_ID },
        data: updateData
      });
      
      console.log("ユーザーのプロフィール情報を更新しました:", user_ID);
    }

    // SNSの順序更新の場合（ドラッグ&ドロップ）
    if (data.snsOrder && Array.isArray(data.snsOrder) && data.snsOrder.length > 0 && !data.snsLinks) {
      // 既存のSNSリンクを取得
      const existingSnsLinks = await prisma.snsUser.findMany({
        where: { user_ID: user_ID },
        include: { snsMaster: true }
      });

      // 各SNSリンクの表示順序を更新
      for (const snsLink of existingSnsLinks) {
        if (snsLink.snsMaster && snsLink.snsMaster.sns_name) {
          const snsName = snsLink.snsMaster.sns_name;
          let displayOrder = data.snsOrder.indexOf(snsName);
          if (displayOrder === -1) displayOrder = 999; // 順序が指定されていない場合は末尾に

          // 表示順序を更新
          await prisma.snsUser.update({
            where: { id: snsLink.id },
            data: { display_order: displayOrder }
          });
        }
      }
    }
    // SNSリンクの内容更新（新規追加・削除・変更）
    else if (data.snsLinks && Object.keys(data.snsLinks).length > 0) {
      // 既存のSNSリンクを削除
      await prisma.snsUser.deleteMany({
        where: { user_ID: user_ID }
      });

      // 新しいSNSリンクを追加
      for (const [snsName, accountId] of Object.entries(data.snsLinks)) {
        if (accountId && String(accountId).trim() !== '') {

          // SNSマスターを検索または作成
          let snsMaster = await prisma.snsMaster.findUnique({
            where: { sns_name: snsName }
          });

          if (!snsMaster) {
            snsMaster = await prisma.snsMaster.create({
              data: {
                sns_ID: Math.floor(Math.random() * 1000) + 1, // 一意のIDを生成
                sns_name: snsName,
              } as any
            });
          }

          // SNSの表示順序を決定
          let displayOrder = 0;
          if (data.snsOrder && Array.isArray(data.snsOrder)) {
            displayOrder = data.snsOrder.indexOf(snsName);
            if (displayOrder === -1) displayOrder = 999; // 順序が指定されていない場合は末尾に
          }

          // SNSユーザーリンクを作成
          await prisma.snsUser.create({
            data: {
              user_ID: user_ID,
              sns_master_id: snsMaster.id,
              account_ID: String(accountId),
              display_order: displayOrder
            }
          });
        }
      }
    }

      
    return NextResponse.json(
      { success: true, message: 'プロフィールを保存しました' },
      { status: 200 }
    );
  } catch (error) {
    console.error('プロフィール保存エラー:', error);
    return NextResponse.json(
      { error: 'プロフィールの保存に失敗しました', details: error },
      { status: 500 }
    );
  } finally {
    // Prismaクライアントを切断
    await prisma.$disconnect();
  }
}
