'use client';

import React from 'react';
import { useEffect } from 'react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {

  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white px-4">
      <div className="max-w-[500px] w-full text-center">
        <h2 className="text-xl font-medium text-[#313131] mb-4">エラーが発生しました</h2>
        <p className="text-[#676767] mb-6">申し訳ありませんが、問題が発生しました。</p>
        <button
          onClick={() => reset()}
          className="bg-yellow-400 text-black font-medium py-3 px-6 rounded-full max-w-xs w-full"
        >
          もう一度試す
        </button>
      </div>
    </div>
  );
}
