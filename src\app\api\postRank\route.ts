import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { generateUniqueRankingId } from '@/lib/rankingIdGenerator';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // 必須フィールドの検証
    if (!data.user_ID || !data.ranking_title || !data.thumbnail_image || data.thumbnail_image.length === 0) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // ユーザー情報の取得（domainを取得するため）
    const user = await prisma.user.findUnique({
      where: { user_ID: data.user_ID }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // 商品リンクを個別のフィールドとして処理
    const amazonUrl = data.amazon_url || null;
    const rakutenUrl = data.rakuten_url || null;
    const yahooUrl = data.yahoo_url || null;
    const qoo10Url = data.qoo10_url || null;
    const officialUrl = data.official_url || null;
    
    // ランキングIDの生成（新しい短い形式）
    const ranking_ID = await generateUniqueRankingId();
    
    // ── サブカテゴリが Category テーブルに無ければ自動追加 ──
    if (data.subCategory_ID) {
      const exists = await prisma.category.findFirst({
        where: {
          id: data.subCategory_ID, 
          user_ID: data.user_ID,
        },
      });

      if (!exists) {
        await prisma.category.create({
          data: {
            category_ID: data.subCategory_ID,
            category_name: data.subCategory_name || data.subCategory_ID,
            user_ID: data.user_ID,
            parent_ID: data.parent_ID || null,
            order: 0,
          },
        });
      }
    }

    // ── 必須チェック & 安全な変数 ─────────────────────
    if (!data.parent_ID) {
      throw new Error("parent_ID is required");
    }

    const safeParentID = data.parent_ID;
    const safeSubID      = data.subCategory_ID  || safeParentID;
    const safeSubName    = data.subCategory_name || "未分類";

    // ── 同じサブカテゴリ内の最大order値を取得 ─────────────────────────
    const maxOrderResult = await prisma.ranking.aggregate({
      where: {
        user_ID: data.user_ID,
        subCategory_ID: safeSubID,
      },
      _max: {
        order: true,
      },
    });

    const nextOrder = (maxOrderResult._max.order || 0) + 1;

    // ── ランキングデータの保存 ─────────────────────────
    const ranking = await prisma.ranking.create({
      data: {
        id:                  uuidv4(),
        ranking_ID:          ranking_ID,
        user_ID:             data.user_ID,
        thumbnail_image:     data.thumbnail_image,
        recommend_rate:      data.recommend_rate
                           ? parseInt(data.recommend_rate)
                           : null,
        amazon_url:          amazonUrl,
        rakuten_url:         rakutenUrl,
        yahoo_url:           yahooUrl,
        qoo10_url:           qoo10Url,
        official_url:        officialUrl,
        ranking_title:       data.ranking_title,
        ranking_description: data.ranking_description || '',
        subCategory_ID:      safeSubID,
        order:               nextOrder,
      },
    });

    return NextResponse.json({ 
      success: true, 
      ranking_ID: ranking_ID,
      message: 'Ranking created successfully' 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating ranking:', error);
    return NextResponse.json({ error: 'Failed to create ranking' }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
