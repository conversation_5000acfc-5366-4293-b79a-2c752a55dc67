"use client";

import React, { useState, useEffect } from "react";
import { HELP_POPUP_CONTENT } from "../constants";

export const HelpPopup: React.FC = () => {
  const [showPopup, setShowPopup] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      const target = event.target as HTMLElement;
      if (showPopup && !target.closest('.help-popup-container')) {
        setShowPopup(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showPopup]);

  return (
    <div className="relative ml-[4px] help-popup-container">
      <button
        className="w-[20px] h-[20px] bg-gray-200 rounded-full flex items-center justify-center text-gray-600 text-[12px] font-bold cursor-pointer"
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation();
          setShowPopup(!showPopup);
        }}
        aria-label="おすすめ度の説明"
      >
        ?
      </button>

      {showPopup && (
        <div className="absolute top-[30px] right-0 sm:right-auto sm:left-[-120px] w-[280px] bg-white rounded-md shadow-lg p-[16px] z-10 border border-gray-200">
          <div className="absolute top-[-10px] right-[5px] sm:right-auto sm:left-[120px] w-0 h-0 border-l-[10px] border-l-transparent border-r-[10px] border-r-transparent border-b-[10px] border-b-gray-200"></div>
          <div className="absolute top-[-9px] right-[5px] sm:right-auto sm:left-[120px] w-0 h-0 border-l-[10px] border-l-transparent border-r-[10px] border-r-transparent border-b-[10px] border-b-white"></div>
          <h3 className="text-[14px] font-bold mb-[8px]">{HELP_POPUP_CONTENT.title}</h3>
          <ul className="text-[12px]">
            {HELP_POPUP_CONTENT.ratings.map((rating, index) => (
              <li key={rating.level} className={`flex items-start ${index < HELP_POPUP_CONTENT.ratings.length - 1 ? 'mb-[4px]' : ''}`}>
                <span className="font-bold mr-[4px] whitespace-nowrap">星{rating.level}：</span>
                <span>{rating.description}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};