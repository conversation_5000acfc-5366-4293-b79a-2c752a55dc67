import React, { useState } from "react";
import { useUser } from "@/contexts/UserContext";

interface CategorySelectProps {
  className?: string;
  category?: string;
  subCategory: string;
  // 親カテゴリIDを受け取るためのprop
  parentCategoryId?: string;
  // 親にカテゴリを反映するためのハンドラ
  changeCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
  changeSubCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const CategorySelect: React.FC<CategorySelectProps> = ({
  className,
  category = "",
  subCategory,
  parentCategoryId,
  changeCategory,
  changeSubCategory,
}) => {
  // UserContextからユーザーIDを取得
  const { userId } = useUser();
  // ▼ モーダルの開閉管理
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ▼ 既存カテゴリのリスト（最初に「未分類（今は設定しない）」を用意）
  const [categories, setCategories] = useState<string[]>([
    "未分類（今は設定しない）",
  ]);

  // ▼ 新しく追加するカテゴリ名
  const [newCategory, setNewCategory] = useState("");

  // ▼ 編集対象のカテゴリ
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedCategory, setEditedCategory] = useState("");

  // ▼ 「追加」ボタンの活性化判定
  const isAddButtonActive = newCategory.trim() !== "";

  // ▼ カテゴリを追加
  const handleAddCategory = async () => {
    if (newCategory.trim() === "") return;
    
    console.log("新しいサブカテゴリを追加:", newCategory);
    setIsLoading(true);
    setError(null);
    
    try {
      // データベースにサブカテゴリを保存
      if (userId && parentCategoryId) {
        const newSubcategoryId = `new_subcategory_${Date.now()}`;
        console.log(`サブカテゴリ保存リクエスト: ID=${newSubcategoryId}, 名前=${newCategory}, ユーザーID=${userId}, 親ID=${parentCategoryId}`);
        
        // updateCategoryエンドポイントを使用
        const response = await fetch('/api/updateCategory', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: newSubcategoryId, // 一時的なIDを生成（必須パラメータ）
            category_name: newCategory,
            user_ID: userId,
            parent_ID: parentCategoryId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'サブカテゴリの保存に失敗しました');
        }

        const result = await response.json();
        console.log('サブカテゴリの保存結果:', result);
      }
      
      // UIを更新
      setCategories((prev) => [...prev, newCategory]);
      
      // 追加したカテゴリを選択状態にする
      const syntheticEvent = {
        target: { value: newCategory },
      } as unknown as React.ChangeEvent<HTMLInputElement>;
      
      changeCategory(syntheticEvent);
      changeSubCategory(syntheticEvent);
      
      setNewCategory("");
      setIsModalOpen(false);
    } catch (err: any) {
      console.error('サブカテゴリの保存エラー:', err);
      setError(err.message || 'サブカテゴリの保存中にエラーが発生しました');
    } finally {
      setIsLoading(false);
    }
  };

  // ▼ 編集モード開始
  const handleEditCategory = (index: number, currentName: string) => {
    setEditingIndex(index);
    setEditedCategory(currentName);
  };

  // ▼ 編集内容を保存
  const handleSaveCategory = (index: number) => {
    if (editedCategory.trim() === "") {
      // 空の場合は保存せずに何も行わない（またはエラーメッセージを表示するなど）
      return;
    }
    const updatedCategories = [...categories];
    updatedCategories[index] = editedCategory;
    setCategories(updatedCategories);
    setEditingIndex(null);
  };

  // ▼ カテゴリ削除（モーダル表示）
  const handleDeleteCategory = (category: string) => {
    setDeleteTarget(category);
    setIsDeleteModalOpen(true);
  };

  // ▼ 削除確定
  const confirmDeleteCategory = () => {
    if (deleteTarget) {
      setCategories(categories.filter((cat) => cat !== deleteTarget));
      setIsDeleteModalOpen(false);
      setDeleteTarget(null);
    }
  };

  // ▼ サブカテゴリを選択してカテゴリに反映
  const handleSelectCategory = (cat: string) => {
    console.log("サブカテゴリ選択:", cat);
    
    // changeCategoryにイベントっぽく渡す（React.ChangeEvent偽装）
    const syntheticEvent = {
      target: { value: cat },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    // カテゴリとサブカテゴリの両方を更新
    changeCategory(syntheticEvent);
    changeSubCategory(syntheticEvent);
    setIsModalOpen(false);
  };

  return (
    <div
      className={`flex flex-col w-full items-center justify-center gap-2.5 px-4 py-0 relative ${className}`}
      data-component-name="CategorySelect"
    >
      {/* ▼ カテゴリボタン */}
      <div className="flex w-full h-12 items-center relative rounded-sm overflow-hidden border border-solid border-[#DDDDDD]">
        <button
          type="button"
          onClick={() => setIsModalOpen(true)}
          className={`flex w-full bg-[rgba(246,247,248,1)] h-12 items-center justify-start px-4 gap-2.5 text-sm ${subCategory ? 'text-[#313131]' : 'text-[#AAAAAA]'}`}
        >
          {subCategory ? subCategory : "サブカテゴリ未設定"}
        </button>
      </div>

      {/* ▼ モーダル */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-[90%] max-w-[400px] p-4 shadow-lg relative">
            {/* モーダルヘッダー */}
            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <h2 className="text-lg font-semibold">サブカテゴリを選ぶ</h2>
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            {/* カテゴリ一覧 */}
            <div className="mt-0">
              {categories.map((cat, idx) => (
                <div
                  key={idx}
                  className="flex items-center justify-between py-2 border-b border-gray-200"
                >
                  {editingIndex === idx ? (
                    <input
                      type="text"
                      value={editedCategory}
                      onChange={(e) => setEditedCategory(e.target.value)}
                      className="flex-1 bg-[#F6F7F8] px-3 py-2 text-sm outline-none"
                    />
                  ) : (
                    // ② サブカテゴリをクリック可能に（ボタン化）
                    <span
                      onClick={() => handleSelectCategory(cat)}
                      className="text-sm flex-1 cursor-pointer text-[#007CBA]"
                    >
                      {cat}
                    </span>
                  )}

                  {/* 編集ボタン or 保存・削除ボタン */}
                  {editingIndex === idx ? (
                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={() => handleSaveCategory(idx)}
                        className="px-3 py-2 text-sm text-white bg-[#E63B5F]"
                      >
                        保存
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDeleteCategory(cat)}
                        className="px-2 py-1 text-sm  text-white bg-black"
                      >
                        削除
                      </button>
                    </div>
                  ) : (
                    <button
                      type="button"
                      onClick={() => handleEditCategory(idx, cat)}
                      className="px-2 py-1 text-sm text-gray-600 border border-gray-300"
                    >
                      編集
                    </button>
                  )}
                </div>
              ))}
            </div>

            {/* 新しくカテゴリを追加するエリア */}
            <div className="mt-0">
              <p className="text-xs text-gray-400 mt-4 mb-16">
                ※サブカテゴリは自由に「追加・変更・削除」できます
              </p>

              {error && (
                <p className="text-red-500 text-xs mb-2">{error}</p>
              )}

              <div className="flex items-center">
                <input
                  type="text"
                  placeholder="新しくサブカテゴリを追加する"
                  value={newCategory}
                  onFocus={() => {
                    // ① タップした時点で編集モード解除
                    setEditingIndex(null);
                  }}
                  onChange={(e) => setNewCategory(e.target.value)}
                  className="flex-1 bg-[#F6F7F8] px-3 py-3 text-sm outline-none"
                />
                <button
                  type="button"
                  onClick={handleAddCategory}
                  className={`px-3 py-3 text-sm text-white ${
                    isAddButtonActive && !isLoading ? "bg-[#E63B5F]" : "bg-gray-300"
                  }`}
                  disabled={!isAddButtonActive || isLoading}
                >
                  {isLoading ? "保存中..." : "追加"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 削除確認モーダル */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-[90%] max-w-[300px] p-4 shadow-lg relative">
            <p className="text-sm">削除しますか？</p>
            <div className="flex justify-end gap-4 mt-4">
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                className="text-[#007CBA] text-sm"
              >
                キャンセルする
              </button>
              <button
                type="button"
                onClick={confirmDeleteCategory}
                className="text-[#007CBA] text-sm"
              >
                削除する
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
