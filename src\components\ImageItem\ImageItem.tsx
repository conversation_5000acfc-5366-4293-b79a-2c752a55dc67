/* eslint-disable @next/next/no-img-element */
/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React, { useState, useEffect } from "react";
import Image from "next/image";

interface Props {
  images?: string[];
  onSelectImage?: (imageUrl: string) => void;
  selectedImage?: string | null;
}

export const ImageItem = ({ images = [], onSelectImage, selectedImage: propSelectedImage }: Props): JSX.Element => {
  const [selectedImage, setSelectedImage] = useState<string | null>(propSelectedImage || (images.length > 0 ? images[0] : null));

  // 親コンポーネントから選択画像が変更された場合に同期（不要な更新を防ぐ）
  useEffect(() => {
    if (propSelectedImage && propSelectedImage !== selectedImage) {
      setSelectedImage(propSelectedImage);
    }
  }, [propSelectedImage, selectedImage]);

  // 画像をクリックしたときの処理
  const handleImageClick = (image: string) => {
    setSelectedImage(image);
    if (onSelectImage) {
      onSelectImage(image);
    }
  };

  // 表示する画像は最大4枚まで
  const displayImages = images.slice(0, 4);

  return (
    <div className="w-full bg-white">
      {/* サムネイル一覧 */}
      <div className="flex w-full items-center justify-start gap-[8px] px-[16px] py-[16px] pb-[6px] relative overflow-x-auto">
        {displayImages.map((image, index) => (
          <div 
            key={index} 
            className={`min-w-[80px] h-[80px] cursor-pointer ${selectedImage === image ? 'border-[1px] border-blue-500' : 'border border-gray-300'}`}
            onClick={() => handleImageClick(image)}
          >
            <Image
              className="w-full h-full object-cover"
              alt={`Image ${index + 1}`}
              src={image}
              width={80}
              height={80}
              priority={index === 0}
              quality={85}
              sizes="80px"
            />
          </div>
        ))}
        
        {/* 画像が4枚未満の場合の空のスロット表示を削除 */}
      </div>
    </div>
  );
};
