# Production Cleanup Guide

## Debug Code Optimization

### 1. Logging System
- ✅ Created centralized logger utility (`src/utils/logger.ts`)
- ✅ Automatic development/production mode detection
- ✅ Configurable log levels and persistence
- ✅ OAuth-specific logging methods

### 2. Environment-Based Logging
```typescript
// Development: All logs enabled + localStorage persistence
// Production: Only warnings and errors

const logger = new Logger();
logger.debug('Debug message'); // Only in development
logger.error('Error message'); // Always logged
```

### 3. Code Cleanup Recommendations

#### Replace Direct Console Logs
**Before:**
```typescript
console.log('🔍 [SignUp] OAuth認証完了ハッシュ検出:', data);
```

**After:**
```typescript
import { logger } from '@/utils/logger';
logger.debug('OAuth認証完了ハッシュ検出', data, 'SignUp');
```

#### Replace persistentLog Function
**Before:**
```typescript
persistentLog('✅ [OAuth-Start] 新規登録OAuth認証開始');
```

**After:**
```typescript
import { logger } from '@/utils/logger';
logger.oauthStart({ type: '新規登録' });
```

### 4. Files to Update

#### High Priority (Contains extensive debug logging)
- [ ] `src/app/sign-up/[[...sign-up]]/page.tsx`
- [ ] `src/app/sso-callback/page.tsx`
- [ ] `src/app/setup/page.tsx`

#### Medium Priority
- [ ] `src/app/api/users/upsert/route.ts`
- [ ] `src/middleware.ts`

#### Low Priority
- [ ] Other API routes with console.log statements

### 5. Production Environment Variables

Add to `.env.production`:
```
NODE_ENV=production
NEXT_PUBLIC_ENABLE_DEBUG_LOGS=false
```

### 6. Performance Optimizations

#### Remove Development-Only Features
- [ ] Debug log persistence to localStorage
- [ ] Verbose console output
- [ ] Development-only UI elements

#### Optimize Bundle Size
- [ ] Tree-shake unused logging code
- [ ] Minimize debug string literals
- [ ] Remove development dependencies

### 7. Security Considerations

#### Sensitive Data Logging
- ✅ Never log passwords or tokens
- ✅ Sanitize user data in logs
- ✅ Limit personal information exposure

#### Production Monitoring
- [ ] Implement error tracking (Sentry, LogRocket)
- [ ] Set up performance monitoring
- [ ] Configure alerting for critical errors

### 8. Testing After Cleanup

#### Functionality Tests
- [ ] OAuth authentication still works
- [ ] User registration completes
- [ ] Redirects function properly
- [ ] Error handling remains intact

#### Performance Tests
- [ ] Page load times improved
- [ ] Bundle size reduced
- [ ] Memory usage optimized

### 9. Rollback Plan

#### Backup Current Implementation
```bash
git branch backup-debug-implementation
git checkout -b production-cleanup
```

#### Gradual Migration
1. Implement logger utility
2. Update one component at a time
3. Test each change thoroughly
4. Monitor production metrics

### 10. Monitoring and Maintenance

#### Log Analysis
- Set up log aggregation for production
- Monitor error rates and patterns
- Track authentication success rates

#### Regular Cleanup
- Review and remove obsolete debug code
- Update logging levels based on needs
- Optimize performance periodically
