.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed; /* 固定位置に配置 */
  top: 0;
  left: 0;
  width: 100%; /* 画面全体をカバー */
  height: 100%; /* 画面全体をカバー */
  background-color: #FFFFFF;
  padding: 2rem;
  z-index: 9999; /* 最前面に表示 */
}

.loading-gif {
  margin-bottom: 1rem;
}

.loading-text {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.loading-message {
  display: block;
}
