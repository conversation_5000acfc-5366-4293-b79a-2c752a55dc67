import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";
import Script from 'next/script';
import type { Metadata } from 'next';

// SEO設定
export const metadata: Metadata = {
  title: '使い方ガイド',
  description: 'mypicks.bestの使い方を分かりやすく解説。プロフィール設定、おすすめの追加・編集、共有方法など、初心者でも簡単に始められる操作マニュアルをご用意しています。',
  keywords: 'mypicks.best, 使い方, ガイド, マニュアル, プロフィール設定, おすすめ, 共有, 操作方法, 初心者向け',
  openGraph: {
    title: '使い方ガイド｜mypicks.best',
    description: 'mypicks.bestの使い方を分かりやすく解説。プロフィール設定、おすすめの追加・編集、共有方法など、初心者でも簡単に始められる操作マニュアルをご用意しています。',
    url: 'https://mypicks.best/guide',
    siteName: 'mypicks.best',
    images: [
      {
        url: 'https://mypicks.best/static/img/og-guide.png',
        width: 1200,
        height: 630,
        alt: 'mypicks.best 使い方ガイド',
      },
    ],
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '使い方ガイド｜mypicks.best',
    description: 'mypicks.bestの使い方を分かりやすく解説。プロフィール設定、おすすめの追加・編集、共有方法など、初心者でも簡単に始められる操作マニュアルをご用意しています。',
    images: ['https://mypicks.best/static/img/og-guide.png'],
  },
  alternates: {
    canonical: 'https://mypicks.best/guide',
  },
  viewport: 'width=device-width, initial-scale=1',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  }
};

// ガイドアイテムの型定義
interface GuideItem {
  id: string;
  title: string;
  icon: string;
  link: string;
}

// ガイドセクションの型定義
interface GuideSection {
  id: string;
  title: string;
  items: GuideItem[];
}

export default function GuideTopPage() {
  // 構造化データを生成
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "使い方ガイド｜mypicks.best",
    "description": "mypicks.bestの使い方を分かりやすく解説。プロフィール設定、おすすめの追加・編集、共有方法など、初心者でも簡単に始められる操作マニュアルをご用意しています。",
    "url": "https://mypicks.best/guide",
    "mainEntity": {
      "@type": "HowTo",
      "name": "mypicks.bestの使い方",
      "description": "おすすめをまとめて共有できるサービス「mypicks.best」の操作方法を初心者向けに解説",
      "step": [
        {
          "@type": "HowToStep",
          "name": "プロフィール設定",
          "text": "プロフィール情報の編集とSNSリンクの追加方法"
        },
        {
          "@type": "HowToStep",
          "name": "おすすめの編集",
          "text": "おすすめの追加、表示順変更、カテゴリ管理方法"
        },
        {
          "@type": "HowToStep",
          "name": "共有方法",
          "text": "プロフィールやおすすめの共有方法"
        },
        {
          "@type": "HowToStep",
          "name": "他ユーザーのおすすめ確認",
          "text": "他のユーザーのおすすめを保存・閲覧する方法"
        }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "ホーム",
          "item": "https://mypicks.best"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "使い方ガイド",
          "item": "https://mypicks.best/guide"
        }
      ]
    },
    "publisher": {
      "@type": "Organization",
      "name": "mypicks.best",
      "url": "https://mypicks.best"
    }
  };

  // ガイドセクションのデータ
  const guideSections: GuideSection[] = [
    {
      id: 'profile',
      title: 'プロフィールの編集方法',
      items: [
        { id: 'profile-edit', title: 'プロフィールを設定', icon: '⚙️', link: '/guide/profile-edit' },
        { id: 'sns-link', title: 'SNSリンクを追加', icon: '🔗', link: '/guide/sns-link' },
      ]
    },
    {
      id: 'ranking',
      title: 'おすすめの編集方法',
      items: [
        { id: 'create-ranking', title: 'おすすめを追加', icon: '👍', link: '/guide/create-ranking' },
        { id: 'change-order', title: 'おすすめの表示順を変更', icon: '📊', link: '/guide/change-order' },
        { id: 'change-category', title: 'カテゴリを変更・追加・削除', icon: '📁', link: '/guide/change-category' },
        { id: 'change-display', title: 'カテゴリの表示順を変更', icon: '👁️', link: '/guide/change-display' },
      ]
    },
  
    {
      id: 'share',
      title: 'ページの共有方法',
      items: [
        { id: 'share-profile', title: 'プロフィールを共有', icon: '👤', link: '/guide/share-profile' },
        { id: 'share-ranking', title: 'おすすめを共有', icon: '📊', link: '/guide/share-ranking' },
      ]
    },
    {
      id: 'other-user',
      title: '他のユーザーのおすすめを確認する',
      items: [
        { id: 'save-ranking', title: 'おすすめを保存', icon: '🔖', link: '/guide/save-ranking' },
        { id: 'view-saved', title: '保存したおすすめを閲覧', icon: '👁️', link: '/guide/view-saved' },
      ]
    },
    {
      id: 'card',
      title: 'マイランクカード',
      items: [
        { id: 'buy-card', title: '購入する', icon: '💳', link: '/guide/buy-card' },
      ]
    },
    {
      id: 'account',
      title: 'アカウント情報',
      items: [
        { id: 'change-password', title: 'パスワードの変更', icon: '🔒', link: '/guide/change-password' },
        { id: 'change-email', title: 'メールアドレスの変更', icon: '✉️', link: '/guide/change-email' },
      ]
    },
  ];

  return (
    <>
      <Script
        id="guide-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden" data-component-name="GuideTopPage">
        {/* 背景オーバーレイ */}
        <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>

        {/* ヘッダー */}
        <Header title="使い方ガイド" showBackButton={true} />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-10">
          <h1 className="text-[20px] font-bold">使い方ガイド｜mypicks.best</h1>
        </div>
        
        <div className="space-y-8">
          {guideSections.map((section) => (
            <div key={section.id} className="space-y-4">
              {/* セクションタイトル */}
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <h2 className="text-sm font-bold text-[#313131]">{section.title}</h2>
              </div>
              
              {/* ガイドアイテム */}
              <div className="grid grid-cols-2 gap-4">
                {section.items.map((item) => (
                  <Link key={item.id} href={item.link}>
                    <div className="border border-[#DDDDDD] rounded p-4 flex flex-col items-center justify-center h-24 hover:bg-[rgba(246,247,248,0.5)] transition duration-200">
                      <div className="text-2xl mb-2">{item.icon}</div>
                      <div className="text-xs text-center text-[#313131]">{item.title}</div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

        {/* フッター */}
        <Footer className="!flex-[0_0_auto] !bg-white" />
      </div>
    </>
  );
}
