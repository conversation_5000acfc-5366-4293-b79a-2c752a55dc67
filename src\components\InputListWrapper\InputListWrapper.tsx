'use client';

import React, { useEffect, useRef } from "react";
import { ImageCropper } from "../ImageCropper";
import { useImagePicker } from "../hooks/useImagePicker";
import { normalizeImagePath } from "../../utils/imageUtils";

interface InputListWrapperProps {
  className?: string;
  initialImage?: string;
  onBackgroundImageChange?: (imageData: string) => void;
}

/**
 * InputListWrapperコンポーネント
 * 背景画像の編集機能のみを提供するシンプルなコンポーネント
 */
export const InputListWrapper = ({ 
  className, 
  initialImage, 
  onBackgroundImageChange 
}: InputListWrapperProps) => {
  // 背景画像用のuseImagePicker
  const { 
    imgSrc, 
    pickFile,
    fileInputRef,
    handleFileChange,
    showCropper,
    tempImage,
    handleCropComplete,
    handleCropCancel
  } = useImagePicker({ 
    aspect: 9/16, 
    onDone: onBackgroundImageChange,
    defaultSrc: initialImage || "/static/img/imagebackground.png"
  });

  // 前回の初期画像を保持するref
  const prevInitialImageRef = useRef<string | null>(null);
  
  // 初期画像の設定（無限ループを防止するため、実際に画像が変更された場合のみコールバックを呼び出す）
  useEffect(() => {
    console.log("InputListWrapper - 初期画像の確認:", initialImage);
    
    // 初期画像がない場合は何もしない
    if (!initialImage) {
      console.log("InputListWrapper - 初期画像がないため処理をスキップ");
      return;
    }
    
    // 画像パスを正規化
    const normalizedImage = normalizeImagePath(initialImage);
    
    // 前回の画像と比較して同じ場合は何もしない
    if (prevInitialImageRef.current === normalizedImage) {
      console.log("InputListWrapper - 画像が同じため処理をスキップ:", normalizedImage);
      return;
    }
    
    console.log("InputListWrapper - 画像が変更されたため更新:", {
      prev: prevInitialImageRef.current,
      current: normalizedImage
    });
    
    // 現在の画像を保存
    prevInitialImageRef.current = normalizedImage;
    
    // コールバックがある場合のみ呼び出す
    if (onBackgroundImageChange) {
      onBackgroundImageChange(normalizedImage);
    }
  }, [initialImage, onBackgroundImageChange]);

  return (
    <>
      <div
        className={`flex max-w-[500px] justify-between items-center relative bg-white border-b border-line ${className || ''}`}
      >
        <div className="flex max-w-[250px] h-[48px] items-center pl-[16px] pr-0 py-0 relative">
          <div className="relative w-fit text-black-1 text-[14px] tracking-[0] leading-[normal] whitespace-nowrap">
            背景画像
          </div>
        </div>
        <div className="flex w-[52px] h-[48px] items-center justify-end pl-0 pr-[16px] py-0 relative bg-white">
          <button
            type="button"
            onClick={pickFile}
            className="relative w-[30px] h-[30px] p-0 border-0 bg-transparent"
            aria-label="背景画像を変更"
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={imgSrc}
              alt="背景画像"
              className="w-[30px] h-[30px] object-cover rounded"
            />
          </button>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            ref={fileInputRef}
            style={{ display: "none" }}
          />
        </div>
      </div>
      
      {showCropper && tempImage && (
        <ImageCropper 
          image={tempImage}
          aspectRatio={9/16}
          onCropComplete={handleCropComplete}
          onCancel={handleCropCancel}
        />
      )}
    </>
  );
};
