import React from 'react';
import { CategorySelect } from '@/components/CategorySelect';

interface CategorySectionProps {
  category: string;
  subCategory: string;
  setCategory: (category: string) => void;
  setSubCategory: (subCategory: string) => void;
  categoryId: string;
  validationErrors: {[key: string]: boolean};
  showErrors: boolean;
}

/**
 * カテゴリ選択セクションコンポーネント
 */
export const CategorySection: React.FC<CategorySectionProps> = ({
  category,
  subCategory,
  setCategory,
  setSubCategory,
  categoryId,
  validationErrors,
  showErrors
}) => {
  return (
    <div className="py-3">
      <div className="px-4 flex flex-col mb-2">
        <p className="text-[#313131] text-[16px] font-bold">サブカテゴリを選択する <span className="text-xs text-red-500">(必須)</span></p>
        <p className="text-[#313131] text-sm mt-1 text-right">カテゴリ：{category}</p>
      </div>
      <div>
        <div>
          <CategorySelect
            subCategory={subCategory}
            changeCategory={(e: React.ChangeEvent<HTMLInputElement>) => {
              setCategory(e.target.value);
            }}
            changeSubCategory={(e: React.ChangeEvent<HTMLInputElement>) => {
              setSubCategory(e.target.value);
            }}
            parentCategoryId={categoryId}
          />
        </div>
        {validationErrors.subCategory && showErrors && (
          <p className="text-red-500 text-xs mt-1">サブカテゴリを選択してください</p>
        )}
      </div>
    </div>
  );
};
