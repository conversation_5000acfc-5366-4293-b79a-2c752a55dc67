.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.formGroup {
  margin-bottom: 20px;
}

.label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
}

.input, .textarea, .urlInput {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.textarea {
  min-height: 120px;
  resize: vertical;
}

.errorInput {
  border-color: #e63b5f;
}

.errorText {
  color: #e63b5f;
  font-size: 14px;
  margin-top: 5px;
}

.imageUploadContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.imageUploadBox {
  width: 120px;
  height: 120px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.uploadPlaceholder {
  font-size: 32px;
  color: #ccc;
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.fileInput {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.urlLabelContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.helpIconContainer {
  cursor: pointer;
}

.helpIcon {
  font-size: 20px;
  color: #666;
}

.helpPopup {
  position: absolute;
  top: 30px;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  width: 280px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.urlInputGroup {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.urlInputWithIcon {
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
}

.urlIcon {
  font-size: 20px;
  color: #666;
}

.submitButton {
  display: block;
  width: 100%;
  padding: 12px;
  background-color: #e63b5f;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 30px;
  margin-bottom: 30px;
}

.submitButton:hover {
  background-color: #d02e50;
}
