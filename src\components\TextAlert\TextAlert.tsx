/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
}

export const TextAlert = ({ className }: Props): JSX.Element => {
  return (
    <div className={`inline-flex items-center justify-center pt-[16px] pb-0 px-0 relative ${className}`}>
      <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-button-red text-[12px] text-center tracking-[0] leading-[14px] whitespace-nowrap">
        入力に誤りがあるか、登録されていません
      </div>
    </div>
  );
};
