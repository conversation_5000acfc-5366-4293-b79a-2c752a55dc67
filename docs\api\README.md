# mypicks.best API リファレンス

## 概要

mypicks.bestアプリケーションのAPIエンドポイントに関する詳細な仕様書です。このドキュメントでは、各APIエンドポイントの機能、パラメータ、レスポンス形式について説明します。

## 認証

ほとんどのAPIエンドポイントは認証が必要です。認証はClerkを使用して行われ、ユーザーIDはCookieまたはリクエストパラメータから取得されます。

## APIエンドポイント一覧

### ユーザー関連

- [ユーザー取得](./user.md) - ユーザー情報の取得
- [ユーザー作成](./user.md#create) - 新規ユーザーの作成
- [ユーザー更新](./user.md#update) - ユーザー情報の更新

### ランキング関連

- [ランキング取得](./ranking.md) - ランキング情報の取得
- [ランキング作成](./ranking.md#create) - 新規ランキングの作成
- [ランキング更新](./ranking.md#update) - ランキング情報の更新
- [ランキング削除](./ranking.md#delete) - ランキングの削除

### カテゴリ関連

- [カテゴリ取得](./category.md) - カテゴリ情報の取得
- [カテゴリ作成](./category.md#create) - 新規カテゴリの作成
- [カテゴリ更新](./category.md#update) - カテゴリ情報の更新
- [カテゴリ削除](./category.md#delete) - カテゴリの削除

### サブカテゴリ関連

- [サブカテゴリ取得](./subcategory.md) - サブカテゴリ情報の取得
- [サブカテゴリ作成](./subcategory.md#create) - 新規サブカテゴリの作成
- [サブカテゴリ更新](./subcategory.md#update) - サブカテゴリ情報の更新

### SNS関連

- [SNSマスター取得](./sns.md) - SNSマスターデータの取得
- [SNSマスター作成](./sns.md#create) - SNSマスターデータの作成
- [SNSユーザー情報更新](./sns.md#update-user) - ユーザーのSNS情報の更新

### その他

- [画像アップロード](./upload.md) - 画像のアップロード
- [シード](./seed.md) - 初期データの作成

## エラーハンドリング

APIは以下の形式でエラーレスポンスを返します：

```json
{
  "error": true,
  "message": "エラーメッセージ"
}
```

一般的なHTTPステータスコード：

- 200: 成功
- 400: リクエストが不正
- 401: 認証エラー
- 404: リソースが見つからない
- 500: サーバーエラー

## APIの使用例

### JavaScriptでのAPI呼び出し例

```javascript
// ユーザー情報の取得
const fetchUserData = async (userId) => {
  try {
    const response = await fetch(`/api/getUser?user_ID=${userId}`);
    
    if (!response.ok) {
      throw new Error('ユーザー情報の取得に失敗しました');
    }
    
    return await response.json();
  } catch (error) {
    return null;
  }
};
```

## 注意事項

- APIリクエストには適切なユーザーIDを含める必要があります
- 一部のAPIは管理者権限が必要です
- 大量のリクエストはレート制限の対象となる場合があります

## 関連ドキュメント

- [データベース設計](../database/README.md)
- [開発ガイド](../development/README.md)

## 最終更新日

2025年4月25日
