/* eslint-disable @next/next/no-img-element */
import React from "react";

interface Props {
  className?: string;
  domain: string;
  textColor?: string;
}

export const TextUserUrlSimple = ({ className, domain, textColor = "text-white" }: Props): JSX.Element => {
  return (
    <div className={`flex flex-col ${className}`}>
      <div className="inline-flex items-center justify-center pt-[8px] pb-0 px-0 relative">
        <div className={`relative w-fit font-normal ${textColor} text-[11px] tracking-[0] leading-[14px] whitespace-nowrap`}>
          mypicks.best/{domain}
        </div>
      </div>
    </div>
  );
};
