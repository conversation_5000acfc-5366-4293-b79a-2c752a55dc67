import { useState } from 'react';

interface UseLinkManagementProps {
  editorRef: React.RefObject<HTMLDivElement>;
  handleInput: () => void;
}

// URL検証の結果を表す型
type ValidationResult = {
  isValid: boolean;
  message: string;
};

export const useLinkManagement = ({
  editorRef,
  handleInput
}: UseLinkManagementProps) => {
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isLinkRemoveModalOpen, setIsLinkRemoveModalOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [selectedRange, setSelectedRange] = useState<Range | null>(null);
  const [linkToRemove, setLinkToRemove] = useState<HTMLAnchorElement | null>(null);


  // URL変更時の処理（検証メッセージは不要なので単純にURLを設定するだけ）
  const handleLinkUrlChange = (url: string) => {
    setLinkUrl(url);
  };

  // 初期値設定用（検証なし）
  const setLinkUrlWithoutValidation = (url: string) => {
    setLinkUrl(url);
  };

  // リンク機能の処理
  const handleLinkCommand = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      // テキスト未選択時は何もしない
      return;
    }

    const range = selection.getRangeAt(0);
    const selectedText = range.toString();

    if (!selectedText) {
      // テキスト未選択時は何もしない
      return;
    }

    const parentElement = range.commonAncestorContainer.nodeType === 1
      ? range.commonAncestorContainer as HTMLElement
      : range.commonAncestorContainer.parentElement;

    const existingLink = parentElement?.closest('a');

    if (existingLink) {
      setLinkToRemove(existingLink);
      setIsLinkRemoveModalOpen(true);
    } else {
      setSelectedRange(range);
      // 初期値設定
      setLinkUrlWithoutValidation('https://');
      setIsLinkModalOpen(true);
    }
  };

  // リンク追加の処理
  const handleAddLink = () => {
    if (!selectedRange || !linkUrl.trim() || linkUrl === 'https://') {
      // 無効なURL時は何もしない
      return;
    }

    try {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(selectedRange);
      }

      const selectedText = selectedRange.toString();

      const linkElement = document.createElement('a');
      linkElement.href = linkUrl;
      linkElement.target = '_blank';
      linkElement.rel = 'noopener noreferrer';
      linkElement.textContent = selectedText;
      linkElement.style.color = '#1a0dab';
      linkElement.style.textDecoration = 'underline';
      linkElement.style.cursor = 'pointer';

      linkElement.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const url = linkElement.getAttribute('href');
        if (url) {
          window.open(url, '_blank', 'noopener,noreferrer');
        }
      });
      linkElement.setAttribute('data-click-handler-added', 'true');

      selectedRange.deleteContents();
      selectedRange.insertNode(linkElement);

      selection!.removeAllRanges();

      handleInput();

      setIsLinkModalOpen(false);
      setLinkUrlWithoutValidation('');
      setSelectedRange(null);

      if (editorRef.current) {
        editorRef.current.focus();
      }
    } catch (error) {
      // リンク作成エラーは無視
    }
  };

  // リンク解除の処理
  const handleRemoveLink = () => {
    if (!linkToRemove) return;

    try {
      const linkText = linkToRemove.textContent || '';
      const textNode = document.createTextNode(linkText);
      linkToRemove.parentNode?.replaceChild(textNode, linkToRemove);

      handleInput();

      setIsLinkRemoveModalOpen(false);
      setLinkToRemove(null);

      if (editorRef.current) {
        editorRef.current.focus();
      }
    } catch (error) {
      // リンク解除エラーは無視
    }
  };

  // モーダルキャンセル処理
  const handleCancelLink = () => {
    setIsLinkModalOpen(false);
    setLinkUrlWithoutValidation('');
    setSelectedRange(null);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  const handleCancelRemoveLink = () => {
    setIsLinkRemoveModalOpen(false);
    setLinkToRemove(null);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  return {
    // State
    isLinkModalOpen,
    isLinkRemoveModalOpen,
    linkUrl,
    selectedRange,
    linkToRemove,

    // State setters
    setLinkUrl: handleLinkUrlChange,

    // Handlers
    handleLinkCommand,
    handleAddLink,
    handleRemoveLink,
    handleCancelLink,
    handleCancelRemoveLink
  };
};