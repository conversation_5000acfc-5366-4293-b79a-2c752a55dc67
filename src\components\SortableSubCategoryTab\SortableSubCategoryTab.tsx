import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface SubCategory {
  id: string;
  category_name: string;
  rankingCount?: number;
}

interface SortableSubCategoryTabProps {
  subCategory: SubCategory;
  index: number;
  selectedIndex: number;
  onSubCategoryClick: (id: string, name: string, index: number, event?: React.MouseEvent) => void;
  isDraggable?: boolean;
}

export const SortableSubCategoryTab: React.FC<SortableSubCategoryTabProps> = ({
  subCategory,
  index,
  selectedIndex,
  onSubCategoryClick,
  isDraggable = false
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: subCategory.id,
    disabled: !isDraggable,
    animateLayoutChanges: () => true,
    data: {
      type: 'subcategory',
      subCategory,
      index,
    },
  });

  const style = {
    transform: transform ? `translate3d(${transform.x}px, 0px, 0)` : undefined,
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <li
      ref={setNodeRef}
      style={{
        ...style,
        scrollSnapAlign: "start",
        position: "relative",
        zIndex: 20,
        pointerEvents: "auto",
        paddingLeft: 0,
        marginLeft: 0,
      }}
      {...(isDraggable ? { ...attributes, ...listeners } : {})}
      className="list-none flex-shrink-0 flex items-center"
      onClick={(event) => {
        // ドラッグ中の場合はクリックを無視
        if (isDragging) {
          event.preventDefault();
          event.stopPropagation();
          return;
        }

        onSubCategoryClick(subCategory.id, subCategory.category_name, index, event);
      }}
    >
      <button
        type="button"
        className={`relative px-4 py-2 rounded-full text-white text-[12px] flex items-center justify-center tracking-[0] leading-[normal] whitespace-nowrap min-w-[80px] ${
          selectedIndex === index ? "bg-[#E63B5F]" : "bg-[#AAAAAA]"
        }`}
        style={{
          pointerEvents: "auto",
          zIndex: 21,
          position: "relative",
          cursor: isDraggable ? "grab" : "pointer",
        }}
      >
        {subCategory.category_name}
      </button>
    </li>
  );
};
