'use client';

import { useEffect } from 'react';
import { AuthenticateWithRedirectCallback } from '@clerk/nextjs';
import { useAuth, useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';

// 注意: upsertUserToSupabase関数は削除されました
// Clerkのwebhookでデータベース登録が自動的に行われるため、この関数は不要になりました



export default function SSOCallbackPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const router = useRouter();

  useEffect(() => {
    console.log('🔍 [SSO-Callback] ページ初期化:', {
      url: window.location.href,
      timestamp: new Date().toISOString(),
      referrer: document.referrer
    });

    // 認証成功時のデータベース登録処理
    const handleAuthSuccess = async () => {
      if (isLoaded && isSignedIn && user && user.id) {
        console.log('✅ [SSO-Callback] 認証成功検出 - データベース登録処理開始');

        // 重複処理を防ぐ
        const processedKey = `oauth_processed_${user.id}`;
        if (localStorage.getItem(processedKey)) {
          console.log('🔍 [SSO-Callback] 既に処理済み - setupページにリダイレクト');
          router.push('/setup');
          return;
        }

        localStorage.setItem(processedKey, 'true');

        // Clerkのwebhookでデータベース登録が自動的に行われるため、
        // 直接setupページにリダイレクト
        console.log('✅ [SSO-Callback] setupページにリダイレクト（webhook経由でDB登録済み）');
        router.push('/setup');
      }
    };

    // 認証状態が変更されたときに実行
    if (isLoaded) {
      handleAuthSuccess();
    }
  }, [isLoaded, isSignedIn, user, router]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <div className="flex-1 flex items-center justify-center">
        <div className="max-w-[500px] w-full mx-auto flex flex-col min-h-screen bg-white p-14">
          <div className="flex-1 flex items-center justify-center">
            <CatLoadingAnimation message="認証処理中..." />
          </div>

          {/* Clerk標準のOAuth認証コールバック処理 */}
          <AuthenticateWithRedirectCallback />
        </div>
      </div>
    </div>
  );
}




