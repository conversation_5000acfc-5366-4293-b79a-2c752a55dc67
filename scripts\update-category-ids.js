const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateCategoryIds() {
  try {
    console.log('🔄 カテゴリIDの更新を開始します...');

    // 全てのメインカテゴリ（parent_ID が null）を取得
    const mainCategories = await prisma.category.findMany({
      where: {
        parent_ID: null,
        category_ID: {
          startsWith: 'category'
        }
      },
      orderBy: [
        { user_ID: 'asc' },
        { order: 'asc' }
      ]
    });

    console.log(`📊 更新対象のカテゴリ数: ${mainCategories.length}`);

    // ユーザーごとにグループ化
    const categoriesByUser = {};
    mainCategories.forEach(category => {
      if (!categoriesByUser[category.user_ID]) {
        categoriesByUser[category.user_ID] = [];
      }
      categoriesByUser[category.user_ID].push(category);
    });

    // ユーザーごとに処理
    for (const [userId, categories] of Object.entries(categoriesByUser)) {
      console.log(`👤 ユーザー ${userId} のカテゴリを更新中...`);
      
      // orderでソート
      categories.sort((a, b) => (a.order || 0) - (b.order || 0));
      
      // tab1, tab2, ... の形式で更新
      for (let i = 0; i < categories.length; i++) {
        const category = categories[i];
        const newCategoryId = `tab${i + 1}`;
        
        console.log(`  📝 ${category.category_ID} → ${newCategoryId}`);
        
        await prisma.category.update({
          where: { id: category.id },
          data: { category_ID: newCategoryId }
        });
      }
    }

    console.log('✅ カテゴリIDの更新が完了しました！');
  } catch (error) {
    console.error('❌ エラーが発生しました:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// スクリプトを実行
updateCategoryIds();
