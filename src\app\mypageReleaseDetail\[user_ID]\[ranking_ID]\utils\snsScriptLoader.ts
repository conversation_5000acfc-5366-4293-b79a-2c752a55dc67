// Utility functions for loading SNS embed scripts

export const loadSNSScripts = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('📜 [スクリプト] SNSスクリプトの読み込み状況を確認中...');
  }

  // Instagram embed script
  if (!window.instgrm && !document.querySelector('script[src*="instagram.com/embed"]')) {
    const instagramScript = document.createElement('script');
    instagramScript.src = 'https://www.instagram.com/embed.js';
    instagramScript.async = true;
    document.head.appendChild(instagramScript);

    if (process.env.NODE_ENV === 'development') {
      console.log('📱 [スクリプト] Instagram埋め込みスクリプトを動的読み込みしました');
    }
  }

  // Twitter embed script
  if (!window.twttr && !document.querySelector('script[src*="platform.twitter.com"]')) {
    const twitterScript = document.createElement('script');
    twitterScript.src = 'https://platform.twitter.com/widgets.js';
    twitterScript.async = true;
    document.head.appendChild(twitterScript);

    if (process.env.NODE_ENV === 'development') {
      console.log('🐦 [スクリプト] Twitter埋め込みスクリプトを動的読み込みしました');
    }
  }

  // TikTok embed script
  if (!window.TikTokEmbed && !document.querySelector('script[src*="tiktok.com/embed"]')) {
    const tiktokScript = document.createElement('script');
    tiktokScript.src = 'https://www.tiktok.com/embed.js';
    tiktokScript.async = true;
    document.head.appendChild(tiktokScript);

    if (process.env.NODE_ENV === 'development') {
      console.log('🎵 [スクリプト] TikTok埋め込みスクリプトを動的読み込みしました');
    }
  }
};

// Hook for using SNS script loader
export const useSNSScripts = () => {
  if (typeof window !== 'undefined') {
    loadSNSScripts();
  }
};