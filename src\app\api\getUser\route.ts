import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
// import { auth } from "@clerk/nextjs/server"; // 一時的にコメントアウト

// Prismaクライアントのシングルトンパターンを実装
// グローバルスコープの型定義
interface CustomNodeJsGlobal {
  prisma: PrismaClient;
}

// グローバルオブジェクトを拡張
declare global {
  var prisma: PrismaClient | undefined;
}

// Prismaクライアントのインスタンスを作成または取得
const prisma = globalThis.prisma || new PrismaClient();

// 開発環境ではグローバルオブジェクトに保存
if (process.env.NODE_ENV === 'development') {
  globalThis.prisma = prisma;
}

// ローカル開発環境かどうかを判定する関数
function isLocalDevelopment() {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || "";
  return baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1') || !baseUrl;
}

export async function GET(request: NextRequest) {
  try {
    // リクエストURLからuser_IDまたはusernameを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get("user_ID");
    const username = searchParams.get("username");

    if (!user_ID && !username) {
      return NextResponse.json(
        { error: 'user_IDまたはusernameが必要です' },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // ローカル開発環境の場合は認証チェックをスキップ
    if (!isLocalDevelopment()) {
      // 本番環境では認証チェックを行う（現在は一時的に無効化）
      // const { userId } = await auth();
      // if (!userId) {
      //   return new Response(JSON.stringify({ error: '認証されていません' }), {
      //     status: 401,
      //     headers: {
      //       'Content-Type': 'application/json',
      //       'Access-Control-Allow-Origin': '*',
      //       'Access-Control-Allow-Methods': 'GET, OPTIONS',
      //       'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      //     },
      //   });
      // }
    }

    // Prismaを使用してuser_IDまたはusernameでユーザーを検索
    let user;
    if (user_ID) {
      user = await prisma.user.findUnique({
        where: { user_ID: String(user_ID) },
      });
    } else if (username) {
      user = await prisma.user.findUnique({
        where: { username: String(username) },
      });
    }

    if (!user) {
      // ユーザーが見つからない場合は404エラーを返す
      return NextResponse.json(
        { error: 'ユーザーが見つかりませんでした' },
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          },
        }
      );
    }

    // ユーザー情報を返す
    return NextResponse.json(user, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'ユーザー情報の取得中にエラーが発生しました', details: String(error) },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
  // Prisma connection pooling handles disconnection automatically
  // Removing manual disconnect to improve performance
}

// OPTIONSリクエストに対応する関数
export function OPTIONS() {
  return NextResponse.json(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
