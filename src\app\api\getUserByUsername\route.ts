import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // URLからusernameを取得
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');

    if (!username) {
      return NextResponse.json(
        { error: 'usernameが提供されていません' },
        { 
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // Prismaを使用してusernameでユーザーを検索
    const user = await prisma.user.findUnique({
      where: { username: String(username) },
      select: {
        id: true,
        user_ID: true,
        username: true,
        name: true,
        email: true,
        profile_image: true,
        contact_url: true,
        contact_email: true,
        self_introduction: true,
        background_image: true,
        created_at: true,
        updated_at: true,
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'ユーザーが見つかりません' },
        { 
          status: 404,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          }
        }
      );
    }

    // レスポンスデータを準備
    const responseData = {
      id: user.id,
      user_ID: user.user_ID,
      username: user.username,
      name: user.name,
      email: user.email,
      profile_image: user.profile_image,
      contact_url: user.contact_url,
      contact_email: user.contact_email,
      self_introduction: user.self_introduction,
      background_image: user.background_image,
      created_at: user.created_at,
      updated_at: user.updated_at,
    };

    return NextResponse.json(responseData, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('ユーザー取得エラー:', error);
    return NextResponse.json(
      { error: 'サーバーエラーが発生しました' },
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
  // Prisma connection pooling handles disconnection automatically
  // Removing manual disconnect to improve performance
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
