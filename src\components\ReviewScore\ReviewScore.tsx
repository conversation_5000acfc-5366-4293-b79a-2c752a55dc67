'use client'
/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React, { useState } from 'react';
interface ReviewScore {
    className: string;
    changeScore?: (score: number) => void;
}

export const ReviewScore: React.FC<ReviewScore>  = ({ className,changeScore }) => {
    const [score, setScore] = useState<number>(0);

    const handleClick = (index: number) => {
      setScore(index + 1);
      if (changeScore) {
        changeScore(index + 1);
      }
    };

    
  return (
    <div className={`flex w-full items-center justify-center relative ${className}`}>
    <p className="relative w-fit mt-[-1.00px] font-normal text-[40px] leading-10 whitespace-nowrap">
      {[...Array(5)].map((_, index) => (
        <span
          key={index}
          className={`cursor-pointer ${
            index < score ? 'text-[#ffc600]' : 'text-[#aaaaaa]'
          } tracking-[8.00px]`}
          onClick={() => handleClick(index)}
        >
          ★
        </span>
      ))}
    </p>
  </div>
  );
};
