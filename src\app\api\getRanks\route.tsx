import { NextRequest } from "next/server";
import { PrismaClient } from "@prisma/client";

// Prismaクライアントのインスタンスを作成（グローバルで共有）
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

export async function GET(request: NextRequest) {
  try {
    console.log('getRanks API 開始');

    // 環境変数の確認
    console.log('DATABASE_URL設定状況:', !!process.env.DATABASE_URL);
    console.log('NODE_ENV:', process.env.NODE_ENV);

    // Prismaクライアントの状態確認
    console.log('Prismaクライアント状態:', !!prisma);

    // リクエストURLからuser_IDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get("user_ID");

    console.log(`getRanks API 呼び出し: user_ID=${user_ID}`);

    if (!user_ID) {
      console.error('getRanks API エラー: user_IDが提供されていません');
      return new Response(JSON.stringify({
        error: 'user_IDが提供されていません',
        details: 'リクエストパラメータにuser_IDが含まれていません'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // user_IDの形式チェック
    if (typeof user_ID !== 'string' || user_ID.trim().length === 0) {
      console.error(`getRanks API エラー: 無効なuser_ID形式: ${user_ID}`);
      return new Response(JSON.stringify({
        error: '無効なuser_ID形式です',
        details: `user_IDは空でない文字列である必要があります: ${user_ID}`
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // データベース接続テスト（軽量）
    try {
      console.log('データベース接続テスト開始');
      await prisma.$queryRaw`SELECT 1`;
      console.log('データベース接続成功');
    } catch (dbError) {
      console.error('データベース接続エラー:', dbError);
      throw new Error(`データベース接続に失敗しました: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
    }

    // Prismaを使用してuser_IDに基づいて公開済みrankingデータを取得
    console.log(`データベースクエリ実行: user_ID=${user_ID} (公開済みのみ)`);
    const rankings = await prisma.ranking.findMany({
      where: {
        user_ID: String(user_ID),
        status: 'PUBLISHED' // 公開済みのみ取得
      },
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        thumbnail_image: true,
        recommend_rate: true, // 星の数を明示的に選択
        subCategory_ID: true,
        user_ID: true,
        order: true,
        status: true, // ステータスも取得
        created_at: true,
        updated_at: true
      },
      orderBy: {
        order: 'asc'
      }
    });

    console.log(`データベースクエリ結果: ${rankings.length}件のランキングを取得`);

    // データが存在しない場合は空の配列を返す
    if (!rankings.length) {
      console.log('ランキングデータが0件のため空配列を返します');
      const emptyResponse = new Response(JSON.stringify({ ranks: [] }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return emptyResponse;
    }

    // 完全なランキングオブジェクトの最初の1件を表示
    if (rankings.length > 0) {
      console.log('=== getRanks API ===');
      console.log('最初のランキングの完全なデータ:', JSON.stringify(rankings[0], null, 2));
      console.log('recommend_rate値の型:', typeof rankings[0].recommend_rate);
      console.log('recommend_rate値:', rankings[0].recommend_rate);
      console.log('===========================');
    }

    // ランキングデータの詳細をログに出力
    console.log('getRanks APIで取得したランキングの詳細:', 
      rankings.slice(0, 3).map(r => ({ 
        id: r.id, 
        title: r.ranking_title, 
        recommend_rate: r.recommend_rate, 
        thumbnail: r.thumbnail_image ? 'あり' : 'なし',
        description: r.ranking_description ? 'あり' : 'なし'
      }))
    );

    // データをJSON形式で返す
    const response = new Response(JSON.stringify({ ranks: rankings }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('getRanks API 正常終了');
    return response;
  } catch (error) {
    console.error("getRanks API エラー:", error);

    // エラーの詳細情報を取得
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error("エラー詳細:", {
      message: errorMessage,
      stack: errorStack,
      timestamp: new Date().toISOString()
    });

    // Prismaエラーの場合の特別な処理
    let userFriendlyMessage = 'データの取得に失敗しました';
    let statusCode = 500;

    if (errorMessage.includes('connect') || errorMessage.includes('timeout')) {
      userFriendlyMessage = 'データベース接続エラーが発生しました';
    } else if (errorMessage.includes('permission') || errorMessage.includes('access')) {
      userFriendlyMessage = 'データベースアクセス権限エラーが発生しました';
      statusCode = 403;
    }

    return new Response(JSON.stringify({
      error: userFriendlyMessage,
      details: process.env.NODE_ENV === 'development' ? errorMessage : undefined,
      timestamp: new Date().toISOString()
    }), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } finally {
    // Prismaクライアントの接続を適切に管理
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('Prisma disconnect error:', disconnectError);
    }
  }
}
