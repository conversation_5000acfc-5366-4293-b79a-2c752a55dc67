/* eslint-disable @next/next/no-img-element */
"use client";

import React, { useEffect, useState, useMemo, useCallback } from "react";
import { flushSync } from "react-dom";
import { usePathname, useRouter } from "next/navigation";
import Script from "next/script";
import { useUser } from "../../../../contexts/UserContext";
import { Footer } from "@/components/Footer";
import { HeaderItemsReview } from "@/components/HeaderItemsReview";
import { ImageItem } from "@/components/ImageItem";
import { ImageSlider } from "@/components/ImageSlider/ImageSlider";
import LoadingAnimation from '../../../../components/LoadingAnimation/LoadingAnimation';
import { MypageReleaseButtonFooter } from "@/components/MypageReleaseButtonFooter";

// Import new components and types
import { RankingContent, StarRating, HelpPopup, Breadcrumb, ErrorDisplay } from './components';
import { SavedRankingItem, ProductLinks, RankingData } from './types';
import { useRankingData } from './hooks/useRankingData';
import { useSNSScripts } from './utils/snsScriptLoader';
import { MAX_IMAGES } from './constants';

const Mypagereleasedetail = (): JSX.Element => {
  const router = useRouter();
  const { userId } = useUser();
  const pathname = usePathname();
  const pathParts = pathname.split("/");
  const ranking_ID = pathParts[3];

  // Use custom hooks
  const { ranking, userData, isLoading, error } = useRankingData(userId, ranking_ID);
  useSNSScripts();

  // Local state for UI interactions
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
  const [snsEmbedProcessed, setSnsEmbedProcessed] = useState<boolean>(false);
  const [processedRankingId, setProcessedRankingId] = useState<string | null>(null);



  // Initialize thumbnail when ranking data is loaded
  useEffect(() => {
    if (ranking && ranking[0] && ranking[0].thumbnail_image) {
      const images = Array.isArray(ranking[0].thumbnail_image) 
        ? ranking[0].thumbnail_image 
        : [ranking[0].thumbnail_image];
      if (images.length > 0 && !selectedThumbnail) {
        setSelectedThumbnail(images[0]);
        setCurrentImageIndex(0);
      }
    }
  }, [ranking, selectedThumbnail]);

  // Reset SNS embed processing flag when ranking ID changes
  useEffect(() => {
    if (ranking && ranking[0]) {
      const currentRankingId = ranking[0].ranking_ID;
      if (processedRankingId !== currentRankingId) {
        setSnsEmbedProcessed(false);
        setProcessedRankingId(null);
        if (process.env.NODE_ENV === 'development') {
          console.log('[RankingDetail] 新しいランキング読み込み - SNS埋め込み処理フラグをリセットしました (ID:', currentRankingId, ')');
        }
      }
    }
  }, [ranking, processedRankingId]);

  // Process SNS embeds monitoring
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [SNS埋め込み] 依存関係変更検出:', {
        rankingId: ranking?.[0]?.ranking_ID,
        snsEmbedProcessed,
        processedRankingId
      });
    }
  }, [ranking, snsEmbedProcessed, processedRankingId]);

  // Prepare unique images array
  const uniqueImages = useMemo(() => {
    if (!ranking || !ranking[0]) return [];

    const allImages = ranking[0]?.thumbnail_image ?
      (typeof ranking[0].thumbnail_image === 'string' ? [ranking[0].thumbnail_image] : ranking[0].thumbnail_image) :
      [];

    const additionalImages = ranking[0]?.images || [];
    const combinedImages = [...allImages, ...additionalImages];
    
    return combinedImages.filter((image, index) => {
      return combinedImages.indexOf(image) === index;
    }).slice(0, MAX_IMAGES);
  }, [ranking]);

  // Parse product links
  const productLinks: ProductLinks = useMemo(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 [useMemo] productLinks 再計算');
    }
    if (!ranking || !ranking[0]) return {};

    return {
      amazon: ranking[0].amazon_url || undefined,
      rakuten: ranking[0].rakuten_url || undefined,
      yahoo: ranking[0].yahoo_url || undefined,
      qoo10: ranking[0].qoo10_url || undefined,
      official: ranking[0].official_url || undefined,
    };
  }, [ranking]);

  // Prepare ranking data for saving
  const rankingData = useMemo(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 [useMemo] rankingData 再計算（画像切り替えとは独立）');
    }
    if (!ranking || !ranking[0]) return null;

    return {
      id: ranking_ID,
      title: ranking[0].ranking_title,
      description: ranking[0].ranking_description,
      imageUrl: uniqueImages.length > 0 ? uniqueImages[0] : '',
      rating: ranking[0].recommend_rate
    };
  }, [ranking_ID, ranking, uniqueImages]);

  // Memoize header component to prevent unnecessary re-renders
  const memoizedHeaderItemsReview = useMemo(() => {
    if (!ranking || !ranking[0]) return null;

    return (
      <HeaderItemsReview
        userID={userId}
        userName={userData?.name}
        profileImage={userData?.profileImage}
        rankingID={ranking_ID}
        categoryName={ranking[0]?.category_name}
        categoryId={ranking[0]?.category_id}
        subCategoryName={ranking[0]?.subcategory_name}
        subCategoryId={ranking[0]?.subCategory_ID?.toString()}
      />
    );
  }, [userId, userData, ranking_ID, ranking]);

  // Handle image selection
  const handleImageSelect = useCallback((imageUrl: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🖼️ [画像選択] サムネイル選択:', imageUrl);
    }
    const index = uniqueImages.findIndex(img => img === imageUrl);

    flushSync(() => {
      setSelectedThumbnail(imageUrl);
      if (index !== -1) {
        setCurrentImageIndex(index);
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('🖼️ [画像選択] state更新完了');
    }
  }, [uniqueImages]);

  // Handle slider image change
  const handleSliderImageChange = useCallback((imageUrl: string, index: number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🖼️ [画像切り替え] スライダー画像変更:', imageUrl, 'インデックス:', index);
    }

    flushSync(() => {
      setSelectedThumbnail(imageUrl);
      setCurrentImageIndex(index);
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('🖼️ [画像切り替え] state更新完了');
    }
  }, []);

  // Show error display if there's an error
  if (error) {
    return <ErrorDisplay error={error} />;
  }

  // Show loading animation while loading
  if (isLoading || !ranking || !ranking[0]) {
    return <LoadingAnimation />;
  }
  
  return (
    <div>
      {/* SNS embed scripts */}
      <Script
        src="//www.instagram.com/embed.js"
        strategy="lazyOnload"
        onLoad={() => {
          if (process.env.NODE_ENV === 'development') {
            console.log('[RankingDetail] Instagram script loaded');
          }
        }}
        onError={(e) => {
          if (process.env.NODE_ENV === 'development') {
            console.error('[RankingDetail] Instagram script load error:', e);
          }
        }}
      />
      <Script
        src="https://platform.twitter.com/widgets.js"
        strategy="lazyOnload"
        onLoad={() => {
          if (process.env.NODE_ENV === 'development') {
            console.log('[RankingDetail] Twitter script loaded');
          }
        }}
        onError={(e) => {
          if (process.env.NODE_ENV === 'development') {
            console.error('[RankingDetail] Twitter script load error:', e);
          }
        }}
      />
      <Script
        src="https://www.tiktok.com/embed.js"
        strategy="lazyOnload"
        onLoad={() => {
          if (process.env.NODE_ENV === 'development') {
            console.log('[RankingDetail] TikTok script loaded');
          }
        }}
        onError={(e) => {
          if (process.env.NODE_ENV === 'development') {
            console.error('[RankingDetail] TikTok script load error:', e);
          }
        }}
      />

      <div className="flex flex-col w-full max-w-[500px] mx-auto min-h-screen items-center relative shadow-[-10px_0_20px_rgba(0,0,0,0.1),10px_0_20px_rgba(0,0,0,0.1)] bg-gray-100">
        {memoizedHeaderItemsReview || (
          <HeaderItemsReview
            userID={userId}
            userName={userData?.name}
            profileImage={userData?.profileImage}
            rankingID={ranking_ID}
            categoryName={ranking[0]?.category_name || ""}
            categoryId={ranking[0]?.category_id || ""}
            subCategoryName={ranking[0]?.subcategory_name || ""}
            subCategoryId={ranking[0]?.subCategory_ID?.toString() || ""}
          />
        )}
        
        {/* Main image slider */}
        {uniqueImages.length > 0 && (
          <div className="w-full bg-black">
            <ImageSlider
              images={uniqueImages}
              selectedImage={selectedThumbnail}
              onImageChange={handleSliderImageChange}
            />
          </div>
        )}
        
        {/* Thumbnail images */}
        <ImageItem
          images={uniqueImages}
          onSelectImage={handleImageSelect}
          selectedImage={selectedThumbnail}
        />

        {/* White background content area */}
        <div className="w-full bg-white">
          {/* Breadcrumb */}
          <Breadcrumb
            userId={userId}
            categoryName={ranking[0].category_name}
            subcategoryName={ranking[0].subcategory_name}
          />

          {/* Title */}
          <div className="flex w-full items-center pt-[16px] pl-[16px] pr-[16px] pb-0 relative">
            <div className="relative w-full text-[18px] tracking-[0] leading-[1.5] font-bold">
              {ranking[0].ranking_title}
            </div>
          </div>

          {/* Star rating with help popup */}
          <div className="flex items-center">
            <StarRating rating={ranking[0].recommend_rate} />
            <HelpPopup />
          </div>

          {/* Content */}
          <RankingContent
            description={ranking[0].ranking_description || ''}
            rankingId={ranking[0].ranking_ID}
          />

          {/* Footer */}
          <Footer
            className="!flex-[0_0_auto]"
            logoImageMyrank={ranking[0].logo_image_myrank}
          />
        </div>

        <MypageReleaseButtonFooter
          url={typeof window !== 'undefined' ? window.location.href : ''}
          rankingData={rankingData}
          productLinks={productLinks}
        />
      </div>
    </div>
  );
};

export default Mypagereleasedetail;