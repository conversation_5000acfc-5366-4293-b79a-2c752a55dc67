import React from 'react';
import { ReviewInputBox } from '@/components/ReviewInputBox/ReviewInputBox';

interface RecommendSectionProps {
  recommendText: string;
  setRecommendText: (text: string) => void;
}

/**
 * おすすめの理由セクションコンポーネント
 */
export const RecommendSection: React.FC<RecommendSectionProps> = ({
  recommendText,
  setRecommendText
}) => {
  return (
    <div className="px-4 py-3">
      <div className="flex justify-between items-center mb-2">
        <p className="text-[#313131] text-[16px] font-bold">おすすめの理由 <span className="text-xs text-gray-400">(任意)</span></p>
      </div>
      
      <ReviewInputBox
        className=""
        value={recommendText}
        onChange={(value: string) => setRecommendText(value)}
      />
    </div>
  );
};
