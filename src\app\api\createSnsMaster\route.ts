import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from "@prisma/client";
import { cookies } from 'next/headers';

// Prismaクライアントのインスタンスを作成
const prisma = new PrismaClient();

// POSTリクエスト - SNSマスターデータの作成
export async function POST(request: NextRequest) {
  try {
    // リクエストからユーザーIDを取得
    const requestData = await request.json().catch(() => ({}));
    
    // リクエストボディからユーザーIDを取得、なければCookieから取得
    let currentUserId = requestData.userId;
    
    // リクエストボディにユーザーIDがない場合はCookieから取得
    if (!currentUserId) {
      const cookieStore = await cookies();
      currentUserId = cookieStore.get('userId')?.value;
    }
    
    // ユーザーIDが取得できない場合はエラーを返す
    if (!currentUserId) {
      return NextResponse.json(
        { error: 'ユーザーIDが指定されていません。認証が必要です。' },
        { status: 401 }
      );
    }
    
    // データベースから既存のSNSマスターデータを取得
    const existingSnsMasters = await prisma.snsMaster.findMany();
    
    // 既に登録されているSNS名を取得
    const existingSnsNames = existingSnsMasters.map(sns => sns.sns_name);
    
    // 必要なSNSデータ（データベースに存在しないもの）を作成
    const requiredSnsData = [
      // メインSNS
      { id: 1, name: "Instagram", image: "/static/img/instagram.png" },
      { id: 2, name: "TikTok", image: "/static/img/tiktok.png" },
      { id: 3, name: "X", image: "/static/img/x.png" },
      { id: 4, name: "YouTube", image: "/static/img/youtube.png" },
      
      // その他のSNS
      { id: 5, name: "facebook", image: "/static/img/facebook.png" },
      { id: 6, name: "LINE", image: "/static/img/line.png" },
      { id: 7, name: "Amazon", image: "/static/img/amazon.png" },
      { id: 8, name: "note", image: "/static/img/note.png" },
      { id: 9, name: "Rakuten", image: "/static/img/rakuten.png" },
      { id: 10, name: "WEAR", image: "/static/img/wear.png" },
      { id: 11, name: "Blog", image: "/static/img/blog.png" },
      { id: 12, name: "BIGO LIVE", image: "/static/img/bigo.png" },
      { id: 13, name: "17Live", image: "/static/img/17live.png" },
      { id: 14, name: "ツイキャス", image: "/static/img/twicasting.png" },
      { id: 15, name: "Pococha", image: "/static/img/pococha.png" },
      { id: 16, name: "SHOWROOM", image: "/static/img/showroom.png" },
      { id: 17, name: "ふわっち", image: "/static/img/fuwatto.png" },
      { id: 18, name: "Clubhouse", image: "/static/img/clubhouse.png" },
      { id: 19, name: "Voicy", image: "/static/img/voicy.png" },
      { id: 20, name: "Podcast", image: "/static/img/podcast.png" },
      { id: 21, name: "Pinterest", image: "/static/img/pinterest.png" },
      { id: 22, name: "Discord", image: "/static/img/discord.png" },
      { id: 23, name: "Telegram", image: "/static/img/telegram.png" },
      { id: 24, name: "Chatwork", image: "/static/img/chatwork.png" },
      { id: 25, name: "LinkedIn", image: "/static/img/linkedin.png" },
      { id: 26, name: "Sansan", image: "/static/img/sansan.png" },
      { id: 27, name: "Eight", image: "/static/img/eight.png" },
      { id: 28, name: "食べログ", image: "/static/img/tabelog.png" },
      { id: 29, name: "Retty", image: "/static/img/retty.png" }
    ].filter(sns => !existingSnsNames.includes(sns.name));
    
    // 新しいSNSデータを作成（存在しないものだけ）
    if (requiredSnsData.length > 0) {
      await prisma.snsMaster.createMany({
        data: requiredSnsData.map(sns => ({
          sns_ID: sns.id,
          sns_name: sns.name
        })) as any,
        skipDuplicates: true,
      });
    }

    // 更新後のSNSマスターデータを確認
    const updatedSnsMasters = await prisma.snsMaster.findMany();

    return NextResponse.json({ 
      success: true, 
      message: `${requiredSnsData.length}件のSNSマスターデータを作成しました`,
      created: requiredSnsData,
      existing: updatedSnsMasters
    }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: 'SNSマスターデータの作成に失敗しました' }, { status: 500 });
  }
}
