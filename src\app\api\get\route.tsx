import { NextRequest } from "next/server";
import { PrismaClient } from "@prisma/client";

// Prismaクライアントのインスタンスを作成
const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // MongoDBから全てのUserデータを取得
    const users = await prisma.user.findMany();
    
    // データをJSON形式で返す
    return new Response(JSON.stringify(users), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    // エラーハンドリング
    return new Response(JSON.stringify({ error: 'データの取得に失敗しました' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}