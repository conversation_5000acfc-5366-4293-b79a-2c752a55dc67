-- CreateEnum
CREATE TYPE "AccounType" AS ENUM ('email', 'line', 'google');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "user_ID" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "account_type" "AccounType" NOT NULL,
    "email" TEXT NOT NULL,
    "line" TEXT,
    "google" TEXT,
    "password" TEXT,
    "name" TEXT NOT NULL,
    "profile_image" TEXT,
    "contact_url" TEXT,
    "contact_phone" INTEGER,
    "contact_email" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Category" (
    "id" TEXT NOT NULL,
    "user_ID" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "category_ID" TEXT NOT NULL,
    "category_name" TEXT NOT NULL,
    "parent_ID" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Category_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SnsMaster" (
    "id" TEXT NOT NULL,
    "sns_ID" INTEGER NOT NULL,
    "user_ID" TEXT NOT NULL,
    "sns_name" TEXT NOT NULL,
    "sns_image" TEXT NOT NULL,
    "account_type" "AccounType",
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SnsMaster_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SnsUser" (
    "id" TEXT NOT NULL,
    "sns_ID" INTEGER NOT NULL,
    "user_ID" TEXT NOT NULL,
    "sns_name" TEXT NOT NULL,
    "sns_image" TEXT NOT NULL,
    "account_type" "AccounType",
    "account_ID" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SnsUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Ranking" (
    "id" TEXT NOT NULL,
    "ranking_ID" TEXT NOT NULL,
    "user_ID" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "thumbnail_image" TEXT[],
    "recommend_rate" INTEGER,
    "ranking_url" TEXT,
    "ranking_title" TEXT NOT NULL,
    "ranking_description" TEXT,
    "category_ID" TEXT,
    "subCategory_ID" TEXT,
    "category_name" TEXT,
    "subCategory_name" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Ranking_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Post" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "published" BOOLEAN NOT NULL,
    "authorId" TEXT NOT NULL,

    CONSTRAINT "Post_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_user_ID_key" ON "User"("user_ID");

-- CreateIndex
CREATE UNIQUE INDEX "User_domain_key" ON "User"("domain");

-- CreateIndex
CREATE INDEX "Category_user_ID_idx" ON "Category"("user_ID");

-- CreateIndex
CREATE INDEX "Category_parent_ID_idx" ON "Category"("parent_ID");

-- CreateIndex
CREATE UNIQUE INDEX "SnsMaster_sns_name_key" ON "SnsMaster"("sns_name");

-- CreateIndex
CREATE UNIQUE INDEX "SnsMaster_sns_image_key" ON "SnsMaster"("sns_image");

-- CreateIndex
CREATE INDEX "SnsMaster_user_ID_idx" ON "SnsMaster"("user_ID");

-- CreateIndex
CREATE INDEX "SnsUser_user_ID_idx" ON "SnsUser"("user_ID");

-- CreateIndex
CREATE INDEX "SnsUser_sns_name_idx" ON "SnsUser"("sns_name");

-- CreateIndex
CREATE INDEX "SnsUser_sns_image_idx" ON "SnsUser"("sns_image");

-- CreateIndex
CREATE INDEX "Ranking_user_ID_idx" ON "Ranking"("user_ID");

-- CreateIndex
CREATE INDEX "Ranking_domain_idx" ON "Ranking"("domain");

-- AddForeignKey
ALTER TABLE "Category" ADD CONSTRAINT "Category_user_ID_fkey" FOREIGN KEY ("user_ID") REFERENCES "User"("user_ID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Category" ADD CONSTRAINT "Category_parent_ID_fkey" FOREIGN KEY ("parent_ID") REFERENCES "Category"("id") ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE "SnsMaster" ADD CONSTRAINT "SnsMaster_user_ID_fkey" FOREIGN KEY ("user_ID") REFERENCES "User"("user_ID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SnsUser" ADD CONSTRAINT "SnsUser_user_ID_fkey" FOREIGN KEY ("user_ID") REFERENCES "User"("user_ID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SnsUser" ADD CONSTRAINT "SnsUser_sns_name_fkey" FOREIGN KEY ("sns_name") REFERENCES "SnsMaster"("sns_name") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SnsUser" ADD CONSTRAINT "SnsUser_sns_image_fkey" FOREIGN KEY ("sns_image") REFERENCES "SnsMaster"("sns_image") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ranking" ADD CONSTRAINT "Ranking_user_ID_fkey" FOREIGN KEY ("user_ID") REFERENCES "User"("user_ID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Ranking" ADD CONSTRAINT "Ranking_domain_fkey" FOREIGN KEY ("domain") REFERENCES "User"("domain") ON DELETE RESTRICT ON UPDATE CASCADE;
