<!DOCTYPE html>
<html>

<head>
  <meta charset="utf8" />
  <title>マイランクバックエンド API</title>
  <!-- needed for adaptive design -->
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      padding: 0;
      margin: 0;
    }
  </style>
  <script src="https://cdn.redoc.ly/redoc/v2.1.3/bundles/redoc.standalone.js"></script><style data-styled="true" data-styled-version="6.1.11">.jLkFlo{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.jLkFlo{width:100%;padding:40px 40px;}}/*!sc*/
data-styled.g4[id="sc-fqkwJk"]{content:"jLkFlo,"}/*!sc*/
.gWwwsw{padding:40px 0;}/*!sc*/
.gWwwsw:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.gWwwsw>.gWwwsw:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.gWwwsw{padding:0;}}/*!sc*/
.dNqlFH{padding:40px 0;position:relative;}/*!sc*/
.dNqlFH:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.dNqlFH>.dNqlFH:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.dNqlFH{padding:0;}}/*!sc*/
.dNqlFH:not(:last-of-type):after{position:absolute;bottom:0;width:100%;display:block;content:'';border-bottom:1px solid rgba(0, 0, 0, 0.2);}/*!sc*/
data-styled.g5[id="sc-dcJtft"]{content:"gWwwsw,dNqlFH,"}/*!sc*/
.hqwGrr{width:40%;color:#ffffff;background-color:#263238;padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.hqwGrr{width:100%;padding:40px 40px;}}/*!sc*/
data-styled.g6[id="sc-iGgVNO"]{content:"hqwGrr,"}/*!sc*/
.ggsSF{background-color:#263238;}/*!sc*/
data-styled.g7[id="sc-gsFSjX"]{content:"ggsSF,"}/*!sc*/
.NrBoN{display:flex;width:100%;padding:0;}/*!sc*/
@media print,screen and (max-width: 75rem){.NrBoN{flex-direction:column;}}/*!sc*/
data-styled.g8[id="sc-kAycRU"]{content:"NrBoN,"}/*!sc*/
.cDCvoe{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#333333;}/*!sc*/
data-styled.g9[id="sc-imWZod"]{content:"cDCvoe,"}/*!sc*/
.hXYRhd{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;margin:0 0 20px;}/*!sc*/
data-styled.g10[id="sc-jXbVAB"]{content:"hXYRhd,"}/*!sc*/
.cgzTaG{color:#ffffff;}/*!sc*/
data-styled.g12[id="sc-kpDprT"]{content:"cgzTaG,"}/*!sc*/
.fZUozT{border-bottom:1px solid rgba(38, 50, 56, 0.3);margin:1em 0 1em 0;color:rgba(38, 50, 56, 0.5);font-weight:normal;text-transform:uppercase;font-size:0.929em;line-height:20px;}/*!sc*/
data-styled.g13[id="sc-dAlxHm"]{content:"fZUozT,"}/*!sc*/
.buKOug{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.buKOug:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
h1:hover>.buKOug::before,h2:hover>.buKOug::before,.buKOug:hover::before{visibility:visible;}/*!sc*/
data-styled.g14[id="sc-jlZhRR"]{content:"buKOug,"}/*!sc*/
.jqLwgG{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.jqLwgG polygon{fill:#1d8127;}/*!sc*/
.bBKyWu{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.bBKyWu polygon{fill:#d41f1c;}/*!sc*/
.fxsrq{height:20px;width:20px;min-width:20px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(0);}/*!sc*/
.fxsrq polygon{fill:white;}/*!sc*/
data-styled.g15[id="sc-cwHqhk"]{content:"jqLwgG,bBKyWu,fxsrq,"}/*!sc*/
.fCxZSn{border-left:1px solid #7c7cbb;box-sizing:border-box;position:relative;padding:10px 10px 10px 0;}/*!sc*/
@media screen and (max-width: 50rem){.fCxZSn{display:block;overflow:hidden;}}/*!sc*/
tr:first-of-type>.fCxZSn,tr.last>.fCxZSn{border-left-width:0;background-position:top left;background-repeat:no-repeat;background-size:1px 100%;}/*!sc*/
tr:first-of-type>.fCxZSn{background-image:linear-gradient(
      to bottom,
      transparent 0%,
      transparent 22px,
      #7c7cbb 22px,
      #7c7cbb 100%
    );}/*!sc*/
tr.last>.fCxZSn{background-image:linear-gradient(
      to bottom,
      #7c7cbb 0%,
      #7c7cbb 22px,
      transparent 22px,
      transparent 100%
    );}/*!sc*/
tr.last+tr>.fCxZSn{border-left-color:transparent;}/*!sc*/
tr.last:first-child>.fCxZSn{background:none;border-left-color:transparent;}/*!sc*/
data-styled.g18[id="sc-dLNtp"]{content:"fCxZSn,"}/*!sc*/
.jTSwDU{vertical-align:top;line-height:20px;white-space:nowrap;font-size:13px;font-family:Courier,monospace;}/*!sc*/
.jTSwDU.deprecated{text-decoration:line-through;color:#707070;}/*!sc*/
data-styled.g20[id="sc-eldOKa"]{content:"jTSwDU,"}/*!sc*/
.kdholP{border-bottom:1px solid #9fb4be;padding:10px 0;width:75%;box-sizing:border-box;}/*!sc*/
tr.expanded .kdholP{border-bottom:none;}/*!sc*/
@media screen and (max-width: 50rem){.kdholP{padding:0 20px;border-bottom:none;border-left:1px solid #7c7cbb;}tr.last>.kdholP{border-left:none;}}/*!sc*/
data-styled.g21[id="sc-fPXMhL"]{content:"kdholP,"}/*!sc*/
.fWSzIS{color:#7c7cbb;font-family:Courier,monospace;margin-right:10px;}/*!sc*/
.fWSzIS::before{content:'';display:inline-block;vertical-align:middle;width:10px;height:1px;background:#7c7cbb;}/*!sc*/
.fWSzIS::after{content:'';display:inline-block;vertical-align:middle;width:1px;background:#7c7cbb;height:7px;}/*!sc*/
data-styled.g22[id="sc-gFqAYk"]{content:"fWSzIS,"}/*!sc*/
.hPLMVw{border-collapse:separate;border-radius:3px;font-size:14px;border-spacing:0;width:100%;}/*!sc*/
.hPLMVw >tr{vertical-align:middle;}/*!sc*/
@media screen and (max-width: 50rem){.hPLMVw{display:block;}.hPLMVw >tr,.hPLMVw >tbody>tr{display:block;}}/*!sc*/
@media screen and (max-width: 50rem) and (-ms-high-contrast:none){.hPLMVw td{float:left;width:100%;}}/*!sc*/
.hPLMVw .sc-ikkyvV,.hPLMVw .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV,.hPLMVw .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV{margin:1em;margin-right:0;background:#fafafa;}/*!sc*/
.hPLMVw .sc-ikkyvV .sc-ikkyvV,.hPLMVw .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV,.hPLMVw .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV .sc-ikkyvV{background:#ffffff;}/*!sc*/
data-styled.g24[id="sc-dAbbbq"]{content:"hPLMVw,"}/*!sc*/
.tlGxN >ul{list-style:none;padding:0;margin:0;margin:0 -5px;}/*!sc*/
.tlGxN >ul >li{padding:5px 10px;display:inline-block;background-color:#11171a;border-bottom:1px solid rgba(0, 0, 0, 0.5);cursor:pointer;text-align:center;outline:none;color:#ccc;margin:0 5px 5px 5px;border:1px solid #07090b;border-radius:5px;min-width:60px;font-size:0.9em;font-weight:bold;}/*!sc*/
.tlGxN >ul >li.react-tabs__tab--selected{color:#333333;background:#ffffff;}/*!sc*/
.tlGxN >ul >li.react-tabs__tab--selected:focus{outline:auto;}/*!sc*/
.tlGxN >ul >li:only-child{flex:none;min-width:100px;}/*!sc*/
.tlGxN >ul >li.tab-success{color:#1d8127;}/*!sc*/
.tlGxN >ul >li.tab-redirect{color:#ffa500;}/*!sc*/
.tlGxN >ul >li.tab-info{color:#87ceeb;}/*!sc*/
.tlGxN >ul >li.tab-error{color:#d41f1c;}/*!sc*/
.tlGxN >.react-tabs__tab-panel{background:#11171a;}/*!sc*/
.tlGxN >.react-tabs__tab-panel>div,.tlGxN >.react-tabs__tab-panel>pre{padding:20px;margin:0;}/*!sc*/
.tlGxN >.react-tabs__tab-panel>div>pre{padding:0;}/*!sc*/
data-styled.g30[id="sc-bXCLgj"]{content:"tlGxN,"}/*!sc*/
.bSgSrX code[class*='language-'],.bSgSrX pre[class*='language-']{text-shadow:0 -0.1em 0.2em black;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none;}/*!sc*/
@media print{.bSgSrX code[class*='language-'],.bSgSrX pre[class*='language-']{text-shadow:none;}}/*!sc*/
.bSgSrX pre[class*='language-']{padding:1em;margin:0.5em 0;overflow:auto;}/*!sc*/
.bSgSrX .token.comment,.bSgSrX .token.prolog,.bSgSrX .token.doctype,.bSgSrX .token.cdata{color:hsl(30, 20%, 50%);}/*!sc*/
.bSgSrX .token.punctuation{opacity:0.7;}/*!sc*/
.bSgSrX .namespace{opacity:0.7;}/*!sc*/
.bSgSrX .token.property,.bSgSrX .token.tag,.bSgSrX .token.number,.bSgSrX .token.constant,.bSgSrX .token.symbol{color:#4a8bb3;}/*!sc*/
.bSgSrX .token.boolean{color:#e64441;}/*!sc*/
.bSgSrX .token.selector,.bSgSrX .token.attr-name,.bSgSrX .token.string,.bSgSrX .token.char,.bSgSrX .token.builtin,.bSgSrX .token.inserted{color:#a0fbaa;}/*!sc*/
.bSgSrX .token.selector+a,.bSgSrX .token.attr-name+a,.bSgSrX .token.string+a,.bSgSrX .token.char+a,.bSgSrX .token.builtin+a,.bSgSrX .token.inserted+a,.bSgSrX .token.selector+a:visited,.bSgSrX .token.attr-name+a:visited,.bSgSrX .token.string+a:visited,.bSgSrX .token.char+a:visited,.bSgSrX .token.builtin+a:visited,.bSgSrX .token.inserted+a:visited{color:#4ed2ba;text-decoration:underline;}/*!sc*/
.bSgSrX .token.property.string{color:white;}/*!sc*/
.bSgSrX .token.operator,.bSgSrX .token.entity,.bSgSrX .token.url,.bSgSrX .token.variable{color:hsl(40, 90%, 60%);}/*!sc*/
.bSgSrX .token.atrule,.bSgSrX .token.attr-value,.bSgSrX .token.keyword{color:hsl(350, 40%, 70%);}/*!sc*/
.bSgSrX .token.regex,.bSgSrX .token.important{color:#e90;}/*!sc*/
.bSgSrX .token.important,.bSgSrX .token.bold{font-weight:bold;}/*!sc*/
.bSgSrX .token.italic{font-style:italic;}/*!sc*/
.bSgSrX .token.entity{cursor:help;}/*!sc*/
.bSgSrX .token.deleted{color:red;}/*!sc*/
data-styled.g32[id="sc-eeDSqt"]{content:"bSgSrX,"}/*!sc*/
.kEwLiZ{opacity:0.7;transition:opacity 0.3s ease;text-align:right;}/*!sc*/
.kEwLiZ:focus-within{opacity:1;}/*!sc*/
.kEwLiZ >button{background-color:transparent;border:0;color:inherit;padding:2px 10px;font-family:Roboto,sans-serif;font-size:14px;line-height:1.5em;cursor:pointer;outline:0;}/*!sc*/
.kEwLiZ >button :hover,.kEwLiZ >button :focus{background:rgba(255, 255, 255, 0.1);}/*!sc*/
data-styled.g33[id="sc-koYCI"]{content:"kEwLiZ,"}/*!sc*/
.eyrQrY{position:relative;}/*!sc*/
data-styled.g37[id="sc-dtBeHJ"]{content:"eyrQrY,"}/*!sc*/
.fpgAnb{margin-left:10px;text-transform:none;font-size:0.929em;color:black;}/*!sc*/
data-styled.g41[id="sc-cWSIco"]{content:"fpgAnb,"}/*!sc*/
.cWARBq{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.cWARBq p:last-child{margin-bottom:0;}/*!sc*/
.cWARBq h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.cWARBq h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.cWARBq code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.cWARBq pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.cWARBq pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.cWARBq pre code:before,.cWARBq pre code:after{content:none;}/*!sc*/
.cWARBq blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.cWARBq img{max-width:100%;box-sizing:content-box;}/*!sc*/
.cWARBq ul,.cWARBq ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.cWARBq ul ul,.cWARBq ol ul,.cWARBq ul ol,.cWARBq ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.cWARBq table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.cWARBq table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.cWARBq table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.cWARBq table th,.cWARBq table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.cWARBq table th{text-align:left;font-weight:bold;}/*!sc*/
.cWARBq .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.cWARBq .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.cWARBq h1:hover>.share-link::before,.cWARBq h2:hover>.share-link::before,.cWARBq .share-link:hover::before{visibility:visible;}/*!sc*/
.cWARBq a{text-decoration:auto;color:#32329f;}/*!sc*/
.cWARBq a:visited{color:#32329f;}/*!sc*/
.cWARBq a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
.gayXgA{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.gayXgA p:last-child{margin-bottom:0;}/*!sc*/
.gayXgA p:first-child{margin-top:0;}/*!sc*/
.gayXgA p:last-child{margin-bottom:0;}/*!sc*/
.gayXgA h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.gayXgA h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.gayXgA code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.gayXgA pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.gayXgA pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.gayXgA pre code:before,.gayXgA pre code:after{content:none;}/*!sc*/
.gayXgA blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.gayXgA img{max-width:100%;box-sizing:content-box;}/*!sc*/
.gayXgA ul,.gayXgA ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.gayXgA ul ul,.gayXgA ol ul,.gayXgA ul ol,.gayXgA ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.gayXgA table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.gayXgA table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.gayXgA table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.gayXgA table th,.gayXgA table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.gayXgA table th{text-align:left;font-weight:bold;}/*!sc*/
.gayXgA .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.gayXgA .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.gayXgA h1:hover>.share-link::before,.gayXgA h2:hover>.share-link::before,.gayXgA .share-link:hover::before{visibility:visible;}/*!sc*/
.gayXgA a{text-decoration:auto;color:#32329f;}/*!sc*/
.gayXgA a:visited{color:#32329f;}/*!sc*/
.gayXgA a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g42[id="sc-eBMFzZ"]{content:"cWARBq,gayXgA,"}/*!sc*/
.gKOXES{display:inline;}/*!sc*/
data-styled.g43[id="sc-dCFGXG"]{content:"gKOXES,"}/*!sc*/
.gpWFPc{position:relative;}/*!sc*/
data-styled.g44[id="sc-fhzEvr"]{content:"gpWFPc,"}/*!sc*/
.hxmTDe:hover>.sc-koYCI{opacity:1;}/*!sc*/
data-styled.g49[id="sc-esYjtY"]{content:"hxmTDe,"}/*!sc*/
.dXllNu{font-family:Courier,monospace;font-size:13px;white-space:pre;contain:content;overflow-x:auto;}/*!sc*/
.dXllNu .redoc-json code>.collapser{display:none;pointer-events:none;}/*!sc*/
.dXllNu .callback-function{color:gray;}/*!sc*/
.dXllNu .collapser:after{content:'-';cursor:pointer;}/*!sc*/
.dXllNu .collapsed>.collapser:after{content:'+';cursor:pointer;}/*!sc*/
.dXllNu .ellipsis:after{content:' … ';}/*!sc*/
.dXllNu .collapsible{margin-left:2em;}/*!sc*/
.dXllNu .hoverable{padding-top:1px;padding-bottom:1px;padding-left:2px;padding-right:2px;border-radius:2px;}/*!sc*/
.dXllNu .hovered{background-color:rgba(235, 238, 249, 1);}/*!sc*/
.dXllNu .collapser{background-color:transparent;border:0;color:#fff;font-family:Courier,monospace;font-size:13px;padding-right:6px;padding-left:6px;padding-top:0;padding-bottom:0;display:flex;align-items:center;justify-content:center;width:15px;height:15px;position:absolute;top:4px;left:-1.5em;cursor:default;user-select:none;-webkit-user-select:none;padding:2px;}/*!sc*/
.dXllNu .collapser:focus{outline-color:#fff;outline-style:dotted;outline-width:1px;}/*!sc*/
.dXllNu ul{list-style-type:none;padding:0px;margin:0px 0px 0px 26px;}/*!sc*/
.dXllNu li{position:relative;display:block;}/*!sc*/
.dXllNu .hoverable{display:inline-block;}/*!sc*/
.dXllNu .selected{outline-style:solid;outline-width:1px;outline-style:dotted;}/*!sc*/
.dXllNu .collapsed>.collapsible{display:none;}/*!sc*/
.dXllNu .ellipsis{display:none;}/*!sc*/
.dXllNu .collapsed>.ellipsis{display:inherit;}/*!sc*/
data-styled.g50[id="sc-fXSgRJ"]{content:"dXllNu,"}/*!sc*/
.OirWV{padding:0.9em;background-color:rgba(38,50,56,0.4);margin:0 0 10px 0;display:block;font-family:Montserrat,sans-serif;font-size:0.929em;line-height:1.5em;}/*!sc*/
data-styled.g51[id="sc-JrEyx"]{content:"OirWV,"}/*!sc*/
.bLpZTa{font-family:Montserrat,sans-serif;font-size:12px;position:absolute;z-index:1;top:-11px;left:12px;font-weight:600;color:rgba(255,255,255,0.7);}/*!sc*/
data-styled.g52[id="sc-fjvwmM"]{content:"bLpZTa,"}/*!sc*/
.kIESIX{position:relative;}/*!sc*/
data-styled.g53[id="sc-bbSYpP"]{content:"kIESIX,"}/*!sc*/
.fszzxn{margin-top:15px;}/*!sc*/
data-styled.g56[id="sc-uVXKs"]{content:"fszzxn,"}/*!sc*/
.kMMoBE{vertical-align:middle;font-size:13px;line-height:20px;}/*!sc*/
data-styled.g58[id="sc-NxrBK"]{content:"kMMoBE,"}/*!sc*/
.hMkdpJ{color:rgba(102,102,102,0.9);}/*!sc*/
data-styled.g59[id="sc-cfxfQh"]{content:"hMkdpJ,"}/*!sc*/
.ffgeXz{color:#666;}/*!sc*/
data-styled.g60[id="sc-gFAXEw"]{content:"ffgeXz,"}/*!sc*/
.gypzqL{color:#d41f1c;font-size:0.9em;font-weight:normal;margin-left:20px;line-height:1;}/*!sc*/
data-styled.g62[id="sc-hRJeED"]{content:"gypzqL,"}/*!sc*/
.hJudwp{margin-top:0;margin-bottom:0.5em;}/*!sc*/
data-styled.g91[id="sc-fTFkHh"]{content:"hJudwp,"}/*!sc*/
.dyWjRw{border:1px solid #32329f;color:#32329f;font-weight:normal;margin-left:0.5em;padding:4px 8px 4px;display:inline-block;text-decoration:none;cursor:pointer;}/*!sc*/
data-styled.g92[id="sc-ktJcvw"]{content:"dyWjRw,"}/*!sc*/
.hkxktA{width:9ex;display:inline-block;height:13px;line-height:13px;background-color:#333;border-radius:3px;background-repeat:no-repeat;background-position:6px 4px;font-size:7px;font-family:Verdana,sans-serif;color:white;text-transform:uppercase;text-align:center;font-weight:bold;vertical-align:middle;margin-right:6px;margin-top:2px;}/*!sc*/
.hkxktA.get{background-color:#2F8132;}/*!sc*/
.hkxktA.post{background-color:#186FAF;}/*!sc*/
.hkxktA.put{background-color:#95507c;}/*!sc*/
.hkxktA.options{background-color:#947014;}/*!sc*/
.hkxktA.patch{background-color:#bf581d;}/*!sc*/
.hkxktA.delete{background-color:#cc3333;}/*!sc*/
.hkxktA.basic{background-color:#707070;}/*!sc*/
.hkxktA.link{background-color:#07818F;}/*!sc*/
.hkxktA.head{background-color:#A23DAD;}/*!sc*/
.hkxktA.hook{background-color:#32329f;}/*!sc*/
.hkxktA.schema{background-color:#707070;}/*!sc*/
data-styled.g99[id="sc-ehiymJ"]{content:"hkxktA,"}/*!sc*/
.gVzKKH{margin:0;padding:0;}/*!sc*/
.gVzKKH:first-child{padding-bottom:32px;}/*!sc*/
.sc-iHmqaY .sc-iHmqaY{font-size:0.929em;}/*!sc*/
data-styled.g100[id="sc-iHmqaY"]{content:"gVzKKH,"}/*!sc*/
.jiiTgL{list-style:none inside none;overflow:hidden;text-overflow:ellipsis;padding:0;}/*!sc*/
data-styled.g101[id="sc-kYxEyd"]{content:"jiiTgL,"}/*!sc*/
.iwtEuE{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;background-color:#fafafa;}/*!sc*/
.iwtEuE:hover{color:#32329f;background-color:#ededed;}/*!sc*/
.iwtEuE .sc-cwHqhk{height:1.5em;width:1.5em;}/*!sc*/
.iwtEuE .sc-cwHqhk polygon{fill:#333333;}/*!sc*/
data-styled.g102[id="sc-bpUCxw"]{content:"iwtEuE,"}/*!sc*/
.kgCeIH{display:inline-block;vertical-align:middle;width:calc(100% - 38px);overflow:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g103[id="sc-eyvHYj"]{content:"kgCeIH,"}/*!sc*/
.fGEtTM{font-size:0.8em;margin-top:10px;text-align:center;position:fixed;width:260px;bottom:0;background:#fafafa;}/*!sc*/
.fGEtTM a,.fGEtTM a:visited,.fGEtTM a:hover{color:#333333!important;padding:5px 0;border-top:1px solid #e1e1e1;text-decoration:none;display:flex;align-items:center;justify-content:center;}/*!sc*/
.fGEtTM img{width:15px;margin-right:5px;}/*!sc*/
@media screen and (max-width: 50rem){.fGEtTM{width:100%;}}/*!sc*/
data-styled.g104[id="sc-gfopwy"]{content:"fGEtTM,"}/*!sc*/
.ciDfNo{cursor:pointer;position:relative;margin-bottom:5px;}/*!sc*/
data-styled.g110[id="sc-dkmVhU"]{content:"ciDfNo,"}/*!sc*/
.gHPYVn{font-family:Courier,monospace;margin-left:10px;flex:1;overflow-x:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g111[id="sc-ejfMNw"]{content:"gHPYVn,"}/*!sc*/
.jJBChh{outline:0;color:inherit;width:100%;text-align:left;cursor:pointer;padding:10px 30px 10px 20px;border-radius:4px 4px 0 0;background-color:#11171a;display:flex;white-space:nowrap;align-items:center;border:1px solid transparent;border-bottom:0;transition:border-color 0.25s ease;}/*!sc*/
.jJBChh .sc-ejfMNw{color:#ffffff;}/*!sc*/
.jJBChh:focus{box-shadow:inset 0 2px 2px rgba(0, 0, 0, 0.45),0 2px 0 rgba(128, 128, 128, 0.25);}/*!sc*/
data-styled.g112[id="sc-iEXLnV"]{content:"jJBChh,"}/*!sc*/
.bBtSVQ{font-size:0.929em;line-height:20px;background-color:#186FAF;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.eJsTbX{font-size:0.929em;line-height:20px;background-color:#95507c;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.dgboYG{font-size:0.929em;line-height:20px;background-color:#cc3333;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.fPaeRy{font-size:0.929em;line-height:20px;background-color:#2F8132;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
data-styled.g113[id="sc-EgOky"]{content:"bBtSVQ,eJsTbX,dgboYG,fPaeRy,"}/*!sc*/
.fuVsyo{position:absolute;width:100%;z-index:100;background:#fafafa;color:#263238;box-sizing:border-box;box-shadow:0 0 6px rgba(0, 0, 0, 0.33);overflow:hidden;border-bottom-left-radius:4px;border-bottom-right-radius:4px;transition:all 0.25s ease;visibility:hidden;transform:translateY(-50%) scaleY(0);}/*!sc*/
data-styled.g114[id="sc-eZYMKX"]{content:"fuVsyo,"}/*!sc*/
.kyqjNC{padding:10px;}/*!sc*/
data-styled.g115[id="sc-dlWDvs"]{content:"kyqjNC,"}/*!sc*/
.lhFNhe{padding:5px;border:1px solid #ccc;background:#fff;word-break:break-all;color:#32329f;}/*!sc*/
.lhFNhe >span{color:#333333;}/*!sc*/
data-styled.g116[id="sc-hHOBVR"]{content:"lhFNhe,"}/*!sc*/
.kRQFMp{text-transform:lowercase;margin-left:0;line-height:1.5em;}/*!sc*/
data-styled.g117[id="sc-kWtorq"]{content:"kRQFMp,"}/*!sc*/
.hDzmxh{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);}/*!sc*/
.hDzmxh:focus{outline:auto #1d8127;}/*!sc*/
.IVbkz{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);}/*!sc*/
.IVbkz:focus{outline:auto #d41f1c;}/*!sc*/
data-styled.g119[id="sc-gdyfxU"]{content:"hDzmxh,IVbkz,"}/*!sc*/
.eBHfiv{vertical-align:top;}/*!sc*/
data-styled.g122[id="sc-bVHBsO"]{content:"eBHfiv,"}/*!sc*/
.hePoiW{font-size:1.3em;padding:0.2em 0;margin:3em 0 1.1em;color:#333333;font-weight:normal;}/*!sc*/
data-styled.g123[id="sc-dSIJcR"]{content:"hePoiW,"}/*!sc*/
.bFMrcX{margin-bottom:30px;}/*!sc*/
data-styled.g128[id="sc-ePDMnc"]{content:"bFMrcX,"}/*!sc*/
.fztUTL{user-select:none;width:20px;height:20px;align-self:center;display:flex;flex-direction:column;color:#32329f;}/*!sc*/
data-styled.g129[id="sc-bVVHAX"]{content:"fztUTL,"}/*!sc*/
.fGVzNH{width:260px;background-color:#fafafa;overflow:hidden;display:flex;flex-direction:column;backface-visibility:hidden;height:100vh;position:sticky;position:-webkit-sticky;top:0;}/*!sc*/
@media screen and (max-width: 50rem){.fGVzNH{position:fixed;z-index:20;width:100%;background:#fafafa;display:none;}}/*!sc*/
@media print{.fGVzNH{display:none;}}/*!sc*/
data-styled.g130[id="sc-dPZUdm"]{content:"fGVzNH,"}/*!sc*/
.bjfrnL{outline:none;user-select:none;background-color:#f2f2f2;color:#32329f;display:none;cursor:pointer;position:fixed;right:20px;z-index:100;border-radius:50%;box-shadow:0 0 20px rgba(0, 0, 0, 0.3);bottom:44px;width:60px;height:60px;padding:0 20px;}/*!sc*/
@media screen and (max-width: 50rem){.bjfrnL{display:flex;}}/*!sc*/
.bjfrnL svg{color:#0065FB;}/*!sc*/
@media print{.bjfrnL{display:none;}}/*!sc*/
data-styled.g131[id="sc-eBHgEO"]{content:"bjfrnL,"}/*!sc*/
.cyBpUp{font-family:Roboto,sans-serif;font-size:14px;font-weight:400;line-height:1.5em;color:#333333;display:flex;position:relative;text-align:left;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:antialiased;text-rendering:optimizeSpeed!important;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);text-size-adjust:100%;}/*!sc*/
.cyBpUp *{box-sizing:border-box;-webkit-tap-highlight-color:rgba(255, 255, 255, 0);}/*!sc*/
data-styled.g132[id="sc-iXzffn"]{content:"cyBpUp,"}/*!sc*/
.exixUa{z-index:1;position:relative;overflow:hidden;width:calc(100% - 260px);contain:layout;}/*!sc*/
@media print,screen and (max-width: 50rem){.exixUa{width:100%;}}/*!sc*/
data-styled.g133[id="sc-lnPyOc"]{content:"exixUa,"}/*!sc*/
.bYFiUR{background:#263238;position:absolute;top:0;bottom:0;right:0;width:calc((100% - 260px) * 0.4);}/*!sc*/
@media print,screen and (max-width: 75rem){.bYFiUR{display:none;}}/*!sc*/
data-styled.g134[id="sc-eulNPF"]{content:"bYFiUR,"}/*!sc*/
.fcCKrX{padding:5px 0;}/*!sc*/
data-styled.g135[id="sc-dExXmK"]{content:"fcCKrX,"}/*!sc*/
.ivCUtK{width:calc(100% - 40px);box-sizing:border-box;margin:0 20px;padding:5px 10px 5px 20px;border:0;border-bottom:1px solid #e1e1e1;font-family:Roboto,sans-serif;font-weight:bold;font-size:13px;color:#333333;background-color:transparent;outline:none;}/*!sc*/
data-styled.g136[id="sc-iapVNj"]{content:"ivCUtK,"}/*!sc*/
.bAFwPb{position:absolute;left:20px;height:1.8em;width:0.9em;}/*!sc*/
.bAFwPb path{fill:#333333;}/*!sc*/
data-styled.g137[id="sc-kqGpvY"]{content:"bAFwPb,"}/*!sc*/
</style>
  <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
</head>

<body>
  
      <div id="redoc"><div class="sc-iXzffn cyBpUp redoc-wrap"><div class="sc-dPZUdm fGVzNH menu-content" style="top:0px;height:calc(100vh - 0px)"><div role="search" class="sc-dExXmK fcCKrX"><svg class="sc-kqGpvY bAFwPb search-icon" version="1.1" viewBox="0 0 1000 1000" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px"><path d="M968.2,849.4L667.3,549c83.9-136.5,66.7-317.4-51.7-435.6C477.1-25,252.5-25,113.9,113.4c-138.5,138.3-138.5,362.6,0,501C219.2,730.1,413.2,743,547.6,666.5l301.9,301.4c43.6,43.6,76.9,14.9,104.2-12.4C981,928.3,1011.8,893,968.2,849.4z M524.5,522c-88.9,88.7-233,88.7-321.8,0c-88.9-88.7-88.9-232.6,0-321.3c88.9-88.7,233-88.7,321.8,0C613.4,289.4,613.4,433.3,524.5,522z"></path></svg><input placeholder="Search..." aria-label="Search" type="text" class="sc-iapVNj ivCUtK search-input" value=""/></div><div class="sc-dtBeHJ eyrQrY scrollbar-container undefined"><ul role="menu" class="sc-iHmqaY gVzKKH"><li tabindex="0" depth="2" data-item-id="/paths/~1signup/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">クライアント作成</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1login/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ログイン</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1login~1google/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">Googleログイン</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1login~1line/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">Lineログイン</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1editUser~1{user_ID}/put" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="put" class="sc-ehiymJ hkxktA operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">クライアント更新</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1deleteUser~1{user_ID}/delete" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="delete" class="sc-ehiymJ hkxktA operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">クライアント削除</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1logout/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ログアウト</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1getUsers/get" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="get" class="sc-ehiymJ hkxktA operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ユーザー一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1getUser/get" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="get" class="sc-ehiymJ hkxktA operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ユーザー情報取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1editUser/put" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="put" class="sc-ehiymJ hkxktA operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ユーザー情報更新</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1getSns/get" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="get" class="sc-ehiymJ hkxktA operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">SNSの一覧取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1registerSns/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">SNSの登録</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1getRanks/get" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="get" class="sc-ehiymJ hkxktA operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ランキング一覧を取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1getRank~1{ranking_ID}/get" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="get" class="sc-ehiymJ hkxktA operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ランキングを取得</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1createRank/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ランキングを作成する</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1editRank~1{ranking_ID}/put" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="put" class="sc-ehiymJ hkxktA operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ランキングを更新する</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1deleteRank~1{ranking_ID}/delete" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="delete" class="sc-ehiymJ hkxktA operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">ランキングを削除する</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1getCategories/get" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="get" class="sc-ehiymJ hkxktA operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">カテゴリー一覧を取得する</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1getCategory~1{category_ID}/get" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="get" class="sc-ehiymJ hkxktA operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">カテゴリーを取得する</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1createCategory/post" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="post" class="sc-ehiymJ hkxktA operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">カテゴリーを作成する</span></label></li><li tabindex="0" depth="2" data-item-id="/paths/~1{user_ID}~1editCategory~1{category_ID}/put" role="menuitem" class="sc-kYxEyd jiiTgL"><label class="sc-bpUCxw iwtEuE -depth2"><span type="put" class="sc-ehiymJ hkxktA operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-eyvHYj kgCeIH">カテゴリーを更新する</span></label></li></ul><div class="sc-gfopwy fGEtTM"><a target="_blank" rel="noopener noreferrer" href="https://redocly.com/redoc/">API docs by Redocly</a></div></div></div><div class="sc-eBHgEO bjfrnL"><div class="sc-bVVHAX fztUTL"><svg class="" style="transform:translate(2px, -4px) rotate(180deg);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg><svg class="" style="transform:translate(2px, 4px);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg></div></div><div class="sc-lnPyOc exixUa api-content"><div class="sc-dcJtft gWwwsw"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo api-info"><h1 class="sc-imWZod sc-fTFkHh cDCvoe hJudwp">マイランクバックエンド API<!-- --> <span>(<!-- -->1.0.0<!-- -->)</span></h1><p>Download OpenAPI specification<!-- -->:<a download="openapi.json" target="_blank" class="sc-ktJcvw dyWjRw">Download</a></p><div class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"></div><div data-role="redoc-summary" html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"></div><div data-role="redoc-description" html="&lt;p&gt;マイランクのバックエンドAPI仕様&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>マイランクのバックエンドAPI仕様</p>
</div></div></div></div><div id="/paths/~1signup/post" data-section-id="/paths/~1signup/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1signup/post" aria-label="/paths/~1signup/post"></a>クライアント作成<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;ログインするクライアントを作成する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>ログインするクライアントを作成する</p>
</div></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;ユーザー情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>ユーザー情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="email" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">email</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;ユーザーのメールアドレス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>ユーザーのメールアドレス</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="password" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">password</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;パスワード&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>パスワード</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">400<!-- --> </strong><div html="&lt;p&gt;リクエストが無効です&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>リクエストが無効です</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/signup</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/signup</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-0" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-1" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-1" aria-labelledby="react-tabs-0"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"password"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-2" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-3" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-4" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-5" data-rttab="true">400</li><li class="tab-error" role="tab" id="react-tabs-6" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-7" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-3" aria-labelledby="react-tabs-2"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;クライアントが正常に作成されました。&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"access_token"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"refresh_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-5" aria-labelledby="react-tabs-4"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-7" aria-labelledby="react-tabs-6"></div></div></div></div></div></div><div id="/paths/~1login/post" data-section-id="/paths/~1login/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1login/post" aria-label="/paths/~1login/post"></a>ログイン<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;ユーザーの認証を行う&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>ユーザーの認証を行う</p>
</div></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;ログイン情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>ログイン情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="email" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">email</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;ユーザーのメールアドレス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>ユーザーのメールアドレス</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="password" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">password</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;パスワード&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>パスワード</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">400<!-- --> </strong><div html="&lt;p&gt;リクエストが無効です&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>リクエストが無効です</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">401<!-- --> </strong><div html="&lt;p&gt;認証失敗&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>認証失敗</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/login</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/login</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-8" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-9" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-9" aria-labelledby="react-tabs-8"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"password"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-10" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-11" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-12" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-13" data-rttab="true">400</li><li class="tab-error" role="tab" id="react-tabs-14" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-15" data-rttab="true">401</li><li class="tab-error" role="tab" id="react-tabs-16" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-17" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-11" aria-labelledby="react-tabs-10"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"access_token"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"refresh_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-13" aria-labelledby="react-tabs-12"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-15" aria-labelledby="react-tabs-14"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-17" aria-labelledby="react-tabs-16"></div></div></div></div></div></div><div id="/paths/~1login~1google/post" data-section-id="/paths/~1login~1google/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1login~1google/post" aria-label="/paths/~1login~1google/post"></a>Googleログイン<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;Googleを使用したユーザー認証を行う&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>Googleを使用したユーザー認証を行う</p>
</div></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;Googleログイン情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>Googleログイン情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="google_token" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">google_token</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Googleトークン&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Googleトークン</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">400<!-- --> </strong><div html="&lt;p&gt;リクエストが無効です&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>リクエストが無効です</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">401<!-- --> </strong><div html="&lt;p&gt;認証失敗&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>認証失敗</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/login/google</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/login/google</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-18" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-19" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-19" aria-labelledby="react-tabs-18"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"google_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-20" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-21" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-22" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-23" data-rttab="true">400</li><li class="tab-error" role="tab" id="react-tabs-24" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-25" data-rttab="true">401</li><li class="tab-error" role="tab" id="react-tabs-26" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-27" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-21" aria-labelledby="react-tabs-20"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"access_token"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"refresh_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-23" aria-labelledby="react-tabs-22"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-25" aria-labelledby="react-tabs-24"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-27" aria-labelledby="react-tabs-26"></div></div></div></div></div></div><div id="/paths/~1login~1line/post" data-section-id="/paths/~1login~1line/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1login~1line/post" aria-label="/paths/~1login~1line/post"></a>Lineログイン<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;Lineを使用したユーザー認証を行う&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>Lineを使用したユーザー認証を行う</p>
</div></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;Lineログイン情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>Lineログイン情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="line_token" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">line_token</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Lineトークン&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Lineトークン</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">400<!-- --> </strong><div html="&lt;p&gt;リクエストが無効です&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>リクエストが無効です</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">401<!-- --> </strong><div html="&lt;p&gt;認証失敗&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>認証失敗</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/login/line</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/login/line</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-28" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-29" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-29" aria-labelledby="react-tabs-28"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"line_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-30" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-31" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-32" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-33" data-rttab="true">400</li><li class="tab-error" role="tab" id="react-tabs-34" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-35" data-rttab="true">401</li><li class="tab-error" role="tab" id="react-tabs-36" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-37" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-31" aria-labelledby="react-tabs-30"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"access_token"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"refresh_token"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-33" aria-labelledby="react-tabs-32"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-35" aria-labelledby="react-tabs-34"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-37" aria-labelledby="react-tabs-36"></div></div></div></div></div></div><div id="/paths/~1editUser~1{user_ID}/put" data-section-id="/paths/~1editUser~1{user_ID}/put" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1editUser~1{user_ID}/put" aria-label="/paths/~1editUser~1{user_ID}/put"></a>クライアント更新<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;既存のクライアント情報を更新する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>既存のクライアント情報を更新する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;更新するクライアント情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>更新するクライアント情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="user_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">user_ID</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="domain" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">domain</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">name</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="email" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">email</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="profile_image" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">profile_image</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="background_image" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">background_image</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="profile_description" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">profile_description</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="contact_url" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">contact_url</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="contact_phone" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">contact_phone</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class="last "><td kind="field" title="contact_email" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">contact_email</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;更新されたクライアント情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>更新されたクライアント情報</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">400<!-- --> </strong><div html="&lt;p&gt;リクエストが無効です&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>リクエストが無効です</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">404<!-- --> </strong><div html="&lt;p&gt;クライアントが見つかりません&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>クライアントが見つかりません</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="put" class="sc-EgOky eJsTbX http-verb put">put</span><span class="sc-ejfMNw gHPYVn">/editUser/{user_ID}</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/editUser/{user_ID}</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-38" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-39" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-39" aria-labelledby="react-tabs-38"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"domain"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"profile_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"background_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"profile_description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_url"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_phone"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-40" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-41" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-42" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-43" data-rttab="true">400</li><li class="tab-error" role="tab" id="react-tabs-44" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-45" data-rttab="true">404</li><li class="tab-error" role="tab" id="react-tabs-46" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-47" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-41" aria-labelledby="react-tabs-40"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;12345&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;Updated Client Name&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-43" aria-labelledby="react-tabs-42"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-45" aria-labelledby="react-tabs-44"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-47" aria-labelledby="react-tabs-46"></div></div></div></div></div></div><div id="/paths/~1deleteUser~1{user_ID}/delete" data-section-id="/paths/~1deleteUser~1{user_ID}/delete" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1deleteUser~1{user_ID}/delete" aria-label="/paths/~1deleteUser~1{user_ID}/delete"></a>クライアント削除<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;ユーザーアカウントを削除する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>ユーザーアカウントを削除する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">401<!-- --> </strong><div html="&lt;p&gt;認証失敗&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>認証失敗</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="delete" class="sc-EgOky dgboYG http-verb delete">delete</span><span class="sc-ejfMNw gHPYVn">/deleteUser/{user_ID}</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/deleteUser/{user_ID}</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-48" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-49" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-50" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-51" data-rttab="true">401</li><li class="tab-error" role="tab" id="react-tabs-52" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-53" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-49" aria-labelledby="react-tabs-48"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;ユーザーアカウントが削除されました&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-51" aria-labelledby="react-tabs-50"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-53" aria-labelledby="react-tabs-52"></div></div></div></div></div></div><div id="/paths/~1logout/post" data-section-id="/paths/~1logout/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1logout/post" aria-label="/paths/~1logout/post"></a>ログアウト<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;ユーザーをログアウトさせる&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>ユーザーをログアウトさせる</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">401<!-- --> </strong><div html="&lt;p&gt;認証失敗&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>認証失敗</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/logout</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/logout</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-54" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-55" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-56" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-57" data-rttab="true">401</li><li class="tab-error" role="tab" id="react-tabs-58" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-59" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-55" aria-labelledby="react-tabs-54"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;ログアウトに成功しました&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-57" aria-labelledby="react-tabs-56"></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-59" aria-labelledby="react-tabs-58"></div></div></div></div></div></div><div id="/paths/~1getUsers/get" data-section-id="/paths/~1getUsers/get" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1getUsers/get" aria-label="/paths/~1getUsers/get"></a>ユーザー一覧取得<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;すべてのユーザーの一覧を取得する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>すべてのユーザーの一覧を取得する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="get" class="sc-EgOky fPaeRy http-verb get">get</span><span class="sc-ejfMNw gHPYVn">/getUsers</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/getUsers</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-60" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-61" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-62" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-63" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-61" aria-labelledby="react-tabs-60"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-63" aria-labelledby="react-tabs-62"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1getUser/get" data-section-id="/paths/~1{user_ID}~1getUser/get" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1getUser/get" aria-label="/paths/~1{user_ID}~1getUser/get"></a>ユーザー情報取得<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;特定のユーザーの情報を取得する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>特定のユーザーの情報を取得する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="get" class="sc-EgOky fPaeRy http-verb get">get</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/getUser</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/getUser</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-64" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-65" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-66" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-67" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-65" aria-labelledby="react-tabs-64"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"domain"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"profile_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"background_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"profile_description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_url"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_phone"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-67" aria-labelledby="react-tabs-66"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1editUser/put" data-section-id="/paths/~1{user_ID}~1editUser/put" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1editUser/put" aria-label="/paths/~1{user_ID}~1editUser/put"></a>ユーザー情報更新<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;ユーザーの情報を更新する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>ユーザーの情報を更新する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;更新するユーザー情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>更新するユーザー情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="user_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">user_ID</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="domain" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">domain</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">name</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="email" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">email</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="profile_image" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">profile_image</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="background_image" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">background_image</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="profile_description" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">profile_description</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="contact_url" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">contact_url</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="contact_phone" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">contact_phone</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class="last "><td kind="field" title="contact_email" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">contact_email</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;更新されたユーザー情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>更新されたユーザー情報</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="put" class="sc-EgOky eJsTbX http-verb put">put</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/editUser</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/editUser</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-68" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-69" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-69" aria-labelledby="react-tabs-68"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"domain"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"profile_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"background_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"profile_description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_url"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_phone"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"contact_email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-70" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-71" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-72" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-73" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-71" aria-labelledby="react-tabs-70"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;12345&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;Updated User Name&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-73" aria-labelledby="react-tabs-72"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1getSns/get" data-section-id="/paths/~1{user_ID}~1getSns/get" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1getSns/get" aria-label="/paths/~1{user_ID}~1getSns/get"></a>SNSの一覧取得<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;すべてのSNSの一覧を取得する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>すべてのSNSの一覧を取得する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="get" class="sc-EgOky fPaeRy http-verb get">get</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/getSns</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/getSns</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-74" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-75" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-76" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-77" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-75" aria-labelledby="react-tabs-74"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"sns_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"sns_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"sns_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-77" aria-labelledby="react-tabs-76"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1registerSns/post" data-section-id="/paths/~1{user_ID}~1registerSns/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1registerSns/post" aria-label="/paths/~1{user_ID}~1registerSns/post"></a>SNSの登録<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;新しいSNSを登録する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>新しいSNSを登録する</p>
</div></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;登録するSNS情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>登録するSNS情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="sns_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">sns_ID</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class="last "><td kind="field" title="user_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">user_ID</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;登録されたSNS情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>登録されたSNS情報</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/registerSns</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/registerSns</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-78" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-79" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-79" aria-labelledby="react-tabs-78"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"sns_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-80" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-81" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-82" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-83" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-81" aria-labelledby="react-tabs-80"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;12345&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"sns_ID"</span>: <span class="token string">&quot;67890&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-83" aria-labelledby="react-tabs-82"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1getRanks/get" data-section-id="/paths/~1{user_ID}~1getRanks/get" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1getRanks/get" aria-label="/paths/~1{user_ID}~1getRanks/get"></a>ランキング一覧を取得<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;すべてのランキングの一覧を取得する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>すべてのランキングの一覧を取得する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="get" class="sc-EgOky fPaeRy http-verb get">get</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/getRanks</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/getRanks</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-84" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-85" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-86" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-87" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-85" aria-labelledby="react-tabs-84"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"ranking_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"ranking_title"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"thumbnail_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"recommend_rate"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"ranking_category"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"ranking_category_sub"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-87" aria-labelledby="react-tabs-86"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1getRank~1{ranking_ID}/get" data-section-id="/paths/~1{user_ID}~1getRank~1{ranking_ID}/get" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1getRank~1{ranking_ID}/get" aria-label="/paths/~1{user_ID}~1getRank~1{ranking_ID}/get"></a>ランキングを取得<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;特定のランキングの情報を取得する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>特定のランキングの情報を取得する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="get" class="sc-EgOky fPaeRy http-verb get">get</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/getRank/{ranking_ID}</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/getRank/{ranking_ID}</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-88" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-89" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-90" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-91" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-89" aria-labelledby="react-tabs-88"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"ranking_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_title"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_url"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"thumbnail_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"recommend_rate"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_category"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_category_sub"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"created_date"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"modified_date"</span>: <span class="token string">&quot;2019-08-24&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-91" aria-labelledby="react-tabs-90"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1createRank/post" data-section-id="/paths/~1{user_ID}~1createRank/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1createRank/post" aria-label="/paths/~1{user_ID}~1createRank/post"></a>ランキングを作成する<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;新しいランキングを作成する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>新しいランキングを作成する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;作成するランキング情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>作成するランキング情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="ranking_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_ID</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_title" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_title</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_description" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_description</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_url" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_url</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="thumbnail_image" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">thumbnail_image</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="recommend_rate" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">recommend_rate</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">integer</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_category" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_category</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_category_sub" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_category_sub</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="created_date" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">created_date</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class="last "><td kind="field" title="modified_date" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">modified_date</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;作成されたランキング情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>作成されたランキング情報</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/createRank</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/createRank</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-92" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-93" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-93" aria-labelledby="react-tabs-92"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"ranking_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_title"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_url"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"thumbnail_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"recommend_rate"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_category"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_category_sub"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"created_date"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"modified_date"</span>: <span class="token string">&quot;2019-08-24&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-94" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-95" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-96" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-97" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-95" aria-labelledby="react-tabs-94"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;12345&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_ID"</span>: <span class="token string">&quot;67890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_title"</span>: <span class="token string">&quot;New Ranking&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-97" aria-labelledby="react-tabs-96"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1editRank~1{ranking_ID}/put" data-section-id="/paths/~1{user_ID}~1editRank~1{ranking_ID}/put" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1editRank~1{ranking_ID}/put" aria-label="/paths/~1{user_ID}~1editRank~1{ranking_ID}/put"></a>ランキングを更新する<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;既存のランキングを更新する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>既存のランキングを更新する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;更新するランキング情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>更新するランキング情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="ranking_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_ID</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_title" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_title</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_description" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_description</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_url" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_url</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="thumbnail_image" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">thumbnail_image</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->uri<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="recommend_rate" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">recommend_rate</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">integer</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_category" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_category</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="ranking_category_sub" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_category_sub</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class=""><td kind="field" title="created_date" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">created_date</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr><tr class="last "><td kind="field" title="modified_date" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">modified_date</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;更新されたランキング情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>更新されたランキング情報</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="put" class="sc-EgOky eJsTbX http-verb put">put</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/editRank/{ranking_ID}</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/editRank/{ranking_ID}</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-98" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-99" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-99" aria-labelledby="react-tabs-98"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"ranking_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_title"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_description"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_url"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"thumbnail_image"</span>: <span class="token string">&quot;</span><a href="http://example.com">http://example.com</a><span class="token string">&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"recommend_rate"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_category"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_category_sub"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"created_date"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"modified_date"</span>: <span class="token string">&quot;2019-08-24&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-100" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-101" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-102" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-103" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-101" aria-labelledby="react-tabs-100"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"user_ID"</span>: <span class="token string">&quot;12345&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_ID"</span>: <span class="token string">&quot;67890&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"ranking_title"</span>: <span class="token string">&quot;Updated Ranking&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-103" aria-labelledby="react-tabs-102"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1deleteRank~1{ranking_ID}/delete" data-section-id="/paths/~1{user_ID}~1deleteRank~1{ranking_ID}/delete" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1deleteRank~1{ranking_ID}/delete" aria-label="/paths/~1{user_ID}~1deleteRank~1{ranking_ID}/delete"></a>ランキングを削除する<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;特定のランキングを削除する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>特定のランキングを削除する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;削除するランキング情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>削除するランキング情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="ranking_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">ranking_ID</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;ランキングが正常に削除されました&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>ランキングが正常に削除されました</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="delete" class="sc-EgOky dgboYG http-verb delete">delete</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/deleteRank/{ranking_ID}</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/deleteRank/{ranking_ID}</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-104" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-105" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-105" aria-labelledby="react-tabs-104"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"ranking_ID"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-106" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-107" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-108" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-109" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-107" aria-labelledby="react-tabs-106"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;ランキングが削除されました&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-109" aria-labelledby="react-tabs-108"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1getCategories/get" data-section-id="/paths/~1{user_ID}~1getCategories/get" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1getCategories/get" aria-label="/paths/~1{user_ID}~1getCategories/get"></a>カテゴリー一覧を取得する<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;すべてのカテゴリーの一覧を取得する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>すべてのカテゴリーの一覧を取得する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="get" class="sc-EgOky fPaeRy http-verb get">get</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/getCategories</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/getCategories</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-110" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-111" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-112" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-113" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-111" aria-labelledby="react-tabs-110"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable "><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"category_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"category_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"parent_ID"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-113" aria-labelledby="react-tabs-112"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1getCategory~1{category_ID}/get" data-section-id="/paths/~1{user_ID}~1getCategory~1{category_ID}/get" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1getCategory~1{category_ID}/get" aria-label="/paths/~1{user_ID}~1getCategory~1{category_ID}/get"></a>カテゴリーを取得する<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;特定のカテゴリーの情報を取得する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>特定のカテゴリーの情報を取得する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;成功時のレスポンス&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>成功時のレスポンス</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="get" class="sc-EgOky fPaeRy http-verb get">get</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/getCategory/{category_ID}</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/getCategory/{category_ID}</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-114" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-115" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-116" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-117" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-115" aria-labelledby="react-tabs-114"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"category_ID"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"category_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"parent_ID"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-117" aria-labelledby="react-tabs-116"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1createCategory/post" data-section-id="/paths/~1{user_ID}~1createCategory/post" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1createCategory/post" aria-label="/paths/~1{user_ID}~1createCategory/post"></a>カテゴリーを作成する<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;カテゴリーを作成する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>カテゴリーを作成する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;作成するカテゴリー情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>作成するカテゴリー情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="category_name" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">category_name</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;カテゴリーの名前&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>カテゴリーの名前</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="parent_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">parent_ID</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;作成されたカテゴリー情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>作成されたカテゴリー情報</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="post" class="sc-EgOky bBtSVQ http-verb post">post</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/createCategory</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/createCategory</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-118" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-119" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-119" aria-labelledby="react-tabs-118"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"category_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"parent_ID"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-120" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-121" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-122" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-123" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-121" aria-labelledby="react-tabs-120"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"category_ID"</span>: <span class="token string">&quot;12345&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"category_name"</span>: <span class="token string">&quot;New Category&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"parent_ID"</span>: <span class="token string">&quot;67890&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-123" aria-labelledby="react-tabs-122"></div></div></div></div></div></div><div id="/paths/~1{user_ID}~1editCategory~1{category_ID}/put" data-section-id="/paths/~1{user_ID}~1editCategory~1{category_ID}/put" class="sc-dcJtft dNqlFH"><div class="sc-kAycRU NrBoN"><div class="sc-fqkwJk jLkFlo"><h2 class="sc-jXbVAB hXYRhd"><a class="sc-jlZhRR buKOug" href="#/paths/~1{user_ID}~1editCategory~1{category_ID}/put" aria-label="/paths/~1{user_ID}~1editCategory~1{category_ID}/put"></a>カテゴリーを更新する<!-- --> </h2><div class="sc-ePDMnc bFMrcX"><div html="&lt;p&gt;既存のカテゴリーを更新する&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>既存のカテゴリーを更新する</p>
</div></div><div><h5 class="sc-dAlxHm fZUozT">header<!-- --> Parameters</h5><table class="sc-dAbbbq hPLMVw"><tbody><tr class="last "><td kind="field" title="Authorization" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">Authorization</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;Bearerトークン形式の認証ヘッダー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>Bearerトークン形式の認証ヘッダー</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-dAlxHm fZUozT">Request Body schema: <span class="sc-cWSIco fpgAnb">application/json</span><div class="sc-NxrBK sc-hRJeED sc-kWtorq kMMoBE gypzqL kRQFMp">required</div></h5><div html="&lt;p&gt;更新するカテゴリー情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq"><p>更新するカテゴリー情報</p>
</div><table class="sc-dAbbbq hPLMVw"><tbody><tr class=""><td kind="field" title="category_name" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">category_name</span><div class="sc-NxrBK sc-hRJeED kMMoBE gypzqL">required</div></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="&lt;p&gt;カテゴリーの名前&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"><p>カテゴリーの名前</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="parent_ID" class="sc-dLNtp sc-eldOKa fCxZSn jTSwDU"><span class="sc-gFqAYk fWSzIS"></span><span class="property-name">parent_ID</span></td><td class="sc-fPXMhL kdholP"><div><div><span class="sc-NxrBK sc-cfxfQh kMMoBE hMkdpJ"></span><span class="sc-NxrBK sc-gFAXEw kMMoBE ffgeXz">string</span></div> <div><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div></div></div></td></tr></tbody></table><div><h3 class="sc-dSIJcR hePoiW">Responses</h3><div><button class="sc-gdyfxU hDzmxh"><svg class="sc-cwHqhk jqLwgG" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">200<!-- --> </strong><div html="&lt;p&gt;更新されたカテゴリー情報&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>更新されたカテゴリー情報</p>
</div></button></div><div><button class="sc-gdyfxU IVbkz"><svg class="sc-cwHqhk bBKyWu" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-bVHBsO eBHfiv">500<!-- --> </strong><div html="&lt;p&gt;サーバーエラー&lt;/p&gt;
" class="sc-eeDSqt sc-eBMFzZ bSgSrX cWARBq sc-dCFGXG gKOXES"><p>サーバーエラー</p>
</div></button></div></div></div><div class="sc-iGgVNO sc-gsFSjX hqwGrr ggsSF"><div class="sc-dkmVhU ciDfNo"><button class="sc-iEXLnV jJBChh"><span type="put" class="sc-EgOky eJsTbX http-verb put">put</span><span class="sc-ejfMNw gHPYVn">/{user_ID}/editCategory/{category_ID}</span><svg class="sc-cwHqhk fxsrq" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-eZYMKX fuVsyo"><div class="sc-dlWDvs kyqjNC"><div html="" class="sc-eeDSqt sc-eBMFzZ bSgSrX gayXgA"></div><div tabindex="0" role="button"><div class="sc-hHOBVR lhFNhe"><span></span>/{user_ID}/editCategory/{category_ID}</div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Request samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="react-tabs-124" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-125" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-125" aria-labelledby="react-tabs-124"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"category_name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"parent_ID"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-kpDprT cgzTaG"> <!-- -->Response samples<!-- --> </h3><div class="sc-bXCLgj tlGxN" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="react-tabs-126" aria-selected="true" aria-disabled="false" aria-controls="react-tabs-127" tabindex="0" data-rttab="true">200</li><li class="tab-error" role="tab" id="react-tabs-128" aria-selected="false" aria-disabled="false" aria-controls="react-tabs-129" data-rttab="true">500</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="react-tabs-127" aria-labelledby="react-tabs-126"><div><div class="sc-bbSYpP kIESIX"><span class="sc-fjvwmM bLpZTa">Content type</span><div class="sc-JrEyx OirWV">application/json</div></div><div class="sc-uVXKs fszzxn"><div class="sc-esYjtY hxmTDe"><div class="sc-koYCI kEwLiZ"><button><div class="sc-fhzEvr gpWFPc">Copy</div></button></div><div class="sc-eeDSqt bSgSrX sc-fXSgRJ dXllNu"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"category_ID"</span>: <span class="token string">&quot;12345&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"category_name"</span>: <span class="token string">&quot;Updated Category&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"parent_ID"</span>: <span class="token string">&quot;67890&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="react-tabs-129" aria-labelledby="react-tabs-128"></div></div></div></div></div></div></div><div class="sc-eulNPF bYFiUR"></div></div></div>
      <script>
      const __redoc_state = {"menu":{"activeItemIdx":-1},"spec":{"data":{"openapi":"3.0.0","info":{"title":"マイランクバックエンド API","description":"マイランクのバックエンドAPI仕様","version":"1.0.0"},"paths":{"/signup":{"post":{"summary":"クライアント作成","description":"ログインするクライアントを作成する","requestBody":{"description":"ユーザー情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string","description":"ユーザーのメールアドレス"},"password":{"type":"string","description":"パスワード"}},"required":["password","email"]}}}},"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","example":"クライアントが正常に作成されました。"},"user_ID":{"type":"string","description":"ユーザーID"},"access_token":{"type":"string","description":"アクセストークン"},"refresh_token":{"type":"string","description":"リフレッシュトークン"}},"required":["message","user_ID","access_token","refresh_token"]}}}},"400":{"description":"リクエストが無効です","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"無効なリクエストパラメータ"}},"required":["error"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/login":{"post":{"summary":"ログイン","description":"ユーザーの認証を行う","requestBody":{"description":"ログイン情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string","description":"ユーザーのメールアドレス"},"password":{"type":"string","description":"パスワード"}},"required":["email","password"]}}}},"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string","description":"ユーザーID"},"access_token":{"type":"string","description":"アクセストークン"},"refresh_token":{"type":"string","description":"リフレッシュトークン"}},"required":["access_token","refresh_token"]}}}},"400":{"description":"リクエストが無効です","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"無効なリクエストパラメータ"}},"required":["error"]}}}},"401":{"description":"認証失敗","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"認証に失敗しました"}},"required":["error"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/login/google":{"post":{"summary":"Googleログイン","description":"Googleを使用したユーザー認証を行う","requestBody":{"description":"Googleログイン情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"google_token":{"type":"string","description":"Googleトークン"}},"required":["google_token"]}}}},"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"access_token":{"type":"string","description":"アクセストークン"},"refresh_token":{"type":"string","description":"リフレッシュトークン"}},"required":["access_token","refresh_token"]}}}},"400":{"description":"リクエストが無効です","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"無効なリクエストパラメータ"}},"required":["error"]}}}},"401":{"description":"認証失敗","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"認証に失敗しました"}},"required":["error"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/login/line":{"post":{"summary":"Lineログイン","description":"Lineを使用したユーザー認証を行う","requestBody":{"description":"Lineログイン情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"line_token":{"type":"string","description":"Lineトークン"}},"required":["line_token"]}}}},"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"access_token":{"type":"string","description":"アクセストークン"},"refresh_token":{"type":"string","description":"リフレッシュトークン"}},"required":["access_token","refresh_token"]}}}},"400":{"description":"リクエストが無効です","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"無効なリクエストパラメータ"}},"required":["error"]}}}},"401":{"description":"認証失敗","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"認証に失敗しました"}},"required":["error"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/editUser/{user_ID}":{"put":{"summary":"クライアント更新","description":"既存のクライアント情報を更新する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"requestBody":{"description":"更新するクライアント情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string"},"domain":{"type":"string"},"name":{"type":"string"},"email":{"type":"string"},"profile_image":{"type":"string","format":"uri"},"background_image":{"type":"string","format":"uri"},"profile_description":{"type":"string"},"contact_url":{"type":"string","format":"uri"},"contact_phone":{"type":"string"},"contact_email":{"type":"string","format":"email"}},"required":["user_ID"]}}}},"responses":{"200":{"description":"更新されたクライアント情報","content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string","example":"12345"},"name":{"type":"string","example":"Updated Client Name"}}}}}},"400":{"description":"リクエストが無効です","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"無効なリクエストパラメータ"}},"required":["error"]}}}},"404":{"description":"クライアントが見つかりません","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"クライアントが見つかりません"}},"required":["error"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/deleteUser/{user_ID}":{"delete":{"summary":"クライアント削除","description":"ユーザーアカウントを削除する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","example":"ユーザーアカウントが削除されました"}},"required":["message"]}}}},"401":{"description":"認証失敗","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"認証に失敗しました"}},"required":["error"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/logout":{"post":{"summary":"ログアウト","description":"ユーザーをログアウトさせる","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","example":"ログアウトに成功しました"}},"required":["message"]}}}},"401":{"description":"認証失敗","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"認証に失敗しました"}},"required":["error"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/getUsers":{"get":{"summary":"ユーザー一覧取得","description":"すべてのユーザーの一覧を取得する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"array","items":{"type":"object","properties":{"user_ID":{"type":"string"},"name":{"type":"string"}}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/getUser":{"get":{"summary":"ユーザー情報取得","description":"特定のユーザーの情報を取得する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string"},"domain":{"type":"string"},"name":{"type":"string"},"email":{"type":"string"},"profile_image":{"type":"string","format":"uri"},"background_image":{"type":"string","format":"uri"},"profile_description":{"type":"string"},"contact_url":{"type":"string","format":"uri"},"contact_phone":{"type":"string"},"contact_email":{"type":"string","format":"email"}},"required":["user_ID"]}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/editUser":{"put":{"summary":"ユーザー情報更新","description":"ユーザーの情報を更新する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"requestBody":{"description":"更新するユーザー情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string"},"domain":{"type":"string"},"name":{"type":"string"},"email":{"type":"string"},"profile_image":{"type":"string","format":"uri"},"background_image":{"type":"string","format":"uri"},"profile_description":{"type":"string"},"contact_url":{"type":"string","format":"uri"},"contact_phone":{"type":"string"},"contact_email":{"type":"string","format":"email"}},"required":["user_ID"]}}}},"responses":{"200":{"description":"更新されたユーザー情報","content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string","example":"12345"},"name":{"type":"string","example":"Updated User Name"},"email":{"type":"string","example":"<EMAIL>"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/getSns":{"get":{"summary":"SNSの一覧取得","description":"すべてのSNSの一覧を取得する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"array","items":{"type":"object","properties":{"sns_ID":{"type":"string"},"user_ID":{"type":"string"},"sns_name":{"type":"string"},"sns_image":{"type":"string","format":"uri"}}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/registerSns":{"post":{"summary":"SNSの登録","description":"新しいSNSを登録する","requestBody":{"description":"登録するSNS情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"sns_ID":{"type":"string"},"user_ID":{"type":"string"}},"required":["sns_ID","user_ID"]}}}},"responses":{"200":{"description":"登録されたSNS情報","content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string","example":"12345"},"sns_ID":{"type":"string","example":"67890"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/getRanks":{"get":{"summary":"ランキング一覧を取得","description":"すべてのランキングの一覧を取得する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"array","items":{"type":"object","properties":{"ranking_ID":{"type":"string"},"ranking_title":{"type":"string"},"thumbnail_image":{"type":"string","format":"uri"},"recommend_rate":{"type":"integer"},"ranking_category":{"type":"string"},"ranking_category_sub":{"type":"string"}}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/getRank/{ranking_ID}":{"get":{"summary":"ランキングを取得","description":"特定のランキングの情報を取得する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"ranking_ID":{"type":"string"},"ranking_title":{"type":"string"},"ranking_description":{"type":"string"},"ranking_url":{"type":"string"},"thumbnail_image":{"type":"string","format":"uri"},"recommend_rate":{"type":"integer"},"ranking_category":{"type":"string"},"ranking_category_sub":{"type":"string"},"created_date":{"type":"string","format":"date"},"modified_date":{"type":"string","format":"date"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/createRank":{"post":{"summary":"ランキングを作成する","description":"新しいランキングを作成する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"requestBody":{"description":"作成するランキング情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"ranking_ID":{"type":"string"},"ranking_title":{"type":"string"},"ranking_description":{"type":"string"},"ranking_url":{"type":"string"},"thumbnail_image":{"type":"string","format":"uri"},"recommend_rate":{"type":"integer"},"ranking_category":{"type":"string"},"ranking_category_sub":{"type":"string"},"created_date":{"type":"string","format":"date"},"modified_date":{"type":"string","format":"date"}},"required":["ranking_ID","ranking_title","thumbnail_image","recommend_rate"]}}}},"responses":{"200":{"description":"作成されたランキング情報","content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string","example":"12345"},"ranking_ID":{"type":"string","example":"67890"},"ranking_title":{"type":"string","example":"New Ranking"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/editRank/{ranking_ID}":{"put":{"summary":"ランキングを更新する","description":"既存のランキングを更新する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"requestBody":{"description":"更新するランキング情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"ranking_ID":{"type":"string"},"ranking_title":{"type":"string"},"ranking_description":{"type":"string"},"ranking_url":{"type":"string"},"thumbnail_image":{"type":"string","format":"uri"},"recommend_rate":{"type":"integer"},"ranking_category":{"type":"string"},"ranking_category_sub":{"type":"string"},"created_date":{"type":"string","format":"date"},"modified_date":{"type":"string","format":"date"}},"required":["ranking_ID"]}}}},"responses":{"200":{"description":"更新されたランキング情報","content":{"application/json":{"schema":{"type":"object","properties":{"user_ID":{"type":"string","example":"12345"},"ranking_ID":{"type":"string","example":"67890"},"ranking_title":{"type":"string","example":"Updated Ranking"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/deleteRank/{ranking_ID}":{"delete":{"summary":"ランキングを削除する","description":"特定のランキングを削除する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"requestBody":{"description":"削除するランキング情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"ranking_ID":{"type":"string"}}}}}},"responses":{"200":{"description":"ランキングが正常に削除されました","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string","example":"ランキングが削除されました"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/getCategories":{"get":{"summary":"カテゴリー一覧を取得する","description":"すべてのカテゴリーの一覧を取得する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"array","items":{"type":"object","properties":{"category_ID":{"type":"string"},"category_name":{"type":"string"},"parent_ID":{"type":"string"}}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/getCategory/{category_ID}":{"get":{"summary":"カテゴリーを取得する","description":"特定のカテゴリーの情報を取得する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"responses":{"200":{"description":"成功時のレスポンス","content":{"application/json":{"schema":{"type":"object","properties":{"category_ID":{"type":"string"},"category_name":{"type":"string"},"parent_ID":{"type":"string"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/createCategory":{"post":{"summary":"カテゴリーを作成する","description":"カテゴリーを作成する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"requestBody":{"description":"作成するカテゴリー情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"category_name":{"type":"string","description":"カテゴリーの名前"},"parent_ID":{"type":"string"}},"required":["category_name"]}}}},"responses":{"200":{"description":"作成されたカテゴリー情報","content":{"application/json":{"schema":{"type":"object","properties":{"category_ID":{"type":"string","example":"12345"},"category_name":{"type":"string","example":"New Category"},"parent_ID":{"type":"string","example":"67890"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}},"/{user_ID}/editCategory/{category_ID}":{"put":{"summary":"カテゴリーを更新する","description":"既存のカテゴリーを更新する","parameters":[{"name":"Authorization","in":"header","required":true,"schema":{"type":"string"},"description":"Bearerトークン形式の認証ヘッダー"}],"requestBody":{"description":"更新するカテゴリー情報","required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"category_name":{"type":"string","description":"カテゴリーの名前"},"parent_ID":{"type":"string"}},"required":["category_name"]}}}},"responses":{"200":{"description":"更新されたカテゴリー情報","content":{"application/json":{"schema":{"type":"object","properties":{"category_ID":{"type":"string","example":"12345"},"category_name":{"type":"string","example":"Updated Category"},"parent_ID":{"type":"string","example":"67890"}}}}}},"500":{"description":"サーバーエラー","content":{"application/json":{"schema":{"type":"object","properties":{"error":{"type":"string","example":"サーバー内部エラーが発生しました"}},"required":["error"]}}}}}}}},"components":{"securitySchemes":{"bearerAuth":{"type":"http","scheme":"bearer","bearerFormat":"JWT"}}}}},"searchIndex":{"store":["/paths/~1signup/post","/paths/~1login/post","/paths/~1login~1google/post","/paths/~1login~1line/post","/paths/~1editUser~1{user_ID}/put","/paths/~1deleteUser~1{user_ID}/delete","/paths/~1logout/post","/paths/~1getUsers/get","/paths/~1{user_ID}~1getUser/get","/paths/~1{user_ID}~1editUser/put","/paths/~1{user_ID}~1getSns/get","/paths/~1{user_ID}~1registerSns/post","/paths/~1{user_ID}~1getRanks/get","/paths/~1{user_ID}~1getRank~1{ranking_ID}/get","/paths/~1{user_ID}~1createRank/post","/paths/~1{user_ID}~1editRank~1{ranking_ID}/put","/paths/~1{user_ID}~1deleteRank~1{ranking_ID}/delete","/paths/~1{user_ID}~1getCategories/get","/paths/~1{user_ID}~1getCategory~1{category_ID}/get","/paths/~1{user_ID}~1createCategory/post","/paths/~1{user_ID}~1editCategory~1{category_ID}/put"],"index":{"version":"2.3.9","fields":["title","description"],"fieldVectors":[["title/0",[0,0.309]],["description/0",[0,0.309,1,2.686]],["title/1",[0,0.309]],["description/1",[0,0.309,2,2.686]],["title/2",[3,2.175]],["description/2",[3,2.175,4,2.686]],["title/3",[5,2.175]],["description/3",[5,2.175,6,2.686]],["title/4",[0,0.309]],["description/4",[0,0.309,7,2.686]],["title/5",[0,0.309]],["description/5",[0,0.309,8,2.686]],["title/6",[0,0.309]],["description/6",[0,0.309,9,2.686]],["title/7",[0,0.309]],["description/7",[0,0.309,10,2.686]],["title/8",[0,0.309]],["description/8",[0,0.309,11,2.686]],["title/9",[0,0.309]],["description/9",[0,0.309,12,2.686]],["title/10",[13,1.587]],["description/10",[13,1.587,14,2.686]],["title/11",[13,1.587]],["description/11",[13,1.587,15,2.686]],["title/12",[0,0.309]],["description/12",[0,0.309,16,2.686]],["title/13",[0,0.309]],["description/13",[0,0.309,17,2.686]],["title/14",[0,0.309]],["description/14",[0,0.309,18,2.686]],["title/15",[0,0.309]],["description/15",[0,0.309,19,2.686]],["title/16",[0,0.309]],["description/16",[0,0.309,20,2.686]],["title/17",[0,0.309]],["description/17",[0,0.309,21,2.686]],["title/18",[0,0.309]],["description/18",[0,0.309,22,2.686]],["title/19",[0,0.309]],["description/19",[0,0.309,23,2.686]],["title/20",[0,0.309]],["description/20",[0,0.309,24,2.686]]],"invertedIndex":[["",{"_index":0,"title":{"0":{},"1":{},"4":{},"5":{},"6":{},"7":{},"8":{},"9":{},"12":{},"13":{},"14":{},"15":{},"16":{},"17":{},"18":{},"19":{},"20":{}},"description":{"0":{},"1":{},"4":{},"5":{},"6":{},"7":{},"8":{},"9":{},"12":{},"13":{},"14":{},"15":{},"16":{},"17":{},"18":{},"19":{},"20":{}}}],["deleteuser/{user_id",{"_index":8,"title":{},"description":{"5":{}}}],["edituser/{user_id",{"_index":7,"title":{},"description":{"4":{}}}],["getus",{"_index":10,"title":{},"description":{"7":{}}}],["googl",{"_index":3,"title":{"2":{}},"description":{"2":{}}}],["line",{"_index":5,"title":{"3":{}},"description":{"3":{}}}],["login",{"_index":2,"title":{},"description":{"1":{}}}],["login/googl",{"_index":4,"title":{},"description":{"2":{}}}],["login/lin",{"_index":6,"title":{},"description":{"3":{}}}],["logout",{"_index":9,"title":{},"description":{"6":{}}}],["signup",{"_index":1,"title":{},"description":{"0":{}}}],["sn",{"_index":13,"title":{"10":{},"11":{}},"description":{"10":{},"11":{}}}],["user_id}/createcategori",{"_index":23,"title":{},"description":{"19":{}}}],["user_id}/createrank",{"_index":18,"title":{},"description":{"14":{}}}],["user_id}/deleterank/{ranking_id",{"_index":20,"title":{},"description":{"16":{}}}],["user_id}/editcategory/{category_id",{"_index":24,"title":{},"description":{"20":{}}}],["user_id}/editrank/{ranking_id",{"_index":19,"title":{},"description":{"15":{}}}],["user_id}/editus",{"_index":12,"title":{},"description":{"9":{}}}],["user_id}/getcategori",{"_index":21,"title":{},"description":{"17":{}}}],["user_id}/getcategory/{category_id",{"_index":22,"title":{},"description":{"18":{}}}],["user_id}/getrank",{"_index":16,"title":{},"description":{"12":{}}}],["user_id}/getrank/{ranking_id",{"_index":17,"title":{},"description":{"13":{}}}],["user_id}/getsn",{"_index":14,"title":{},"description":{"10":{}}}],["user_id}/getus",{"_index":11,"title":{},"description":{"8":{}}}],["user_id}/registersn",{"_index":15,"title":{},"description":{"11":{}}}]],"pipeline":[]}},"options":{}};

      var container = document.getElementById('redoc');
      Redoc.hydrate(__redoc_state, container);

      </script>
</body>

</html>
