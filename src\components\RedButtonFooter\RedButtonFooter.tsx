/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import PropTypes from "prop-types";
import React from "react";

interface Props {
  buttonYellowClassName: any;
  hasFrameLeft: boolean;
  divClassName: any;
  text: string;
  hasFrameRight: boolean;
  onClick?: () => void;
}

export const RedButtonFooter = ({
  buttonYellowClassName,
  hasFrameLeft = true,
  divClassName,
  text = "ランキングを作る",
  hasFrameRight = true,
  onClick,
}: Props): JSX.Element => {
  return (
    <div className="flex flex-col w-[500px] max-w-[500px] h-[56px] items-center justify-center fixed bottom-0 left-1/2 -translate-x-1/2 bg-white border-t [border-top-style:solid] border-line-color">
      <div
        className={`flex w-[90%] h-[40px] items-center relative bg-button-yellow rounded-[100px] overflow-hidden ${buttonYellowClassName}`}
        onClick={onClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            onClick && onClick();
          }
        }}
      >
        {hasFrameLeft && <div className="relative w-[115px] h-[40px] rounded-[100px_0px_0px_100px]" />}

        <div className="inline-flex flex-col h-[40px] items-center justify-center relative flex-[0_0_auto]">
          <div
            className={`relative
              text-black text-[14px] text-center tracking-[0] leading-[normal] whitespace-nowrap ${divClassName}`}
          >
            {text}
          </div>
        </div>
        {hasFrameRight && (
          <div className="flex w-[115px] h-[40px] items-center justify-end pl-0 pr-[8px] py-0 relative rounded-[0px_100px_100px_0px] overflow-hidden">
            <img className="relative w-[24px] h-[24px] object-cover" alt="Image move on" src="/static/img/imagemoveon.png" />
          </div>
        )}
      </div>
    </div>
  );
};

RedButtonFooter.propTypes = {
  hasFrameLeft: PropTypes.bool,
  text: PropTypes.string,
  hasFrameRight: PropTypes.bool,
  onClick: PropTypes.func,
};
