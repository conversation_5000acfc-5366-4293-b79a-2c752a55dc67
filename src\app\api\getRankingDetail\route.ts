import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ranking_ID = searchParams.get('ranking_ID');

    // ranking_IDが指定されていない場合
    if (!ranking_ID) {
      console.error('getRankingDetail: ranking_IDが指定されていません');
      return NextResponse.json(
        { error: 'ranking_IDが必要です' }, 
        { status: 400 }
      );
    }

    console.log('getRankingDetail: ランキング詳細を取得中...', { ranking_ID });

    // ランキング詳細を取得
    const ranking = await prisma.ranking.findFirst({
      where: {
        ranking_ID: ranking_ID,
        status: 'PUBLISHED' // 公開済みのみ取得
      },
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        thumbnail_image: true,
        recommend_rate: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        user_ID: true,
        subCategory_ID: true,
        order: true,
        status: true,
        created_at: true,
        updated_at: true
      }
    });

    // ランキングが見つからない場合
    if (!ranking) {
      console.error('getRankingDetail: ランキングが見つかりません', { ranking_ID });
      return NextResponse.json(
        { error: 'ランキングが見つかりません' }, 
        { status: 404 }
      );
    }

    // カテゴリ情報を取得
    let categoryInfo = null;
    if (ranking.subCategory_ID) {
      try {
        const subCategory = await prisma.category.findFirst({
          where: {
            id: ranking.subCategory_ID
          },
          select: {
            id: true,
            category_name: true,
            parent_ID: true
          }
        });

        if (subCategory) {
          // 親カテゴリ情報も取得
          let parentCategory = null;
          if (subCategory.parent_ID) {
            parentCategory = await prisma.category.findFirst({
              where: {
                id: subCategory.parent_ID
              },
              select: {
                id: true,
                category_name: true
              }
            });
          }

          categoryInfo = {
            subcategory_name: subCategory.category_name,
            category_name: parentCategory?.category_name || null
          };
        }
      } catch (categoryError) {
        console.warn('getRankingDetail: カテゴリ情報の取得に失敗しました', categoryError);
        // カテゴリ情報の取得に失敗してもランキング情報は返す
      }
    }

    // レスポンスデータを構築
    const responseData = {
      ...ranking,
      category_name: categoryInfo?.category_name || null,
      subcategory_name: categoryInfo?.subcategory_name || null,
      logo_image_myrank: '/static/img/logo.png' // デフォルトロゴ
    };

    console.log('getRankingDetail: ランキング詳細を正常に取得しました', {
      ranking_ID: responseData.ranking_ID,
      title: responseData.ranking_title,
      user_ID: responseData.user_ID
    });

    // 配列形式で返す（既存のコードとの互換性のため）
    return NextResponse.json([responseData], { status: 200 });

  } catch (error) {
    console.error('getRankingDetail: エラーが発生しました:', error);
    return NextResponse.json(
      { error: 'ランキング詳細の取得に失敗しました' }, 
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
