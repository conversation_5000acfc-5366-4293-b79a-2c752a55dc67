/* ==========================================================================
   IMPORTS
   ========================================================================== */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500&display=swap');
@import url('http://fonts.googleapis.com/earlyaccess/notosansjp.css');

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Clerkのデフォルトカードを透明化 */
.cl-card {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

.cl-rootBox {
  width: 100% !important;
}

.cl-main {
  width: 100% !important;
}

/* 全体のコンテナ */
.cl-component {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

/* ソーシャルボタンのレイアウト */
.cl-socialButtonsBlock {
  width: 100% !important;
}

/* ソーシャルボタンのスタイル */
.cl-socialButtonsBlockButton {
  width: 100% !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding: 0 12px !important;
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  color: #1F1F1F !important;
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  border-radius: 100px !important;
  box-shadow: none !important;
  transition: background-color 0.2s !important;
}

.cl-socialButtonsBlockButton:hover {
  background-color: #e6f0ff !important;
}

/* アイコンとテキストのスタイル */
.cl-socialButtonsProviderIcon {
  width: 20px !important;
  height: 20px !important;
  margin-right: 10px !important;
}

.cl-socialButtonsBlockButtonText {
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
}

/* メインボタンのスタイル */
.cl-formButtonPrimary {
  width: 100% !important;
  height: 44px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  border: none !important;
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  background-color: #E63B5F !important;
  border-radius: 100px !important;
  color: #FFFFFF !important;
  box-shadow: none !important;
  transition: background-color 0.2s !important;
}

.cl-formButtonPrimary:hover {
  background-color: #DD0F2B !important;
}

/* 入力フィールド */
.cl-formFieldInput {
  width: 100% !important;
  padding: 8px 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  font-size: 14px !important;
  line-height: 20px !important;
}

.cl-formFieldInput::placeholder {
  color: #9ca3af !important;
}

.cl-formFieldInput:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* ラベル */
.cl-formFieldLabel {
  display: block !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 4px !important;
}

/* 区切り線 */
.cl-dividerRow {
  position: relative !important;
  margin: 24px 0 !important;
}

.cl-dividerLine {
  width: 100% !important;
  border-top: 1px solid #d1d5db !important;
}

.cl-dividerText {
  padding: 0 8px !important;
  background-color: white !important;
  color: #6b7280 !important;
  font-size: 14px !important;
}

/* フォーム全体 */
.cl-form {
  display: flex !important;
  flex-direction: column !important;
  gap: 16px !important;
}

.cl-formFieldRow {
  display: flex !important;
  flex-direction: column !important;
  gap: 16px !important;
}

/* ヘッダーを非表示 */
.cl-headerTitle,
.cl-headerSubtitle {
  display: none !important;
}

/* フッターを非表示 */
.cl-footer,
.cl-footerActionLink,
.cl-footerActionText {
  display: none !important;
}

/* 全体のシャドウを削除 */
.cl-signUp-start,
.cl-signIn-start,
.cl-signUp-complete,
.cl-component,
.cl-card,
.cl-cardBox,
.cl-main {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

/* 利用規約同意ページのシャドウを削除 */
.cl-signUp-complete .cl-card {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

.cl-cardBox.cl-signUp-complete {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

/* ==========================================================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   ========================================================================== */
:root {
  /* Base Colors */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* Button Colors */
  --button-yellow: #FFD814;
  --button-yellow-2nd: #E1BC03;
  --button-red: #E63B5F;
  --button-red-2nd: #DD0F2B;
  --button-black: #313131;
  --button-gray: #AAAAAA;
  --button-gray-2nd: #676767;

  /* Font Colors */
  --font-yellow: #FFD814;
  --font-red: #E63B5F;
  --font-link: #007CBA;
  --font-link-2nd: #1D9BF0;
  --font-black: #313131;
  --font-gray: #AAAAAA;
  --font-gray-2nd: #676767;

  /* Background Colors */
  --background-black: #000000;
  --background-light-black: #333333;
  --background-gray: #F6F7F8;
  --background-contrast: #FFFFFF;
  --background-contrast-2nd: #F5F5F5;

  /* Line Colors */
  --line-color: #DDDDDD;
  --line-color-light: #F5F5F5;

  /* Special Colors */
  --star-color: #FFC700;
  --star-background-color: #AAAAAA;
  --primary: #FF2442;
  --primary-contrast: #FFFFFF;
  --review: #FFC700;
  --alert: #FFFBE3;

  /* Layout */
  --header-offset: 320px;
  --toggle-button: 0px 1px 4px 0px rgba(0, 0, 0, 0.25);
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

/* Container */
.container {
  @apply flex flex-col w-full max-w-[500px] mx-auto min-h-[1400px] max-h-[1400px] items-center relative shadow-[-10px_0_20px_rgba(0,0,0,0.1),10px_0_20px_rgba(0,0,0,0.1)] bg-white;
}

/* Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  display: none;
}

.custom-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Scroll Offset */
.scroll-offset {
  scroll-margin-top: var(--header-offset);
}

/* ==========================================================================
   ANIMATIONS
   ========================================================================== */
@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* ==========================================================================
   DRAG & DROP STYLES
   ========================================================================== */
[data-dragging-id] .sortable-sns-icon:not([data-id]) {
  transform: scale(1.05);
  background-color: rgba(230, 230, 230, 0.5);
  border-radius: 8px;
  transition: all 0.2s ease;
}

[data-dragging-id] .sortable-sns-icon:hover::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 0;
  right: 0;
  height: 4px;
  background-color: var(--font-link);
  border-radius: 2px;
  animation: pulse 1s infinite;
}

/* ==========================================================================
   SNS EMBED STYLES - PLATFORM SPECIFIC
   ========================================================================== */

/* 🔧 修正: 基本的な段落スタイル（位置固定問題解決） */
.rich-text-editor [contenteditable] p {
  margin: 0.8em 0 !important;
  line-height: 1.8 !important;
  display: block !important;
  clear: both !important;
  position: relative !important;
  background-color: transparent !important;
}

/* 🔧 修正: 空の段落の適切な表示（改行機能のため） */
.rich-text-editor [contenteditable] p:has(> br:only-child) {
  margin: 0.8em 0 !important;
  min-height: 1.8em !important;
  line-height: 1.8 !important;
  display: block !important;
  clear: both !important;
}

/* 🔧 修正: 段落内のbrタグの表示を確保 */
.rich-text-editor [contenteditable] p br {
  display: block !important;
  margin: 0 !important;
  line-height: 1.8 !important;
  clear: both !important;
}

/* ---------------------------------------------------------
   RICH TEXT EDITOR BASE STYLES
   --------------------------------------------------------- */

/* リッチテキストエディタの基本スタイル */
.rich-text-editor [contenteditable] {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  cursor: text !important;
  background: white !important;
 
 
  border-radius: 4px !important;
  outline: none !important;
  line-height: 1.6 !important;
  font-family: inherit !important;
  font-size: 14px !important;
  color: #333 !important;

  overflow-x: hidden !important;
  word-wrap: break-word !important;

}

/* 🔧 修正: 埋め込み要素のスペーサー段落（位置固定問題解決） */
.rich-text-editor [contenteditable] p[data-embed-spacer] {
  margin: 0.5em 0 !important;
  line-height: 1.2 !important;
  clear: both !important;
  position: relative !important;
  cursor: text !important;
  outline: none !important;
  border: none !important;
  background: transparent !important;
  user-select: text !important;
  pointer-events: auto !important;
}

/* 🔧 修正: テキスト要素の基本スタイル（重複表示バグ解決） */
.rich-text-editor [contenteditable] p,
.rich-text-editor [contenteditable] div,
.rich-text-editor [contenteditable] h1,
.rich-text-editor [contenteditable] h2,
.rich-text-editor [contenteditable] h3 {
  background-color: transparent !important;
  position: static !important;
  contain: none !important;
  /* 🔧 テキスト重複防止 */
  display: block !important;
  clear: both !important;
  overflow: visible !important;
  isolation: auto !important;
}

/* 🔧 修正: H2要素の後の段落要素の改行幅を最小化 */
.rich-text-editor [contenteditable] h2 + p {
  margin-top: 0.5em !important;
  margin-bottom: 0 !important;
  line-height: 1.4 !important;
  font-size: inherit !important;
  font-weight: normal !important;
  border-left: none !important;
  padding: 0 !important;
  color: inherit !important;
}

/* 🔧 修正: H3要素の後の段落要素の改行幅を最小化 */
.rich-text-editor [contenteditable] h3 + p {
  margin-top: 0.5em !important;
  margin-bottom: 0 !important;
  line-height: 1.4 !important;
  font-size: inherit !important;
  font-weight: normal !important;
  border-bottom: none !important;
  padding: 0 !important;
  color: inherit !important;
}

/* 🔧 修正: 通常の段落要素のマージン設定（行間余白を最小化） */
.rich-text-editor [contenteditable] p {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  line-height: 1.4 !important;
}

/* 🔧 修正: <br>タグによる行内改行のスタイル（確実な表示） */
.rich-text-editor [contenteditable] br {
  display: block !important;
  line-height: 1.4 !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 1.4em !important;
  content: "" !important;
  white-space: pre !important;
}

/* 🔧 追加: <br>タグの後の要素の表示を確実にする */
.rich-text-editor [contenteditable] br + * {
  display: block !important;
}

/* 🔧 追加: 空のテキストノードでもカーソルが表示されるようにする */
.rich-text-editor [contenteditable]:focus {
  caret-color: #333 !important;
}

/* 🔧 追加: 埋め込み要素のスペーサーのスタイル統一 */
.rich-text-editor [contenteditable] p[data-embed-spacer] {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  line-height: 1.4 !important;
  padding: 0 !important;
  font-size: inherit !important;
  font-weight: normal !important;
  border: none !important;
  background: transparent !important;
}

/* 🔧 追加: 埋め込み要素の前後の余白を最小化 */
.rich-text-editor [contenteditable] .social-embed {
  margin: 0.5em 0 !important;
  display: block !important;
}

/* 🔧 追加: 埋め込み要素の前後のスペーサーを非表示にする */
.rich-text-editor [contenteditable] p[data-embed-spacer="before"],
.rich-text-editor [contenteditable] p[data-embed-spacer="after"] {
  display: none !important;
}

/* 🔧 追加: リッチテキストエディター内でのH2要素のスタイル */
.rich-text-editor [contenteditable] h2 {
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.8em 0 !important;
  padding: 0.3em 0.5em !important;
  border-left: 4px solid #FFD814 !important;
  color: #333 !important;
  line-height: 1.4 !important;
  display: block !important;
}

/* 🔧 追加: リッチテキストエディター内でのH3要素のスタイル */
.rich-text-editor [contenteditable] h3 {
  font-size: 1.25em !important;
  font-weight: bold !important;
  margin: 0.7em 0 !important;
  padding: 0.2em 0 0.3em !important;
  border-bottom: 3px solid #FFD814 !important;
  color: #444 !important;
  line-height: 1.4 !important;
  display: block !important;
}

/* 🔧 追加: 全ての埋め込み要素の共通スタイル（テキスト重複防止） */
.rich-text-editor [contenteditable] .social-embed {
  display: block !important;
  margin: 20px auto !important;
  padding: 0 !important;
  width: 100% !important;
  position: static !important;
  background: transparent !important;
  border: none !important;
  clear: both !important;
  /* 🔧 重要: 独立したブロック要素として配置（テキスト重複防止） */
  float: none !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
  /* 🔧 追加: 前後に強制改行を挿入 */
  white-space: normal !important;
  line-height: normal !important;
}

/* 🔧 追加: 埋め込み要素の前後に改行を強制挿入 */
.rich-text-editor [contenteditable] .social-embed::before,
.rich-text-editor [contenteditable] .social-embed::after {
  content: '\A' !important;
  white-space: pre !important;
  display: block !important;
  height: 0 !important;
  line-height: 0 !important;
}

/* ---------------------------------------------------------
   TWITTER/X EMBED STYLES
   --------------------------------------------------------- */

/* 🔧 修正: Twitter/X 埋め込み要素（パーセンテージベースの柔軟な幅システム） */
.rich-text-editor [contenteditable] .social-embed[data-platform="twitter"],
.rich-text-editor [contenteditable] .social-embed[data-platform="x"] {
  display: block !important;
  margin: 15px auto !important;
  padding: 0 !important; /* パディングを削除してコンテナ内に収める */
  max-width: 95% !important; /* コンテナ幅の95%に制限 */
  width: 95% !important; /* パーセンテージベースの幅 */
  height: auto !important;
  position: static !important;
  background: transparent !important;
  border: none !important;
  clear: both !important;
  float: none !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
  white-space: normal !important;
  line-height: normal !important;
  overflow: visible !important;
  transform: none !important;
}

/* 🔧 簡素化: 高さ保護スタイルを削除（自然な調整のみ） */

/* 🔧 修正: Twitter/X iframe（パーセンテージベース + 高さ縮小防止） */
.rich-text-editor [contenteditable] .social-embed[data-platform="twitter"] iframe,
.rich-text-editor [contenteditable] .social-embed[data-platform="x"] iframe,
.rich-text-editor [contenteditable] iframe[id^="twitter-widget"] {
  /* 🔧 パーセンテージベースの柔軟な幅（固定幅を廃止） */
  max-width: 100% !important; /* 親要素の100%まで */
  width: 100% !important; /* 親要素に合わせて調整 */
  min-width: 0 !important; /* 最小幅制限を削除 */
  height: auto !important; /* 高さは自然な調整を許可 */
  min-height: 200px !important; /* 🔧 最小高さを設定して縮小防止 */

  margin: 0 auto !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  display: block !important;
  position: static !important;
  top: 0 !important;
  left: 0 !important;
  overflow: visible !important;
  box-sizing: border-box !important;
  vertical-align: top !important;
  resize: none !important;
  transform: none !important;
}

/* 🔧 追加: エディタコンテナの制約設定 */
.rich-text-editor [contenteditable] {
  overflow-x: hidden !important; /* 横スクロールを防止 */
  word-wrap: break-word !important; /* 長いコンテンツを折り返し */
}

/* Twitter/X blockquote */
.rich-text-editor [contenteditable] .social-embed[data-platform="twitter"] blockquote,
.rich-text-editor [contenteditable] .social-embed[data-platform="x"] blockquote,
.rich-text-editor [contenteditable] blockquote[class*="twitter"] {
  width: 100% !important;
  max-width: 550px !important;
  height: auto !important;
  min-height: auto !important;
  margin: 0 auto !important;
  padding: 20px !important;
  border: 1px solid #e1e8ed !important;
  border-radius: 12px !important;
  background: #ffffff !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  overflow: visible !important;
  box-sizing: border-box !important;
}

/* Twitter/X ローディング表示（TikTok/YouTubeと統一） */
.rich-text-editor [contenteditable] .x-loading {
  width: 100% !important;
  max-width: 550px !important;
  height: 60px !important;
  margin: 0 auto 20px auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: static !important;
  background: transparent !important;
  overflow: visible !important;
}

.rich-text-editor [contenteditable] .x-loading-subtitle {
  color: rgba(30, 30, 30, 0.8) !important;
  font-size: 14px !important;
  padding: 10px 20px !important;
  text-align: center !important;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
}

/* ---------------------------------------------------------
   INSTAGRAM EMBED STYLES
   --------------------------------------------------------- */

/* Instagram ローディング表示（他のSNSと統一） */
.rich-text-editor [contenteditable] .instagram-loading {
  width: 100% !important;
  max-width: 540px !important;
  height: 60px !important;
  margin: 0 auto 20px auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: static !important;
  background: transparent !important;
  overflow: visible !important;
}

.rich-text-editor [contenteditable] .instagram-loading-subtitle {
  color: rgba(30, 30, 30, 0.8) !important;
  font-size: 14px !important;
  padding: 10px 20px !important;
  text-align: center !important;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
  font-weight: normal !important;
  line-height: 1.4 !important;
  background: transparent !important;
  border: none !important;
  margin: 0 !important;
  white-space: nowrap !important;
  overflow: visible !important;
}

/* 🔧 修正: Instagram 埋め込み要素（通常フロー配置） */
.rich-text-editor [contenteditable] .social-embed[data-platform="instagram"] {
  display: block !important;
  margin: 20px auto !important;
  padding: 0 !important;
  max-width: 540px !important;
  width: 100% !important;
  position: static !important;
  background: transparent !important;
  clear: both !important;
  /* 🔧 通常のドキュメントフローに配置 */
  float: none !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
}

/* 🔧 修正: Instagram iframe（レイヤー問題解決） */
.rich-text-editor [contenteditable] .social-embed[data-platform="instagram"] iframe {
  width: 100% !important;
  max-width: 540px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  background: transparent !important;
  display: block !important;
  position: relative !important;
  top: 0 !important;
  overflow: visible !important;
  box-sizing: border-box !important;
  /* 🔧 垂直位置統一 */
  vertical-align: top !important;
  /* 🔧 レイヤー問題解決: テキストより下に配置 */
  isolation: isolate !important;
}

/* Instagram blockquote */
.rich-text-editor [contenteditable] .social-embed[data-platform="instagram"] blockquote,
.rich-text-editor [contenteditable] blockquote[class*="instagram"] {
  width: 100% !important;
  max-width: 540px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  background: transparent !important;
  font-family: Arial, sans-serif !important;
  overflow: visible !important;
  box-sizing: border-box !important;
}


/* ---------------------------------------------------------
   TIKTOK EMBED STYLES
   --------------------------------------------------------- */

/* 🔧 修正: TikTok 埋め込み要素（通常フロー配置） */
.rich-text-editor [contenteditable] .social-embed[data-platform="tiktok"] {
  display: block !important;
  margin: 20px auto !important;
  padding: 0 !important;
  max-width: 325px !important;
  width: 325px !important;
  position: static !important;
  background: transparent !important;
  border: none !important;
  clear: both !important;
  text-align: center !important;
  /* 🔧 通常のドキュメントフローに配置 */
  float: none !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
}

/* 🔧 修正: TikTok iframe コンテナ（通常フロー配置） */
.rich-text-editor [contenteditable] .tiktok-iframe-container {
  margin: 0 auto !important;
  padding: 0 !important;
  display: block !important;
  text-align: center !important;
  position: static !important;
  overflow: visible !important;
  height: auto !important;
  background: transparent !important;
  border: none !important;
  width: 325px !important;
  /* 🔧 通常のドキュメントフローに配置 */
  vertical-align: baseline !important;
}

/* 🔧 修正: TikTok iframe（垂直位置ずれ問題解決） */
.rich-text-editor [contenteditable] .tiktok-iframe-container iframe,
.rich-text-editor [contenteditable] .social-embed[data-platform="tiktok"] iframe {
  width: 325px !important;
  height: 740px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 8px !important;
  background: transparent !important;
  display: block !important;
  position: relative !important;
  top: 0 !important;
  left: 0 !important;
  overflow: visible !important;
  box-sizing: border-box !important;
  /* 🔧 垂直位置ずれ修正 */
  vertical-align: top !important;
  align-self: flex-start !important;
}

/* TikTok blockquote */
.rich-text-editor [contenteditable] .social-embed[data-platform="tiktok"] blockquote,
.rich-text-editor [contenteditable] blockquote[class*="tiktok"] {
  width: 325px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
  overflow: visible !important;
  box-sizing: border-box !important;
}

/* TikTok ローディングアニメーション */
.rich-text-editor [contenteditable] .tiktok-loading {
  width: 325px !important;
  height: 60px !important;
  margin: 0 auto 20px auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: static !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 8px !important;
  overflow: visible !important;
}

.rich-text-editor [contenteditable] .tiktok-loading-subtitle {
  color: rgba(30, 30, 30, 0.8) !important;
  font-size: 14px !important;
  padding: 10px 20px !important;
  text-align: center !important;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
}

/* ---------------------------------------------------------
   YOUTUBE EMBED STYLES
   --------------------------------------------------------- */

/* YouTube埋め込み要素のスタイル */
.rich-text-editor [contenteditable] .social-embed[data-platform="youtube"] {
  margin: 20px auto !important;
  padding: 0 !important;
  width: 100% !important;
  max-width: 560px !important;
  display: block !important;
  position: relative !important;
  background: transparent !important;
  border: none !important;
  clear: both !important;
  overflow: visible !important;
  float: none !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
  z-index: 5 !important;
  pointer-events: none !important;
  isolation: isolate !important;
}

/* YouTube埋め込みコンテンツ */
.rich-text-editor [contenteditable] .social-embed-youtube {
  padding: 0 !important;
  width: 100% !important;
  max-width: 560px !important;
  display: block !important;
  pointer-events: auto !important;
}

/* YouTube埋め込み内の要素はクリック可能 */
.rich-text-editor [contenteditable] .social-embed[data-platform="youtube"] * {
  pointer-events: auto !important;
}

/* YouTube iframe */
.rich-text-editor [contenteditable] .social-embed[data-platform="youtube"] iframe {
  margin: 20px auto !important;
  padding: 0 !important;
  width: 100% !important;
  max-width: 560px !important;
  height: 315px !important;
  display: block !important;
  position: relative !important;
  border: none !important;
  border-radius: 8px !important;
  background: transparent !important;
  overflow: visible !important;
  box-sizing: border-box !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* YouTubeローディングアニメーション */
.youtube-loading {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 315px !important;
  background: #f8f9fa !important;
  border-radius: 8px !important;
  margin: 20px auto !important;
  padding: 0 !important;
  width: 100% !important;
  max-width: 560px !important;
}

.youtube-loading-subtitle {
  color: #666 !important;
  font-size: 14px !important;
  margin-top: 10px !important;
  text-align: center !important;
}

/* ---------------------------------------------------------
   FRONT PAGE CONTENT STYLES (for dangerouslySetInnerHTML)
   --------------------------------------------------------- */

/* フロントページでのHTML表示用スタイル - H3見出しのスコープ化 */
.ranking-content h3[data-styled="true"] {
  font-size: 1.25em !important;
  font-weight: bold !important;
  margin: 0.7em 0 !important;
  padding: 0.2em 0 0.3em !important;
  position: relative !important;
  color: #444 !important;
  line-height: 1.4 !important;
  display: block !important;
  word-break: break-word !important;
  white-space: normal !important;
  text-indent: 0 !important;
  border-bottom: 3px solid #FFD814 !important;
}

/* フロントページでのHTML表示用スタイル - H2見出しのスコープ化 */
.ranking-content h2[data-styled="true"] {
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.8em 0 !important;
  padding: 0.3em 0.5em !important;
  border-left: 4px solid #FFD814 !important;
  color: #333 !important;
  line-height: 1.4 !important;
}

/* フロントページでの通常テキストスタイル - 見出しスタイルの継承を防止 */
.ranking-content span:not([data-styled]),
.ranking-content p:not([data-styled]),
.ranking-content div:not(.social-embed):not([data-styled]),
.ranking-content a:not([data-styled]),
.ranking-content strong:not([data-styled]),
.ranking-content em:not([data-styled]),
.ranking-content u:not([data-styled]),
.ranking-content i:not([data-styled]),
.ranking-content b:not([data-styled]) {
  border-bottom: none !important;
  border-left: none !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  margin: inherit !important;
  padding: inherit !important;
  position: static !important;
  color: inherit !important;
  line-height: inherit !important;
  display: inline !important;
  word-break: normal !important;
  white-space: normal !important;
  text-indent: 0 !important;
}

/* 特に span 要素の見出しスタイル継承を完全に防止 */
.ranking-content span {
  border-bottom: none !important;
  border-left: none !important;
  background-color: transparent !important;
  font-family: inherit !important;
}

/* フロントページでのSNS埋め込み表示用スタイル */
.ranking-content .social-embed {
  display: block !important;
  margin: 20px auto !important;
  padding: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
  text-align: center !important;
  clear: both !important;
}

/* Twitter/X埋め込み（フロントページ用） */
.ranking-content .social-embed[data-platform="twitter"],
.ranking-content .social-embed[data-platform="x"] {
  max-width: 550px !important;
}

.ranking-content .social-embed[data-platform="twitter"] iframe,
.ranking-content .social-embed[data-platform="x"] iframe,
.ranking-content iframe[id^="twitter-widget"] {
  max-width: 100% !important;
  width: 100% !important;
  margin: 0 auto !important;
}

.ranking-content .social-embed[data-platform="twitter"] blockquote,
.ranking-content .social-embed[data-platform="x"] blockquote,
.ranking-content blockquote[class*="twitter"] {
  width: 100% !important;
  max-width: 550px !important;
  margin: 0 auto !important;
}

/* Instagram埋め込み（フロントページ用） */
.ranking-content .social-embed[data-platform="instagram"] {
  max-width: 540px !important;
}

.ranking-content .social-embed[data-platform="instagram"] iframe {
  width: 100% !important;
  max-width: 540px !important;
  margin: 0 auto !important;
}

.ranking-content .social-embed[data-platform="instagram"] blockquote,
.ranking-content blockquote[class*="instagram"] {
  width: 100% !important;
  max-width: 540px !important;
  margin: 0 auto !important;
}

/* TikTok埋め込み（フロントページ用） */
.ranking-content .social-embed[data-platform="tiktok"] {
  max-width: 325px !important;
  width: 325px !important;
}

.ranking-content .social-embed[data-platform="tiktok"] iframe {
  width: 325px !important;
  height: 740px !important;
  margin: 0 auto !important;
}

.ranking-content .social-embed[data-platform="tiktok"] blockquote,
.ranking-content blockquote[class*="tiktok"] {
  width: 325px !important;
  margin: 0 auto !important;
}

/* YouTube埋め込み（フロントページ用） */
.ranking-content .social-embed[data-platform="youtube"] {
  max-width: 560px !important;
}

.ranking-content .social-embed[data-platform="youtube"] iframe {
  width: 100% !important;
  max-width: 560px !important;
  height: 315px !important;
  margin: 0 auto !important;
}

/* ---------------------------------------------------------
   PLACEHOLDER STYLES
   --------------------------------------------------------- */

/* 汎用プレースホルダー */
.rich-text-editor [contenteditable] .social-embed-placeholder {
  width: 90% !important;
  margin: 20px auto !important;
  padding: 20px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  background-color: #f9f9f9 !important;
  border-radius: 8px !important;
  text-align: center !important;
  min-height: 100px !important;
  height: auto !important;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
  color: #666 !important;
  border: 1px solid #e1e1e1 !important;
}

/* ==========================================================================
   CLERK AUTHENTICATION CUSTOM STYLES
   ========================================================================== */

/* ソーシャルボタンのスタイル統一 */
.cl-socialButtonsBlockButton,
[data-localization-key="socialButtonsBlockButton"] {
  width: 100% !important;
  height: 44px !important;
  border-radius: 100px !important;
  border: 1px solid #d1d5db !important;
  background-color: white !important;
  color: #1F1F1F !important;
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  margin-bottom: 12px !important;
  justify-content: flex-start !important;
  padding-left: 16px !important;
  display: flex !important;
  align-items: center !important;
  box-shadow: none !important;
}

.cl-socialButtonsBlockButton:hover,
[data-localization-key="socialButtonsBlockButton"]:hover {
  background-color: #e6f0ff !important;
}

/* ソーシャルボタンのテキストスタイル */
.cl-socialButtonsBlockButtonText {
  margin-left: 8px !important;
  font-weight: 500 !important;
}

/* ソーシャルボタンブロックを縦並びに */
.cl-socialButtonsBlock {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  margin-bottom: 24px !important;
}

/* フッター要素を非表示 */
.cl-footer,
.cl-footerAction,
.cl-footerActionText,
.cl-footerActionLink {
  display: none !important;
}

/* カードスタイルをリセット */
.cl-card {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

/* アイコンのスタイル */
.cl-socialButtonsProviderIcon {
  width: 20px !important;
  height: 20px !important;
}

/* メインボタンのスタイル */
.cl-formButtonPrimary {
  width: 100% !important;
  height: 44px !important;
  border-radius: 100px !important;
  background: linear-gradient(to right, #ee7752, #e73c7e, #23a6d5) !important;
  border: none !important;
  color: white !important;
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
}

.cl-formButtonPrimary:hover {
  opacity: 0.9 !important;
}

/* 入力フィールドのスタイル */
.cl-formFieldInput {
  width: 100% !important;
  padding: 8px 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  font-size: 14px !important;
}

.cl-formFieldInput::placeholder {
  color: #9ca3af !important;
}

.cl-formFieldInput:focus {
  outline: none !important;
  ring: 2px !important;
  ring-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

/* ラベルのスタイル */
.cl-formFieldLabel {
  display: block !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 4px !important;
}

/* 区切り線のスタイル */
.cl-dividerLine {
  width: 100% !important;
  border-top: 1px solid #d1d5db !important;
}

.cl-dividerText {
  padding: 0 8px !important;
  background-color: white !important;
  color: #6b7280 !important;
  font-size: 14px !important;
}

/* 最強のセレクターでソーシャルボタンを強制的にスタイル */
.cl-socialButtonsBlockButton.cl-button,
.cl-socialButtonsBlockButton__google.cl-button__google,
.cl-socialButtonsBlockButton__line.cl-button__line,
button.cl-socialButtonsBlockButton,
button.cl-button[class*="socialButtons"] {
  width: 100% !important;
  height: 44px !important;
  border-radius: 100px !important;
  border: 1px solid #d1d5db !important;
  background-color: white !important;
  color: #1F1F1F !important;
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  margin-bottom: 12px !important;
  justify-content: flex-start !important;
  padding: 0 16px !important;
  display: flex !important;
  align-items: center !important;
  box-shadow: none !important;
  text-transform: none !important;
  min-height: 44px !important;
  max-height: 44px !important;
}

.cl-socialButtonsBlockButton.cl-button:hover,
.cl-socialButtonsBlockButton__google.cl-button__google:hover,
.cl-socialButtonsBlockButton__line.cl-button__line:hover {
  background-color: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

/* アイコンのスタイル */
.cl-socialButtonsProviderIcon,
.cl-providerIcon,
.cl-socialButtonsProviderIcon__google,
.cl-socialButtonsProviderIcon__line {
  width: 20px !important;
  height: 20px !important;
  margin-right: 8px !important;
}

/* テキストのスタイル */
.cl-socialButtonsBlockButtonText,
.cl-socialButtonsBlockButtonText__google,
.cl-socialButtonsBlockButtonText__line {
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  margin-left: 8px !important;
}

/* Clerkの内部クラスを直接ターゲット（最終手段） */
.cl-internal-yj31gg {
  width: 100% !important;
  height: 44px !important;
  border-radius: 100px !important;
  border: 1px solid #d1d5db !important;
  background-color: white !important;
  color: #1F1F1F !important;
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  margin-bottom: 12px !important;
  justify-content: flex-start !important;
  padding: 0 16px !important;
  display: flex !important;
  align-items: center !important;
  box-shadow: none !important;
  text-transform: none !important;
  min-height: 44px !important;
  max-height: 44px !important;
}

.cl-internal-yj31gg:hover {
  background-color: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

/* 内部アイコンクラス */
.cl-internal-2gzuzc {
  width: 20px !important;
  height: 20px !important;
  margin-right: 8px !important;
}

/* 内部テキストクラス */
.cl-internal-15di0mo {
  font-family: "Google Sans", arial, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  margin-left: 8px !important;
}

