import React, { useState, useEffect } from 'react';
import { RankingListItem } from '../RankingListItem/RankingListItem';
import Link from 'next/link';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';

interface Ranking {
  id: string;
  ranking_ID: string;
  ranking_title: string;
  ranking_description: string;
  thumbnail_image: string[] | string;
  recommend_rate: number | null;
  subCategory_ID: string | null;
  created_at: string;
  updated_at: string;
}

interface RankingListProps {
  userId: string;
  subCategoryId: string;
  className?: string;
}

export const RankingList: React.FC<RankingListProps> = ({
  userId,
  subCategoryId,
  className = ''
}) => {
  const [rankings, setRankings] = useState<Ranking[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ランキングデータを取得
  useEffect(() => {
    const fetchRankings = async () => {
      if (!userId || !subCategoryId) {
        // ユーザーIDまたはサブカテゴリIDが未設定の場合は処理をスキップ
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // サブカテゴリIDのランキングデータ取得開始
        const response = await fetch(`/api/getRankingsBySubCategory?user_ID=${userId}&subCategory_ID=${subCategoryId}`);
        
        if (!response.ok) {
          throw new Error('ランキングの取得に失敗しました');
        }

        const responseData = await response.json();
        // ランキングデータ取得成功
        
        // APIレスポンスの形式に応じてデータを取得
        const rankingsData = Array.isArray(responseData) 
          ? responseData 
          : responseData.ranks && Array.isArray(responseData.ranks) 
            ? responseData.ranks 
            : [];
        
        // 処理対象のランキングデータを確認
        setRankings(rankingsData);
      } catch (err) {
        // ランキング取得エラーの処理
        setError(err instanceof Error ? err.message : '不明なエラーが発生しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRankings();
  }, [userId, subCategoryId]);

  // ランキング詳細ページへ遷移
  const handleRankingClick = (rankingId: string) => {
    // ランキング詳細ページへのリンク
    // 実装例: router.push(`/ranking/${rankingId}`);
  };

  // メニューボタンがクリックされたときの処理
  const handleMenuClick = (e: React.MouseEvent, rankingId: string) => {
    e.stopPropagation();
    // メニュー表示のロジックを追加
  };

  if (isLoading) {
    return (
      <div className="py-4 flex justify-center">
        <CatLoadingAnimation message="Loading..." />
      </div>
    );
  }

  if (error) {
    return <div className="py-4 text-center text-red-500">{error}</div>;
  }

  if (rankings.length === 0) {
    return <div className="py-4 text-center text-gray-500">ランキングがありません</div>;
  }

  // サブカテゴリボタンのクリックを検出するグローバルイベントリスナー
  useEffect(() => {
    // サブカテゴリボタンのクリックを検出する関数
    const handleGlobalClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      
      // サブカテゴリボタンまたはその子要素がクリックされたか確認
      const isSubcategoryButton = target.closest('.subcategory-button') || 
                                 target.closest('[data-subcategory-button="true"]');
      
      if (isSubcategoryButton) {
        // サブカテゴリボタンのクリックを検出
        
        // ランキング詳細ページへの遷移を防止
        e.preventDefault();
        e.stopPropagation();
        
        // クリックイベントの伝播を完全に防止
        if (e.stopImmediatePropagation) {
          e.stopImmediatePropagation();
        }
        
        // デフォルトのクリック動作をキャンセル
        return false;
      }
    };
    
    // グローバルイベントリスナーを追加
    document.addEventListener('click', handleGlobalClick, true); // キャプチャフェーズで登録
    
    // クリーンアップ関数
    return () => {
      document.removeEventListener('click', handleGlobalClick, true);
    };
  }, []);

  return (
    <div className={`w-full ${className}`}>
      {rankings.map((ranking) => (
        // リンクを無効化し、通常のdiv要素に置き換え
        <div 
          key={ranking.id} 
          className="ranking-item-wrapper"
          onClick={(e) => {
            // サブカテゴリボタンのクリックを検出
            const target = e.target as HTMLElement;
            const isSubcategoryButton = target.closest('.subcategory-button') || 
                                       target.closest('[data-subcategory-button="true"]');
            
            // サブカテゴリボタンがクリックされた場合は、詳細ページへの遷移を防止
            if (isSubcategoryButton) {
              e.preventDefault();
              e.stopPropagation();
              // サブカテゴリボタンのクリックを検出し、詳細ページへの遷移を防止
              return;
            }
            
            // サブカテゴリボタン以外のクリックの場合は、タイムアウトを設定して詳細ページに遷移
            // タイムアウトを設定することで、サブカテゴリボタンのクリックイベントが先に処理されるようにする
            setTimeout(() => {
              window.location.href = `/ranking/${ranking.id}`;
            }, 100);
          }}
        >
          <RankingListItem
            id={ranking.id}
            title={ranking.ranking_title}
            description={ranking.ranking_description || ''}
            thumbnailImage={ranking.thumbnail_image}
            recommendRate={ranking.recommend_rate}
            onClick={() => handleRankingClick(ranking.id)}
            onMenuClick={(e) => handleMenuClick(e, ranking.id)}
          />
        </div>
      ))}
    </div>
  );
};
