'use client';

/*
We're constantly improving the code you see.
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import PropTypes from "prop-types";
import React from "react";

interface Props {
  text: string;
}

export const HeaderItems = ({ text }: Props): JSX.Element => {
  const handleBack = () => {
    // 直接的なページ遷移（最高速）
    const currentPath = window.location.pathname;
    if (currentPath.includes('/edit')) {
      // 編集ページから戻る場合は、ユーザーページに直接遷移
      const username = currentPath.split('/')[1];
      window.location.href = `/${username}`;
    } else {
      // その他の場合は履歴を使用
      window.history.back();
    }
  };

  return (
    <div className="flex justify-between max-w-[500px] h-[52px] items-center relative bg-white shadow-[0px_0px_4px_#00000040]">
      <div className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative bg-white">
        <img 
          className="relative w-[30px] h-[30px] object-cover cursor-pointer" 
          alt="Image left arrow" 
          src="/static/img/imageleftarrow.png" 
          onClick={handleBack}
        />
        <p className="relative flex-[0_0_auto]"> {text} </p>
      </div>
      <div className="flex w-[52px] h-[52px] items-center justify-end pl-0 pr-[16px] py-0 relative bg-white">
        <img className="relative w-[20px] h-[20px] object-cover" alt="Image question" src="/static/img/imagequestion.png" />
      </div>
    </div>
  );
};

HeaderItems.propTypes = {
  text: PropTypes.string,
};
