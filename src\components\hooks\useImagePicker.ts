import { useState, useRef } from 'react';
import { fileToBase64 } from '../../utils/imageUtils';

interface UseImagePickerOptions {
  defaultSrc?: string;
  aspect: number;
  onDone?: (imageData: string) => void;
}

/**
 * 画像選択とクロッピングを管理するカスタムフック
 * @param options フックのオプション
 * @returns 画像選択と表示に関する状態と関数
 */
export const useImagePicker = ({ 
  defaultSrc = '/static/img/profile-default.png',
  aspect, 
  onDone
}: UseImagePickerOptions) => {
  const [imgSrc, setImgSrc] = useState<string>(defaultSrc);
  const [showCropper, setShowCropper] = useState<boolean>(false);
  const [tempImage, setTempImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * 画像ファイル選択を開始する
   */
  const pickFile = () => {
    fileInputRef.current?.click();
  };

  /**
   * 画像ファイル選択時の処理
   */
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // ファイル入力をリセット（同じファイルを選択した場合でもイベントが発生するように）
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
      // ファイルをBase64に変換
      fileToBase64(file).then(imageUrl => {
        setTempImage(imageUrl);
        setShowCropper(true);
      });
    }
  };

  /**
   * クロップ確定時の処理
   */
  const handleCropComplete = (croppedImage: string) => {
    setImgSrc(croppedImage);
    setShowCropper(false);
    if (onDone) {
      onDone(croppedImage);
    }
  };

  /**
   * クロップキャンセル時の処理
   */
  const handleCropCancel = () => {
    setShowCropper(false);
    setTempImage(null);
  };

  return {
    imgSrc,
    pickFile,
    fileInputRef,
    handleFileChange,
    showCropper,
    tempImage,
    aspect,
    handleCropComplete,
    handleCropCancel
  };
};
