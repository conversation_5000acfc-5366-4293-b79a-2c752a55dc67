'use client';

import { SignIn, useUser } from '@clerk/nextjs';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Footer } from '@/components/Footer';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';


export default function Page() {
  const { user, isLoaded: isUserLoaded } = useUser();
  const router = useRouter();

  // ログイン済みユーザーのリダイレクト処理
  useEffect(() => {
    const handleLoggedInUserRedirect = async () => {
      console.log('🔍 [Sign-In] useEffect開始');

      if (isUserLoaded && user) {
        console.log('🔍 [Sign-In] 高速リダイレクト開始');
        const fastStartTime = performance.now();

        // Clerkのユーザー情報から名前を取得
        const userFullName = user.fullName || `${user.firstName || ''} ${user.lastName || ''}`.trim();
        const userEmail = user.primaryEmailAddress?.emailAddress;

        // 名前が設定されている場合は既存ユーザーかチェック
        if (userFullName && userFullName !== 'Unknown User' && userEmail) {
          try {
            // ユーザー情報を取得してusernameを確認
            const response = await fetch(`/api/getUser?user_ID=${user.id}`);
            if (response.ok) {
              const userData = await response.json();
              if (userData.username && userData.setup_completed) {
                const fastEndTime = performance.now();
                const duration = (fastEndTime - fastStartTime).toFixed(2);
                console.log(`✅ [Sign-In] 高速リダイレクト実行: ${duration}ms → /${userData.username}`);

                // 動的にユーザー名ページにリダイレクト
                window.location.replace(`/${userData.username}`);
                return;
              }
            }
          } catch (error) {
            console.error('🔍 [Sign-In] ユーザー情報取得エラー:', error);
          }

          // セットアップが未完了の場合はsetupページにリダイレクト
          router.replace('/setup');
          return;
        }

        // 新規ユーザーの場合はsetupにリダイレクト
        console.log('🔍 [Sign-In] 新規ユーザー - setupにリダイレクト');
        router.replace('/setup');
      }
    };

    handleLoggedInUserRedirect();
  }, [isUserLoaded, user, router]);

  if (!isUserLoaded) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-[500px] w-full mx-auto bg-white shadow-md p-8 min-h-screen flex flex-col items-center justify-center">
            <CatLoadingAnimation message="Loading..." />
          </div>
        </div>
      </div>
    );
  }

  // ログイン済みの場合はローディング表示
  if (user) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-[500px] w-full mx-auto bg-white shadow-md p-8 min-h-screen flex flex-col items-center justify-center">
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <div className="max-w-[500px] w-full mx-auto flex flex-col min-h-screen bg-white p-14">
        {/* カスタムヘッダー */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            ログインする
          </h1>
        </div>

        {/* Clerkコンポーネント */}
        <div className="flex-1 flex flex-col">
          <SignIn
            appearance={{
              elements: {
                card: 'bg-transparent shadow-none border-none',
                rootBox: 'w-full',
                headerTitle: 'hidden',
                headerSubtitle: 'hidden',
                footerActionLink: 'hidden',
                footerActionText: 'hidden',
                footer: 'hidden',
                socialButtonsBlock: 'w-full',
                socialButtonsBlockButton: 'w-full h-11 flex items-center justify-start px-3 bg-white border border-gray-300 text-gray-900 hover:bg-gray-50 rounded-full font-medium text-sm transition-colors shadow-none',
                socialButtonsProviderIcon: 'w-5 h-5 mr-2',
                socialButtonsBlockButtonText: 'font-medium text-sm',
              },
              variables: {
                colorPrimary: '#E63B5F',
              },
              layout: {
                socialButtonsPlacement: 'top',
                socialButtonsVariant: 'blockButton',
                showOptionalFields: false,
              },
            }}
            signUpUrl="/sign-up"
          />
        </div>

        {/* アカウントをお持ちでない方はこちら */}
        <div className="text-center mt-6">
          <span className="text-gray-600 text-sm">アカウントをお持ちでない方は </span>
          <a href="/sign-up" className="text-blue-600 hover:text-blue-800 underline text-sm">
            新規登録
          </a>
        </div>

        {/* フッター */}
        <Footer className="!flex-[0_0_auto] mt-auto w-full" />
      </div>
    </div>
  );
}