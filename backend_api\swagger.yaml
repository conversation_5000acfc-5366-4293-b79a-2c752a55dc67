openapi: 3.0.0
info:
  title: マイランクバックエンド API
  description: マイランクのバックエンドAPI仕様
  version: 1.0.0

paths:
  /signup:
    post:
      summary: クライアント作成
      description: ログインするクライアントを作成する
      requestBody:
        description: ユーザー情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_ID:
                  type: string
                  description: ユーザーID
                domain:
                  type: string
                  description: ドメイン
                account_type:
                  type: string
                  description: アカウントタイプ
                email:
                  type: string
                  description: ユーザーのメールアドレス
                line:
                  type: string
                  description: Lineアカウント情報
                google:
                  type: string
                  description: Googleアカウント情報
                password:
                  type: string
                  description: パスワード
                name:
                  type: string
                  description: ユーザー名
                profile_image:
                  type: string
                  format: uri
                  description: プロフィール画像のURL
                contact_url:
                  type: string
                  format: uri
                  description: 連絡用URL
                contact_phone:
                  type: string
                  description: 連絡用電話番号
                contact_email:
                  type: string
                  format: email
                  description: 連絡用メールアドレス
                created_at:
                  type: string
                  format: date-time
                  description: 作成日時
                updated_at:
                  type: string
                  format: date-time
                  description: 更新日時
                categoryIds:
                  type: array
                  items:
                    type: string
                  description: カテゴリーIDのリスト
                snsMasterIds:
                  type: array
                  items:
                    type: string
                  description: SNSマスターIDのリスト
                snsUserIds:
                  type: array
                  items:
                    type: string
                  description: SNSユーザーIDのリスト
              required:
                - user_ID
                - email
                - password
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_ID:
                    type: string
                    description: ユーザーID
                  domain:
                    type: string
                    description: ドメイン
                  account_type:
                    type: string
                    description: アカウントタイプ
                  email:
                    type: string
                    description: ユーザーのメールアドレス
                  name:
                    type: string
                    description: ユーザー名
                  profile_image:
                    type: string
                    format: uri
                    description: プロフィール画像のURL
                  contact_url:
                    type: string
                    format: uri
                    description: 連絡用URL
                  contact_phone:
                    type: string
                    description: 連絡用電話番号
                  contact_email:
                    type: string
                    format: email
                    description: 連絡用メールアドレス
                  created_at:
                    type: string
                    format: date-time
                    description: 作成日時
                  updated_at:
                    type: string
                    format: date-time
                    description: 更新日時
                required:
                  - user_ID
                  - email
                  - name
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 無効なリクエストパラメータ
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /login:
    post:
      summary: ログイン
      description: ユーザーの認証を行う
      requestBody:
        description: ログイン情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: ユーザーのメールアドレス
                password:
                  type: string
                  description: パスワード
              required:
                - email
                - password
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_ID:
                    type: string
                    description: ユーザーID                
                  access_token:
                    type: string
                    description: アクセストークン
                  refresh_token:
                    type: string
                    description: リフレッシュトークン
                required:
                  - access_token
                  - refresh_token
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 無効なリクエストパラメータ
                required:
                  - error
        401:
          description: 認証失敗
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 認証に失敗しました
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /login/google:
    post:
      summary: Googleログイン
      description: Googleを使用したユーザー認証を行う
      requestBody:
        description: Googleログイン情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                google_token:
                  type: string
                  description: Googleトークン
              required:
                - google_token
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    description: アクセストークン
                  refresh_token:
                    type: string
                    description: リフレッシュトークン
                required:
                  - access_token
                  - refresh_token
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 無効なリクエストパラメータ
                required:
                  - error
        401:
          description: 認証失敗
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 認証に失敗しました
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /login/line:
    post:
      summary: Lineログイン
      description: Lineを使用したユーザー認証を行う
      requestBody:
        description: Lineログイン情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                line_token:
                  type: string
                  description: Lineトークン
              required:
                - line_token
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    description: アクセストークン
                  refresh_token:
                    type: string
                    description: リフレッシュトークン
                required:
                  - access_token
                  - refresh_token
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 無効なリクエストパラメータ
                required:
                  - error
        401:
          description: 認証失敗
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 認証に失敗しました
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /editUser/{user_ID}:
    put:
      summary: クライアント更新
      description: 既存のクライアント情報を更新する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー  
      requestBody:
        description: 更新するクライアント情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_ID:
                  type: string
                domain:
                  type: string
                name:
                  type: string
                email:
                  type: string
                profile_image:
                  type: string
                  format: uri
                background_image:
                  type: string
                  format: uri
                profile_description:
                  type: string
                contact_url:
                  type: string
                  format: uri
                contact_phone:
                  type: string
                contact_email:
                  type: string
                  format: email
              required:
                - user_ID
      responses:
        200:
          description: 更新されたクライアント情報
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_ID:
                    type: string
                    example: "12345"
                  name:
                    type: string
                    example: "Updated Client Name"
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 無効なリクエストパラメータ
                required:
                  - error
        404:
          description: クライアントが見つかりません
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: クライアントが見つかりません
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /deleteUser/{user_ID}:
    delete:
      summary: クライアント削除
      description: ユーザーアカウントを削除する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: ユーザーアカウントが削除されました
                required:
                  - message
        401:
          description: 認証失敗
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 認証に失敗しました
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /logout:
    post:
      summary: ログアウト
      description: ユーザーをログアウトさせる
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: ログアウトに成功しました
                required:
                  - message
        401:
          description: 認証失敗
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 認証に失敗しました
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /getUsers:
    get:
      summary: ユーザー一覧取得
      description: すべてのユーザーの一覧を取得する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    user_ID:
                      type: string
                    name:
                      type: string
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /getUser/{user_ID}:
    get:
      summary: ユーザー情報取得
      description: 特定のユーザーの情報を取得する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_ID:
                    type: string
                  domain:
                    type: string
                  name:
                    type: string
                  email:
                    type: string
                  profile_image:
                    type: string
                    format: uri
                  background_image:
                    type: string
                    format: uri
                  profile_description:
                    type: string
                  contact_url:
                    type: string
                    format: uri
                  contact_phone:
                    type: string
                  contact_email:
                    type: string
                    format: email
                required:
                  - user_ID
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /getSns/{user_ID}:
    get:
      summary: SNSの一覧取得
      description: すべてのSNSの一覧を取得する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    sns_ID:
                      type: string
                    user_ID:
                      type: string
                    sns_name:
                      type: string
                    sns_image:
                      type: string
                      format: uri
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /registerSns/{user_ID}:
    post:
      summary: SNSの登録
      description: 新しいSNSを登録する
      requestBody:
        description: 登録するSNS情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sns_ID:
                  type: string
                user_ID:
                  type: string
              required:
                - sns_ID
                - user_ID
      responses:
        200:
          description: 登録されたSNS情報
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_ID:
                    type: string
                    example: "12345"
                  sns_ID:
                    type: string
                    example: "67890"
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /getRanks/{user_ID}:
    get:
      summary: ランキング一覧を取得
      description: すべてのランキングの一覧を取得する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    ranking_ID:
                      type: string
                    ranking_title:
                      type: string
                    thumbnail_image:
                      type: string
                      format: uri
                    recommend_rate:
                      type: integer
                    category_ID:
                      type: string
                    subCategory_ID:
                      type: string
                    category_name:
                      type: string
                    subCategory_name:
                      type: string
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /getRank/{user_ID}/{ranking_ID}:
    get:
      summary: ランキングを取得
      description: 特定のランキングの情報を取得する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  ranking_ID:
                    type: string
                  ranking_title:
                    type: string
                  ranking_description:
                    type: string
                  ranking_url:
                    type: string
                  thumbnail_image:
                    type: string
                    format: uri
                  recommend_rate:
                    type: integer
                  category_ID:
                    type: string
                  subCategory_ID:
                    type: string
                  created_date:
                    type: string
                    format: date
                  modified_date:
                    type: string
                    format: date
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /createRank/{user_ID}:
    post:
      summary: ランキングを作成する
      description: 新しいランキングを作成する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      requestBody:
        description: 作成するランキング情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ranking_ID:
                  type: string
                ranking_title:
                  type: string
                ranking_description:
                  type: string
                ranking_url:
                  type: string
                thumbnail_image:
                  type: string
                  format: uri
                recommend_rate:
                  type: integer
                category_ID:
                  type: string
                subCategory_ID:
                  type: string
                created_date:
                  type: string
                  format: date
                modified_date:
                  type: string
                  format: date
              required:
                - ranking_ID
                - ranking_title
                - thumbnail_image
                - recommend_rate
      responses:
        200:
          description: 作成されたランキング情報
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_ID:
                    type: string
                    example: "12345"
                  ranking_ID:
                    type: string
                    example: "67890"
                  ranking_title:
                    type: string
                    example: "New Ranking"
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /editRank/{user_ID}/{ranking_ID}:
    put:
      summary: ランキングを更新する
      description: 既存のランキングを更新する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      requestBody:
        description: 更新するランキング情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ranking_ID:
                  type: string
                ranking_title:
                  type: string
                ranking_description:
                  type: string
                ranking_url:
                  type: string
                thumbnail_image:
                  type: string
                  format: uri
                recommend_rate:
                  type: integer
                category_ID:
                  type: string
                subCategory_ID:
                  type: string
                created_date:
                  type: string
                  format: date
                modified_date:
                  type: string
                  format: date
              required:
                - ranking_ID
      responses:
        200:
          description: 更新されたランキング情報
          content:
            application/json:
              schema:
                type: object
                properties:
                  user_ID:
                    type: string
                    example: "12345"
                  ranking_ID:
                    type: string
                    example: "67890"
                  ranking_title:
                    type: string
                    example: "Updated Ranking"
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /deleteRank/{user_ID}/{ranking_ID}:
    delete:
      summary: ランキングを削除する
      description: 特定のランキングを削除する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      requestBody:
        description: 削除するランキング情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ranking_ID:
                  type: string
      responses:
        200:
          description: ランキングが正常に削除されました
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: ランキングが削除されました
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /getCategories/{user_ID}:
    get:
      summary: カテゴリー一覧を取得
      description: ユーザーIDに基づいてカテゴリーの一覧を取得する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    category_ID:
                      type: string
                      description: カテゴリーID
                    category_name:
                      type: string
                      description: カテゴリー名
                    parent_ID:
                      type: string
                      description: 親カテゴリーID
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: データの取得に失敗しました
                required:
                  - error

  /getCategory/{user_ID}/{category_ID}:
    get:
      summary: カテゴリーを取得する
      description: 特定のカテゴリーの情報を取得する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  category_ID:
                    type: string
                  category_name:
                    type: string
                  parent_ID:
                    type: string
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /createCategory/{user_ID}:
    post:
      summary: カテゴリーを作成する
      description: カテゴリーを作成する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      requestBody:
        description: 作成するカテゴリー情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                category_name:
                  type: string
                  description: カテゴリーの名前
                parent_ID:
                  type: string
              required:
                - category_name
      responses:
        200:
          description: 作成されたカテゴリー情報
          content:
            application/json:
              schema:
                type: object
                properties:
                  category_ID:
                    type: string
                    example: "12345"
                  category_name:
                    type: string
                    example: "New Category"
                  parent_ID:
                    type: string
                    example: "67890"
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /editCategory/{user_ID}/{category_ID}:
    put:
      summary: カテゴリーを更新する
      description: 既存のカテゴリーを更新する
      parameters:
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
          description: Bearerトークン形式の認証ヘッダー
      requestBody:
        description: 更新するカテゴリー情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                category_name:
                  type: string
                  description: カテゴリーの名前
                parent_ID:
                  type: string
              required:
                - category_name
      responses:
        200:
          description: 更新されたカテゴリー情報
          content:
            application/json:
              schema:
                type: object
                properties:
                  category_ID:
                    type: string
                    example: "12345"
                  category_name:
                    type: string
                    example: "Updated Category"
                  parent_ID:
                    type: string
                    example: "67890"
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

  /getRankByCategory:
    get:
      summary: カテゴリー別ランキング取得
      description: 指定されたユーザーIDとカテゴリーIDに基づいてランキングを取得する
      parameters:
        - name: user_ID
          in: query
          required: true
          schema:
            type: string
          description: ユーザーID
        - name: categoryID
          in: query
          required: false
          schema:
            type: string
          description: カテゴリーID
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    ranking_ID:
                      type: string
                      description: ランキングID
                    ranking_title:
                      type: string
                      description: ランキングのタイトル
                    category_ID:
                      type: string
                      description: カテゴリーID
                    user_ID:
                      type: string
                      description: ユーザーID
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: user_IDが提供されていません
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: データの取得に失敗しました
                required:
                  - error

  /getRankBySubcategory:
    get:
      summary: サブカテゴリー別ランキング取得
      description: 指定されたユーザーIDとサブカテゴリーIDに基づいてランキングを取得する
      parameters:
        - name: user_ID
          in: query
          required: true
          schema:
            type: string
          description: ユーザーID
        - name: subCategoryID
          in: query
          required: false
          schema:
            type: string
          description: サブカテゴリーID
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    ranking_ID:
                      type: string
                      description: ランキングID
                    ranking_title:
                      type: string
                      description: ランキングのタイトル
                    subCategory_ID:
                      type: string
                      description: サブカテゴリーID
                    user_ID:
                      type: string
                      description: ユーザーID
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: user_IDが提供されていません
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: データの取得に失敗しました
                required:
                  - error

  /getSubCategories:
    get:
      summary: サブカテゴリー一覧を取得
      description: 指定されたユーザーIDとカテゴリ名に基づいてサブカテゴリーを取得する
      parameters:
        - name: user_ID
          in: query
          required: true
          schema:
            type: string
          description: ユーザーID
        - name: category_name
          in: query
          required: true
          schema:
            type: string
          description: カテゴリ名
      responses:
        200:
          description: 成功時のレスポンス
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    category_ID:
                      type: string
                      description: サブカテゴリーID
                    category_name:
                      type: string
                      description: サブカテゴリー名
                    parent_ID:
                      type: string
                      description: 親カテゴリーID
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: ユーザーIDまたはカテゴリ名が提供されていません
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: データの取得に失敗しました
                required:
                  - error

  /postCategory:
    post:
      summary: カテゴリーとサブカテゴリーの作成
      description: 指定されたユーザーIDに基づいて新しいカテゴリーとサブカテゴリーを作成します。
      requestBody:
        description: カテゴリーとサブカテゴリーの情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_ID:
                  type: string
                  description: ユーザーID
                category:
                  type: string
                  description: カテゴリー名
                subCategory:
                  type: string
                  description: サブカテゴリー名
                created_at:
                  type: string
                  format: date-time
                  description: 作成日時
                updated_at:
                  type: string
                  format: date-time
                  description: 更新日時
              required:
                - user_ID
                - category
                - subCategory
      responses:
        201:
          description: カテゴリーとサブカテゴリーが正常に作成されました
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: データの挿入に失敗しました
                required:
                  - success
                  - error

  /postRank:
    post:
      summary: ランキングを作成する
      description: 新しいランキングを作成する
      requestBody:
        description: 作成するランキング情報
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_ID:
                  type: string
                  description: ユーザーID
                domain:
                  type: string
                  description: ドメイン
                thumbnail_image:
                  type: string
                  format: uri
                  description: サムネイル画像のURL
                recommend_rate:
                  type: integer
                  description: 推奨率
                ranking_url:
                  type: string
                  format: uri
                  description: ランキングのURL
                ranking_title:
                  type: string
                  description: ランキングのタイトル
                ranking_description:
                  type: string
                  description: ランキングの説明
                category_ID:
                  type: string
                  description: カテゴリーID
                subCategory_ID:
                  type: string
                  description: サブカテゴリーID
                created_at:
                  type: string
                  format: date-time
                  description: 作成日時
                updated_at:
                  type: string
                  format: date-time
                  description: 更新日時
              required:
                - user_ID
                - domain
                - thumbnail_image
                - recommend_rate
                - ranking_url
                - ranking_title
                - ranking_description
                - category_ID
                - subCategory_ID
      responses:
        201:
          description: ランキングが正常に作成されました
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      ranking_ID:
                        type: string
                        description: ランキングID
                      user_ID:
                        type: string
                        description: ユーザーID
                      domain:
                        type: string
                        description: ドメイン
                      thumbnail_image:
                        type: string
                        format: uri
                        description: サムネイル画像のURL
                      recommend_rate:
                        type: integer
                        description: 推奨率
                      ranking_url:
                        type: string
                        format: uri
                        description: ランキングのURL
                      ranking_title:
                        type: string
                        description: ランキングのタイトル
                      ranking_description:
                        type: string
                        description: ランキングの説明
                      category_ID:
                        type: string
                        description: カテゴリーID
                      subCategory_ID:
                        type: string
                        description: サブカテゴリーID
                      created_at:
                        type: string
                        format: date-time
                        description: 作成日時
                      updated_at:
                        type: string
                        format: date-time
                        description: 更新日時
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: データの挿入に失敗しました
                required:
                  - success
                  - error

  /uploadImages:
    post:
      summary: 画像アップロード
      description: 画像をアップロードし、そのメタデータを保存する
      requestBody:
        description: 画像ファイルとユーザー情報
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                user_ID:
                  type: string
                  description: ユーザーID
                domain:
                  type: string
                  description: ドメイン
                file:
                  type: string
                  format: binary
                  description: アップロードする画像ファイル
              required:
                - user_ID
                - file
      responses:
        200:
          description: アップロード成功時のレスポンス
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: アップロード成功メッセージ
                  imageUrls:
                    type: array
                    items:
                      type: string
                      format: uri
                      description: アップロードされた画像のURL
        400:
          description: リクエストが無効です
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 無効なリクエストパラメータ
                required:
                  - error
        500:
          description: サーバーエラー
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: サーバー内部エラーが発生しました
                required:
                  - error

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT