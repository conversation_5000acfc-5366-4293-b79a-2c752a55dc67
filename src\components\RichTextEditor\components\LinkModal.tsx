import React from 'react';

interface LinkModalProps {
  type: 'add' | 'remove';
  isOpen: boolean;
  linkUrl?: string;
  onLinkUrlChange?: (url: string) => void;
  onConfirm: () => void;
  onCancel: () => void;
}

const LinkModal: React.FC<LinkModalProps> = ({
  type,
  isOpen,
  linkUrl = '',
  onLinkUrlChange,
  onConfirm,
  onCancel
}) => {
  if (!isOpen) return null;

  const isAddMode = type === 'add';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999999]">
      <div className="bg-white rounded-lg shadow-xl p-6 w-96 max-w-[90vw]">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            {isAddMode ? 'リンクを追加' : 'リンクを解除'}
          </h3>
          <button
            type="button"
            className="text-gray-400 hover:text-gray-600"
            onClick={onCancel}
            aria-label="閉じる"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        {isAddMode && (
          <div className="mb-4">
            <input
              id="linkUrl"
              type="text"
              value={linkUrl}
              onChange={(e) => onLinkUrlChange?.(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none"
              placeholder="https://"
              autoFocus
            />
          </div>
        )}

        <div className="flex justify-end gap-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            キャンセル
          </button>
          <button
            type="button"
            onClick={onConfirm}
            className="px-4 py-2 text-white bg-[#e63b5f] rounded-md hover:bg-[#d1355a] transition-colors"
          >
            {isAddMode ? '追加' : '解除'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinkModal;