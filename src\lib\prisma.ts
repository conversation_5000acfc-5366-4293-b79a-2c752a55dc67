/**
 * Prismaクライアントの共通実装
 * 開発環境ではグローバルインスタンスを再利用し、本番環境では新しいインスタンスを作成
 */

// 型定義の問題を回避するために、any型を使用

// Prismaクライアントの初期化
let prisma: any;

// グローバルオブジェクトにアクセスするための安全な方法
const globalAny = global as any;

if (process.env.NODE_ENV === 'production') {
  // 本番環境では毎回新しいインスタンスを作成
  const { PrismaClient } = require('@prisma/client');
  prisma = new PrismaClient();
} else {
  // 開発環境では既存のインスタンスがあれば再利用
  if (!globalAny.prisma) {
    const { PrismaClient } = require('@prisma/client');
    globalAny.prisma = new PrismaClient();
  }
  prisma = globalAny.prisma;
}

export { prisma };
