"use client";  // この行を追加します
import React, { useState } from 'react';
import Cropper from 'react-easy-crop';
import ImgDialog from './ImgDialog';
import getCroppedImg from './cropImage';
import { Area } from 'react-easy-crop';

const dogImg =
  'https://img.huffingtonpost.com/asset/5ab4d4ac2000007d06eb2c56.jpeg?cache=sih0jwle4e&ops=1910_1000';

const Home: React.FC = () => {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [croppedImage, setCroppedImage] = useState<string | null>(null);

  const onCropComplete = (croppedArea: Area, croppedAreaPixels:Area ) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };

  const showCroppedImage = async () => {
    try {
      if (croppedAreaPixels) {
        const croppedImage = await getCroppedImg(dogImg, croppedAreaPixels, rotation) as string;
        setCroppedImage(croppedImage);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const onClose = () => {
    setCroppedImage(null);
  };

  return (
    <div className="flex flex-col items-center p-4">
      <div className="relative w-full h-64 sm:h-96 bg-gray-800">
        <Cropper
          image={dogImg}
          crop={crop}
          rotation={rotation}
          zoom={zoom}
          cropShape="round"
          showGrid={false}
          aspect={1}
          onCropChange={setCrop}
          onRotationChange={setRotation}
          onCropComplete={onCropComplete}
          onZoomChange={setZoom}
        />
      </div>
      <div className="flex flex-col sm:flex-row items-center mt-4 space-y-4 sm:space-y-0 sm:space-x-4 w-full">
        <div className="flex items-center w-full">
          <label className="min-w-[65px]">Zoom</label>
          <input
            type="range"
            className="ml-8 flex-1"
            value={zoom}
            min={1}
            max={3}
            step={0.1}
            onChange={(e) => setZoom(Number(e.target.value))}
          />
        </div>
        <div className="flex items-center w-full">
          <label className="min-w-[65px]">Rotation</label>
          <input
            type="range"
            className="ml-8 flex-1"
            value={rotation}
            min={0}
            max={360}
            step={1}
            onChange={(e) => setRotation(Number(e.target.value))}
          />
        </div>
        <button
          onClick={showCroppedImage}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Show Result
        </button>
      </div>
      <ImgDialog img={croppedImage} onClose={onClose} />
    </div>
  );
};

export default Home;
