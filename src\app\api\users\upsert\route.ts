import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { clerkUserId, email, firstName, lastName, fullName, imageUrl } = body;

    console.log('🔄 [API] User upsert request:', {
      clerkUserId,
      email,
      firstName,
      lastName,
      fullName,
      imageUrl: imageUrl ? 'provided' : 'not provided'
    });

    // 必須フィールドのチェック
    if (!clerkUserId) {
      console.error('❌ [API] Missing required field: clerkUserId');
      return NextResponse.json(
        { error: 'clerkUserId is required' },
        { status: 400 }
      );
    }

    // ユーザー情報をupsert（存在しない場合は作成、存在する場合は更新）
    const userData = {
      user_ID: clerkUserId,
      email: email || '<EMAIL>', // emailは必須フィールド
      name: fullName || `${firstName || ''} ${lastName || ''}`.trim() || 'Unknown User',
      profile_image: imageUrl || null,
      account_type: 'google' as any, // OAuth認証で登録されたユーザー（Googleアカウント）
      username: `user-${clerkUserId.slice(-8)}`, // 一時的なユーザー名（後で変更可能）
      setup_completed: false, // OAuth認証時はセットアップ未完了状態
    };

    console.log('📝 [API] Upserting user data:', userData);

    const user = await prisma.user.upsert({
      where: { user_ID: clerkUserId },
      update: {
        email: userData.email,
        name: userData.name,
        profile_image: userData.profile_image,
        // setup_completedは更新時には変更しない（既存の状態を保持）
      },
      create: userData,
    });

    console.log('✅ [API] User upsert successful:', {
      id: user.id,
      user_ID: user.user_ID,
      email: user.email,
      name: user.name
    });

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        user_ID: user.user_ID,
        email: user.email,
        name: user.name,
        username: user.username,
        profile_image: user.profile_image
      }
    });

  } catch (error) {
    console.error('❌ [API] User upsert error:', error);
    console.error('❌ [API] Request data was:', {
      clerkUserId,
      email,
      firstName,
      lastName,
      fullName,
      imageUrl
    });

    // Prismaエラーの詳細ログ
    if (error instanceof Error) {
      console.error('❌ [API] Error message:', error.message);
      console.error('❌ [API] Error stack:', error.stack);
    }

    // Prismaエラーの場合の詳細情報
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('❌ [API] Prisma error code:', (error as any).code);
      console.error('❌ [API] Prisma error meta:', (error as any).meta);
    }

    // データベース接続エラーかどうかを判定
    const isDatabaseError = error instanceof Error && (
      error.message.includes('database') ||
      error.message.includes('connection') ||
      error.message.includes('timeout') ||
      (error as any).code === 'P1001' || // Can't reach database server
      (error as any).code === 'P1008' || // Operations timed out
      (error as any).code === 'P1017'    // Server has closed the connection
    );

    return NextResponse.json(
      {
        error: 'Failed to upsert user',
        details: error instanceof Error ? error.message : 'Unknown error',
        isDatabaseError,
        retryable: isDatabaseError
      },
      { status: isDatabaseError ? 503 : 500 } // 503 Service Unavailable for database errors
    );
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('❌ [API] Prisma disconnect error:', disconnectError);
    }
  }
}
