/**
 * Instagram埋め込み処理のユーティリティ
 * Minified invariant #4412エラーを防ぐための安全な実装
 */

// Instagram埋め込み処理の状態管理
let isInstagramScriptLoading = false;
let isInstagramScriptLoaded = false;
let pendingCallbacks: (() => void)[] = [];

/**
 * InstagramのURLをクリーンアップ（UTMパラメータなどを除去）
 */
export const cleanInstagramUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    // UTMパラメータやその他のクエリパラメータを除去
    urlObj.search = '';
    // ハッシュも除去
    urlObj.hash = '';
    return urlObj.toString();
  } catch (error) {
    // URLが無効な場合は元のURLを返す
    return url;
  }
};

/**
 * DOM内のInstagram blockquote要素の存在を確認
 */
export const hasInstagramBlockquotes = (): boolean => {
  const selectors = [
    '.instagram-media',
    'blockquote[class*="instagram"]',
    '[data-platform="instagram"]'
  ];
  
  return selectors.some(selector => document.querySelector(selector) !== null);
};

/**
 * 特定の要素内にInstagram blockquote要素が存在するかチェック
 */
export const hasInstagramBlockquotesInElement = (element: HTMLElement): boolean => {
  const selectors = [
    '.instagram-media',
    'blockquote[class*="instagram"]',
    '[data-platform="instagram"]'
  ];
  
  return selectors.some(selector => element.querySelector(selector) !== null);
};

/**
 * Instagram埋め込みスクリプトの読み込み状態をチェック
 */
export const isInstagramEmbedReady = (): boolean => {
  return !!(window.instgrm && 
           typeof window.instgrm.Embeds === 'object' && 
           typeof window.instgrm.Embeds.process === 'function');
};

/**
 * Instagram埋め込みスクリプトを安全に読み込む
 */
export const loadInstagramEmbedScript = (callback?: () => void): void => {
  // コールバックがある場合は配列に追加
  if (callback) {
    pendingCallbacks.push(callback);
  }

  // 既に読み込み済みの場合
  if (isInstagramScriptLoaded && isInstagramEmbedReady()) {
    executePendingCallbacks();
    return;
  }

  // 既に読み込み中の場合は待機
  if (isInstagramScriptLoading) {
    return;
  }

  // 既存のスクリプトタグをチェック
  const existingScript = document.querySelector('script[src*="instagram.com/embed"]');
  if (existingScript) {
    isInstagramScriptLoading = true;
    // 既存スクリプトの読み込み完了を待機
    waitForInstagramReady();
    return;
  }

  // 新しいスクリプトタグを作成
  isInstagramScriptLoading = true;
  const script = document.createElement('script');
  script.src = '//www.instagram.com/embed.js';
  script.async = true;
  script.id = 'instagram-embed-script';
  
  script.onload = () => {
    isInstagramScriptLoaded = true;
    isInstagramScriptLoading = false;
    
    // 少し待ってからコールバックを実行（DOM準備のため）
    setTimeout(() => {
      executePendingCallbacks();
    }, 100);
  };
  
  script.onerror = () => {
    isInstagramScriptLoading = false;
    console.error('[Instagram] 埋め込みスクリプトの読み込みに失敗しました');
    // エラーでもコールバックを実行
    executePendingCallbacks();
  };
  
  document.head.appendChild(script);
};

/**
 * Instagram埋め込みスクリプトの準備完了を待機
 */
const waitForInstagramReady = (attempts: number = 0): void => {
  if (isInstagramEmbedReady()) {
    isInstagramScriptLoaded = true;
    isInstagramScriptLoading = false;
    executePendingCallbacks();
  } else if (attempts < 20) {
    setTimeout(() => waitForInstagramReady(attempts + 1), 250);
  } else {
    isInstagramScriptLoading = false;
    console.error('[Instagram] 埋め込みスクリプトの準備がタイムアウトしました');
    executePendingCallbacks();
  }
};

/**
 * 待機中のコールバックを実行
 */
const executePendingCallbacks = (): void => {
  const callbacks = [...pendingCallbacks];
  pendingCallbacks = [];
  callbacks.forEach(callback => {
    try {
      callback();
    } catch (error) {
      console.error('[Instagram] コールバック実行エラー:', error);
    }
  });
};

/**
 * Instagram埋め込みを安全に処理
 */
export const processInstagramEmbeds = (targetElement?: HTMLElement): void => {
  // DOM要素の存在確認
  const hasEmbeds = targetElement 
    ? hasInstagramBlockquotesInElement(targetElement)
    : hasInstagramBlockquotes();
    
  if (!hasEmbeds) {
    return;
  }

  // スクリプトが準備できている場合は即座に処理
  if (isInstagramEmbedReady()) {
    executeInstagramProcess(targetElement);
  } else {
    // スクリプトを読み込んでから処理
    loadInstagramEmbedScript(() => {
      executeInstagramProcess(targetElement);
    });
  }
};

/**
 * Instagram埋め込み処理を実行
 */
const executeInstagramProcess = (targetElement?: HTMLElement): void => {
  if (!isInstagramEmbedReady()) {
    return;
  }

  try {
    // DOM要素の最終確認
    const hasEmbeds = targetElement 
      ? hasInstagramBlockquotesInElement(targetElement)
      : hasInstagramBlockquotes();
      
    if (!hasEmbeds) {
      return;
    }

    // Instagram埋め込み処理を実行
    window.instgrm.Embeds.process();
    
    if (process.env.NODE_ENV === 'development') {
      console.log('[Instagram] 埋め込み処理が完了しました');
    }
  } catch (error) {
    // Minified invariant #4412やその他のエラーをキャッチ
    if (process.env.NODE_ENV === 'development') {
      console.error('[Instagram] 埋め込み処理エラー:', error);
    }
  }
};

/**
 * Instagram埋め込みHTMLを生成（クリーンなURL使用）
 */
export const generateInstagramEmbedHTML = (postUrl: string): string => {
  const cleanUrl = cleanInstagramUrl(postUrl);
  
  return `<blockquote class="instagram-media" data-instgrm-permalink="${cleanUrl}" data-instgrm-version="14">
    <a href="${cleanUrl}"></a>
  </blockquote>`;
};

/**
 * Instagram埋め込みの初期化（ページ読み込み時に呼び出し）
 */
export const initializeInstagramEmbeds = (): void => {
  // ページにInstagram埋め込みが存在する場合のみスクリプトを読み込み
  if (hasInstagramBlockquotes()) {
    loadInstagramEmbedScript(() => {
      processInstagramEmbeds();
    });
  }
};
