"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useUser } from '@clerk/nextjs';
import { getUserId as getUserIdFromUserContext } from './UserContext';

// プロフィールデータの型定義
interface ProfileData {
  website: string;       // Userモデルのcontact_url
  email: string;         // Userモデルのcontact_email
  selfIntroduction: string; // Userモデルのself_introduction
  profileImage: string;  // Userモデルのprofile_image
  backgroundImage: string; // Userモデルのbackground_image
  userName: string;      // Userモデルのname
  snsLinks: {
    [key: string]: string;  // SNS名とURLのマッピング
  };
  snsOrder?: string[];  // SNSの表示順序
  snsUserLinks?: any[]; // SNSユーザーリンク
  user_ID: string; // ユーザーID
}

// 初期値
const defaultProfileData: ProfileData = {
  website: '',
  email: '',
  selfIntroduction: '',
  profileImage: '/static/img/profile-default.png',
  backgroundImage: '',
  userName: '',
  snsLinks: {},
  snsOrder: [],
  snsUserLinks: [],
  user_ID: "",
};

// コンテキストの型定義
interface ProfileContextType {
  profileData: ProfileData;
  updateWebsite: (value: string) => Promise<void>;
  updateEmail: (value: string) => Promise<void>;
  updateSelfIntroduction: (value: string) => Promise<void>;
  updateProfileImage: (value: string) => Promise<string | undefined>;
  updateBackgroundImage: (value: string) => Promise<string | undefined>;
  updateUserName: (value: string) => Promise<void>;
  updateSnsLink: (type: string, value: string) => void;
  updateSnsOrder: (newOrder: string[]) => void;
  updateSnsUserLinks: (links: any[]) => void;
  saveProfile: () => Promise<void>;
}

// コンテキストの作成
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// プロフィールデータの取得関数
const fetchProfileData = async (userId: string, setProfileData: (data: ProfileData) => void, setIsLoading: (isLoading: boolean) => void) => {
  try {
    if (!userId) {
      return;
    }

    setIsLoading(true);

    // プロフィールデータを取得
    const apiUrl = `/api/profile?user_ID=${userId}`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`プロフィールデータの取得に失敗しました: ${response.status}`);
    }

    const data = await response.json();

    // 画像パスの修正
    if (data.profileImage) {
      // パスの正規化（Next.jsの静的ファイル配信の仕組みに合わせる）
      if (data.profileImage.startsWith('/img/')) {
        // /img/ から始まる場合は /static/img/ に変更
        data.profileImage = `/static${data.profileImage}`;
      }
    } else {
      // デフォルト画像パスを設定
      data.profileImage = "/static/img/defaultUserIcon.png";
    }
    
    // 背景画像パスの修正
    if (data.backgroundImage) {
      // パスの正規化（Next.jsの静的ファイル配信の仕組みに合わせる）
      if (data.backgroundImage.startsWith('/img/')) {
        // /img/ から始まる場合は /static/img/ に変更
        data.backgroundImage = `/static${data.backgroundImage}`;
      }
    } else {
      // デフォルト背景画像パスを設定
      data.backgroundImage = "";
    }
    
    setProfileData(data);
    return data;
  } catch (error) {
    console.error("ProfileContext - プロフィールデータの取得エラー:", error);
    // エラー時はデフォルト値を設定
    setProfileData({
      userName: "",
      selfIntroduction: "",
      profileImage: "/static/img/profile-default.png",
      backgroundImage: "",
      email: "",
      website: "",
      user_ID: userId || "",
      snsLinks: {},
      snsOrder: []
    });
  } finally {
    setIsLoading(false);
  }
};

// コンテキストプロバイダーコンポーネント
export const ProfileProvider = ({ children }: { children: ReactNode }): JSX.Element => {
  const [profileData, setProfileData] = useState<ProfileData>(defaultProfileData);
  const { user: clerkUser, isLoaded } = useUser();

  // 認証エラーの状態を追跡
  const [authError, setAuthError] = useState(false);
  // 最後に認証エラーが発生した時刻
  const [lastAuthErrorTime, setLastAuthErrorTime] = useState(0);
  // 認証エラー後の再試行間隔（ミリ秒）
  const AUTH_RETRY_INTERVAL = 30000; // 30秒

  // ユーザーIDをキャッシュ
  const [cachedUserId, setCachedUserId] = useState<string>('');

  // ユーザーIDを取得する関数（Clerkを使用）
  const getUserId = async (): Promise<string | null> => {
    // Clerkが読み込み完了していない場合は待機
    if (!isLoaded) {
      return null;
    }

    // ClerkからユーザーIDを取得
    if (clerkUser?.id) {
      setCachedUserId(clerkUser.id);
      return clerkUser.id;
    }

    // キャッシュされたユーザーIDがあれば使用
    if (cachedUserId) {
      return cachedUserId;
    }

    // 認証エラーが発生してから一定時間経過していない場合はnullを返す
    const currentTime = Date.now();
    if (authError && (currentTime - lastAuthErrorTime < AUTH_RETRY_INTERVAL)) {
      return null;
    }

    // ユーザーIDが見つからない場合
    console.log('❌ [ProfileContext] ユーザーIDが見つかりません');
    setAuthError(true);
    setLastAuthErrorTime(currentTime);
    return null;
  };

  // 初回マウント時にプロフィールデータを取得（Clerkの読み込み完了後）
  useEffect(() => {
    // Clerkが読み込み完了していない場合は早期リターン
    if (!isLoaded) {
      console.log('🔍 [ProfileContext] Clerk未読み込み - 待機中');
      return;
    }

    let isMounted = true; // コンポーネントがマウントされているかを追跡
    let fetchExecuted = false; // 重複実行防止フラグ

    const fetchProfile = async () => {
      try {
        // 重複実行防止
        if (fetchExecuted) {
          return;
        }

        fetchExecuted = true; // 実行フラグを設定

        const userId = await getUserId();
        if (!userId || !isMounted) {
          fetchExecuted = false; // リセット
          return;
        }

        // 既存のプロフィールデータがある場合はスキップ（重複取得防止）
        if (profileData && profileData.user_ID === userId && profileData.userName) {
          return;
        }

        await fetchProfileData(userId, (data) => {
          if (isMounted) {
            setProfileData(data);
          }
        }, () => {});
      } catch (error) {
        console.error("❌ [ProfileContext] 初回プロフィール取得エラー:", error);
        fetchExecuted = false; // エラー時はリセット
      }
    };

    fetchProfile();

    // クリーンアップ関数
    return () => {
      isMounted = false;
      fetchExecuted = false;
    };
  }, [isLoaded, clerkUser?.id]); // Clerkの読み込み状態とユーザーIDに依存

  // 各フィールドの更新関数
  const updateWebsite = async (value: string) => {
    setProfileData(prev => ({ ...prev, website: value }));
    
    // データベースに即時反映
    try {
      // キャッシュされたユーザーIDを使用
      const userId = await getUserId();
      if (!userId) {
        throw new Error('ユーザーIDが見つかりません');
      }
      
      const response = await fetch('/api/updateUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          website: value
        }),
      });
      
      if (!response.ok) {
        console.error('ウェブサイト更新エラー:', response.status);
      }
    } catch (error) {
      console.error('ウェブサイト更新中にエラーが発生しました:', error);
    }
  };

  const updateEmail = async (value: string) => {
    setProfileData(prev => ({ ...prev, email: value }));
    
    // データベースに即時反映
    try {
      // キャッシュされたユーザーIDを使用
      const userId = await getUserId();
      if (!userId) {
        throw new Error('ユーザーIDが見つかりません');
      }
      
      const response = await fetch('/api/updateUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          email: value
        }),
      });
      
      if (!response.ok) {
      } else {
      }
    } catch (error) {
    }
  };

  const updateSelfIntroduction = async (value: string) => {
    setProfileData(prev => ({ ...prev, selfIntroduction: value }));
    
    // データベースに即時反映
    try {
      // キャッシュされたユーザーIDを使用
      const userId = await getUserId();
      if (!userId) {
        throw new Error('ユーザーIDが見つかりません');
      }
      
      const response = await fetch('/api/updateUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          selfIntroduction: value
        }),
      });

      if (!response.ok) {
      }
    } catch (error) {
    }
  };

  // プロフィール画像の更新処理を行う関数
  // 状態更新と画像アップロードを分離し、無限ループを防止
  const updateProfileImage = async (value: string) => {
    // 入力値が空またはnullの場合は処理をスキップ
    if (!value) {
      return undefined;
    }
    
    // 画像URLからタイムスタンプパラメータを除去して比較する
    const removeTimestamp = (url: string): string => {
      if (url.includes('?t=')) {
        return url.replace(/\?t=\d+/, '');
      }
      if (url.includes('&t=')) {
        return url.replace(/&t=\d+/, '');
      }
      return url;
    };
    
    // 現在のプロフィール画像と同じ場合は処理をスキップ
    // 画像のURLは複数の形式があるため、基本的な部分を比較
    const isSameImage = (() => {
      // Blob URLの場合は常に異なると判断
      if (value.startsWith('blob:')) {
        return false;
      }
      
      // Base64データの場合は常に異なると判断
      if (value.startsWith('data:image')) {
        return false;
      }
      
      // タイムスタンプを除去して比較
      const cleanValue = removeTimestamp(value);
      const cleanProfileImage = profileData.profileImage ? removeTimestamp(profileData.profileImage) : '';
      
      // 通常のURLの場合はタイムスタンプを除いた部分で比較
      return cleanValue === cleanProfileImage;
    })();
    
    if (isSameImage) {
      return profileData.profileImage; // 既存の画像URLを返す（タイムスタンプ付きのまま）
    }
    
    try {
      // ユーザーIDを取得
      const userId = await getUserId();
      if (!userId) {
        throw new Error('ユーザーIDが見つかりません');
      }
      
      // 引数で渡された画像を使用
      let profileImageUrl = value;
      
      // Base64形式またはBlob URLの場合はアップロード処理を実行
      if (value.startsWith('data:image') || value.startsWith('blob:')) {
        
        try {
          // Base64データの場合はそのままアップロードする
          if (value.startsWith('data:image')) {
            
            try {
              // Base64データの形式を確認
              const parts = value.split(',');
              if (parts.length !== 2 || !parts[0].includes('image/')) {
                console.error("ProfileContext - 無効なBase64画像形式です");
                throw new Error('無効なBase64画像形式です');
              }
              
              
              // Base64データからBlobを作成
              const base64Data = value.split(',')[1];
              const mimeType = value.split(',')[0].split(':')[1].split(';')[0];
              const byteCharacters = atob(base64Data);
              const byteArrays = [];
              
              for (let i = 0; i < byteCharacters.length; i += 512) {
                const slice = byteCharacters.slice(i, i + 512);
                const byteNumbers = new Array(slice.length);
                for (let j = 0; j < slice.length; j++) {
                  byteNumbers[j] = slice.charCodeAt(j);
                }
                const byteArray = new Uint8Array(byteNumbers);
                byteArrays.push(byteArray);
              }
              
              const blob = new Blob(byteArrays, { type: mimeType });
              
              // FormDataを作成
              const formData = new FormData();
              formData.append('file', blob, 'profile-image.jpg'); // fileフィールドとしてBlobを送信
              formData.append('user_ID', userId);
              formData.append('imageType', 'profile'); // プロフィール画像として明示的に指定
              
              
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒タイムアウト
              
              // キャッシュ無効化ヘッダーを追加
              const imageResponse = await fetch('/api/uploadImage', {
                method: 'POST',
                headers: {
                  'Cache-Control': 'no-cache, no-store, must-revalidate',
                  'Pragma': 'no-cache',
                  'Expires': '0'
                },
                body: formData,
                signal: controller.signal
              });
              
              clearTimeout(timeoutId); // タイマーをクリア
              
              if (imageResponse.ok) {
                const imageData = await imageResponse.json();
                profileImageUrl = imageData.url;
                
                // 画像URLにタイムスタンプを追加してキャッシュを回避
                if (profileImageUrl && !profileImageUrl.includes('?t=') && !profileImageUrl.startsWith('data:') && !profileImageUrl.startsWith('blob:')) {
                  profileImageUrl = `${profileImageUrl}?t=${Date.now()}`;
                }
                
                return profileImageUrl;
              } else {
                const errorText = await imageResponse.text();
                console.error("ProfileContext - Base64画像アップロード失敗:", errorText);
                throw new Error(`プロフィール画像のアップロードに失敗しました: ${errorText}`);
              }
            } catch (error) {
              console.error("ProfileContext - Base64画像処理エラー:", error);
              throw error;
            }
          } 
          // Blob URLの場合はデータベースから画像を取得する
          else if (value.startsWith('blob:')) {
            console.log("ProfileContext - Blob URLが検出されました:", value);
            
            try {
              // Blob URLが有効かチェックする
              const response = await fetch(value, { method: 'HEAD' });
              if (!response.ok) {
                throw new Error('無効なBlobURL');
              }
              
              
              const userResponse = await fetch(`/api/profile?user_ID=${userId}`);
              if (userResponse.ok) {
                const userData = await userResponse.json();
                if (userData.profileImage && userData.profileImage.trim() !== "") {
                  console.log("ProfileContext - データベースからプロフィール画像を取得しました:", userData.profileImage);
                  return userData.profileImage;
                }
              }
              
              // データベースから画像が取得できなかった場合はエラーをスロー
              console.error("ProfileContext - データベースから画像が取得できませんでした");
              throw new Error('データベースから画像が取得できませんでした');
            } catch (error) {
              console.error("ProfileContext - Blob URLからのBLobの取得エラー:", error);
              throw error;
            }
          }
          
          // 通常の画像パスの場合はそのまま使用する
          if (value.startsWith('/') || value.startsWith('http')) {
            console.log("ProfileContext - 通常の画像パスを使用します:", value);
            
            // 画像パスの正規化
            if (value.startsWith('/img/')) {
              profileImageUrl = `/static${value}`;
              console.log("ProfileContext - 画像パス正規化 (/img/ -> /static/img/):", profileImageUrl);
            } else {
              profileImageUrl = value;
            }
            
            try {
              // ユーザー情報を更新
              const updateResponse = await fetch('/api/updateUser', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  user_ID: userId,
                  profileImage: profileImageUrl
                }),
              });
              
              if (updateResponse.ok) {
                console.log("ProfileContext - データベース更新成功");
                return profileImageUrl;
              } else {
                console.error("ProfileContext - データベース更新失敗:", await updateResponse.text());
                return profileImageUrl; // データベース更新に失敗しても画像パスを返す
              }
            } catch (error) {
              console.error("ProfileContext - データベース更新エラー:", error);
              return profileImageUrl; // エラーが発生しても画像パスを返す
            }
          }
          
          // ここまで来た場合はエラーをスロー
          console.error("ProfileContext - サポートされていない画像形式です:", value);
          throw new Error('サポートされていない画像形式です');
        } catch (error) {
          console.error("ProfileContext - 画像アップロード処理中の予期しないエラー:", error);
          throw error;
        }
      }
      
      // 画像パスの正規化
      if (profileImageUrl && profileImageUrl.startsWith('/img/')) {
        profileImageUrl = `/static${profileImageUrl}`;
        console.log("ProfileContext - 画像パス正規化 (/img/ -> /static/img/):", profileImageUrl);
      } else if (!profileImageUrl) {
        console.error("ProfileContext - 画像パスが取得できませんでした");
        return undefined;
      }
      
      // 画像URLにタイムスタンプを追加してキャッシュを回避
      if (profileImageUrl && !profileImageUrl.includes('?t=') && !profileImageUrl.startsWith('data:') && !profileImageUrl.startsWith('blob:')) {
        profileImageUrl = `${profileImageUrl}?t=${Date.now()}`;
      }
      
      // データベース更新の前に状態を更新しておく
      // これにより、ユーザーに即座に変更が反映される
      if (profileData.profileImage !== profileImageUrl) {
        // 状態を更新
        setProfileData(prev => ({ ...prev, profileImage: profileImageUrl }));
      }
      
      // データベースに保存
      try {
        const updateResponse = await fetch('/api/updateUser', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          body: JSON.stringify({
            user_ID: userId,
            profileImage: profileImageUrl
          }),
        });
        
        if (!updateResponse.ok) {
          console.error("ProfileContext - データベース更新失敗:", await updateResponse.text());
        }
      } catch (dbError) {
        console.error("ProfileContext - データベース更新エラー:", dbError);
        // データベース更新に失敗しても、UIには変更を反映させる
      }
      
      return profileImageUrl;
    } catch (error) {
      console.error("ProfileContext - 画像更新エラー:", error);
      throw error; // 背景画像と同じようにエラーを上位に伝播
    }
  };

  const updateBackgroundImage = async (value: string) => {
    try {
      console.log("ProfileContext - 背景画像更新開始:", (typeof value === 'string' ? value.substring(0, 50) : String(value).substring(0, 50)) + "...");
      
      // ユーザーIDの確認 - getCurrentUserAPIを使用
      const userId = await getUserId();
      if (!userId) {
        throw new Error('ユーザーIDが見つかりません');
      }
      
      // 画像URLからタイムスタンプパラメータを除去して比較する
      const removeTimestamp = (url: string): string => {
        if (url.includes('?t=')) {
          return url.replace(/\?t=\d+/, '');
        }
        if (url.includes('&t=')) {
          return url.replace(/&t=\d+/, '');
        }
        return url;
      };
      
      // 現在の背景画像と同じ場合は処理をスキップ
      if (value.startsWith('data:image') || value.startsWith('blob:')) {
        // Base64データやBlob URLの場合は常に変更と判断
      } else {
        // タイムスタンプを除去して比較
        const cleanValue = removeTimestamp(value);
        const cleanBackgroundImage = profileData.backgroundImage ? removeTimestamp(profileData.backgroundImage) : '';
        
        if (cleanValue === cleanBackgroundImage) {
          console.log("ProfileContext - 背景画像が同じなので処理をスキップします");
          return profileData.backgroundImage; // 既存の画像URLを返す（タイムスタンプ付きのまま）
        }
      }
      
      // 画像URLの正規化
      let normalizedImagePath = value;
      
      // Base64データまたはBlobURLの場合は画像アップロードAPIを使用
      if (value.startsWith('data:image') || value.startsWith('blob:')) {
        console.log("ProfileContext - 画像データを検出、アップロード処理を開始");
        
        try {
          // 画像データをBlobに変換
          const fetchResponse = await fetch(value);
          const blob = await fetchResponse.blob();
          console.log("ProfileContext - 画像をBlobに変換完了:", blob.size, "bytes");
          
          // FormDataを作成
          const formData = new FormData();
          formData.append('file', blob, 'background-image.jpg');
          formData.append('user_ID', userId);
          formData.append('imageType', 'background'); // 背景画像として指定
          
          // 画像アップロードAPIを呼び出し
          const imageResponse = await fetch('/api/uploadImage', {
            method: 'POST',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            },
            body: formData,
          });
          
          if (imageResponse.ok) {
            const imageData = await imageResponse.json();
            console.log("ProfileContext - 背景画像アップロード成功:", imageData);
            
            // アップロードされた画像のパスを取得
            let backgroundImageUrl = imageData.url;
            console.log("ProfileContext - アップロードされた画像パス:", backgroundImageUrl);
            
            if (!backgroundImageUrl) {
              console.error("ProfileContext - 画像パスが取得できませんでした");
              // エラーをスローせず、処理を継続
              return undefined;
            }
            
            // 画像URLにタイムスタンプを追加してキャッシュを回避
            if (!backgroundImageUrl.includes('?t=') && !backgroundImageUrl.startsWith('data:') && !backgroundImageUrl.startsWith('blob:')) {
              backgroundImageUrl = `${backgroundImageUrl}?t=${Date.now()}`;
            }
            
            // 最終的なプロフィールデータを更新
            setProfileData(prev => ({ ...prev, backgroundImage: backgroundImageUrl }));
            console.log("ProfileContext - 背景画像更新完了:", backgroundImageUrl);
            
            // 背景画像をデータベースに保存
            console.log("ProfileContext - ユーザー情報更新API呼び出し開始");
            console.log("ProfileContext - 更新リクエストボディ:", JSON.stringify({
              user_ID: userId,
              background_image: backgroundImageUrl
            }).substring(0, 100) + "...");
            
            const updateResponse = await fetch('/api/updateUser', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
              },
              body: JSON.stringify({
                user_ID: userId,
                background_image: backgroundImageUrl
              }),
            });
            
            if (!updateResponse.ok) {
              const errorText = await updateResponse.text();
              console.error("ProfileContext - 背景画像保存エラー:", updateResponse.status, errorText);
              // エラーをスローせず、画像パスを返す
              return backgroundImageUrl; // データベース更新に失敗しても画像パスを返す
            }
            
            const updateData = await updateResponse.json();
            console.log("ProfileContext - 背景画像をデータベースに保存しました:", updateData);
            
            // 最新のプロフィールデータを再取得
            try {
              const refreshResponse = await fetch(`/api/profile?user_ID=${userId}`, {
                headers: {
                  'Cache-Control': 'no-cache, no-store, must-revalidate',
                  'Pragma': 'no-cache',
                  'Expires': '0'
                }
              });
              
              if (refreshResponse.ok) {
                const refreshedData = await refreshResponse.json();
                if (refreshedData && refreshedData.backgroundImage) {
                  // 状態を最新のデータで更新
                  const updatedBackgroundImage = refreshedData.backgroundImage.includes('?t=') ? 
                    refreshedData.backgroundImage : 
                    `${refreshedData.backgroundImage}?t=${Date.now()}`;
                  
                  setProfileData(prev => ({
                    ...prev,
                    backgroundImage: updatedBackgroundImage
                  }));
                  console.log("ProfileContext - 最新の背景画像データを再取得しました");
                  return updatedBackgroundImage;
                }
              }
            } catch (refreshError) {
              console.error("ProfileContext - 背景画像データ再取得エラー:", refreshError);
            }
            
            return backgroundImageUrl;
          } else {
            const errorText = await imageResponse.text();
            console.error("ProfileContext - 背景画像アップロードエラー:", imageResponse.status, errorText);
            // エラーをスローせず、処理を継続
            return undefined;
          }
        } catch (error) {
          console.error("ProfileContext - 背景画像アップロードエラー:", error);
          // エラーをスローせず、処理を継続
          return undefined;
        }
      } else {
        // 既存の画像URLの場合は、そのままプロフィールデータを更新
        console.log("ProfileContext - 既存背景画像URL使用:", normalizedImagePath);
        
        // 最終的なプロフィールデータを更新
        setProfileData(prev => ({ ...prev, backgroundImage: normalizedImagePath }));
        
        // 画像URLにタイムスタンプを追加してキャッシュを回避
        if (!normalizedImagePath.includes('?t=') && !normalizedImagePath.startsWith('data:') && !normalizedImagePath.startsWith('blob:')) {
          normalizedImagePath = `${normalizedImagePath}?t=${Date.now()}`;
        }
        
        // 背景画像をデータベースに保存
        console.log("ProfileContext - ユーザー情報更新API呼び出し開始");
        console.log("ProfileContext - 更新リクエストボディ:", JSON.stringify({
          user_ID: userId,
          background_image: normalizedImagePath
        }).substring(0, 100) + "...");
        
        const updateResponse = await fetch('/api/updateUser', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          body: JSON.stringify({
            user_ID: userId,
            background_image: normalizedImagePath
          }),
        });
        
        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.error("ProfileContext - 背景画像保存エラー:", updateResponse.status, errorText);
          throw new Error(`背景画像の保存に失敗しました: ${updateResponse.status}`);
        }
        
        const updateData = await updateResponse.json();
        console.log("ProfileContext - 背景画像をデータベースに保存しました:", updateData);
        
        // 最新のプロフィールデータを再取得
        try {
          const refreshResponse = await fetch(`/api/profile?user_ID=${userId}`, {
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          });
          
          if (refreshResponse.ok) {
            const refreshedData = await refreshResponse.json();
            if (refreshedData && refreshedData.backgroundImage) {
              // 状態を最新のデータで更新
              const updatedBackgroundImage = refreshedData.backgroundImage.includes('?t=') ? 
                refreshedData.backgroundImage : 
                `${refreshedData.backgroundImage}?t=${Date.now()}`;
              
              setProfileData(prev => ({
                ...prev,
                backgroundImage: updatedBackgroundImage
              }));
              console.log("ProfileContext - 最新の背景画像データを再取得しました");
              return updatedBackgroundImage;
            }
          }
        } catch (refreshError) {
          console.error("ProfileContext - 背景画像データ再取得エラー:", refreshError);
        }
        
        return normalizedImagePath;
      }
    } catch (error) {
      console.error("ProfileContext - 背景画像更新エラー:", error);
      // エラーをスローせず、undefinedを返す
      return undefined;
    }
  };

  const updateUserName = async (value: string) => {
    setProfileData(prev => ({ ...prev, userName: value }));
    
    // データベースに即時反映
    try {
      const userId = await getUserId();
      if (!userId) {
        throw new Error('ユーザーIDが見つかりません');
      }
      
      const response = await fetch('/api/updateUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          name: value
        }),
      });
      
      if (!response.ok) {
      } else {
      }
    } catch (error) {
    }
  };

  const updateSnsLink = (type: string, value: string) => {
    setProfileData(prev => {
      // 新しいSNSリンクが追加された場合、snsOrderにも追加
      const newSnsOrder = [...(prev.snsOrder || [])];
      if (value && !prev.snsLinks[type] && !newSnsOrder.includes(type)) {
        newSnsOrder.push(type);
      }

      return {
        ...prev,
        snsLinks: {
          ...prev.snsLinks,
          [type]: value
        },
        snsOrder: newSnsOrder
      };
    });
  };

  // SNSの表示順序を更新
  const updateSnsOrder = async (newOrder: string[]) => {
    setProfileData(prev => ({
      ...prev,
      snsOrder: newOrder
    }));
    
    // データベースに即時反映
    try {
      const userId = await getUserId();
      if (!userId) {
        throw new Error('ユーザーIDが見つかりません');
      }
      
      const response = await fetch('/api/updateUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          snsOrder: newOrder
        }),
      });
      
      if (!response.ok) {
        console.error('SNS表示順序更新エラー:', response.status);
      }
    } catch (error) {
      console.error('SNS表示順序更新中にエラーが発生しました:', error);
    }
  };

  const updateSnsUserLinks = (links: any[]) => {
    setProfileData(prev => ({
      ...prev,
      snsUserLinks: links
    }));
  };

  // プロフィールデータの保存
  const saveProfile = async () => {
    try {
      // キャッシュされたユーザーIDを使用
      const userId = await getUserId();
      if (!userId) {
        // ローカルストレージからユーザーIDを取得する試み
        if (typeof window !== 'undefined') {
          const localUserId = localStorage.getItem('userId');
          if (localUserId) {
            setCachedUserId(localUserId);
            // 再度プロフィール保存を試みる
            return saveProfile();
          }
        }

        throw new Error('ユーザーIDが見つかりません');
      }
      
      // プロフィール画像とバックグラウンド画像のアップロード処理
      let profileImageUrl = profileData.profileImage;
      let backgroundImageUrl = profileData.backgroundImage;
      
      // 画像URLからタイムスタンプパラメータを除去して比較する関数
      const removeTimestamp = (url: string): string => {
        if (!url) return '';
        if (url.includes('?t=')) {
          return url.replace(/\?t=\d+/, '');
        }
        if (url.includes('&t=')) {
          return url.replace(/&t=\d+/, '');
        }
        return url;
      };
      
      // プロフィール画像がBase64形式の場合
      if (profileData.profileImage && profileData.profileImage.startsWith('data:image')) {
        try {
          // Base64文字列をBlobに変換
          const response = await fetch(profileData.profileImage);
          const blob = await response.blob();
          
          // FormDataを作成
          const formData = new FormData();
          formData.append('file', blob, 'profile-image.jpg');
          formData.append('user_ID', userId);
          
          const imageResponse = await fetch('/api/uploadImage', {
            method: 'POST',
            body: formData,
          });
          
          if (imageResponse.ok) {
            const imageData = await imageResponse.json();
            profileImageUrl = imageData.url;
          }
        } catch (error) {
          // エラー処理
        }
      }
      
      // 背景画像がBase64形式の場合
      if (profileData.backgroundImage && profileData.backgroundImage.startsWith('data:image')) {
        try {
          console.log("ProfileContext - 背景画像のBase64データを処理します");
          // Base64文字列をBlobに変換
          const response = await fetch(profileData.backgroundImage);
          const blob = await response.blob();
          
          // FormDataを作成
          const formData = new FormData();
          formData.append('file', blob, 'background-image.jpg');
          formData.append('user_ID', userId);
          formData.append('imageType', 'background'); // 背景画像として指定
          
          console.log("ProfileContext - 背景画像アップロード開始");
          const imageResponse = await fetch('/api/uploadImage', {
            method: 'POST',
            body: formData,
          });
          
          if (imageResponse.ok) {
            const imageData = await imageResponse.json();
            console.log("ProfileContext - 背景画像アップロード成功:", imageData);
            
            // アップロードされた画像のURLを取得
            if (imageData.url) {
              backgroundImageUrl = imageData.url;
              console.log("ProfileContext - 背景画像の新しいURL:", backgroundImageUrl);
              
              // 画像パスの正規化
              if (backgroundImageUrl.startsWith('/img/')) {
                backgroundImageUrl = `/static${backgroundImageUrl}`;
                console.log("ProfileContext - 背景画像パス正規化 (/img/ -> /static/img/):", backgroundImageUrl);
              }
            } else {
              console.error("ProfileContext - 画像パスが取得できませんでした");
              backgroundImageUrl = profileData.backgroundImage; // フォールバックとして元の画像データを使用
            }
          } else {
            const errorText = await imageResponse.text();
            console.error("ProfileContext - 背景画像アップロード失敗:", errorText);
            backgroundImageUrl = profileData.backgroundImage; // エラー時は元の画像を使用
          }
        } catch (error) {
          console.error("ProfileContext - 背景画像処理エラー:", error);
          backgroundImageUrl = profileData.backgroundImage; // エラー時は元の画像を使用
        }
      } else if (profileData.backgroundImage) {
        // 既存の背景画像パスがあればそのまま使用
        console.log("ProfileContext - 既存の背景画像パスを使用:", profileData.backgroundImage);
        backgroundImageUrl = profileData.backgroundImage;
      }
      
      console.log("ProfileContext - 保存する背景画像URL:", backgroundImageUrl);
      
      // 保存するデータを準備（undefinedのフィールドは除外）
      const saveData: any = { user_ID: userId };
      
      // 各フィールドが有効な値の場合のみ追加
      if (profileData.userName !== undefined) saveData.name = profileData.userName;
      if (profileData.website !== undefined) saveData.website = profileData.website;
      if (profileData.email !== undefined) saveData.email = profileData.email;
      if (profileData.selfIntroduction !== undefined) saveData.selfIntroduction = profileData.selfIntroduction;
      
      // プロフィール画像の変更チェック
      const isSameProfileImage = profileImageUrl && 
        removeTimestamp(profileImageUrl) === removeTimestamp(profileData.profileImage) &&
        !profileImageUrl.startsWith('data:image') && 
        !profileImageUrl.startsWith('blob:');
      
      // 背景画像の変更チェック
      const isSameBackgroundImage = backgroundImageUrl && 
        removeTimestamp(backgroundImageUrl) === removeTimestamp(profileData.backgroundImage) &&
        !backgroundImageUrl.startsWith('data:image') && 
        !backgroundImageUrl.startsWith('blob:');
      
      // 変更がある場合のみ画像URLを追加
      if (!isSameProfileImage && profileImageUrl) {
        saveData.profileImage = profileImageUrl;
        console.log("ProfileContext - プロフィール画像に変更があるため保存します");
      } else {
        console.log("ProfileContext - プロフィール画像に変更がないためスキップします");
      }
      
      if (!isSameBackgroundImage && backgroundImageUrl) {
        saveData.backgroundImage = backgroundImageUrl;
        console.log("ProfileContext - 背景画像に変更があるため保存します");
      } else {
        console.log("ProfileContext - 背景画像に変更がないためスキップします");
      }

      // APIを呼び出してプロフィールデータを保存
      const response = await fetch('/api/updateUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(saveData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`プロフィールの保存に失敗しました: ${errorText}`);
      } else {
        // 保存後にプロフィールデータを更新
        setProfileData(prev => ({
          ...prev,
          profileImage: profileImageUrl,
          backgroundImage: backgroundImageUrl
        }));
      }

      // SNSリンクの保存
      const snsResponse = await fetch('/api/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          snsLinks: profileData.snsLinks,
          snsOrder: profileData.snsOrder
        }),
      });

      if (!snsResponse.ok) {
        const snsErrorText = await snsResponse.text();
        console.error("ProfileContext - SNSリンク保存失敗:", snsResponse.status, snsErrorText);
      }

    } catch (error) {
      console.error("ProfileContext - プロフィール保存エラー:", error);
      throw error; // エラーを再スローして呼び出し側で処理できるようにする
    }
  };

  // コンテキスト値
  const value = {
    profileData,
    updateWebsite,
    updateEmail,
    updateSelfIntroduction,
    updateProfileImage,
    updateBackgroundImage,
    updateUserName,
    updateSnsLink,
    updateSnsOrder,
    updateSnsUserLinks,
    saveProfile,
    getUserId, // ユーザーID取得関数を公開
  };

  // ProfileProviderではローディング表示を行わず、個別のページで制御する

  // 型定義の問題を回避するために、型アサーションを使用
  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  ) as JSX.Element;
};

// カスタムフック
export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};
