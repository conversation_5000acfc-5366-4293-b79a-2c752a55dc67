import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const { userId, contactUrl } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'ユーザーIDが指定されていません' }, { status: 400 });
    }

    // ユーザー情報を更新
    const updatedUser = await prisma.user.update({
      where: {
        user_ID: userId,
      },
      data: {
        contact_url: contactUrl,
      },
      select: {
        contact_url: true,
      },
    });

    return NextResponse.json({ contact_url: updatedUser.contact_url });
  } catch (error) {
    console.error('ユーザー情報更新エラー:', error);
    return NextResponse.json({ error: 'サーバーエラーが発生しました' }, { status: 500 });
  }
}
