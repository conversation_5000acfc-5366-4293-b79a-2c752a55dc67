"use client";

import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface RankingType {
  thumbnail_image?: string;
  ranking_title: string;
  recommend_rate: number;
  ranking_url?: string;
  ranking_description?: string;
  ranking_ID: string;
}

interface SortableItemProps {
  id: string;
  ranking: RankingType;
  renderStars: (rate: number) => JSX.Element[];
  isDragging: boolean;
  isEditMode: boolean;
  onItemClick: () => void;
  onOptionsClick: (e: React.MouseEvent, index: number) => void;
  index: number;
  openIndex: number | null;
  onToggleEditMode: () => void;
  onDeleteItem?: (ranking_ID: string) => void;
  isFirst: boolean;
  isLast?: boolean;
  customRender?: () => React.ReactNode;
}

export const SortableItem: React.FC<SortableItemProps> = ({
  id,
  ranking,
  renderStars,
  isDragging,
  isEditMode,
  onItemClick,
  onOptionsClick,
  index,
  openIndex,
  onToggleEditMode,
  onDeleteItem,
  isFirst,
  isLast,
  customRender,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isSortableDragging ? 10 : 1,
    opacity: 1, // 常に不透過
    position: "relative" as const,
  };

  // 編集モード時にドラッグハンドルを表示するか
  const showDragHandle = isEditMode && !isSortableDragging;

  // カードクリック
  const handleItemClick = () => {
    if (!isEditMode && !isDragging) {
      onItemClick();
    }
  };

  // 三点リーダークリック
  const handleOptionsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isEditMode) {
      onOptionsClick(e, index);
    }
  };

  // 「表示順を変更する」 → 編集モード
  const handleToggleEditModeButton = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    onToggleEditMode();
  };

  // 「削除する」
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDeleteItem) {
      if (window.confirm(`「${ranking.ranking_title}」を削除してよろしいですか？`)) {
        onDeleteItem(ranking.ranking_ID);
      }
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex w-full justify-between 
        ${isEditMode && !customRender
          ? index === 0
            ? "p-[16px] border-t border-gray-300 border-b border-gray-300"
            : "p-[16px] border-b border-gray-300"
          : ""}
        ${isSortableDragging ? "bg-[rgb(236,236,236)]" : "bg-white"}`}
      onClick={handleItemClick}
      {...(isEditMode ? { ...attributes, ...listeners } : {})}
    >
      {/* カスタムレンダリングがある場合はそれを使用 */}
      {customRender ? (
        <div className="w-full">
          {customRender()}
        </div>
      ) : (
        <>
          {/* ドラッグハンドル - 編集モード時のみ表示 */}
          {showDragHandle && (
            <div className="absolute left-0 top-0 bottom-0 w-8 flex items-center justify-center cursor-grab">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="8" y1="6" x2="16" y2="6"></line>
                <line x1="8" y1="12" x2="16" y2="12"></line>
                <line x1="8" y1="18" x2="16" y2="18"></line>
              </svg>
            </div>
          )}

          {/* 画像部分 */}
          <div className={`w-[114px] h-[114px] ${showDragHandle ? "ml-8" : ""}`}>
            {ranking.thumbnail_image ? (
              <img
                src={ranking.thumbnail_image}
                alt="Ranking"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-400">No Image</span>
              </div>
            )}
          </div>

          {/* テキスト部分 */}
          <div className="flex flex-col justify-start items-start w-[70%] pt-[4px] pl-[16px]">
            <div className="flex justify-between items-start w-full">
              <div className="flex items-start text-left text-[14px] pr-[8px] mt-[-5px]">
                {ranking.ranking_title}
              </div>
            </div>
            <div className="flex">{renderStars(ranking.recommend_rate)}</div>
            <span className="text-left justify-start text-font-gray text-[12px] font-regular font-medium pr-[8px]">
              {ranking.ranking_description}
            </span>
          </div>

          {/* 三点リーダー (編集モード以外で表示) */}
          {!isEditMode && (
            <div
              className="relative flex flex-col justify-start items-center pt-[16px]"
              onClick={handleOptionsClick}
            >
              <span className="w-[3px] h-[3px] bg-black rounded-full mb-[3px]"></span>
              <span className="w-[3px] h-[3px] bg-black rounded-full mb-[3px]"></span>
              <span className="w-[3px] h-[3px] bg-black rounded-full"></span>

              {openIndex === index && (
                <div
                  className="absolute top-[20%] right-full -translate-y-1/2 mr-2 z-50 bg-white text-black text-sm shadow-lg rounded-md px-4 py-2"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="absolute top-1/2 left-full -translate-y-1/2 w-0 h-0 border-y-4 border-y-transparent border-l-4 border-l-white" />
                  <div className="flex flex-col items-start gap-2">
                    <button
                      type="button"
                      className="text-black text-sm whitespace-nowrap hover:text-gray-700 cursor-pointer edit-mode-toggle-button"
                      onClick={handleToggleEditModeButton}
                    >
                      表示順を変更する
                    </button>
                    <button
                      type="button"
                      className="text-black text-sm whitespace-nowrap hover:text-gray-700 cursor-pointer"
                      onClick={handleDelete}
                    >
                      削除する
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};
