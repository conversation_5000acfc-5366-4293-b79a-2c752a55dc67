# mypicks.best コンポーネント概要

## コンポーネント設計の原則

mypicks.bestアプリケーションでは、再利用可能なコンポーネントを多用し、一貫したユーザーインターフェースを提供しています。コンポーネントは以下の原則に基づいて設計されています：

1. **再利用性**: 複数の場所で使用できるように設計
2. **単一責任**: 各コンポーネントは明確な役割を持つ
3. **カスタマイズ性**: プロパティを通じて外観や動作をカスタマイズ可能
4. **アクセシビリティ**: すべてのユーザーが利用できるように配慮
5. **パフォーマンス**: 不要な再レンダリングを避ける最適化

## コンポーネントカテゴリ

コンポーネントは以下のカテゴリに分類されています：

### ボタン系コンポーネント

- [ButtonFooter](./buttons.md#buttonfooter) - フッターに配置するボタン
- [ButtonGray](./buttons.md#buttongray) - グレーのボタン
- [ButtonMainCategory](./buttons.md#buttonmaincategory) - メインカテゴリ選択ボタン
- [ButtonSubCategory](./buttons.md#buttonsubcategory) - サブカテゴリ選択ボタン
- [ButtonToggelEdit](./buttons.md#buttontogeledit) - 編集モード切り替えボタン
- [MypageReleaseButtonFooter](./buttons.md#mypagereleasebuttonfooter) - マイページリリース画面のフッターボタン
- [RankingRegisterButtonFooter](./buttons.md#rankingregisterbuttonfooter) - ランキング登録画面のフッターボタン
- [RedButtonFooter](./buttons.md#redbuttonfooter) - 赤色のフッターボタン

### 入力系コンポーネント

- [CategorySelect](./inputs.md#categoryselect) - カテゴリ選択コンポーネント
- [InputBox](./inputs.md#inputbox) - 入力ボックス
- [InputList](./inputs.md#inputlist) - 入力リスト
- [RichTextEditor](./inputs.md#richtexteditor) - リッチテキストエディタ
- [ReviewInputBox](./inputs.md#reviewinputbox) - レビュー入力ボックス
- [ReviewScore](./inputs.md#reviewscore) - レビュースコア入力

### 画像系コンポーネント

- [ImageItem](./images.md#imageitem) - 画像アイテム
- [ImageUserFrame](./images.md#imageuserframe) - ユーザーフレーム画像
- [ButtonImageSaveWrapper](./images.md#buttonimagesavewrapper) - 画像保存ボタンラッパー

### テキスト系コンポーネント

- [TextAlert](./texts.md#textalert) - アラートテキスト
- [TextConsent](./texts.md#textconsent) - 同意テキスト
- [TextProgressTitle](./texts.md#textprogresstitle) - 進行状況タイトル
- [TextTitle](./texts.md#texttitle) - タイトルテキスト
- [TextUserName](./texts.md#textusername) - ユーザー名テキスト
- [TextUserUrlOpen](./texts.md#textuserurlopen) - ユーザーURL表示（開く機能付き）
- [TextUserUrlSimple](./texts.md#textuserurlsimple) - ユーザーURL表示（シンプル）

### レイアウト系コンポーネント

- [DivWrapper](./layouts.md#divwrapper) - ラッパーdiv
- [Footer](./layouts.md#footer) - フッター
- [Header](./layouts.md#header) - ヘッダー
- [HeaderItems](./layouts.md#headeritems) - ヘッダーアイテム
- [LineOr](./layouts.md#lineor) - 「または」の区切り線
- [LineProgress](./layouts.md#lineprogress) - 進行状況を示す線

### SNS系コンポーネント

- [OtherSns](./sns.md#othersns) - その他のSNSリンク
- [SnsLink](./sns.md#snslink) - SNSリンク
- [SnsLinkEdit](./sns.md#snslinkedit) - SNSリンク編集

### その他のコンポーネント

- [LoadingAnimation](./others.md#loadinganimation) - ローディングアニメーション
- [LoginAuthentication](./others.md#loginauthentication) - ログイン認証
- [RecommendedLevel](./others.md#recommendedlevel) - おすすめレベル
- [ReviewDescription](./others.md#reviewdescription) - レビュー説明
- [SelfIntroduction](./others.md#selfintroduction) - 自己紹介
- [SortableItem](./others.md#sortableitem) - ドラッグ＆ドロップ可能なアイテム
- [TableOfContents](./others.md#tableofcontents) - 目次

## デザインシステム

mypicks.bestアプリケーションでは、以下のデザイン要素を標準化して使用しています：

### 色

- 背景色: `bg-[rgba(246,247,248,1)]`
- テキスト色: `text-[#313131]`（通常）、`text-[#AAAAAA]`（プレースホルダー/ラベル）
- ボーダー色: `border-[var(--line-color)]`、`border-[#DDDDDD]`

### ヘッダー項目の統一スタイル

```jsx
<div className="flex max-w-[500px] h-[46px] items-center pl-[16px] pr-0 py-0 relative border-y border-y-[var(--line-color)] bg-[rgba(246,247,248,1)]">
  <p className="relative w-fit text-font-gray text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
    <span className="text-[rgb(170,170,170)] text-[12px] tracking-[0]">見出しテキスト</span>
  </p>
</div>
```

### ボタンスタイル

- メインアクション: `bg-yellow-400 text-black font-medium py-3 rounded-full`
- 最大幅: `max-w-xs`

## コンポーネントの使用例

```jsx
import { ButtonMainCategory } from '@/components/ButtonMainCategory';
import { TextUserUrlOpen } from '@/components/TextUserUrlOpen';
import { ImageUserFrame } from '@/components/ImageUserFrame';
import { useUser } from '@/contexts/UserContext';

const MyPage = () => {
  // UserContextからユーザーIDを取得
  const { userId } = useUser();
  
  return (
    <div>
      {userId && <ImageUserFrame user_ID={userId} />}
      <TextUserUrlOpen domain={process.env.DOMAIN || 'mypicks.best'} />
      <ButtonMainCategory
        categories={categories}
        selectedCategory={selectedCategory}
        onCategorySelect={handleCategorySelect}
      />
    </div>
  );
};
```

## 関連ドキュメント

- [ボタン系コンポーネント](./buttons.md)
- [入力系コンポーネント](./inputs.md)
- [画像系コンポーネント](./images.md)
- [テキスト系コンポーネント](./texts.md)
- [レイアウト系コンポーネント](./layouts.md)
- [SNS系コンポーネント](./sns.md)
- [その他のコンポーネント](./others.md)

## 最終更新日

2025年5月16日
