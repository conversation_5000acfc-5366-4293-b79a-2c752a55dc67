import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// APIリクエストごとに新しいPrismaインスタンスを作成するのではなく、
// グローバルに1つのインスタンスを再利用する
let prisma: PrismaClient;

if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  // 開発環境では、グローバル変数にPrismaインスタンスを保存して再利用
  if (!(global as any).prisma) {
    (global as any).prisma = new PrismaClient();
  }
  prisma = (global as any).prisma;
}

// リクエスト重複防止のためのキャッシュ
const cache = new Map<string, { data: any, timestamp: number }>();
const CACHE_TTL = 5000; // キャッシュの有効期限（ミリ秒）

export async function GET(request: NextRequest) {
  // URLからクエリパラメータを取得
  const { searchParams } = new URL(request.url);
  const user_ID = searchParams.get('user_ID');
  const categoryId = searchParams.get('categoryID');
  const subCategoryId = searchParams.get('subCategoryID');

  try {

    console.log('getRankBySubcategory: API呼び出し開始', { user_ID, categoryId, subCategoryId });

    // 入力値の検証
    if (!user_ID || user_ID.trim() === '') {
      console.error('getRankBySubcategory: ユーザーIDが無効です', { user_ID });
      return NextResponse.json(
        { error: 'ユーザーIDが指定されていません' },
        { status: 400 }
      );
    }

    // キャッシュキーを生成
    const cacheKey = `${user_ID}-${categoryId || ''}-${subCategoryId || ''}`;

    // キャッシュチェック
    const now = Date.now();
    const cachedResult = cache.get(cacheKey);
    if (cachedResult && (now - cachedResult.timestamp < CACHE_TTL)) {
      console.log(`getRankBySubcategory: キャッシュからデータを返します: ${cacheKey}`);
      return NextResponse.json(cachedResult.data);
    }

    // クエリ条件を構築 - ユーザーIDと公開済みステータスで検索
    const whereCondition: any = {
      user_ID: user_ID,
      status: 'PUBLISHED', // 公開済みのみ取得
    };

    console.log('検索条件（ユーザーID + 公開済み）:', JSON.stringify(whereCondition));

    // テスト用：公開済みランキングを取得して確認
    let allRankings = [];
    try {
      allRankings = await prisma.ranking.findMany({
        where: {
          user_ID: user_ID,
          status: 'PUBLISHED' // 公開済みのみ
        },
        take: 10 // 最初の10件だけ取得
      });

      console.log('getRankBySubcategory: ユーザーの公開済みランキング(最初の10件):',
        allRankings.map(r => ({
          id: r.id,
          title: r.ranking_title,
          subCategory_ID: r.subCategory_ID,
          status: r.status,
          thumbnail: r.thumbnail_image ? 'あり' : 'なし',
          description: r.ranking_description ? 'あり' : 'なし'
        }))
      );
    } catch (dbError) {
      console.error('getRankBySubcategory: データベースアクセスエラー:', dbError);
      throw new Error(`データベースアクセスに失敗しました: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
    }

    // サブカテゴリIDが指定されている場合、そのサブカテゴリに関連するランキングを取得
    // ただし、subCategory_IDがnullや特定の値の場合も考慮
    if (subCategoryId) {
      console.log(`getRankBySubcategory: サブカテゴリID検索: ${subCategoryId}`);

      // サブカテゴリIDの存在確認（idとcategory_IDの両方で検索）
      let subCategoryExists = false;
      try {
        const subCategory = await prisma.category.findFirst({
          where: {
            OR: [
              { id: subCategoryId },
              { category_ID: subCategoryId }
            ]
          }
        });
        subCategoryExists = !!subCategory;

        if (subCategory) {
          console.log(`getRankBySubcategory: サブカテゴリが見つかりました: ${subCategory.category_name} (id: ${subCategory.id}, category_ID: ${subCategory.category_ID})`);
        } else {
          console.log(`getRankBySubcategory: 指定されたサブカテゴリID ${subCategoryId} は存在しません（idとcategory_IDの両方で検索）`);
        }
      } catch (dbError) {
        console.error('getRankBySubcategory: サブカテゴリ存在確認エラー:', dbError);
        // エラーの場合は存在しないものとして処理を継続
        subCategoryExists = false;
      }

      // 存在しないサブカテゴリIDでも、ランキングデータの検索は実行する
      // （過去に作成されたランキングが存在する可能性があるため）
      whereCondition.OR = [
        { subCategory_ID: subCategoryId },
        { subCategory_ID: null },
        { subCategory_ID: '未分類（今は設定しない）' }
      ];
    }
    // カテゴリIDが指定されている場合は、そのカテゴリに属するサブカテゴリを検索
    else if (categoryId) {
      console.log(`getRankBySubcategory: カテゴリID検索: ${categoryId}`);

      // カテゴリIDの存在確認（idとcategory_IDの両方で検索）
      let categoryExists = false;
      try {
        const category = await prisma.category.findFirst({
          where: {
            OR: [
              { id: categoryId },
              { category_ID: categoryId }
            ]
          }
        });
        categoryExists = !!category;

        if (category) {
          console.log(`getRankBySubcategory: カテゴリが見つかりました: ${category.category_name} (id: ${category.id}, category_ID: ${category.category_ID})`);
        } else {
          console.log(`getRankBySubcategory: 指定されたカテゴリID ${categoryId} は存在しません（idとcategory_IDの両方で検索）`);
        }
      } catch (dbError) {
        console.error('getRankBySubcategory: カテゴリ存在確認エラー:', dbError);
        // エラーの場合は存在しないものとして処理を継続
        categoryExists = false;
      }

      // 存在しないカテゴリIDでも、サブカテゴリとランキングデータの検索は実行する
      try {
        // カテゴリに属するサブカテゴリを取得
        const subCategories = await prisma.category.findMany({
          where: {
            parent_ID: categoryId
          },
          select: {
            id: true
          }
        });

        // サブカテゴリIDの配列を作成
        const subCategoryIds = subCategories.map(subCat => subCat.id);
        console.log(`getRankBySubcategory: 見つかったサブカテゴリ数: ${subCategoryIds.length}`);

        // サブカテゴリIDの配列が空でない場合
        if (subCategoryIds.length > 0) {
          whereCondition.OR = [
            { subCategory_ID: { in: subCategoryIds } },
            { subCategory_ID: null },
            { subCategory_ID: '未分類（今は設定しない）' }
          ];
        } else {
          // サブカテゴリがない場合はメインカテゴリIDを使用
          whereCondition.OR = [
            { subCategory_ID: categoryId },
            { subCategory_ID: null },
            { subCategory_ID: '未分類（今は設定しない）' }
          ];
        }
      } catch (subCatError) {
        console.error('getRankBySubcategory: サブカテゴリ取得エラー:', subCatError);
        // エラーの場合はメインカテゴリIDのみで検索
        whereCondition.OR = [
          { subCategory_ID: categoryId },
          { subCategory_ID: null },
          { subCategory_ID: '未分類（今は設定しない）' }
        ];
      }
    }

    console.log('最終検索条件:', JSON.stringify(whereCondition));

    // ランキングデータを取得
    const rankings = await prisma.ranking.findMany({
      where: whereCondition,
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        thumbnail_image: true,
        recommend_rate: true,
        subCategory_ID: true,
        user_ID: true,
        order: true,
        status: true, // ステータスも取得
        created_at: true,
        updated_at: true
      },
      orderBy: {
        order: 'asc'
      }
    });

    console.log(`getRankBySubcategory: ${rankings.length}件のランキングを取得しました`);

    // 完全なランキングオブジェクトの最初の1件を表示
    if (rankings.length > 0) {
      console.log('=== getRankBySubcategory API ===');
      console.log('最初のランキングの完全なデータ:', JSON.stringify(rankings[0], null, 2));
      console.log('recommend_rate値の型:', typeof rankings[0].recommend_rate);
      console.log('recommend_rate値:', rankings[0].recommend_rate);
      console.log('===========================');
    } else {
      console.log('getRankBySubcategory: ランキングが見つかりませんでした', {
        user_ID,
        categoryId,
        subCategoryId,
        whereCondition: JSON.stringify(whereCondition)
      });
    }

    console.log('getRankBySubcategory: 取得したランキングの詳細:',
      rankings.slice(0, 3).map(r => ({
        id: r.id,
        title: r.ranking_title,
        recommend_rate: r.recommend_rate,
        thumbnail: r.thumbnail_image ? 'あり' : 'なし',
        description: r.ranking_description ? 'あり' : 'なし'
      }))
    );

    // 結果をキャッシュに保存（データがある場合のみ）
    // 空のデータはキャッシュしないことで、次回のリクエスト時に再度データベースを確認する
    if (rankings.length > 0) {
      cache.set(cacheKey, { data: rankings, timestamp: now });
      console.log(`getRankBySubcategory: データをキャッシュに保存しました: ${cacheKey}`);
    } else {
      // 空のデータの場合はキャッシュから削除（存在する場合）
      if (cache.has(cacheKey)) {
        cache.delete(cacheKey);
        console.log(`getRankBySubcategory: 空のデータのためキャッシュを削除しました: ${cacheKey}`);
      } else {
        console.log(`getRankBySubcategory: 空のデータはキャッシュしません: ${cacheKey}`);
      }
    }

    return NextResponse.json(rankings);
  } catch (error) {
    return NextResponse.json(
      {
        error: 'ランキングの取得中にエラーが発生しました',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      },
      { status: 500 }
    );
  }
}
