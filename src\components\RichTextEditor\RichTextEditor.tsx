'use client';

import React, { useState, useRef, useEffect } from 'react';
import { RichTextEditorProps, TextAlign } from './types';
import { debugLog, trackEmbedState, processExistingLinks, loadEmbedScripts } from './utils';
import { useEmbedProtection } from './hooks/useEmbedProtection';
import { useSocialEmbed } from './hooks/useSocialEmbed';
import { useEditorEvents } from './hooks/useEditorEvents';
import { useToolbarActions } from './hooks/useToolbarActions';
import { useLinkManagement } from './hooks/useLinkManagement';
import { useKeyboardNavigation } from './hooks/useKeyboardNavigation';
import Toolbar from './components/Toolbar';
import LinkModal from './components/LinkModal';
import EditorContainer from './components/EditorContainer';

const RichTextEditor: React.FC<RichTextEditorProps> = React.memo(({
  value,
  initialHtml,
  onChange,
  placeholder = '',
  className = '',
  isFullScreen = false,
  showToolbar = true,
  onGetCurrentContent,
  onInsertEmbed
}): JSX.Element => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [textAlign, setTextAlign] = useState<TextAlign>('left');

  // 各プラットフォームのスクリプト読み込み状態を追跡
  const loadedScripts = useRef({
    instagram: false,
    twitter: false,
    tiktok: false
  });

  // 初期化関連の状態
  const isInitialized = useRef(false);
  const lastInitializedContent = useRef<string>('');

  // カスタムフックの初期化
  const { protectEmbed, restoreLostEmbeds, removeEmbedFromProtection, markKeyboardDeletion } = useEmbedProtection(editorRef);
  const { insertEmbed, processSocialEmbeds } = useSocialEmbed(editorRef, onChange, protectEmbed);

  // エディタイベントハンドラー
  const {
    handleInput,
    handleFocus,
    handleBlur,
    handleClick
  } = useEditorEvents({
    editorRef,
    onChange,
    value,
    placeholder,
    setIsFocused,
    // restoreLostEmbeds, // 🔧 無効化: 自動復元機能を削除
    processSocialEmbeds
  });

  // ツールバーアクション
  const {
    handleToolbarClick,
    handleTextAlignCommand
  } = useToolbarActions({
    editorRef,
    textAlign,
    setTextAlign,
    handleInput
  });

  // リンク管理
  const {
    isLinkModalOpen,
    isLinkRemoveModalOpen,
    linkUrl,
    setLinkUrl,
    handleLinkCommand,
    handleAddLink,
    handleRemoveLink,
    handleCancelLink,
    handleCancelRemoveLink
  } = useLinkManagement({
    editorRef,
    handleInput
  });

  // キーボードナビゲーション
  const { handleKeyDown } = useKeyboardNavigation({
    editorRef,
    handleInput,
    removeEmbedFromProtection,
    markKeyboardDeletion
  });

  // 現在のDOM内容を取得する関数
  const getCurrentDomContent = () => {
    if (!editorRef.current) return '';
    return editorRef.current.innerHTML;
  };

  // 親コンポーネントに getCurrentDomContent 関数を提供
  useEffect(() => {
    if (onGetCurrentContent) {
      onGetCurrentContent(getCurrentDomContent);
    }
  }, [onGetCurrentContent]);

  // 親コンポーネントに insertEmbed 関数を提供
  useEffect(() => {
    if (onInsertEmbed) {
      onInsertEmbed(insertEmbed);
    }
  }, [onInsertEmbed, insertEmbed]);

  // 🔧 修正: 初期値セット（valueとinitialHtmlの両方に対応）
  const initialHtmlRef = useRef<string>('');

  useEffect(() => {
    if (!editorRef.current) return;

    // 🔧 重要: 既に初期化済みの場合は処理をスキップ（カーソルジャンプ防止）
    if (isInitialized.current) {
      return;
    }

    // 🔧 修正: initialHtmlまたはvalueから初期内容を決定
    let initialContent = '';
    if (initialHtml) {
      initialContent = initialHtml.replace(/<!--.*?-->/g, '');
    } else if (value) {
      const valueString = typeof value === 'string' ? value : '';
      initialContent = valueString.replace(/<!--.*?-->/g, '');
    }



    // 初期内容がない場合は初期化をスキップ
    if (!initialContent) {
      return;
    }

    // 🔧 重要: エディタに既にコンテンツがある場合は初期化をスキップ（ユーザー入力保護）
    const currentContent = editorRef.current.innerHTML;
    if (currentContent && currentContent.trim() !== '' && currentContent !== '<br>' && currentContent !== '<div><br></div>') {

      isInitialized.current = true; // 初期化済みとしてマーク
      return;
    }

    trackEmbedState('USEEFFECT_INIT_START', undefined, {
      hasInitialHtml: !!initialHtml,
      hasValue: !!value,
      cleanInitialHtml: typeof initialContent === 'string' && initialContent.length > 0 ? initialContent.substring(0, 100) : String(initialContent || '').substring(0, 100),
      currentContent: typeof currentContent === 'string' && currentContent.length > 0 ? currentContent.substring(0, 100) : String(currentContent || '').substring(0, 100)
    });

    // 🔧 修正: 確実にエディタ内容を設定
    editorRef.current.innerHTML = initialContent;
    lastInitializedContent.current = initialContent;
    initialHtmlRef.current = initialContent;
    isInitialized.current = true;

    // 🔧 修正: カーソル位置設定を削除（カーソルジャンプの原因）
    // フォーカスやカーソル位置の設定は行わない

    // SNS埋め込み処理
    setTimeout(() => {
      if (initialContent.includes('social-embed') && initialContent.includes('data-loaded="false"')) {
        processSocialEmbeds();
      }
      processExistingLinks(editorRef);
    }, 100);
  }, [initialHtml, value]); // initialHtmlとvalueの両方を監視

  // SNS埋め込みイベントリスナーを設定
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.classList.add('rich-text-editor');

      const handleInsertSnsEmbed = (event: CustomEvent) => {
        debugLog('★★★ insertSnsEmbedイベントを受信しました ★★★');

        if (event.detail && event.detail.html) {
          const html = event.detail.html;

          if (editorRef.current) {
            // 🔧 修正: カーソル位置を取得
            const selection = window.getSelection();
            const range = selection && selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

            const wrappedHtml = `<br><br>${html}<br><br>`;
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = wrappedHtml;

            if (range) {
              // カーソル位置に挿入
              try {
                // 現在の選択範囲を削除
                range.deleteContents();

                // 埋め込み要素を順番に挿入
                const fragment = document.createDocumentFragment();
                while (tempDiv.firstChild) {
                  fragment.appendChild(tempDiv.firstChild);
                }

                range.insertNode(fragment);

                // カーソルを挿入した内容の後に移動
                range.collapse(false);
                selection?.removeAllRanges();
                selection?.addRange(range);

                debugLog('★★★ カーソル位置に埋め込みを挿入しました ★★★');
              } catch (error) {
                debugLog('カーソル位置への挿入に失敗、最後に追加します:', error);
                // フォールバック: 最後に追加
                while (tempDiv.firstChild) {
                  editorRef.current.appendChild(tempDiv.firstChild);
                }
              }
            } else {
              // カーソル位置が取得できない場合は最後に追加
              debugLog('カーソル位置が取得できないため、最後に追加します');
              while (tempDiv.firstChild) {
                editorRef.current.appendChild(tempDiv.firstChild);
              }
            }

            // フォーカスを確保
            editorRef.current.focus();

            onChange(editorRef.current.innerHTML);

            setTimeout(() => {
              processSocialEmbeds();
            }, 100);
          }
        }
      };
      
      editorRef.current.addEventListener('insertSnsEmbed', handleInsertSnsEmbed as EventListener);
      
      return () => {
        if (editorRef.current) {
          editorRef.current.removeEventListener('insertSnsEmbed', handleInsertSnsEmbed as EventListener);
        }
      };
    }
  }, [onChange, processSocialEmbeds]);

  // 各プラットフォームのスクリプトを読み込む
  useEffect(() => {
    loadEmbedScripts(loadedScripts.current);
  }, []);

  return (
    <div className={`rich-text-editor ${className} relative flex flex-col ${isFullScreen ? 'h-full' : ''} max-w-full bg-white`} style={{ overflowX: 'hidden', width: '100%', maxWidth: '100%' }}>
      {/* ツールバー */}
      {showToolbar && (
        <Toolbar
          onToolbarClick={handleToolbarClick}
          onTextAlignCommand={handleTextAlignCommand}
          onLinkCommand={handleLinkCommand}
          textAlign={textAlign}
        />
      )}

      {/* リンクモーダル */}
      <LinkModal
        type="add"
        isOpen={isLinkModalOpen}
        linkUrl={linkUrl}
        onLinkUrlChange={setLinkUrl}
        onConfirm={handleAddLink}
        onCancel={handleCancelLink}
      />

      {/* リンク解除モーダル */}
      <LinkModal
        type="remove"
        isOpen={isLinkRemoveModalOpen}
        onConfirm={handleRemoveLink}
        onCancel={handleCancelRemoveLink}
      />

      {/* エディタ本体 */}
      <EditorContainer
        editorRef={editorRef}
        value={value}
        isFocused={isFocused}
        placeholder={placeholder}
        onInput={handleInput}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
      />
    </div>
  );
});

export default RichTextEditor;