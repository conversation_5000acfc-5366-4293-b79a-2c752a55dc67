import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const user_ID = searchParams.get('user_ID');

    console.log('🔍 [debug-category] デバッグ開始:', { categoryId, user_ID });

    // 1. 指定されたIDでカテゴリを検索（id）
    const categoryById = await prisma.category.findUnique({
      where: { id: categoryId || '' }
    });

    // 2. 指定されたIDでカテゴリを検索（category_ID）
    const categoryByCategoryId = await prisma.category.findFirst({
      where: { category_ID: categoryId || '' }
    });

    // 3. ユーザーの全カテゴリを取得
    const allUserCategories = await prisma.category.findMany({
      where: { user_ID: user_ID || '' },
      select: {
        id: true,
        category_ID: true,
        category_name: true,
        user_ID: true,
        parent_ID: true
      }
    });

    // 4. 指定されたIDとユーザーIDの組み合わせで検索
    const categoryByIdAndUser = await prisma.category.findFirst({
      where: {
        OR: [
          { id: categoryId || '' },
          { category_ID: categoryId || '' }
        ],
        user_ID: user_ID || ''
      }
    });

    return NextResponse.json({
      searchParams: { categoryId, user_ID },
      results: {
        categoryById,
        categoryByCategoryId,
        categoryByIdAndUser,
        allUserCategories,
        totalUserCategories: allUserCategories.length
      }
    });

  } catch (error) {
    console.error('❌ [debug-category] エラー:', error);
    return NextResponse.json({
      error: 'デバッグAPIでエラーが発生しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
