'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function ChangePasswordGuidePage() {
  return (

    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">パスワードを変更する方法</h1>
            <div className="pt-6 text-[14px] text-[#313131]">
              <p>1. マイページから「設定」アイコンをタップします。</p>
              <p>2. 「アカウント設定」を選択します。</p>
              <p>3. 「パスワード変更」をタップします。</p>
              <p>4. 現在のパスワードを入力します。</p>
              <p>5. 新しいパスワードを入力します。</p>
              <p>6. 確認のため、新しいパスワードをもう一度入力します。</p>
              <p>7. 「変更を保存」ボタンをタップして確定します。</p>
              <p>8. パスワード変更完了の通知が表示され、登録メールアドレスに確認メールが送信されます。</p>
            </div>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
