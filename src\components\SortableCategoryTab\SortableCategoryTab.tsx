import React from 'react';
import { useSortable } from '@dnd-kit/sortable';

interface Category {
  id: string;
  category_name: string;
}

interface SortableCategoryTabProps {
  category: Category;
  index: number;
  selectedIndex: number;
  editingIndex: number | null;
  editedName: string;
  onCategoryClick: (index: number, id: string, event?: React.MouseEvent) => void;
  onOptionsClick: (e: React.MouseEvent, index: number) => void;
  onInputChange: (index: number, value: string) => void;
  onInputBlur: (index: number) => void;
  onKeyDown: (index: number, e: React.KeyboardEvent) => void;
  isModalOpen?: boolean;
  onModalClose?: () => void;
  optionsIndex?: number | null;
  onCloseOptionsModal?: () => void;
  isOwnPage?: boolean;
  onDirectShare?: (index: number) => void;
  isDraggable?: boolean;
}

export const SortableCategoryTab: React.FC<SortableCategoryTabProps> = ({
  category,
  index,
  selectedIndex,
  editingIndex,
  editedName,
  onCategoryClick,
  onOptionsClick,
  onInputChange,
  onInputBlur,
  onKeyDown,
  isModalOpen = false,
  onModalClose,
  optionsIndex = null,
  onCloseOptionsModal,
  isOwnPage = true,
  onDirectShare,
  isDraggable = false
}) => {
  // 選択中のカテゴリのみドラッグ可能
  const isSelected = selectedIndex === index;
  const canDrag = isDraggable && isSelected;

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: category.id,
    disabled: !canDrag,
    animateLayoutChanges: () => true,
    data: {
      type: 'category',
      category,
      index,
    },
  });

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, 0px, 0)`
      : undefined,
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.9 : 1,
    touchAction: isDraggable ? "none" : "auto",
    position: "relative" as const,
    // 通常時は常にpointerカーソル、ドラッグ中のみgrabbingカーソル
    cursor: isDragging ? "grabbing" : "pointer",
  };

  return (
    <li
      ref={setNodeRef}
      style={style}
      {...(canDrag ? { ...attributes, ...listeners } : {})}
      className={`category-tab flex-none w-auto pt-4 pb-4 pl-[20px] pr-0 cursor-pointer flex items-center ${
        selectedIndex === index ? "bg-white" : ""
      }`}
      onClick={(event) => {
        // ドラッグ中の場合はクリックを無視
        if (isDragging) {
          event.preventDefault();
          event.stopPropagation();
          return;
        }

        // サイドメニューモーダルが開いている場合は、モーダルを閉じるだけでカテゴリ切り替えは行わない
        if (isModalOpen && onModalClose) {
          event.preventDefault();
          event.stopPropagation();
          onModalClose();
          return;
        }

        // オプションモーダルが開いている場合は、モーダルを閉じるだけでカテゴリ切り替えは行わない
        if (optionsIndex !== null && onCloseOptionsModal) {
          event.preventDefault();
          event.stopPropagation();
          onCloseOptionsModal();
          return;
        }

        // Chromeタブと同じ挙動：選択中のタブをクリックしても何も変化しない
        if (selectedIndex === index) {
          return;
        }

        onCategoryClick(index, category.id, event);
      }}
    >
      {editingIndex === index ? (
        <input
          type="text"
          data-index={index}
          value={editedName}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onInputChange(index, e.target.value)}
          onBlur={() => onInputBlur(index)}
          onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => onKeyDown(index, e)}
          autoFocus
          className="text-black-1 text-[14px] focus:outline-none"
        />
      ) : (
        <div className="text-black-1 text-[14px]">{editedName}</div>
      )}
      {/* すべてのカテゴリに三点リーダーボタンを表示 */}
      <button
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation();

          // サイドメニューモーダルが開いている場合は、モーダルを閉じるだけで他の処理は行わない
          if (isModalOpen && onModalClose) {
            onModalClose();
            return;
          }

          // ログアウト状態の場合は選択中のカテゴリのみ共有機能を実行
          if (!isOwnPage && onDirectShare) {
            if (index === selectedIndex) {
              onDirectShare(index);
            }
            return;
          }

          // ログイン状態での三点リーダーボタンのクリックを処理
          if (index === selectedIndex) {
            // 選択中のカテゴリの場合はモーダルを開く
            onOptionsClick(e, index);
          } else {
            // 選択中でない場合は、まずカテゴリを選択する
            onCategoryClick(index, category.id);
          }
        }}
        className="border-r border-[#DDDDDD] pt-1 pb-1 flex flex-col justify-start items-center w-[42px] focus:outline-none cursor-pointer"
        style={{transform: 'translateZ(0)', backfaceVisibility: 'hidden'}}
      >
        <span className="w-[2px] h-[2px] bg-black rounded-full mb-[2px]" style={{transform: 'translateZ(0)'}}></span>
        <span className="w-[2px] h-[2px] bg-black rounded-full mb-[2px]" style={{transform: 'translateZ(0)'}}></span>
        <span className="w-[2px] h-[2px] bg-black rounded-full" style={{transform: 'translateZ(0)'}}></span>
      </button>
    </li>
  );
};
