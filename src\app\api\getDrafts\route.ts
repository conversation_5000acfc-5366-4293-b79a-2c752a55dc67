import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // URLパラメータからuser_IDを取得
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_ID');
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'ユーザーIDが必要です' },
        { status: 400 }
      );
    }
    
    console.log('下書き一覧を取得します。ユーザーID:', userId);
    
    // データベースから下書きステータスのランキングを取得
    const drafts = await prisma.ranking.findMany({
      where: {
        user_ID: userId,
        status: 'DRAFT',
      },
      orderBy: {
        updated_at: 'desc', // 更新日時の降順（新しいものから）
      },
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        thumbnail_image: true,
        recommend_rate: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        subCategory_ID: true,
        created_at: true,
        updated_at: true,
      },
    });
    

    
    // フロントエンド用の形式に変換（カテゴリ情報も取得）
    const formattedDrafts = await Promise.all(drafts.map(async (draft) => {
      // サブカテゴリからカテゴリ情報を取得
      let categoryInfo = { name: '', categoryId: '', subCategoryName: '' };
      if (draft.subCategory_ID) {
        try {
          const subCategory = await prisma.category.findFirst({
            where: {
              id: draft.subCategory_ID,
              user_ID: userId,
            },
            include: {
              Parent: true,
            },
          });

          if (subCategory) {
            categoryInfo.subCategoryName = subCategory.category_name;

            if (subCategory.Parent) {
              categoryInfo = {
                name: subCategory.Parent.category_name,
                categoryId: subCategory.Parent.category_ID,
                subCategoryName: subCategory.category_name,
              };
            }
          }
        } catch (error) {
          console.error('カテゴリ情報取得エラー:', error);
        }
      }



      return {
        id: draft.ranking_ID, // フロントエンドではranking_IDをidとして使用
        title: draft.ranking_title,
        description: draft.ranking_description || '',
        thumbnailImage: draft.thumbnail_image && draft.thumbnail_image.length > 0 ? draft.thumbnail_image[0] : '',
        images: draft.thumbnail_image || [],
        userId: userId,
        createdAt: draft.created_at.toISOString(),
        updatedAt: draft.updated_at.toISOString(),
        // 追加フィールド
        category: categoryInfo.name,
        categoryId: categoryInfo.categoryId,
        categoryParam: categoryInfo.categoryId, // categoryIdをcategoryParamとしても使用
        subCategory: categoryInfo.subCategoryName,
        rating: draft.recommend_rate || 1,
        amazonUrl: draft.amazon_url || '',
        rakutenUrl: draft.rakuten_url || '',
        yahooUrl: draft.yahoo_url || '',
        qoo10Url: draft.qoo10_url || '',
        officialUrl: draft.official_url || '',
      };
    }));
    
    // 成功した場合のレスポンスを返す
    return NextResponse.json(
      { 
        success: true, 
        data: formattedDrafts,
        count: formattedDrafts.length
      },
      {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (error) {
    console.error('下書き一覧取得中にエラーが発生しました:', error);
    return NextResponse.json(
      { success: false, error: '下書き一覧の取得に失敗しました' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  // プリフライトリクエストに対するレスポンス
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}
