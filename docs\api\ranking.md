# ランキング関連API

## ランキング取得 API

### エンドポイント

```
GET /api/getRank
```

### 説明

指定されたランキングIDに基づいてランキング情報を取得します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明                   |
|--------------|--------|------|------------------------|
| ranking_ID   | string | はい | 取得するランキングのID |

### レスポンス

成功時（200 OK）:

```json
{
  "id": "uuid",
  "ranking_ID": "string",
  "user_ID": "string",
  "title": "string",
  "description": "string",
  "category_ID": "string",
  "subCategory_ID": "string",
  "thumbnail": "string",
  "items": [
    {
      "id": "uuid",
      "item_name": "string",
      "item_image": "string",
      "item_description": "string",
      "item_score": "number",
      "item_order": "number"
    }
    // ...
  ],
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（404 Not Found）:

```json
{
  "error": true,
  "message": "ランキングが見つかりません"
}
```

## ユーザー別ランキング一覧取得 API

### エンドポイント

```
GET /api/getRanks
```

### 説明

指定されたユーザーIDに基づいて、そのユーザーが作成したすべてのランキングを取得します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明                             |
|--------------|--------|------|----------------------------------|
| user_ID      | string | はい | ランキングを取得するユーザーのID |

### レスポンス

成功時（200 OK）:

```json
[
  {
    "id": "uuid",
    "ranking_ID": "string",
    "user_ID": "string",
    "title": "string",
    "description": "string",
    "category_ID": "string",
    "subCategory_ID": "string",
    "thumbnail": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  // ...
]
```

## カテゴリ別ランキング取得 API

### エンドポイント

```
GET /api/getRankByCategory
```

### 説明

指定されたカテゴリIDに基づいて、そのカテゴリに属するすべてのランキングを取得します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明                               |
|--------------|--------|------|------------------------------------|
| category_ID  | string | はい | ランキングを取得するカテゴリのID   |
| user_ID      | string | はい | ランキングを取得するユーザーのID   |

### レスポンス

成功時（200 OK）:

```json
[
  {
    "id": "uuid",
    "ranking_ID": "string",
    "user_ID": "string",
    "title": "string",
    "description": "string",
    "category_ID": "string",
    "subCategory_ID": "string",
    "thumbnail": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  // ...
]
```

## サブカテゴリ別ランキング取得 API

### エンドポイント

```
GET /api/getRankBySubcategory
```

### 説明

指定されたサブカテゴリIDに基づいて、そのサブカテゴリに属するすべてのランキングを取得します。

### パラメータ

| パラメータ名   | 型     | 必須 | 説明                                   |
|----------------|--------|------|----------------------------------------|
| subCategory_ID | string | はい | ランキングを取得するサブカテゴリのID   |
| user_ID        | string | はい | ランキングを取得するユーザーのID       |

### レスポンス

成功時（200 OK）:

```json
[
  {
    "id": "uuid",
    "ranking_ID": "string",
    "user_ID": "string",
    "title": "string",
    "description": "string",
    "category_ID": "string",
    "subCategory_ID": "string",
    "thumbnail": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  // ...
]
```

## <a id="create"></a>ランキング作成 API

### エンドポイント

```
POST /api/postRank
```

### 説明

新しいランキングを作成します。カテゴリやサブカテゴリが存在しない場合は自動的に作成されます。

### リクエストボディ

```json
{
  "user_ID": "string",
  "title": "string",
  "description": "string",
  "category_name": "string",
  "subCategory_name": "string",
  "thumbnail": "string",
  "items": [
    {
      "item_name": "string",
      "item_image": "string",
      "item_description": "string",
      "item_score": "number",
      "item_order": "number"
    }
    // ...
  ]
}
```

### レスポンス

成功時（201 Created）:

```json
{
  "id": "uuid",
  "ranking_ID": "string",
  "user_ID": "string",
  "title": "string",
  "description": "string",
  "category_ID": "string",
  "subCategory_ID": "string",
  "thumbnail": "string",
  "items": [
    {
      "id": "uuid",
      "item_name": "string",
      "item_image": "string",
      "item_description": "string",
      "item_score": "number",
      "item_order": "number"
    }
    // ...
  ],
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（400 Bad Request）:

```json
{
  "error": true,
  "message": "ランキングの作成に失敗しました"
}
```

## ランキング保存 API

### エンドポイント

```
POST /api/saveRanking
```

### 説明

ランキングを保存します。新しいカテゴリやサブカテゴリを自動的にCategoryテーブルに保存する機能があります。

### リクエストボディ

```json
{
  "user_ID": "string",
  "title": "string",
  "description": "string",
  "category_ID": "string",
  "category_name": "string",
  "subCategory_ID": "string",
  "subCategory_name": "string",
  "thumbnail": "string",
  "items": [
    {
      "item_name": "string",
      "item_image": "string",
      "item_description": "string",
      "item_score": "number",
      "item_order": "number"
    }
    // ...
  ]
}
```

### レスポンス

成功時（201 Created）:

```json
{
  "id": "uuid",
  "ranking_ID": "string",
  "user_ID": "string",
  "title": "string",
  "description": "string",
  "category_ID": "string",
  "subCategory_ID": "string",
  "thumbnail": "string",
  "items": [
    {
      "id": "uuid",
      "item_name": "string",
      "item_image": "string",
      "item_description": "string",
      "item_score": "number",
      "item_order": "number"
    }
    // ...
  ],
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## 実装上の注意点

- ランキング登録時にカテゴリとサブカテゴリを自動的にCategoryテーブルに保存する機能があります
- 以前は`category_ID`フィールドが使用されていましたが、Prismaスキーマでは`id`フィールドが主キーとして定義されているため、コードを修正しました
- カテゴリ検索・作成時に`category_ID`フィールドを削除し、代わりに`id`フィールドを使用するように変更しました
- ランキング作成時に`category_ID`や`category_name`、`subCategory_name`フィールドを適切に処理するように修正しました

## 関連ファイル

- `src/app/api/getRank/route.ts` - ランキング取得API
- `src/app/api/getRanks/route.ts` - ユーザー別ランキング一覧取得API
- `src/app/api/getRankByCategory/route.ts` - カテゴリ別ランキング取得API
- `src/app/api/getRankBySubcategory/route.ts` - サブカテゴリ別ランキング取得API
- `src/app/api/postRank/route.ts` - ランキング作成API
- `src/app/api/saveRanking/route.ts` - ランキング保存API

## 最終更新日

2025年4月25日
