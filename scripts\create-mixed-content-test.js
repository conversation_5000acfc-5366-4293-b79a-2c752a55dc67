const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createMixedContentTest() {
  console.log('Creating test ranking with mixed content (normal text + H3 headings)...');
  
  try {
    // 1. 既存のユーザーを確認
    const users = await prisma.user.findMany({
      select: {
        user_ID: true,
        domain: true,
        name: true
      },
      take: 3
    });
    
    if (users.length === 0) {
      console.log('❌ No users found. Please create a user first.');
      return;
    }
    
    const testUser = users[0];
    console.log(`Using user: ${testUser.name} (ID: ${testUser.user_ID})`);

    // 2. 既存のカテゴリを確認
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        category_ID: true,
        category_name: true,
        user_ID: true,
        parent_ID: true
      },
      take: 5
    });
    
    if (categories.length === 0) {
      console.log('❌ No categories found. Please create a category first.');
      return;
    }
    
    const testCategory = categories.find(cat => cat.user_ID === testUser.user_ID) || categories[0];
    console.log(`Using category: ${testCategory.category_name} (ID: ${testCategory.id})`);

    // 3. 通常テキストとH3見出しが混在するテストデータを作成
    const mixedContent = `
<p>この商品について詳しくレビューします。まず最初に感じたのは、<span style="background-color: transparent; font-family: inherit;">品質の高さ</span>でした。</p>

<h3 style="font-size: 1.25em; font-weight: bold; margin: 0.7em 0px; padding: 0.2em 0px 0.3em; position: relative; color: rgb(68, 68, 68); line-height: 1.4; display: block; word-break: break-word; white-space: normal; text-indent: 0px; border-bottom: 3px solid rgb(230, 59, 95);" data-styled="true">使用感について</h3>

<p>実際に使ってみると、<span style="background-color: transparent; font-family: inherit;">とても使いやすく</span>、日常的に活用できそうです。</p>

<div>特に気に入った点は以下の通りです：</div>
<ul>
  <li><span style="background-color: transparent; font-family: inherit;">デザインが美しい</span></li>
  <li>機能性が高い</li>
  <li><span style="background-color: transparent; font-family: inherit;">コストパフォーマンスが良い</span></li>
</ul>

<h3 style="font-size: 1.25em; font-weight: bold; margin: 0.7em 0px; padding: 0.2em 0px 0.3em; position: relative; color: rgb(68, 68, 68); line-height: 1.4; display: block; word-break: break-word; white-space: normal; text-indent: 0px; border-bottom: 3px solid rgb(230, 59, 95);" data-styled="true">価格について</h3>

<p>価格に関しては、<span style="background-color: transparent; font-family: inherit;">他の類似商品と比較しても非常にリーズナブル</span>だと思います。</p>

<div style="text-align: center;"><span style="background-color: transparent; font-family: inherit;">総合評価：★★★★★</span></div>

<h3 style="font-size: 1.25em; font-weight: bold; margin: 0.7em 0px; padding: 0.2em 0px 0.3em; position: relative; color: rgb(68, 68, 68); line-height: 1.4; display: block; word-break: break-word; white-space: normal; text-indent: 0px; border-bottom: 3px solid rgb(230, 59, 95);" data-styled="true">まとめ</h3>

<p>この商品は<span style="background-color: transparent; font-family: inherit;">本当におすすめできる商品</span>です。購入を検討している方は、ぜひ一度試してみてください。</p>

<div><span style="background-color: transparent; font-family: inherit;">最後に、この商品を使って感じたのは、やはり品質の重要性です。</span></div>
    `.trim();

    const testRankingData = {
      ranking_ID: `mixed_content_test_${Date.now()}`,
      user_ID: testUser.user_ID,
      domain: testUser.domain,
      ranking_title: '通常テキスト+H3見出し混在テスト',
      ranking_description: mixedContent,
      amazon_url: 'https://amazon.co.jp/test-mixed-content',
      rakuten_url: 'https://rakuten.co.jp/test-mixed-content',
      yahoo_url: 'https://shopping.yahoo.co.jp/test-mixed-content',
      qoo10_url: null,
      official_url: 'https://example.com/test-mixed-content',
      recommend_rate: 5,
      thumbnail_image: ['https://via.placeholder.com/300x300/95E1D3/FFFFFF?text=Mixed+Test'],
      subCategory_ID: testCategory.id,
      order: 3
    };

    console.log('Creating test ranking with mixed content data...');
    
    const createdRanking = await prisma.ranking.create({
      data: testRankingData
    });
    
    console.log('✅ Mixed content test ranking created successfully!');
    console.log('Ranking ID:', createdRanking.ranking_ID);
    console.log('User ID:', createdRanking.user_ID);
    console.log('Access URL:', `http://localhost:3001/mypageReleaseDetail/${createdRanking.user_ID}/${createdRanking.ranking_ID}`);
    
    // 4. 作成されたランキングを確認
    console.log('\n4. Verifying created ranking...');
    const verifyRanking = await prisma.ranking.findFirst({
      where: {
        ranking_ID: createdRanking.ranking_ID
      },
      select: {
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        user_ID: true
      }
    });
    
    if (verifyRanking) {
      console.log('✅ Ranking verification successful');
      console.log('Mixed content length:', verifyRanking.ranking_description.length);
      console.log('Contains H3 headings:', verifyRanking.ranking_description.includes('data-styled="true"'));
      console.log('Contains span elements:', verifyRanking.ranking_description.includes('<span style="background-color: transparent'));
      console.log('Contains border-bottom style:', verifyRanking.ranking_description.includes('border-bottom: 3px solid rgb(230, 59, 95)'));
    } else {
      console.log('❌ Ranking verification failed');
    }

  } catch (error) {
    console.error('❌ Error during mixed content test ranking creation:', error);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.meta) {
      console.error('Error meta:', error.meta);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
createMixedContentTest();
