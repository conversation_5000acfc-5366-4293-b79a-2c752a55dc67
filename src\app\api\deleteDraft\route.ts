import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function DELETE(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('受信した削除リクエスト:', data);
    
    // 必須フィールドのバリデーション
    if (!data.ranking_ID) {
      return NextResponse.json(
        { success: false, error: 'ランキングIDが必要です' },
        { status: 400 }
      );
    }
    
    if (!data.user_ID) {
      return NextResponse.json(
        { success: false, error: 'ユーザーIDが必要です' },
        { status: 400 }
      );
    }
    
    console.log(`下書き削除処理を開始します: ランキングID=${data.ranking_ID}, ユーザーID=${data.user_ID}`);

    // まず、削除対象の下書きを検索
    const targetDraft = await prisma.ranking.findFirst({
      where: {
        ranking_ID: data.ranking_ID,
        user_ID: data.user_ID,
        status: 'DRAFT', // 下書きのみ削除可能
      },
      select: {
        id: true,
        ranking_title: true,
      },
    });

    if (!targetDraft) {
      return NextResponse.json(
        { success: false, error: '指定された下書きが見つかりません' },
        { status: 404 }
      );
    }

    // プライマリキー（id）を使用して削除
    const result = await prisma.ranking.delete({
      where: {
        id: targetDraft.id,
      },
    });

    console.log('下書き削除完了:', result);
    
    // 成功した場合のレスポンスを返す
    return NextResponse.json(
      { 
        success: true, 
        message: '下書きを削除しました',
        deletedId: data.ranking_ID
      },
      {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'DELETE, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (error) {
    console.error('下書き削除中にエラーが発生しました:', error);

    // Prismaのエラーハンドリング
    if (error.code === 'P2025') {
      // レコードが見つからない場合
      return NextResponse.json(
        { success: false, error: '指定された下書きが見つかりません' },
        { status: 404 }
      );
    }

    if (error.code === 'P2003') {
      // 外部キー制約エラー
      return NextResponse.json(
        { success: false, error: '関連データが存在するため削除できません' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '下書きの削除に失敗しました' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'DELETE, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  // プリフライトリクエストに対するレスポンス
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'DELETE, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}
