/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
}

export const ButtonGray = ({ className }: Props): JSX.Element => {
  return (
    <div className={`inline-flex flex-col items-start gap-[10px] relative ${className}`}>
      <div className="inline-flex items-center justify-center px-[88px] py-[12px] relative flex-[0_0_auto] bg-button-gray rounded-[100px]">
        <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-white text-[14px] text-center tracking-[0] leading-[14px] whitespace-nowrap">
          ログインする
        </div>
      </div>
    </div>
  );
};
