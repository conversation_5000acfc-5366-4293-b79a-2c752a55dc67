import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * テスト用：手動でhistory_slugを設定するAPI
 */
export async function POST(request: NextRequest) {
  try {
    const { user_ID, categoryName, historySlug } = await request.json();

    if (!user_ID || !categoryName) {
      return NextResponse.json({
        error: 'user_IDとcategoryNameが必要です'
      }, { status: 400 });
    }



    // カテゴリを検索
    const category = await prisma.category.findFirst({
      where: {
        user_ID: user_ID,
        category_name: categoryName,
        parent_ID: null // メインカテゴリのみ
      }
    });

    if (!category) {
      return NextResponse.json({
        error: 'カテゴリが見つかりません',
        details: { user_ID, categoryName }
      }, { status: 404 });
    }



    // history_slugを更新
    const updatedCategory = await prisma.category.update({
      where: { id: category.id },
      data: { history_slug: historySlug },
      select: {
        id: true,
        category_name: true,
        category_slug: true,
        history_slug: true
      }
    });

    // 更新後の確認
    const verifyCategory = await prisma.category.findUnique({
      where: { id: category.id },
      select: {
        id: true,
        category_name: true,
        category_slug: true,
        history_slug: true
      }
    });

    return NextResponse.json({
      success: true,
      message: 'history_slugを設定しました',
      before: {
        history_slug: category.history_slug
      },
      after: updatedCategory,
      verification: verifyCategory
    });

  } catch (error) {
    console.error('🚨 [setTestHistorySlug] エラー:', error);
    return NextResponse.json({
      error: 'history_slug設定中にエラーが発生しました',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
