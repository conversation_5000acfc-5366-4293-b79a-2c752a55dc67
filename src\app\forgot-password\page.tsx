'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { HeaderItems } from '@/components/HeaderItems';
import { Footer } from '@/components/Footer';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  // パスワードリセットリクエスト処理
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    // 入力検証
    if (!email) {
      setError('メールアドレスを入力してください');
      return;
    }
    
    // 簡易的なメールアドレス検証
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('有効なメールアドレスを入力してください');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // ここにAPIリクエストを実装
      // 例: await requestPasswordReset(email);
      
      // 成功時の処理
      setIsSuccess(true);
    } catch (err) {
      console.error('パスワードリセットエラー:', err);
      setError('パスワードリセットリクエストに失敗しました。もう一度お試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  // ログインページに戻る
  const handleBackToLogin = () => {
    router.push('/sign-in');
  };

  return (
    <div className="max-w-[500px] mx-auto flex flex-col min-h-screen relative bg-white">
      <HeaderItems text="パスワードをリセット" />

      {/* メインコンテンツ */}
      <main className="flex-1 px-10 py-6">
        {isSuccess ? (
          <div className="text-center">
            <h2 className="text-[18px] font-medium text-[#313131] mb-4">メールを送信しました</h2>
            <p className="text-[14px] text-[#313131] mb-8">
              パスワードリセットの手順をメールで送信しました。メールをご確認ください。
            </p>
            <button
              onClick={handleBackToLogin}
              className="w-full bg-[#E63B5F] text-white py-3 rounded-full mb-4 hover:bg-[#DD0F2B]"
            >
              ログインページに戻る
            </button>
          </div>
        ) : (
          <form onSubmit={handleResetPassword}>
            <p className="text-[14px] text-[#313131] mb-6">
              登録したメールアドレスを入力するとパスワードリセットリンクを送信します。
            </p>
            
            {/* メールアドレス入力 */}
            <div className="">
              <label 
                htmlFor="email" 
                className="block text-[14px] text-[#313131] mb-2"
              >
                メールアドレス
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-3 border border-[#DDDDDD] rounded text-[#313131] bg-white"
                placeholder="<EMAIL>"
              />
            </div>

            {/* エラーメッセージ */}
            {error && (
              <div className="mb-4 text-red-500 text-[14px]">
                {error}
              </div>
            )}

            {/* 送信ボタン */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#E63B5F] text-white py-3 rounded-full mt-14 hover:bg-[#DD0F2B]"
            >
              {isLoading ? '処理中...' : 'パスワードをリセット'}
            </button>
          </form>
        )}
      </main>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
