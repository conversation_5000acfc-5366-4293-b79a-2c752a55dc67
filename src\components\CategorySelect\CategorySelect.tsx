import React, { useState, useEffect, useCallback, useRef } from "react";
import { useUser } from "@/contexts/UserContext";

interface CategorySelectProps {
  className?: string;
  category?: string;
  subCategory: string;
  parentCategoryId?: string;
  changeCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
  changeSubCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onActionChange?: (action: 'add' | 'edit' | 'delete', categoryId?: string | null, categoryName?: string) => void;
  isEditMode?: boolean;
  showAddButton?: boolean;
  userId?: string; // userIdをpropsとして追加
}

interface SubCategory {
  id: string;
  category_name: string;
  category_ID: string;
  order?: number; // orderプロパティを追加
}

export const CategorySelect: React.FC<CategorySelectProps> = ({
  className,
  category = "",
  subCategory,
  parentCategoryId,
  changeCategory,
  changeSubCategory,
  onActionChange,
  isEditMode = false,
  showAddButton = true,
  userId: propUserId,
}) => {
  const { userId: contextUserId } = useUser();
  // propsで渡されたuserIdを優先し、なければcontextから取得
  const userId = propUserId || contextUserId;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [subCategories, setSubCategories] = useState<SubCategory[]>([]);
  const [newCategory, setNewCategory] = useState("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editedCategory, setEditedCategory] = useState("");
  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastSelectedSubCategoryRef = useRef<string | null>(null);
  const [initialised, setInitialised] = useState(false);
  
  // 「追加」ボタンのアクティブ状態を管理
  const isAddButtonActive = newCategory.trim() !== "";
  

  
  // ⯈ サブカテゴリの取得
  useEffect(() => {

    const fetchSubCategories = async () => {
      // ユーザーIDがない場合は処理を中止
      if (!userId) {
        return;
      }

      // サブカテゴリ取得開始
      
      setIsLoading(true);
      setError(null);
      
      try {
        // 親カテゴリIDがあれば、そのカテゴリに紐づくサブカテゴリを取得
        // なければ、すべてのサブカテゴリを取得
        let apiUrl = '';
        if (parentCategoryId) {
          apiUrl = `/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`;
        } else {
          apiUrl = `/api/getAllSubCategories?user_ID=${userId}`;
        }
        
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
          throw new Error('サブカテゴリの取得に失敗しました');
        }
        
        const data = await response.json();
        
        // サブカテゴリをセット
        setSubCategories(data);
        
        // カテゴリ名のリストを更新
        // 新しいサブカテゴリが下に表示されるようにする
        const categoryNames = data.map((item: SubCategory) => item.category_name);
        
        // サブカテゴリをセット（アカウント登録時に「未分類」が自動作成されるため、空の場合の処理は不要）
        setCategories(categoryNames);
        setInitialised(true);
      } catch (err) {
        console.error('サブカテゴリ取得エラー:', err);
        setError(err instanceof Error ? err.message : '不明なエラー');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSubCategories();
  }, [userId, parentCategoryId, subCategory]);

  // サブカテゴリの選択処理のための状態
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState<string | null>(null);

  // サブカテゴリの選択状態を監視し、必要に応じて親コンポーネントに通知
  useEffect(() => {
    if (selectedSubCategoryId) {
      // 変更イベントを作成
      const syntheticEvent = {
        target: { value: selectedSubCategoryId }
      } as React.ChangeEvent<HTMLInputElement>;

      // 親コンポーネントに通知（次のレンダリングサイクルで実行）
      Promise.resolve().then(() => {
        changeSubCategory(syntheticEvent);
      });
    }
  }, [selectedSubCategoryId, changeSubCategory]);

  // サブカテゴリの選択処理
  useEffect(() => {
    // 初期化されていない場合は処理しない
    if (!initialised || !subCategories.length) {
      return;
    }

    // 前回選択したサブカテゴリと同じ場合は処理をスキップ（無限ループ防止）
    if (lastSelectedSubCategoryRef.current === subCategory) {
      return;
    }
    
    // サブカテゴリの選択ロジック
    let newSelectedId: string | null = null;
    
    // subCategoryが指定されている場合は、そのサブカテゴリを選択する
    if (subCategory) {
      
      // まず、IDで一致するサブカテゴリを探す
      let matchingSubCategory = subCategories.find((item: SubCategory) => item.id === subCategory);
      
      // IDで見つからない場合、名前で一致するサブカテゴリを探す
      if (!matchingSubCategory) {
        matchingSubCategory = subCategories.find((item: SubCategory) => item.category_name === subCategory);
      }
      
      if (matchingSubCategory) {
        // 前回選択したサブカテゴリを記録
        lastSelectedSubCategoryRef.current = matchingSubCategory.id;
        newSelectedId = matchingSubCategory.id;
      } else if (subCategories.length > 0) {

        // 前回選択したサブカテゴリを記録
        lastSelectedSubCategoryRef.current = subCategories[0].id;
        newSelectedId = subCategories[0].id;
      }
    } else if (subCategories.length > 0 && !lastSelectedSubCategoryRef.current) {
      // subCategoryが指定されておらず、まだ何も選択されていない場合のみ最初のサブカテゴリを選択

      // 前回選択したサブカテゴリを記録
      lastSelectedSubCategoryRef.current = subCategories[0].id;
      newSelectedId = subCategories[0].id;
    }
    
    // 選択されたサブカテゴリIDを状態に保存（別のuseEffectがこれを検知して親コンポーネントに通知）
    if (newSelectedId) {
      setSelectedSubCategoryId(newSelectedId);
    }
  }, [initialised, subCategories, subCategory]);

  // ▼ カテゴリを追加
  const handleAddCategory = async () => {
    if (newCategory.trim() === "") return;

    // 重複チェック: 同じ名前のサブカテゴリが既に存在するか確認
    const isDuplicate = categories.some(cat => cat.toLowerCase() === newCategory.trim().toLowerCase());
    if (isDuplicate) {
      setError("同じ名前のサブカテゴリが既に存在します");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // データベースにサブカテゴリを保存
      if (userId && parentCategoryId) {
        const newSubcategoryId = `new_subcategory_${Date.now()}`;
        
        // 同じparent_IDの中で一番大きいorderの値を取得
        let nextOrder = 1; // デフォルト値は1
        
        try {
          // 同じparent_IDのサブカテゴリの最大order値を取得
          const orderResponse = await fetch(`/api/getMaxOrder?parent_ID=${parentCategoryId}`);
          if (orderResponse.ok) {
            const orderData = await orderResponse.json();
            if (orderData && orderData.maxOrder) {
              nextOrder = orderData.maxOrder + 1;
            }
          }
        } catch (err) {
          console.error('order値の取得エラー:', err);
          // エラーが発生しても処理を続行するため、デフォルト値を使用
        }
        
        const requestData = {
          id: newSubcategoryId, // 一時的なIDを生成（必須パラメータ）
          category_name: newCategory,
          user_ID: userId,
          parent_ID: parentCategoryId,
          order: nextOrder, // order値を設定
        };

        // updateCategoryエンドポイントを使用
        const response = await fetch('/api/updateCategory', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'サブカテゴリの保存に失敗しました');
        }

        await response.json();

        // サブカテゴリリストを再取得
        const fetchResponse = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`);
        if (fetchResponse.ok) {
          const data = await fetchResponse.json();
          setSubCategories(data);
          
          // カテゴリ名のリストを更新
          // 新しいサブカテゴリが下に表示されるようにする
          const categoryNames = data.map((item: SubCategory) => item.category_name);
          setCategories(categoryNames);
          
          // 親コンポーネントにカテゴリ追加を通知
          if (onActionChange) {
            onActionChange('add', null, newCategory);
          }
        } else {
          // UIを更新（APIが失敗した場合のフォールバック）
          // 新しいサブカテゴリを配列の末尾に追加
          setCategories((prev) => [...prev, newCategory]);
        }
      } else {
        // UIを更新
        // 新しいサブカテゴリを配列の末尾に追加
        setCategories((prev) => [...prev, newCategory]);
      }
      
      // 追加したカテゴリを選択状態にする処理を削除
      // モーダルを閉じる処理を削除
      
      setNewCategory("");
      // setIsModalOpen(false); // モーダルを閉じないように変更
    } catch (err: any) {
      console.error('サブカテゴリの保存エラー:', err);
      setError(err.message || 'サブカテゴリの保存中にエラーが発生しました');
    } finally {
      setIsLoading(false);
    }
  };

  // ▼ カテゴリを編集
  const handleEditCategory = (idx: number, cat: string) => {
    setEditingIndex(idx);
    setEditedCategory(cat);
  };

  // ▼ カテゴリ編集を保存
  const handleSaveEdit = async () => {
    if (editingIndex === null || editedCategory.trim() === "") return;
    
    // 重複チェック: 同じ名前のサブカテゴリが既に存在するか確認（自分自身は除く）
    const isDuplicate = categories.some((cat, idx) => 
      idx !== editingIndex && cat.toLowerCase() === editedCategory.trim().toLowerCase()
    );
    
    if (isDuplicate) {
      setError("同じ名前のサブカテゴリが既に存在します");
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const oldCategoryName = categories[editingIndex];
      
      // データベースのカテゴリ名を更新
      if (userId && parentCategoryId) {
        // 対応するサブカテゴリIDを取得
        const targetSubCategory = subCategories.find(sc => sc.category_name === oldCategoryName);
        
        if (targetSubCategory) {
          
          // 既存のorder値を維持する
          const order = targetSubCategory.order || 1; // orderがない場合はデフォルト値を1に設定
          
          const response = await fetch('/api/updateCategory', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              id: targetSubCategory.id,
              category_name: editedCategory,
              user_ID: userId,
              parent_ID: parentCategoryId,
              order: order, // 既存のorder値を維持
            }),
          });
          
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'サブカテゴリの更新に失敗しました');
          }
          
          // サブカテゴリリストを再取得
          const fetchResponse = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`);
          if (fetchResponse.ok) {
            const data = await fetchResponse.json();
            setSubCategories(data);
            
            // カテゴリ名のリストを更新
            // 新しいサブカテゴリが下に表示されるようにする
            const categoryNames = data.map((item: SubCategory) => item.category_name);
            setCategories(categoryNames);
            
            // 親コンポーネントにカテゴリ編集を通知
            if (onActionChange) {
              onActionChange('edit', targetSubCategory.id, editedCategory);
            }
          } else {
            // UIのみ更新（APIが失敗した場合のフォールバック）
            const newCategories = [...categories];
            newCategories[editingIndex] = editedCategory;
            setCategories(newCategories);
          }
        } else {
          // 対応するサブカテゴリが見つからない場合はUIのみ更新
          const newCategories = [...categories];
          newCategories[editingIndex] = editedCategory;
          setCategories(newCategories);
        }
      } else {
        // UIのみ更新
        const newCategories = [...categories];
        newCategories[editingIndex] = editedCategory;
        setCategories(newCategories);
      }
      
      // 編集モードを解除
      setEditingIndex(null);
      setEditedCategory("");
    } catch (err) {
      console.error('サブカテゴリの更新エラー:', err);
      setError(err instanceof Error ? err.message : '不明なエラー');
    } finally {
      setIsLoading(false);
    }
  };

  // ▼ カテゴリを削除
  const handleDeleteCategory = (cat: string) => {
    setDeleteTarget(cat);
    setIsDeleteModalOpen(true);
  };

  // ▼ カテゴリ削除を確定
  const confirmDeleteCategory = async () => {
    if (!deleteTarget) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // データベースからカテゴリを削除
      if (userId && parentCategoryId) {
        // 対応するサブカテゴリIDを取得
        const targetSubCategory = subCategories.find(sc => sc.category_name === deleteTarget);
        
        if (targetSubCategory) {
          
          const response = await fetch('/api/deleteCategory', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              id: targetSubCategory.id,
              user_ID: userId,
            }),
          });
          
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'サブカテゴリの削除に失敗しました');
          }
          
          // サブカテゴリリストを再取得
          const fetchResponse = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentCategoryId}`);
          if (fetchResponse.ok) {
            const data = await fetchResponse.json();
            setSubCategories(data);
            
            // カテゴリ名のリストを更新
            // 新しいサブカテゴリが下に表示されるようにする
            const categoryNames = data.map((item: SubCategory) => item.category_name);
            setCategories(categoryNames);
            
            // 親コンポーネントにカテゴリ削除を通知
            if (onActionChange) {
              onActionChange('delete', targetSubCategory.id, deleteTarget);
            }
          } else {
            // APIが失敗した場合のフォールバック
            setCategories((prev) => prev.filter((cat) => cat !== deleteTarget));
          }
        } else {
          // 親カテゴリIDがない場合はUIのみ更新
          setCategories((prev) => prev.filter((cat) => cat !== deleteTarget));
        }
      } else {
        // APIが利用できない場合はUIのみ更新
        setCategories((prev) => prev.filter((cat) => cat !== deleteTarget));
      }
      
      setIsDeleteModalOpen(false);
      setDeleteTarget(null);
    } catch (err) {
      console.error('サブカテゴリの削除エラー:', err);
      setError(err instanceof Error ? err.message : '不明なエラー');
    } finally {
      setIsLoading(false);
    }
  };

  // 表示用のサブカテゴリ名を管理する状態
  const [displaySubCategoryName, setDisplaySubCategoryName] = useState("");

  // サブカテゴリ名の表示を更新するuseEffect
  useEffect(() => {
    if (!subCategory) {
      setDisplaySubCategoryName("");
      return;
    }

    // まず、IDで一致するサブカテゴリを探す
    let selectedSubCategory = subCategories.find(sc => sc.id === subCategory);

    // IDで見つからない場合、名前で一致するサブカテゴリを探す
    if (!selectedSubCategory) {
      selectedSubCategory = subCategories.find(sc => sc.category_name === subCategory);

      // 名前で一致するサブカテゴリが見つかった場合、そのIDを設定する（レンダリング外で実行）
      if (selectedSubCategory) {
        Promise.resolve().then(() => {
          const event = {
            target: { value: selectedSubCategory.id }
          } as React.ChangeEvent<HTMLInputElement>;
          changeSubCategory(event);
        });
      }
    }

    // 表示名を設定
    setDisplaySubCategoryName(selectedSubCategory ? selectedSubCategory.category_name : "");
  }, [subCategory, subCategories, changeSubCategory]);
  
  return (
    <div className={className}>
      <div className="mb-4">
        <div className="flex items-center px-4">
          <input
            type="text"
            value={displaySubCategoryName} /* サブカテゴリIDではなく名前を表示 */
            onChange={changeSubCategory}
            className="appearance-none border w-full bg-gray-100 py-4 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="サブカテゴリを選択"
            onClick={() => setIsModalOpen(true)}
            readOnly
          />
          <div className="absolute right-4 pointer-events-none pr-[10px]">
            <svg
              className="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      {/* モーダル */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
          onClick={() => {
            // 編集中の場合、モーダルの外側をクリックしたときに編集モードを解除
            if (editingIndex !== null) {
              setEditingIndex(null);
              setEditedCategory("");
            }
            setIsModalOpen(false);
          }}
        >
          <div 
            className="bg-white rounded-lg w-[90%] max-w-[380px] p-4 shadow-lg relative"
            onClick={(e: React.MouseEvent) => {
              // イベントの伝播を停止して親要素のクリックイベントが発火しないようにする
              e.stopPropagation();
              
              // 編集中の場合、モーダル内をクリックしたときに編集モードを解除
              // ただし、入力フィールドや保存ボタンのクリックは除外する
              if (editingIndex !== null && 
                  !(e.target as HTMLElement).closest('input') && 
                  !(e.target as HTMLElement).closest('button[type="button"][class*="bg-[#E63B5F]"]')) {
                setEditingIndex(null);
                setEditedCategory("");
              }
            }}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">サブカテゴリを選択</h3>
              <button
                type="button"
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>

            {/* サブカテゴリリスト - 新しいサブカテゴリが下に表示されるようにする */}
            <div className="mb-4 max-h-[200px] overflow-y-auto">
              {/* サブカテゴリデータをログ出力して確認 */}
              {subCategories.length > 0 && (() => {
                return null;
              })()}
              
              
              
              {/* サブカテゴリリストを表示 */}
              {subCategories.map((subCat, idx) => (
                <div
                  key={idx}
                  className="flex justify-between items-center border-b border-gray-100 py-2"
                >
                  {editingIndex === idx ? (
                    <div className="flex w-full">
                      <input
                        type="text"
                        value={editedCategory}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditedCategory(e.target.value)}
                        className="flex-1 bg-[#F6F7F8] px-3 py-2 text-sm outline-none"
                        onClick={(e: React.MouseEvent) => e.stopPropagation()} // クリックイベントの伝播を停止
                      />
                      <button
                        type="button"
                        onClick={handleSaveEdit}
                        className="px-2 py-1 text-sm text-white bg-[#E63B5F]"
                      >
                        保存
                      </button>
                    </div>
                  ) : (
                    <>
                      <button
                        type="button"
                        onClick={() => {
                          // サブカテゴリIDを設定
                          const syntheticEvent = {
                            target: { value: subCat.id },
                          } as unknown as React.ChangeEvent<HTMLInputElement>;
                          changeSubCategory(syntheticEvent);
                          setIsModalOpen(false);
                        }}
                        className="flex-1 text-left text-sm"
                      >
                        {subCat.category_name}
                      </button>
                      
                      <div className="flex">
                        <button
                          type="button"
                          onClick={() => handleEditCategory(idx, subCat.category_name)}
                          className="px-2 py-1 text-sm text-gray-600 border border-gray-300 mr-3"
                        >
                          編集
                        </button>
                        <button
                          type="button"
                          onClick={() => handleDeleteCategory(subCat.category_name)}
                          className="px-2 py-1 text-sm text-white bg-[#1f2937]"
                        >
                          削除
                        </button>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>

            {/* 新しくカテゴリを追加するエリア */}
            <div className="mt-0">
              <p className="text-xs text-gray-400 mt-4 mb-16">
                ※サブカテゴリは自由に「追加・変更・削除」できます
              </p>

              {error && (
                <p className="text-red-500 text-xs mb-2">{error}</p>
              )}

              <div className="flex items-center">
                <input
                  type="text"
                  placeholder="新しくサブカテゴリを追加する"
                  value={newCategory}
                  onFocus={() => {
                    // ① タップした時点で編集モード解除
                    setEditingIndex(null);
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewCategory(e.target.value)}
                  className="flex-1 bg-[#F6F7F8] px-3 py-3 text-sm outline-none"
                />
                <button
                  type="button"
                  onClick={handleAddCategory}
                  className={`px-3 py-3 text-sm text-white ${
                    isAddButtonActive && !isLoading ? "bg-[#E63B5F]" : "bg-gray-300"
                  }`}
                  disabled={!isAddButtonActive || isLoading}
                >
                  {isLoading ? "保存中..." : "追加"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 削除確認モーダル */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg w-[90%] max-w-[380px] p-4 shadow-lg">
            <h3 className="text-lg font-bold mb-4">サブカテゴリの削除</h3>
            <p className="mb-6">
              「{deleteTarget}」を削除してもよろしいですか？
            </p>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                disabled={isLoading}
                className={`px-4 py-2 text-sm text-gray-600 border border-gray-300 mr-3 ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                }`}
              >
                キャンセル
              </button>
              <button
                type="button"
                onClick={confirmDeleteCategory}
                className={`px-4 py-2 text-sm text-white flex items-center justify-center min-w-[80px] ${
                  isLoading
                    ? 'bg-red-400 cursor-not-allowed'
                    : 'bg-red-500 hover:bg-red-600'
                }`}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    削除中...
                  </div>
                ) : (
                  '削除する'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategorySelect;
