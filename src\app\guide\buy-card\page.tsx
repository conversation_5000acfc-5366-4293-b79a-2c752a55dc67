'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function BuyCardGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">NFCカードを購入する方法</h1>
        </div>
        
        <div className="mt-8 space-y-6">
          <div>
            <h2 className="text-[16px] font-bold text-[#313131] mb-3">coming soon</h2>
          </div>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
