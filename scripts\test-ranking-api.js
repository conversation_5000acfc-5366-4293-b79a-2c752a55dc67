const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testRankingAPI() {
  console.log('Testing ranking API and database connection...');
  
  try {
    // 1. データベース接続テスト
    console.log('\n1. Testing database connection...');
    const userCount = await prisma.user.count();
    console.log(`✅ Database connected. Total users: ${userCount}`);

    // 2. 既存のランキングを確認
    console.log('\n2. Checking existing rankings...');
    const existingRankings = await prisma.ranking.findMany({
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        user_ID: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        created_at: true
      },
      orderBy: {
        created_at: 'desc'
      },
      take: 5
    });
    
    console.log(`Found ${existingRankings.length} existing rankings:`);
    existingRankings.forEach(ranking => {
      console.log(`- ${ranking.ranking_title} (ID: ${ranking.ranking_ID})`);
      console.log(`  URLs: Amazon=${ranking.amazon_url || 'null'}, Rakuten=${ranking.rakuten_url || 'null'}`);
    });

    // 3. ユーザー情報を確認
    console.log('\n3. Checking user information...');
    const users = await prisma.user.findMany({
      select: {
        user_ID: true,
        domain: true,
        name: true
      },
      take: 3
    });
    
    console.log('Available users:');
    users.forEach(user => {
      console.log(`- ${user.name} (ID: ${user.user_ID}, Domain: ${user.domain})`);
    });

    // 4. カテゴリ情報を確認
    console.log('\n4. Checking category information...');
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        category_ID: true,
        category_name: true,
        user_ID: true,
        parent_ID: true
      },
      take: 5
    });
    
    console.log('Available categories:');
    categories.forEach(category => {
      console.log(`- ${category.category_name} (ID: ${category.id}, User: ${category.user_ID})`);
    });

    // 5. テスト用ランキングデータを作成してみる
    console.log('\n5. Testing ranking creation...');
    
    if (users.length > 0 && categories.length > 0) {
      const testUser = users[0];
      const testCategory = categories.find(cat => cat.user_ID === testUser.user_ID) || categories[0];
      
      const testRankingData = {
        ranking_ID: `test_rank_${Date.now()}`,
        user_ID: testUser.user_ID,
        domain: testUser.domain,
        ranking_title: 'テストランキング',
        ranking_description: 'APIテスト用のランキングです',
        amazon_url: 'https://amazon.co.jp/test',
        rakuten_url: 'https://rakuten.co.jp/test',
        yahoo_url: null,
        qoo10_url: null,
        official_url: 'https://example.com/test',
        recommend_rate: 5,
        thumbnail_image: ['https://example.com/test.jpg'],
        subCategory_ID: testCategory.id,
        order: 1
      };

      console.log('Creating test ranking with data:', testRankingData);
      
      const createdRanking = await prisma.ranking.create({
        data: testRankingData
      });
      
      console.log('✅ Test ranking created successfully:', createdRanking.ranking_ID);
      
      // 作成したテストランキングを削除
      await prisma.ranking.delete({
        where: { id: createdRanking.id }
      });
      
      console.log('✅ Test ranking deleted successfully');
    } else {
      console.log('⚠️  No users or categories found for testing');
    }

  } catch (error) {
    console.error('❌ Error during testing:', error);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.meta) {
      console.error('Error meta:', error.meta);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
testRankingAPI();
