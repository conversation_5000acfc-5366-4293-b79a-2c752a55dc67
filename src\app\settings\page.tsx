"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";
import { HeaderItems } from "@/components/HeaderItems";

export default function Settings() {
  const router = useRouter();

  // 各セクションへの遷移処理
  const handleNavigation = (path: string) => {
    router.push(path);
  };

  return (
    <div className="max-w-[500px] mx-auto flex flex-col min-h-screen relative bg-white">
      <HeaderItems text="設定" />
      
      {/* メインコンテンツ */}
      <main className="w-full flex-grow">
        {/* アカウント設定セクション */}
        <div className="bg-white">
          <div 
            className="flex justify-between items-center p-4 border-b border-[var(--line-color)] hover:bg-[rgba(246,247,248,0.5)] active:bg-[rgba(246,247,248,0.8)] cursor-pointer transition-colors duration-200"
            onClick={() => handleNavigation('/change-email')}
            role="button"
            aria-label="メールアドレス変更へ移動"
          >
            <h2 className="text-[14px] text-[#313131] font-medium">メールアドレス変更</h2>
            <span className="text-[#AAAAAA]">＞</span>
          </div>
          <div 
            className="flex justify-between items-center p-4 border-b border-[var(--line-color)] hover:bg-[rgba(246,247,248,0.5)] active:bg-[rgba(246,247,248,0.8)] cursor-pointer transition-colors duration-200"
            onClick={() => handleNavigation('/change-password')}
            role="button"
            aria-label="パスワード変更へ移動"
          >
            <h2 className="text-[14px] text-[#313131] font-medium">パスワード変更</h2>
            <span className="text-[#AAAAAA]">＞</span>
          </div>
        </div>

        {/* 利用規約セクション */}
        <div className="bg-white">
          <div 
            className="flex justify-between items-center p-4 border-b border-[var(--line-color)] hover:bg-[rgba(246,247,248,0.5)] active:bg-[rgba(246,247,248,0.8)] cursor-pointer transition-colors duration-200"
            onClick={() => handleNavigation('/terms')}
            role="button"
            aria-label="利用規約へ移動"
          >
            <h2 className="text-[14px] text-[#313131] font-medium">利用規約</h2>
            <span className="text-[#AAAAAA]">＞</span>
          </div>
        </div>

        {/* プライバシーポリシーセクション */}
        <div className="bg-white">
          <div 
            className="flex justify-between items-center p-4 border-b border-[var(--line-color)] hover:bg-[rgba(246,247,248,0.5)] active:bg-[rgba(246,247,248,0.8)] cursor-pointer transition-colors duration-200"
            onClick={() => handleNavigation('/privacy')}
            role="button"
            aria-label="プライバシーポリシーへ移動"
          >
            <h2 className="text-[14px] text-[#313131] font-medium">プライバシーポリシー</h2>
            <span className="text-[#AAAAAA]">＞</span>
          </div>
        </div>

        {/* 特定商取引法に基づく表記セクション */}
        <div className="bg-white">
          <div 
            className="flex justify-between items-center p-4 border-b border-[var(--line-color)] hover:bg-[rgba(246,247,248,0.5)] active:bg-[rgba(246,247,248,0.8)] cursor-pointer transition-colors duration-200"
            onClick={() => handleNavigation('/legal')}
            role="button"
            aria-label="特定商取引法に基づく表記へ移動"
          >
            <h2 className="text-[14px] text-[#313131] font-medium">特定商取引法に基づく表記</h2>
            <span className="text-[#AAAAAA]">＞</span>
          </div>
        </div>

        {/* 退会 */}
        <div className="bg-white mb-4">
          <div 
            className="flex justify-between items-center p-4 border-b border-[var(--line-color)] hover:bg-[rgba(246,247,248,0.5)] active:bg-[rgba(246,247,248,0.8)] cursor-pointer transition-colors duration-200"
            onClick={() => handleNavigation('/withdrawal1')}
            role="button"
            aria-label="退会へ移動"
          >
            <h2 className="text-[14px] text-[#313131] font-medium">退会</h2>
            <span className="text-[#AAAAAA]">＞</span>
          </div>
        </div>

        {/* ログアウトボタン */}
        <div className="px-4 mt-14 mb-8 flex justify-center">
          <button 
            className="w-full max-w-xs bg-[#313131] hover:bg-[#000000] active:bg-[#000000] text-white font-medium py-3 transition-colors duration-200 rounded-full"
            onClick={() => {
              // ログアウト処理
              // 例: ログアウトAPIを呼び出す
              // logout();
              // ログイン画面に遷移
              router.push('/sign-in');
            }}
          >
            ログアウト
          </button>
        </div>

      </main>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
