import { Metadata } from "next";

// ユーザー情報を取得する関数
async function getUserInfo(username: string) {
  try {
    // APIエンドポイントからユーザー情報を取得（ローカル開発環境用）
    const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : (process.env.NEXT_PUBLIC_API_URL || 'https://mypicks.best');
    const apiUrl = `${baseUrl}/api/getUserByUsername?username=${username}`;

    const response = await fetch(apiUrl, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return null;
    }

    const userData = await response.json();
    return userData;
  } catch (error) {
    return null;
  }
}

// メタディスクリプションを生成する関数
function generateMetaDescription(userInfo: any, userName: string): string {
  const selfIntroduction = userInfo?.self_introduction?.trim();


  // 自己紹介文がある場合はそのまま使用（160文字制限）
  if (selfIntroduction && selfIntroduction.length > 0) {
    const result = selfIntroduction.length > 160
      ? selfIntroduction.substring(0, 157) + '...'
      : selfIntroduction;
    console.log('Using self introduction:', result);
    return result;
  }

  // 自己紹介文がない場合はデフォルト説明文を使用
  const result = `${userName}さんのおすすめ商品やレビューをチェック。mypicks.bestならみんなのおすすめアイテムが見つかる！。`;
  return result;
}

// キーワードを動的生成する関数
function generateKeywords(userInfo: any, userName: string, username: string): string {
  const baseKeywords = [userName, username];

  // カテゴリからキーワードを追加
  if (userInfo?.categories) {
    const categoryKeywords = userInfo.categories.map((cat: any) => cat.name);
    baseKeywords.push(...categoryKeywords);
  }

  return [...new Set(baseKeywords)].join(', ');
}

// メタデータを返す関数
export async function generateMetadata({ params }: { params: { username: string } }): Promise<Metadata> {
  // ユーザー名を安全に取得
  const username = params?.username || "";

  // ユーザー情報を取得
  const userInfo = await getUserInfo(username);

  // ユーザー名（存在しない場合はユーザー名をそのまま使用）
  const userName = userInfo?.name || username;

  // 動的メタディスクリプションを生成
  const metaDescription = generateMetaDescription(userInfo, userName);

  // 動的キーワードを生成
  const metaKeywords = generateKeywords(userInfo, userName, username);

  // プロフィール画像URL
  const profileImageUrl = userInfo?.profile_image || '/static/img/default-avatar.png';

  // サイトURL
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://mypicks.best';
  const pageUrl = `${siteUrl}/${username}`;

  // メタデータを返す
  return {
    title: `${userName}のおすすめ`,
    description: metaDescription,
    keywords: metaKeywords,
    openGraph: {
      title: `${userName}のおすすめ｜mypicks.best`,
      description: metaDescription,
      url: pageUrl,
      siteName: 'mypicks.best',
      images: [
        {
          url: profileImageUrl,
          width: 1200,
          height: 630,
          alt: `${userName}のプロフィール画像`,
        },
      ],
      locale: 'ja_JP',
      type: 'profile',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${userName}のおすすめ｜mypicks.best`,
      description: metaDescription,
      images: [profileImageUrl],
    },
    alternates: {
      canonical: pageUrl,
    },
    viewport: 'width=device-width, initial-scale=1',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    }
  };
}

export default async function UsernamePageLayout({
  children,
  params,
}: {
  children: React.ReactNode
  params: { username: string }
}) {
  // パラメータを取得
  const username = params?.username || "";

  // ユーザー情報を取得（構造化データ用）
  const userInfo = await getUserInfo(username);
  const userName = userInfo?.name || username;
  const metaDescription = generateMetaDescription(userInfo, userName);
  const profileImageUrl = userInfo?.profile_image || '/static/img/profile-default.png';
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://mypicks.best';
  const pageUrl = `${siteUrl}/${username}`;
  const hasRankings = userInfo?.rankings && userInfo.rankings.length > 0;
  const topCategories = userInfo?.categories?.slice(0, 3)?.map((cat: any) => cat.name) || [];

  // 構造化データを生成
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ProfilePage",
    "mainEntity": {
      "@type": "Person",
      "name": userName,
      "description": metaDescription,
      "image": profileImageUrl,
      "url": pageUrl,
      "sameAs": userInfo?.sns_links ? Object.values(userInfo.sns_links).filter(Boolean) : []
    },
    "about": hasRankings ? {
      "@type": "ItemList",
      "name": `${userName}のおすすめ商品ランキング`,
      "description": metaDescription,
      "url": pageUrl,
      "numberOfItems": userInfo.rankings?.length || 0,
      "itemListElement": topCategories.map((category: string, index: number) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": `${category}のおすすめ`
      }))
    } : {
      "@type": "ItemList",
      "name": `${userName}のおすすめ商品`,
      "description": metaDescription,
      "url": pageUrl
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "ホーム",
          "item": siteUrl
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": `${userName}のおすすめ`,
          "item": pageUrl
        }
      ]
    },
    "publisher": {
      "@type": "Organization",
      "name": "mypicks.best",
      "url": siteUrl
    }
  };

  return (
    <>
      {/* リソースヒント - パフォーマンス最適化 */}
      <link rel="dns-prefetch" href="//storage.cloud.google.com" />
      <link rel="dns-prefetch" href="//www.instagram.com" />
      <link rel="dns-prefetch" href="//platform.twitter.com" />
      <link rel="dns-prefetch" href="//www.tiktok.com" />
      <link rel="preconnect" href="//storage.cloud.google.com" crossOrigin="anonymous" />

      <script
        id="structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      {children}
    </>
  );
}
