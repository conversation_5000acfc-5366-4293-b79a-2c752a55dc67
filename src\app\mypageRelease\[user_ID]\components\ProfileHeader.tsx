import React from "react";
import { ImageUserFrame } from "@/components/ImageUserFrame";
import { TextUserName } from "@/components/TextUserName";
import { TextUserUrlOpen } from "@/components/TextUserUrlOpen";
import { ButtonProfileEdit } from "@/components/ButtonProfileEdit";
import type { UserInfo } from "../types";

interface ProfileHeaderProps {
  user: UserInfo | null;
  pageUserId: string;
  domain: string;
  isOwnPage: boolean;
  onUrlClick: () => void;
}

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  user,
  pageUserId,
  domain,
  isOwnPage,
  onUrlClick,
}) => {
  return (
    <>
      {/* ---------------- ヘッダー ---------------- */}
      <div className="flex flex-col items-center pt-4 pb-6 w-full">
        <ImageUserFrame profile_image={user?.profile_image ?? ""} />
      </div>
      <TextUserName title="" pageUserId={pageUserId} />
      <TextUserUrlOpen
        className="!flex-[0_0_auto]"
        domain={domain}
        onClick={onUrlClick}
        useButtonProfileEdit={false}
      />

      {isOwnPage && (
        <ButtonProfileEdit
          className="!flex-[0_0_auto]"
          useButtonProfileEdit={true}
          userId={domain}
        />
      )}
    </>
  );
};