# 下書き保存機能

## 概要

下書き保存機能は、ユーザーがランキング作成途中で内容を一時的に保存し、後で編集を再開できるようにする機能です。下書きは専用のページで一覧表示され、管理することができます。

## 機能一覧

- ランキングの下書き保存
- 下書き一覧の表示
- 下書きの編集
- 下書きの削除

## ランキング下書き保存機能

### 機能概要

ユーザーはランキング作成ページで「下書き保存」ボタンをクリックすることで、現在の入力内容を下書きとして保存できます。

### 実装の詳細

下書き保存機能は以下のように実装されています：

1. ランキング作成ページ（rankingRegister）で「下書き保存」ボタンをクリックすると、現在の入力内容（タイトル、説明、画像など）がローカルストレージに保存される
2. 「下書き一覧」ボタンをクリックすると、下書き一覧ページ（draftList）に遷移する
3. 下書き一覧ページでは、保存された下書きのタイトルとサムネイル画像が表示される
4. 下書きをクリックすると、将来的には編集ページに遷移する予定（現在は仮実装）

### 実装上のポイント

- 下書きデータはローカルストレージに保存されています
- 将来的にはサーバーサイドでの保存に変更することも検討されています
- Next.jsの`<Image />`コンポーネントを使用して画像の最適化を実現しています
- サムネイルが設定されていない場合は「no image」の画像を表示します

### コード例

```javascript
// 下書きの保存
const saveDraft = () => {
  // 現在の入力内容を取得
  const draftData = {
    id: `draft_${Date.now()}`,
    title: title || 'タイトルなし',
    description: description || '',
    thumbnail: thumbnail || '',
    category: category || '',
    subCategory: subCategory || '',
    items: items || [],
    createdAt: new Date().toISOString()
  };
  
  // ローカルストレージから既存の下書きを取得
  const drafts = JSON.parse(localStorage.getItem('rankingDrafts') || '[]');
  
  // 新しい下書きを追加
  drafts.push(draftData);
  
  // ローカルストレージに保存
  localStorage.setItem('rankingDrafts', JSON.stringify(drafts));
  
  // 保存完了メッセージ
  alert('下書きを保存しました');
};
```

## 下書き一覧表示機能

### 機能概要

ユーザーは保存した下書きを一覧で表示できます。各下書きにはタイトルとサムネイル画像が表示されます。

### 実装の詳細

下書き一覧表示機能は以下のように実装されています：

1. draftListページに下書きデータの表示機能を実装
2. useEffectを使用してページ読み込み時に下書きデータを取得
3. 各下書きのタイトルとサムネイル画像を表示
4. 下書きがない場合は「下書きがありません」と表示

### UI改善

下書きリスト画面のUIが改善され、以下の変更が行われました：

- 「no image」テキストを中央寄せに修正
- 矢印アイコンを削除
- 下書きがない場合は「下書きがありません」と表示

## 下書きの編集・削除機能

### 機能概要

ユーザーは下書きを編集モードで管理し、不要な下書きを削除できます。

### 実装の詳細

編集・削除機能は以下のように実装されています：

1. 右下の「編集」ボタンをクリックすると編集モードに切り替わる
2. 編集モード中は各下書き項目の右側に「削除」ボタンが表示される
3. 編集モード中は「編集」ボタンが「完了」に変わる
4. 下書きがない場合は編集ボタンを非表示

### 削除機能の実装

削除機能は以下のように実装されています：

1. 編集モード中に「削除」ボタンをクリックすると確認モーダルが表示される
2. モーダルには「削除しますか？」というメッセージと「キャンセルする」「削除する」ボタンがある
3. 「削除する」をクリックすると選択した下書きがローカルストレージから削除される
4. 削除後、下書きリストが更新される

### コード例

```javascript
// 下書きの削除
const deleteDraft = (draftId) => {
  // 確認モーダルを表示
  setDeleteTargetId(draftId);
  setShowDeleteModal(true);
};

// 削除の実行
const confirmDelete = () => {
  if (!deleteTargetId) return;
  
  // ローカルストレージから下書きを取得
  const drafts = JSON.parse(localStorage.getItem('rankingDrafts') || '[]');
  
  // 指定されたIDの下書きを除外
  const updatedDrafts = drafts.filter(draft => draft.id !== deleteTargetId);
  
  // ローカルストレージに更新後の下書きリストを保存
  localStorage.setItem('rankingDrafts', JSON.stringify(updatedDrafts));
  
  // 状態を更新
  setDrafts(updatedDrafts);
  setShowDeleteModal(false);
  setDeleteTargetId(null);
};
```

## 下書きの編集再開

### 機能概要

ユーザーは下書き一覧から下書きを選択して、編集を再開できます。

### 実装の詳細

編集再開機能は以下のように実装される予定です：

1. 下書き一覧ページで下書きをクリックすると、ランキング作成ページに遷移
2. 選択した下書きのデータがフォームに読み込まれる
3. ユーザーは編集を続行し、完成したら正式に登録するか、再度下書き保存できる

## 関連コンポーネント

- [RankingRegisterButtonFooter](../components/buttons.md#rankingregisterbuttonfooter)

## 最終更新日

2025年4月25日
