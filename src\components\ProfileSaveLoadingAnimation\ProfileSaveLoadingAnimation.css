.profile-save-loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fade-in 0.3s ease-in-out;
}

.profile-save-loading-content {
  background-color: #FFFCE1;
  border-radius: 12px;
  padding: 24px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
  border: 1px solid #FFD700;
}

.profile-save-icon {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
}

.save-icon {
  width: 48px;
  height: 48px;
  color: #FFD700;
  animation: pulse 1.5s infinite;
}

.check-icon {
  width: 48px;
  height: 48px;
  color: #4CAF50;
  animation: check-appear 0.5s ease-in-out;
}

.profile-save-message {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.profile-save-meter-container {
  width: 100%;
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  margin-bottom: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.profile-save-meter-fill {
  height: 100%;
  background: linear-gradient(to right, #FFD700, #FFA500);
  border-radius: 12px;
  transition: width 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.profile-save-meter-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  animation: shine 2s infinite;
}

.profile-save-meter-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: bold;
  color: #333;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
}

/* アニメーション定義 */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes check-appear {
  from { 
    transform: scale(0); 
    opacity: 0;
  }
  to { 
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
