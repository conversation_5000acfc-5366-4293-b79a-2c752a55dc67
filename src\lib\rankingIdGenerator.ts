import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 新しいranking_IDを生成する関数
 * "001", "002", "003"... の形式で連番を生成（3桁ゼロパディング）
 */
export async function generateRankingId(): Promise<string> {
  try {
    // 既存のranking_IDから最大の番号を取得
    // PostgreSQLでは正規表現を使用
    const existingRankings = await prisma.$queryRaw<{ranking_ID: string}[]>`
      SELECT ranking_ID FROM "Ranking"
      WHERE ranking_ID ~ '^[0-9]{3}$'
      ORDER BY created_at DESC
    `;

    let maxNumber = 0;

    // 既存のranking_IDから最大の番号を抽出
    for (const ranking of existingRankings) {
      const number = parseInt(ranking.ranking_ID, 10);
      if (!isNaN(number) && number > maxNumber) {
        maxNumber = number;
      }
    }

    // 次の番号を生成（3桁ゼロパディング）
    const nextNumber = maxNumber + 1;
    return nextNumber.toString().padStart(3, '0');

  } catch (error) {
    console.error('ranking_ID生成エラー:', error);
    // エラーの場合はタイムスタンプベースのIDを生成
    return `ranking_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
  }
}

/**
 * ranking_IDが既に存在するかチェックする関数
 */
export async function isRankingIdExists(rankingId: string): Promise<boolean> {
  try {
    const existing = await prisma.ranking.findFirst({
      where: {
        ranking_ID: rankingId
      }
    });
    return !!existing;
  } catch (error) {
    console.error('ranking_ID存在チェックエラー:', error);
    return false;
  }
}

/**
 * 重複しない新しいranking_IDを生成する関数
 */
export async function generateUniqueRankingId(): Promise<string> {
  let attempts = 0;
  const maxAttempts = 1000; // 3桁なので最大999まで

  while (attempts < maxAttempts) {
    const rankingId = await generateRankingId();
    const exists = await isRankingIdExists(rankingId);

    if (!exists) {
      return rankingId;
    }

    attempts++;
    console.warn(`ranking_ID重複検出: ${rankingId}, 再試行中... (${attempts}/${maxAttempts})`);
  }

  // 最大試行回数に達した場合はタイムスタンプベースのIDを生成
  console.error('ranking_ID生成の最大試行回数に達しました。タイムスタンプベースのIDを使用します。');
  return `ranking_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
}
