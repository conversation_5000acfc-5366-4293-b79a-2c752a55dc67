import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // URLパラメータからranking_IDとuser_IDを取得
    const { searchParams } = new URL(request.url);
    const rankingId = searchParams.get('ranking_ID');
    const userId = searchParams.get('user_ID');
    
    if (!rankingId) {
      return NextResponse.json(
        { success: false, error: 'ランキングIDが必要です' },
        { status: 400 }
      );
    }
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'ユーザーIDが必要です' },
        { status: 400 }
      );
    }
    
    console.log('下書き取得を開始します。ランキングID:', rankingId, 'ユーザーID:', userId);
    
    // データベースから指定された下書きを取得
    const draft = await prisma.ranking.findFirst({
      where: {
        ranking_ID: rankingId,
        user_ID: userId,
        status: 'DRAFT', // 下書きのみ取得
      },
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        thumbnail_image: true,
        recommend_rate: true,
        amazon_url: true,
        rakuten_url: true,
        yahoo_url: true,
        qoo10_url: true,
        official_url: true,
        subCategory_ID: true,
        created_at: true,
        updated_at: true,
      },
    });
    
    if (!draft) {
      return NextResponse.json(
        { success: false, error: '指定された下書きが見つかりません' },
        { status: 404 }
      );
    }
    
    console.log('下書きが見つかりました:', draft.ranking_title);
    
    // サブカテゴリからカテゴリ情報を取得
    let categoryInfo = { name: '', categoryId: '', subCategoryName: '' };
    if (draft.subCategory_ID) {
      try {
        const subCategory = await prisma.category.findFirst({
          where: {
            id: draft.subCategory_ID, // IDで検索
            user_ID: userId,
          },
          include: {
            Parent: true, // 親カテゴリ情報も取得
          },
        });

        if (subCategory) {
          categoryInfo.subCategoryName = subCategory.category_name;

          if (subCategory.Parent) {
            categoryInfo = {
              name: subCategory.Parent.category_name,
              categoryId: subCategory.Parent.category_ID,
              subCategoryName: subCategory.category_name,
            };
          }
        }
      } catch (error) {
        console.error('カテゴリ情報取得エラー:', error);
      }
    }

    // フロントエンド用の形式に変換
    const formattedDraft = {
      id: draft.ranking_ID,
      title: draft.ranking_title,
      description: draft.ranking_description || '',
      thumbnailImage: draft.thumbnail_image && draft.thumbnail_image.length > 0 ? draft.thumbnail_image[0] : '',
      images: draft.thumbnail_image || [],
      userId: userId,
      createdAt: draft.created_at.toISOString(),
      updatedAt: draft.updated_at.toISOString(),
      // 追加フィールド
      category: categoryInfo.name,
      categoryId: categoryInfo.categoryId,
      subCategory: categoryInfo.subCategoryName,
      rating: draft.recommend_rate || 1,
      amazonUrl: draft.amazon_url || '',
      rakutenUrl: draft.rakuten_url || '',
      yahooUrl: draft.yahoo_url || '',
      qoo10Url: draft.qoo10Url || '',
      officialUrl: draft.official_url || '',
    };
    
    // 成功した場合のレスポンスを返す
    return NextResponse.json(
      { 
        success: true, 
        data: formattedDraft
      },
      {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (error) {
    console.error('下書き取得中にエラーが発生しました:', error);
    return NextResponse.json(
      { success: false, error: '下書きの取得に失敗しました' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  // プリフライトリクエストに対するレスポンス
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}
