'use client';

import React, { useState } from 'react';
import YouTubeValidationTest from '../../../components/YouTubeValidationTest/YouTubeValidationTest';
import XValidationTest from '../../../components/XValidationTest/XValidationTest';
import TikTokValidationTest from '../../../components/TikTokValidationTest/TikTokValidationTest';

export default function SNSValidationTestPage() {
  const [activeTab, setActiveTab] = useState<'youtube' | 'x' | 'tiktok'>('youtube');

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto">
        {/* タブナビゲーション */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm">
            <button
              onClick={() => setActiveTab('youtube')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'youtube'
                  ? 'bg-red-500 text-white shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              YouTube バリデーション
            </button>
            <button
              onClick={() => setActiveTab('x')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'x'
                  ? 'bg-black text-white shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              X（Twitter） バリデーション
            </button>
            <button
              onClick={() => setActiveTab('tiktok')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'tiktok'
                  ? 'bg-pink-500 text-white shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              TikTok バリデーション
            </button>
          </div>
        </div>

        {/* タブコンテンツ */}
        <div>
          {activeTab === 'youtube' && <YouTubeValidationTest />}
          {activeTab === 'x' && <XValidationTest />}
          {activeTab === 'tiktok' && <TikTokValidationTest />}
        </div>
      </div>
    </div>
  );
}
