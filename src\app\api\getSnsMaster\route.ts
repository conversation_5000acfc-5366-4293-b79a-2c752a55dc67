import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from "@prisma/client";

// Prismaクライアントのインスタンスを作成
const prisma = new PrismaClient();

// GETリクエスト - 利用可能なSNSマスターデータを取得
export async function GET(request: NextRequest) {
  try {
    // データベースからSNSマスターデータを取得
    const snsMasters = await prisma.snsMaster.findMany({
      orderBy: {
        sns_ID: 'asc'
      }
    });

    // SNSマスターデータがない場合は空の配列を返す
    if (!snsMasters || snsMasters.length === 0) {
      return NextResponse.json({ snsMasters: [] }, { status: 200 });
    }

    // フロントエンドで使いやすい形式に変換
    const formattedSnsMasters = snsMasters.map(master => ({
      id: master.id,
      sns_ID: master.sns_ID,
      sns_name: master.sns_name,
      created_at: master.created_at,
      updated_at: master.updated_at
    }));

    return NextResponse.json({ snsMasters: formattedSnsMasters }, { status: 200 });
  } catch (error) {
    console.error("SNSマスターデータの取得中にエラーが発生しました:", error);
    return NextResponse.json({ error: 'SNSマスターデータの取得に失敗しました' }, { status: 500 });
  }
}
