import React, { useEffect, useMemo } from "react";
import Image from "next/image";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  closestCenter,
  closestCorners,
  pointerWithin,
  rectIntersection,
} from "@dnd-kit/core";
import {
  SortableContext,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { getSnsIconSrc, getSnsDisplayName } from "../utils/snsUtils";
import type { UserInfo } from "../types";
import { SortableSnsIcon } from "./SortableSnsIcon";

interface ProfileDetailsProps {
  user: UserInfo | null;
  slideIn: boolean;
  setSlideIn: (value: boolean) => void;
  isOwnPage: boolean;
  handleSnsAndSubcategoryDragStart: (event: DragStartEvent) => void;
  handleSnsAndSubcategoryDragEnd: (event: DragEndEvent) => void;
  sensors: any; // This should be typed properly based on DnD kit types
}

export const ProfileDetails: React.FC<ProfileDetailsProps> = ({
  user,
  slideIn,
  setSlideIn,
  isOwnPage,
  handleSnsAndSubcategoryDragStart,
  handleSnsAndSubcategoryDragEnd,
  sensors,
}) => {
  // SNSアイコンの超高速プリロード
  useEffect(() => {
    if (user?.snsLinks) {
      const preloadImages = () => {
        const snsTypes = user.snsOrder || Object.keys(user.snsLinks);
        snsTypes.forEach((snsType) => {
          if (user.snsLinks[snsType]) {
            const iconSrc = getSnsIconSrc(snsType);
            // 画像を事前読み込み
            const img = document.createElement('img');
            img.src = iconSrc;
          }
        });

        // 共通アイコンも事前読み込み
        const commonIcons = [
          getSnsIconSrc("language"), // Website
          getSnsIconSrc("email")     // Email
        ];
        commonIcons.forEach((iconSrc) => {
          const img = document.createElement('img');
          img.src = iconSrc;
        });
      };

      // 即座に実行
      preloadImages();
    }
  }, [user?.snsLinks, user?.snsOrder]);



  // SNSアイテムの順序をメモ化して強制的な更新を確実にする
  const snsItems = useMemo(() => {
    if (!user?.snsLinks) return [];
    return user.snsOrder || Object.keys(user.snsLinks);
  }, [user?.snsOrder, user?.snsLinks, (user as any)?._lastSnsUpdate]);

  // カスタム衝突検出アルゴリズム（より寛容）
  const customCollisionDetection = (args: any) => {
    // まず標準的な衝突検出を試行
    const closestCenterCollision = closestCenter(args);
    if (closestCenterCollision) {
      return closestCenterCollision;
    }

    // 標準的な検出で見つからない場合、より寛容な検出を使用
    const pointerCollision = pointerWithin(args);
    if (pointerCollision) {
      return pointerCollision;
    }

    // 最後の手段として矩形交差を使用
    const rectCollision = rectIntersection(args);
    if (rectCollision) {
      return rectCollision;
    }

    // 最も近いコーナーを使用
    return closestCorners(args);
  };

  if (!slideIn || !user) return null;

  return (
    <div className="w-full max-w-[500px] px-8 mt-14 text-white">
      {/* Self Introduction */}
      {user.selfIntroduction && (
        <div className="mb-6">
          <p className="text-sm whitespace-pre-wrap">{user.selfIntroduction}</p>
        </div>
      )}

      {/* Website */}
      {user.website && (
        <div className="mb-3 flex items-center">
          <Image
            src={getSnsIconSrc("language")}
            alt="Website"
            width={20}
            height={20}
            priority={true}
            loading="eager"
            className="mr-2 w-5 h-5"
          />
          <a
            href={user.website}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-[#1D9BF0]"
          >
            {user.website}
          </a>
        </div>
      )}

      {/* Email */}
      {user.email && (
        <div className="mb-6 flex items-center">
          <Image
            src={getSnsIconSrc("email")}
            alt="Email"
            width={20}
            height={20}
            priority={true}
            loading="eager"
            className="mr-2 w-5 h-5"
          />
          <a href={`mailto:${user.email}`} className="text-sm text-[#1D9BF0]">
            {user.email}
          </a>
        </div>
      )}

      {/* SNS Links */}
      {user.snsLinks && (
        <DndContext
          sensors={sensors}
          collisionDetection={customCollisionDetection}
          onDragStart={handleSnsAndSubcategoryDragStart}
          onDragEnd={handleSnsAndSubcategoryDragEnd}
        >
          <div className="mb-6">
            <div className="grid grid-cols-4 gap-4">
              <SortableContext
                items={snsItems}
                strategy={rectSortingStrategy}
              >
                {snsItems.map((snsType, index) => {
                  const url = user.snsLinks[snsType];
                  const iconSrc = getSnsIconSrc(snsType);
                  if (!url) return null;

                  // 強制的な再レンダリングのために、順序とタイムスタンプを含むkeyを使用
                  const uniqueKey = `${snsType}-${index}-${(user as any)._lastSnsUpdate || 0}`;

                  return (
                    <SortableSnsIcon
                      key={uniqueKey}
                      id={snsType}
                      type={getSnsDisplayName(snsType)}
                      url={url}
                      iconSrc={iconSrc}
                      disabled={!isOwnPage}
                    />
                  );
                })}
              </SortableContext>
            </div>
          </div>
        </DndContext>
      )}

      {/* Close Button */}
      <div className="w-full h-px bg-white" />
      <div className="flex justify-center mt-6 mb-4">
        <button
          onClick={() => setSlideIn(false)}
          className="bg-gray-700 hover:bg-gray-600 rounded-full p-1 transition-colors"
        >
          <svg
            className="w-4 h-4 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};