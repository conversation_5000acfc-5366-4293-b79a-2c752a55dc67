import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * history_slug機能のテスト用APIエンドポイント
 * GET: 指定されたユーザーのカテゴリとhistory_slug情報を取得
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');

    if (!user_ID) {
      return NextResponse.json({
        error: 'user_IDが必要です'
      }, { status: 400 });
    }

    // ユーザーのカテゴリ情報を取得（history_slug含む）
    const categories = await prisma.category.findMany({
      where: {
        user_ID: user_ID,
        parent_ID: null // メインカテゴリのみ
      },
      select: {
        id: true,
        category_ID: true,
        category_name: true,
        category_slug: true,
        history_slug: true,
        created_at: true,
        updated_at: true
      },
      orderBy: {
        order: 'asc'
      }
    });

    // history_slugが設定されているカテゴリの統計
    const categoriesWithHistory = categories.filter(cat => cat.history_slug);
    
    return NextResponse.json({
      success: true,
      user_ID,
      totalCategories: categories.length,
      categoriesWithHistory: categoriesWithHistory.length,
      categories: categories.map(cat => ({
        id: cat.id,
        category_ID: cat.category_ID,
        category_name: cat.category_name,
        category_slug: cat.category_slug,
        history_slug: cat.history_slug,
        hasHistory: !!cat.history_slug,
        created_at: cat.created_at,
        updated_at: cat.updated_at
      })),
      historySlugMappings: categoriesWithHistory.map(cat => ({
        categoryName: cat.category_name,
        currentSlug: cat.category_slug,
        historySlug: cat.history_slug,
        redirectUrl: `/${user_ID}?category=${cat.category_slug}` // 仮のURL
      }))
    });

  } catch (error) {
    console.error('history_slug情報取得エラー:', error);
    return NextResponse.json({
      error: 'サーバーエラーが発生しました',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * history_slug機能のテスト用
 * POST: 指定されたカテゴリのhistory_slugを手動で設定（テスト用）
 */
export async function POST(request: NextRequest) {
  try {
    const { categoryId, historySlug } = await request.json();

    if (!categoryId) {
      return NextResponse.json({
        error: 'categoryIdが必要です'
      }, { status: 400 });
    }

    // カテゴリのhistory_slugを更新
    const updatedCategory = await prisma.category.update({
      where: { id: categoryId },
      data: { history_slug: historySlug },
      select: {
        id: true,
        category_name: true,
        category_slug: true,
        history_slug: true
      }
    });

    return NextResponse.json({
      success: true,
      message: 'history_slugを更新しました',
      category: updatedCategory
    });

  } catch (error) {
    console.error('history_slug更新エラー:', error);
    return NextResponse.json({
      error: 'サーバーエラーが発生しました',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
