import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    console.log("API: uploadImage - リクエスト受信");
    let file: File | null = null;
    let user_ID: string | null = null;
    let imageData: Uint8Array | null = null;
    let fileExtension = 'jpg'; // デフォルト拡張子
    let imageType = 'profile'; // デフォルトは'profile'（プロフィール画像）、他に'background'（背景画像）、'ranking'（ランキング画像）
    
    // リクエストのContent-Typeをチェック
    const contentType = request.headers.get('Content-Type') || '';
    console.log("API: uploadImage - Content-Type:", contentType);
    
    // マルチパートフォームデータの場合
    if (contentType.includes('multipart/form-data')) {
      console.log("API: uploadImage - マルチパートフォームデータを処理");
      const formData = await request.formData();
      file = formData.get('file') as File;
      user_ID = formData.get('user_ID') as string;
      
      // imageTypeパラメータを取得（デフォルトは'profile'）
      const formImageType = formData.get('imageType');
      if (formImageType && (formImageType === 'background' || formImageType === 'profile' || formImageType === 'ranking')) {
        imageType = formImageType;
      }
      console.log("API: uploadImage - 画像タイプ:", imageType);
      
      console.log("API: uploadImage - ユーザーID:", user_ID);
      console.log("API: uploadImage - ファイル名:", file?.name);
      
      if (!file || !user_ID) {
        console.error("API: uploadImage - 必須項目が不足しています", {
          file: !file,
          user_ID: !user_ID
        });
        return NextResponse.json({ 
          error: '必須項目が不足しています',
          missingFields: {
            file: !file,
            user_ID: !user_ID
          }
        }, { status: 400 });
      }
      
      // ファイルをバイナリとして読み込む
      const bytes = await file.arrayBuffer();
      imageData = new Uint8Array(bytes);
      
      // ファイル名から拡張子を取得
      const originalName = file.name;
      fileExtension = originalName.split('.').pop() || 'jpg';
      console.log("API: uploadImage - ファイル拡張子:", fileExtension);
    } 
    // JSONデータの場合（Base64エンコードされた画像）
    else if (contentType.includes('application/json')) {
      console.log("API: uploadImage - JSONデータを処理");
      const jsonData = await request.json();
      user_ID = jsonData.user_ID;
      const base64Image = jsonData.image;
      
      // imageTypeパラメータを取得（デフォルトは'profile'）
      if (jsonData.imageType && (jsonData.imageType === 'background' || jsonData.imageType === 'profile' || jsonData.imageType === 'ranking')) {
        imageType = jsonData.imageType;
      }
      console.log("API: uploadImage - 画像タイプ:", imageType);
      
      if (!base64Image || !user_ID) {
        console.error("API: uploadImage - 必須項目が不足しています", {
          image: !base64Image,
          user_ID: !user_ID
        });
        return NextResponse.json({ 
          error: 'ユーザーIDが必要です',
          missingFields: {
            image: !base64Image,
            user_ID: !user_ID
          }
        }, { status: 400 });
      }
      
      // Base64データからMIMEタイプと実際のデータを抽出
      const matches = base64Image.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
      
      if (!matches || matches.length !== 3) {
        console.error("API: uploadImage - 無効な画像データ形式です");
        return NextResponse.json({ error: '無効な画像データ形式です' }, { status: 400 });
      }
      
      const mimeType = matches[1];
      const base64Data = matches[2];
      const buffer = Buffer.from(base64Data, 'base64');
      imageData = new Uint8Array(buffer);
      
      // MIMEタイプから拡張子を取得
      if (mimeType === 'image/jpeg' || mimeType === 'image/jpg') {
        fileExtension = 'jpg';
      } else if (mimeType === 'image/png') {
        fileExtension = 'png';
      } else if (mimeType === 'image/gif') {
        fileExtension = 'gif';
      } else if (mimeType === 'image/webp') {
        fileExtension = 'webp';
      }
      console.log("API: uploadImage - ファイル拡張子:", fileExtension);
    } else {
      console.error("API: uploadImage - サポートされていないContent-Typeです:", contentType);
      return NextResponse.json({ 
        error: 'サポートされていないContent-Typeです',
        receivedContentType: contentType
      }, { status: 400 });
    }
    
    // 画像データがない場合はエラーを返す
    if (!imageData) {
      console.error("API: uploadImage - 画像データが不正です");
      return NextResponse.json({ 
        error: '画像データが不正です',
      }, { status: 400 });
    }
    
    // ファイル名を生成（一意のID + 拡張子）
    const fileName = `${uuidv4()}.${fileExtension}`;
    
    // ファイルをサーバーに保存（publicフォルダ内のstatic/uploadsディレクトリ）
    const uploadsDir = join(process.cwd(), 'public', 'static', 'uploads');
    
    try {
      // ディレクトリが存在することを確認（なければ作成）
      await mkdir(uploadsDir, { recursive: true });

      // ファイルを書き込む（fs.promisesのwriteFileは型定義が厳密なため、Uint8Arrayを使用）
      await writeFile(join(uploadsDir, fileName), imageData);
    } catch (error) {
      console.error("API: uploadImage - ファイルの保存に失敗しました:", error);
      return NextResponse.json({ 
        error: 'ファイルの保存に失敗しました',
        details: error instanceof Error ? error.message : '不明なエラー'
      }, { status: 500 });
    }
    
    // 画像URLを生成（相対パス）
    const imageUrl = `/static/uploads/${fileName}`;
    
    // ユーザーの画像URLを更新（画像タイプに応じて）
    try {
      // ランキング画像の場合はユーザープロフィールを更新しない
      if (imageType === 'ranking') {
        console.log("API: uploadImage - ランキング画像のため、ユーザープロフィールは更新しません");
      } else {
        console.log(`API: uploadImage - ユーザー${imageType === 'background' ? '背景' : 'プロフィール'}画像更新開始:`, user_ID);
        
        // 画像タイプに応じてデータベースの更新フィールドを変更
        if (imageType === 'background') {
          await prisma.user.update({
            where: { user_ID: user_ID },
            data: { background_image: imageUrl }
          });
          console.log("API: uploadImage - ユーザー背景画像更新成功");
        } else if (imageType === 'profile') {
          await prisma.user.update({
            where: { user_ID: user_ID },
            data: { profile_image: imageUrl }
          });
          console.log("API: uploadImage - ユーザープロフィール画像更新成功");
        }
      }
    } catch (error) {
      console.error("ユーザープロフィール更新失敗:", error);
      // 画像のアップロードは成功したが、DBの更新に失敗した場合は部分的に成功とみなす
    }
    return NextResponse.json({ 
      success: true,
      url: imageUrl
    });
  } catch (error) {
    console.error("画像のアップロードに失敗しました:", error);
    return NextResponse.json({
      error: '画像のアップロードに失敗しました',
      details: error instanceof Error ? error.message : '不明なエラー'
    }, { status: 500 });
  }
}
