'use client';

import { useEffect } from 'react';
import { useClerk } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';

export default function SignOutPage() {
  const { signOut } = useClerk();
  const router = useRouter();

  useEffect(() => {
    const handleSignOut = async () => {
      try {
        await signOut();
        // サインアウト後はホームページにリダイレクト
        router.push('/');
      } catch (error) {
        console.error('サインアウトエラー:', error);
        // エラーが発生してもホームページにリダイレクト
        router.push('/');
      }
    };

    handleSignOut();
  }, [signOut, router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <CatLoadingAnimation message="サインアウト中..." />
    </div>
  );
}
