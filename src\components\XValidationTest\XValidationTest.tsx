'use client';

import React, { useState } from 'react';
import { validateXURL, generateXEmbedHTML } from '../SocialEmbed';

/**
 * X（旧Twitter）のバリデーション機能をテストするためのコンポーネント
 */
const XValidationTest: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [validationResult, setValidationResult] = useState<string | null>(null);
  const [embedHtml, setEmbedHtml] = useState('');

  // テスト用のURL例
  const testUrls = [
    // 有効なURL（投稿）
    'https://x.com/elonmusk/status/1234567890123456789',
    'https://twitter.com/elonmusk/status/1234567890123456789',
    'https://x.com/user123/status/9876543210987654321?t=abc&s=xyz',
    'https://twitter.com/test_user/status/1111111111111111111?ref_src=twsrc',
    'https://x.com/a/status/1234567890123456789',
    'https://twitter.com/user_name_15/status/9999999999999999999',
    
    // 無効なURL（プロフィール・その他）
    'https://x.com/elonmusk',
    'https://twitter.com/elonmusk',
    'https://x.com/elonmusk/',
    'https://twitter.com/elonmusk?tab=replies',
    'https://x.com/i/lists/123456789',
    'https://x.com/i/communities/123456789',
    'https://x.com/search?q=test',
    'https://x.com/settings/profile',
    'https://x.com/notifications',
    'https://x.com/messages',
    'https://x.com/home',
    'https://example.com/not-twitter',
    'invalid-url',
    // ステータスIDが短すぎる/長すぎる
    'https://x.com/user/status/123456789', // 9桁（短い）
    'https://x.com/user/status/12345678901234567890123456', // 26桁（長い）
    // ユーザー名が長すぎる
    'https://x.com/this_username_is_too_long_for_twitter/status/1234567890123456789'
  ];

  const handleTest = () => {
    const result = validateXURL(testUrl);
    setValidationResult(result);
    
    if (result) {
      const html = generateXEmbedHTML(result, testUrl);
      setEmbedHtml(html);
    } else {
      setEmbedHtml('');
    }
  };

  const handleTestUrlClick = (url: string) => {
    setTestUrl(url);
    const result = validateXURL(url);
    setValidationResult(result);
    
    if (result) {
      const html = generateXEmbedHTML(result, url);
      setEmbedHtml(html);
    } else {
      setEmbedHtml('');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-6 text-gray-800">X（旧Twitter） URL バリデーションテスト</h1>
      
      {/* 入力エリア */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          X（旧Twitter） URL を入力してください:
        </label>
        <div className="flex gap-2">
          <input
            type="text"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://x.com/username/status/..."
          />
          <button
            onClick={handleTest}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            テスト
          </button>
        </div>
      </div>

      {/* 結果表示 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2 text-gray-800">バリデーション結果:</h3>
        <div className={`p-3 rounded-md ${validationResult ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {validationResult ? (
            <>
              <div className="font-semibold">✓ 有効な投稿URL</div>
              <div className="text-sm">投稿ID: {validationResult}</div>
            </>
          ) : (
            <div className="font-semibold">✗ 無効なURL</div>
          )}
        </div>
      </div>

      {/* 埋め込みHTML表示 */}
      {embedHtml && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-800">生成された埋め込みHTML:</h3>
          <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto">
            {embedHtml}
          </pre>
        </div>
      )}

      {/* テスト用URL一覧 */}
      <div>
        <h3 className="text-lg font-semibold mb-3 text-gray-800">テスト用URL一覧:</h3>
        <div className="space-y-2">
          <div className="text-sm font-medium text-green-600 mb-2">✓ 有効なURL（投稿）:</div>
          {testUrls.slice(0, 6).map((url, index) => (
            <button
              key={index}
              onClick={() => handleTestUrlClick(url)}
              className="block w-full text-left px-3 py-2 bg-green-50 hover:bg-green-100 rounded-md text-sm text-green-700 border border-green-200"
            >
              {url}
            </button>
          ))}
          
          <div className="text-sm font-medium text-red-600 mb-2 mt-4">✗ 無効なURL（プロフィール・その他）:</div>
          {testUrls.slice(6).map((url, index) => (
            <button
              key={index + 6}
              onClick={() => handleTestUrlClick(url)}
              className="block w-full text-left px-3 py-2 bg-red-50 hover:bg-red-100 rounded-md text-sm text-red-700 border border-red-200"
            >
              {url}
            </button>
          ))}
        </div>
      </div>

      {/* 仕様説明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-md">
        <h3 className="text-lg font-semibold mb-2 text-blue-800">バリデーション仕様:</h3>
        <div className="text-sm text-blue-700 space-y-1">
          <div><strong>対応URL形式:</strong></div>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>https://x.com/ユーザー名/status/ステータスID</li>
            <li>https://twitter.com/ユーザー名/status/ステータスID</li>
            <li>クエリパラメータ（?t=...&s=...など）は無視</li>
          </ul>
          <div className="mt-2"><strong>除外URL形式:</strong></div>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>プロフィールページ: /ユーザー名</li>
            <li>リスト: /i/lists/...</li>
            <li>コミュニティ: /i/communities/...</li>
            <li>検索: /search?...</li>
            <li>設定・通知・メッセージなど</li>
          </ul>
          <div className="mt-2"><strong>制限:</strong></div>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>ユーザー名: 1-15文字（英数字・アンダースコア）</li>
            <li>ステータスID: 10-25桁の数字</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default XValidationTest;
