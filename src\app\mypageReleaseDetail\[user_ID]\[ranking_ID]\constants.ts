// Constants for mypageReleaseDetail

export const MAX_IMAGES = 4;

export const DEFAULT_USER_DATA = {
  name: 'ユーザー',
  profileImage: '/static/img/default-profile.png'
};

export const DEFAULT_LOGO_IMAGE = '/static/img/logo.png';

export const SAVED_RANKING_CATEGORY = '保存したランキング';

export const SNS_EMBED_DELAY = 1000; // milliseconds

export const HELP_POPUP_CONTENT = {
  title: 'おすすめ度の目安',
  ratings: [
    { level: 5, description: '自信をもっておすすめできる。リピートしている' },
    { level: 4, description: '良かったが人を選ぶ。リピートしたい' },
    { level: 3, description: '普通。リピートするか迷う' },
    { level: 2, description: '改善点がある。自分には合わなかった' },
    { level: 1, description: 'おすすめできない' }
  ]
};

export const SNS_SCRIPTS = {
  instagram: {
    src: 'https://www.instagram.com/embed.js',
    selector: 'instagram.com/embed'
  },
  twitter: {
    src: 'https://platform.twitter.com/widgets.js',
    selector: 'platform.twitter.com'
  },
  tiktok: {
    src: 'https://www.tiktok.com/embed.js',
    selector: 'tiktok.com/embed'
  }
};

export const ERROR_MESSAGES = {
  INVALID_URL: 'URLが正しくありません。ユーザーIDまたはランキングIDが不足しています。',
  RANKING_NOT_FOUND: 'ランキングデータが見つかりませんでした',
  USER_NOT_FOUND: 'ユーザー情報が見つかりませんでした',
  FETCH_ERROR: 'データの取得に失敗しました'
};