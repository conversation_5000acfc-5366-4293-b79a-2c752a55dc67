/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";
import Link from 'next/link';

interface ButtonFooterProps {
  user_ID: string;
  showAddButton?: boolean;
  onAddRanking?: () => void;
}

export const ButtonFooter = ({ 
  user_ID, 
  showAddButton = true, 
  onAddRanking 
}: ButtonFooterProps): JSX.Element => {
  // ボタンが非表示の場合は何も表示しない
  if (!showAddButton) {
    return <div className="h-[56px]"></div>; // スペースを確保するための空のdiv
  }

  // onAddRankingが提供されている場合はそれを使用し、そうでない場合はデフォルトのリンク動作
  if (onAddRanking) {
    return (
      <div className="flex flex-col w-[390px] h-[56px] items-center justify-center fixed bottom-0 bg-white border-t [border-top-style:solid] border-line-color">
        <button 
          onClick={onAddRanking}
          className="flex w-[342px] h-[40px] items-center justify-center bg-button rounded-[100px] overflow-hidden"
        >
          <div className="inline-flex flex-col h-[40px] items-center justify-center flex-[0_0_auto]">
            <div className="w-fit [font-family:'Roboto',Helvetica] font-normal text-black text-[14px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
              ランキングを作る
            </div>
          </div>
        </button>
      </div>
    );
  }

  // デフォルトのリンク動作
  return (
    <Link href={`/rankingRegister/${user_ID}`} className="flex flex-col w-[390px] h-[56px] items-center justify-center fixed bottom-0 bg-white border-t [border-top-style:solid] border-line-color">
      <div className="flex w-[342px] h-[40px] items-center justify-center bg-button rounded-[100px] overflow-hidden">
        <div className="inline-flex flex-col h-[40px] items-center justify-center flex-[0_0_auto]">
          <div className="w-fit [font-family:'Roboto',Helvetica] font-normal text-black text-[14px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
            ランキングを作る
          </div>
        </div>
      </div>
    </Link>
  );
};
