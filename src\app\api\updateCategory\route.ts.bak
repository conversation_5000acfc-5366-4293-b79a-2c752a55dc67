import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

let prisma: PrismaClient;
if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  if (!(global as any).prisma) {
    (global as any).prisma = new PrismaClient();
  }
  prisma = (global as any).prisma;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { id, category_name, user_ID, parent_ID } = body;

    console.log(`カテゴリ更新リクエスト: ID=${id}, 名前=${category_name}, ユーザーID=${user_ID}, 親ID=${parent_ID}`);

    if (!id || !category_name || !user_ID) {
      return NextResponse.json(
        { success: false, error: '必須パラメータが不足しています' },
        { status: 400 }
      );
    }

    // ユーザー情報を取得（domain が必要なため）
    const user = await prisma.user.findUnique({ where: { user_ID } });
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'ユーザーが見つかりません' },
        { status: 404 }
      );
    }

    // ───────────────────────────────────────────────
    // 「new_category_」で始まる ID の場合：【一度作成→それ以降は更新】
    // ───────────────────────────────────────────────
    if (id.startsWith("new_category_")) {
      // 1) まず同じ category_ID を持つレコードがあるかチェック
      const existingByID = await prisma.category.findFirst({
        where: { user_ID, id },
      });

      if (existingByID) {
        // 既に作成済み → 名前だけ更新して返す
        const updated = await prisma.category.update({
          where: { id: existingByID.id },
          data: { category_name },
        });
        console.log("既存カテゴリを更新しました:", updated);
        return NextResponse.json(
          { success: true, message: "カテゴリを更新しました", data: updated },
          { status: 200 }
        );
      }

      // 2) ID 未存在 → 本当に新規作成する
      //    （ここでは同名重複チェック＋自動2,3付番ロジックをそのまま使えます）
      let baseName = category_name;
      let uniqueName = baseName;
      let count = 1;
      while (true) {
        const dup = await prisma.category.findFirst({
          where: { user_ID, category_name: uniqueName },
        });
        if (!dup) break;
        count++;
        uniqueName = `${baseName}${count}`;
      }

      const newCategory = await prisma.category.create({
        data: {
          id: id, // idを明示的に指定
          category_name: uniqueName,
          user_ID,
          domain: process.env.DOMAIN || "default-domain.com",
          parent_ID: parent_ID || null,
        },
      });
      console.log("カテゴリを作成しました:", newCategory);
      return NextResponse.json(
        { success: true, message: "カテゴリを作成しました", category: newCategory },
        { status: 201 }
      );
    }

    // ───────────────────────────────────────────────
    // 「new_category_」以外の既存カテゴリ更新 or 存在しなければ作成
    // ───────────────────────────────────────────────
    try {
      const existing = await prisma.category.findFirst({
        where: { user_ID, id },
      });
      if (!existing) {
        // 既存レコードがなければ作成
        const created = await prisma.category.create({
          data: {
            id: id, // idを明示的に指定
            category_name,
            user_ID,
            domain: process.env.DOMAIN || "default-domain.com",
            parent_ID: parent_ID || null,
          },
        });
        console.log("既存カテゴリなし → 新規作成:", created);
        return NextResponse.json(
          { success: true, data: created },
          { status: 201 }
        );
      }
      // 見つかったら名前だけ更新
      const updated = await prisma.category.update({
        where: { id: existing.id },
        data: { category_name },
      });
      console.log("カテゴリ更新成功:", updated);
      return NextResponse.json(
        { success: true, data: updated },
        { status: 200 }
      );
    } catch (updateError: any) {
      console.error('カテゴリ更新エラー:', updateError);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to update category: ${updateError.message || updateError}`,
          details: updateError
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Error during Prisma operation:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to update category: ${error.message || error}`,
        details: error
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
