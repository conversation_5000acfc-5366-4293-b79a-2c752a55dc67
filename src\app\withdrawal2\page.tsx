'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Footer } from '../../components/Footer/Footer';
import { ImageUserFrame } from '../../components/ImageUserFrame/ImageUserFrame';
import { TextUserUrlSimple } from '../../components/TextUserUrlSimple/TextUserUrlSimple';

// 退会理由の型定義
interface WithdrawalReason {
  id: string;
  label: string;
  checked: boolean;
}

export default function WithdrawalStep2Page() {
  const [userDomain, setUserDomain] = useState('');
  const [profileImage, setProfileImage] = useState('/static/img/profile-default.png');
  
  // 退会理由のリスト
  const [reasons, setReasons] = useState<WithdrawalReason[]>([
    { id: 'usage_difficulty', label: '使い方がわからなかった', checked: false },
    { id: 'usability', label: '使いにくい', checked: false },
    { id: 'service_expectation', label: '期待していたサービスではなかった', checked: false },
    { id: 'other', label: 'その他', checked: false },
  ]);
  
  // フィードバックコメント
  const [feedback, setFeedback] = useState('');
  
  // ユーザー情報を取得
  useEffect(() => {
    // APIからユーザー情報を取得する
    // 実際の実装では、ここで適切なAPIエンドポイントを呼び出す
    const fetchUserData = async () => {
      try {
        // 例: const response = await fetch('/api/user/profile');
        // const data = await response.json();
        // setUserDomain(data.customUrl);
        // setProfileImage(data.profileImage || '/static/img/default-avatar.png');
        
        // 実際のAPIが実装されるまでは、ローカルストレージやセッションから取得するなどの方法も考えられる
        // 例: const domain = localStorage.getItem('userDomain') || '';
        // setUserDomain(domain);
      } catch (error) {
        console.error('Failed to fetch user data:', error);
      }
    };
    
    fetchUserData();
  }, []);
  
  // チェックボックスの状態を更新する関数
  const handleReasonChange = (id: string) => {
    setReasons(reasons.map(reason => 
      reason.id === id ? { ...reason, checked: !reason.checked } : reason
    ));
  };
  
  // フィードバックの変更を処理する関数
  const handleFeedbackChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFeedback(e.target.value);
  };
  
  // アカウント削除処理
  const handleDeleteAccount = () => {
    // 選択された退会理由を取得
    const selectedReasons = reasons.filter(reason => reason.checked);
    
    // 実際の実装ではAPIとの連携などが必要
    // 例: アカウント削除APIを呼び出す
    console.log('Selected reasons:', selectedReasons);
    console.log('Feedback:', feedback);
    
    // 削除完了後のリダイレクト
    // router.push('/withdrawal-complete');
    alert('アカウントが削除されました');
  };

  const handleBack = () => {
    window.history.back();
  };

  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden" data-component-name="WithdrawalStep2Page">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <div className="flex justify-between max-w-[500px] h-[52px] items-center relative shadow-[0px_0px_4px_#00000040] w-full">
        <div 
          className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative cursor-pointer"
          onClick={handleBack}
        >
          <Image 
            alt="Image left arrow" 
            src="/static/img/imageleftarrow.png" 
            width={30}
            height={30}
            className="relative w-[30px] h-[30px] object-cover"
          />
          <p className="relative flex-[0_0_auto]">退会</p>
        </div>
        <div className="flex w-[52px] h-[52px] items-center justify-end pl-0 pr-[16px] py-0 relative">
          <Image 
            alt="Image question" 
            src="/static/img/imagequestion.png" 
            width={20}
            height={20}
            className="relative w-[20px] h-[20px] object-cover"
          />
        </div>
      </div>

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6">
        {/* ユーザー情報 */}
        <div className="mb-8 text-center">
          <div className="flex flex-col items-center">
            <ImageUserFrame profile_image={profileImage} />
            <div className="mt-2">
              <TextUserUrlSimple domain={userDomain} textColor="text-gray-600" />
            </div>
          </div>
        </div>
        
        {/* 注意事項 */}
        <div className="mb-6">
          <div className="flex max-w-[500px] h-[46px] items-center pl-[16px] pr-0 py-0 relative border-y border-y-[var(--line-color)] bg-[rgba(246,247,248,1)]">
            <p className="relative w-fit text-font-gray text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
              <span className="text-[rgb(170,170,170)] text-[12px] tracking-[0]">注意事項</span>
            </p>
          </div>
          <div className="p-4">
            <p className="text-sm text-gray-800">
              退会をご希望される場合、「アカウント削除」ボタンをクリックしてください。アカウントを削除すると、mypics.bestに登録したデータが全て削除されます。
            </p>
          </div>
        </div>
        
        {/* 退会理由 */}
        <div className="mb-6">
          <div className="flex max-w-[500px] h-[46px] items-center pl-[16px] pr-0 py-0 relative border-y border-y-[var(--line-color)] bg-[rgba(246,247,248,1)]">
            <p className="relative w-fit text-font-gray text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
              <span className="text-[rgb(170,170,170)] text-[12px] tracking-[0]">退会理由</span>
            </p>
          </div>
          <div className="p-4">
            <div className="space-y-3">
              {reasons.map((reason) => (
                <div key={reason.id} className="flex items-center justify-between">
                  <label htmlFor={reason.id} className="text-sm text-gray-800 flex-1">
                    {reason.label}
                  </label>
                  <div className="relative flex items-center justify-center w-5 h-5">
                    <input
                      type="checkbox"
                      id={reason.id}
                      checked={reason.checked}
                      onChange={() => handleReasonChange(reason.id)}
                      className="appearance-none w-5 h-5 border border-gray-300 rounded-sm bg-white/20 checked:bg-yellow-400 checked:border-yellow-400 transition-colors duration-200"
                    />
                    {reason.checked && (
                      <svg 
                        className="absolute w-3 h-3 text-white pointer-events-none" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24" 
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                      </svg>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* ご意見 */}
        <div className="mb-8">
          <div className="flex max-w-[500px] h-[46px] items-center pl-[16px] pr-0 py-0 relative border-t border-t-[var(--line-color)] bg-[rgba(246,247,248,1)]">
            <p className="relative w-fit text-font-gray text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
              <span className="text-[rgb(170,170,170)] text-[12px] tracking-[0]">ご意見</span>
            </p>
          </div>
          <div className="">
            <textarea
              value={feedback}
              onChange={handleFeedbackChange}
              className="w-full h-24 p-3 border-y border-y-[var(--line-color)] resize-none focus:outline-none bg-white/90 text-[14px]"
              placeholder="ご意見やフィードバックをお聞かせください"
            ></textarea>
          </div>
        </div>
        
        {/* アカウント削除ボタン */}
        <div className="flex justify-center mt-6 px-4 mb-8">
          <button
            onClick={handleDeleteAccount}
            className="w-full max-w-xs bg-yellow-400 text-black font-medium py-3 rounded-full hover:bg-yellow-500 transition duration-200"
          >
            アカウント削除
          </button>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
