import React from 'react';
import Image from 'next/image';
import './CatLoadingAnimation.css';

interface CatLoadingAnimationProps {
  message?: string;
}

export const CatLoadingAnimation: React.FC<CatLoadingAnimationProps> = ({
  message = "Loading..."
}) => {
  return (
    <div className="loading-container">
      <div className="loading-gif">
        <Image
          src="/static/img/animation.gif"
          alt="Loading..."
          width={60}
          height={60}
          unoptimized={true}
          priority
        />
      </div>

      <div className="loading-text">
        <span className="loading-message">{message}</span>
      </div>
    </div>
  );
};

export default CatLoadingAnimation;
