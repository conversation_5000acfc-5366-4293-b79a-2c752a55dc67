'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function ShareRankingGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">ランキングを共有する方法</h1>
          <div className="pt-6 text-[14px] text-[#313131]">
            <p>1. 共有したいランキングの詳細ページにアクセスします。</p>
            <p>2. 「共有」ボタンをタップします。</p>
            <p>3. 以下の共有方法から選択できます：</p>
            <ul className="list-disc pl-5">
              <li>リンクをコピー</li>
              <li>QRコードを表示</li>
              <li>SNSで直接共有（Twitter、Instagram、LINEなど）</li>
            </ul>
            <p>4. 選択した方法でランキングを共有します。</p>
          </div>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
