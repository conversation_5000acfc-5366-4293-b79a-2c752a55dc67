interface RankingRegisterTitleProps {
  className?: string;
  text?: string;
  text1?: string;
  text2?: string; // ⭐️ 追加
}

export const RankingRegisterTitle: React.FC<RankingRegisterTitleProps> = ({
  className = "",
  text,
  text1,
  text2 // ⭐️ 追加
}) => {
  const displayText = text === "" || text === undefined ? "おすすめの理由を入力" : text;

  return (
    <div className={`inline-flex w-full flex-col items-start pt-4 pb-0 px-0 relative ${className}`}>
      <div className="flex w-full h-12 items-center pl-4 pr-0 py-0 relative">
        <p className="relative w-fit font-normal text-sm tracking-[0] leading-[normal] whitespace-nowrap">
          <span className="font-semibold">{displayText}</span>
          {text1 && <span className="text-[10px]">{text1}</span>}
          {text2 && <span className="text-[#e63b5f] text-[10px]">{text2}</span>}
        </p>
      </div>
    </div>
  );
};
