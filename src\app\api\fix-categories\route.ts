import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');

    if (!user_ID) {
      return NextResponse.json({ error: 'user_IDが必要です' }, { status: 400 });
    }

    console.log(`カテゴリ修正開始: ユーザーID=${user_ID}`);

    // ユーザーの全カテゴリを取得
    const allUserCategories = await prisma.category.findMany({
      where: { user_ID }
    });

    console.log(`修正対象カテゴリ: ${allUserCategories.length}件`);

    const fixResults = [];

    for (const category of allUserCategories) {
      // parent_IDが自分自身のIDまたはcategory_IDと同じ場合、nullに修正
      if (category.parent_ID === category.id || 
          category.parent_ID === category.category_ID ||
          (category.parent_ID && category.parent_ID !== '' && category.parent_ID !== null)) {
        
        console.log(`修正対象: ${category.category_name} (parent_ID: ${category.parent_ID} -> null)`);
        
        const updatedCategory = await prisma.category.update({
          where: { id: category.id },
          data: { parent_ID: null }
        });

        fixResults.push({
          id: category.id,
          name: category.category_name,
          oldParentId: category.parent_ID,
          newParentId: null,
          status: 'fixed'
        });
      } else {
        fixResults.push({
          id: category.id,
          name: category.category_name,
          parentId: category.parent_ID,
          status: 'no_change_needed'
        });
      }
    }

    console.log('カテゴリ修正完了:', fixResults);

    return NextResponse.json({
      message: 'カテゴリの修正が完了しました',
      user_ID,
      results: fixResults
    });

  } catch (error) {
    console.error('カテゴリ修正エラー:', error);
    return NextResponse.json({
      error: 'カテゴリの修正に失敗しました',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('Prisma disconnect error:', disconnectError);
    }
  }
}
