'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useSearchParams, useRouter, useParams } from 'next/navigation';
import { flushSync } from 'react-dom';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';

// 既存のコンポーネントを再利用
import { RankingContent, HelpPopup, Breadcrumb } from '../../mypageReleaseDetail/[user_ID]/[ranking_ID]/components';
import { SavedRankingItem, ProductLinks } from '../../mypageReleaseDetail/[user_ID]/[ranking_ID]/types';
import { MAX_IMAGES } from '../../mypageReleaseDetail/[user_ID]/[ranking_ID]/constants';
import { Footer } from '@/components/Footer';
import { MypageReleaseButtonFooter } from '@/components/MypageReleaseButtonFooter';
import { HeaderItemsReview } from '@/components/HeaderItemsReview';
import { ImageItem } from '@/components/ImageItem';
import { ImageSlider } from '@/components/ImageSlider/ImageSlider';
import { BrowserExtensionProtectionProvider } from '../components/BrowserExtensionProtectionProvider';
import Script from 'next/script';

interface RankingDetailData {
  ranking_ID: string;
  ranking_title: string;
  ranking_description: string;
  recommend_rate: number;
  thumbnail_image: string[];
  images?: string[]; // 追加の画像配列
  amazon_url?: string;
  rakuten_url?: string;
  yahoo_url?: string;
  qoo10_url?: string;
  official_url?: string;
  category_name?: string;
  category_id?: string; // カテゴリID
  subcategory_name?: string;
  subCategory_ID?: string; // サブカテゴリID
  logo_image_myrank?: string;
  user_ID: string;
}

interface UserData {
  name: string;
  profileImage?: string;
  username?: string;
}

const PostDetailPage = (): JSX.Element => {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  const username = params.username as string;
  const rankingId = searchParams.get('ranking');

  // State
  const [ranking, setRanking] = useState<RankingDetailData[] | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);

  // SNS埋め込みが含まれているかどうかを判定
  const hasInstagramEmbed = ranking?.[0]?.ranking_description?.includes('instagram-media') || false;
  const hasTwitterEmbed = ranking?.[0]?.ranking_description?.includes('twitter-tweet') || false;
  const hasTikTokEmbed = ranking?.[0]?.ranking_description?.includes('tiktok-embed') || false;

  // Data fetching
  useEffect(() => {
    const fetchRankingData = async () => {
      if (!rankingId) {
        setError('ランキングIDが指定されていません');
        setIsLoading(false);
        return;
      }

      if (!username) {
        setError('ユーザー名が指定されていません');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // ユーザー名からユーザーIDを取得
        const userResponse = await fetch(`/api/getUserByUsername?username=${username}`);
        if (!userResponse.ok) {
          throw new Error('ユーザー情報の取得に失敗しました');
        }
        const userInfo = await userResponse.json();
        const userId = userInfo.user_ID;

        // ランキングデータを取得
        const rankingResponse = await fetch(`/api/getRankingDetail?ranking_ID=${rankingId}`);

        if (!rankingResponse.ok) {
          const errorData = await rankingResponse.json().catch(() => ({}));
          console.error('PostDetailPage: API応答エラー:', {
            status: rankingResponse.status,
            statusText: rankingResponse.statusText,
            errorData
          });

          if (rankingResponse.status === 404) {
            throw new Error('指定されたランキングが見つかりません');
          } else if (rankingResponse.status === 400) {
            throw new Error('無効なランキングIDです');
          } else {
            throw new Error(`ランキングデータの取得に失敗しました (${rankingResponse.status})`);
          }
        }

        const rankingData = await rankingResponse.json();

        if (!rankingData || rankingData.length === 0) {
          throw new Error('ランキングデータが空です');
        }

        // ランキングの所有者とURLのユーザー名が一致するかチェック
        if (rankingData[0].user_ID !== userId) {
          throw new Error('このランキングにアクセスする権限がありません');
        }

        setRanking(rankingData);

        // ユーザーデータを設定
        setUserData({
          name: userInfo.name || 'ユーザー',
          profileImage: userInfo.profile_image,
          username: userInfo.username
        });

      } catch (err) {
        setError(err instanceof Error ? err.message : 'データの取得に失敗しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRankingData();
  }, [rankingId, username]);

  // Prepare unique images array
  const uniqueImages = useMemo(() => {
    if (!ranking || !ranking[0]) return [];

    const allImages = ranking[0]?.thumbnail_image ?
      (typeof ranking[0].thumbnail_image === 'string' ? [ranking[0].thumbnail_image] : ranking[0].thumbnail_image) :
      [];

    const additionalImages = ranking[0]?.images || [];
    const combinedImages = [...allImages, ...additionalImages];

    return combinedImages.filter((image, index) => {
      return combinedImages.indexOf(image) === index;
    }).slice(0, MAX_IMAGES);
  }, [ranking]);

  // Initialize thumbnail when ranking data is loaded
  useEffect(() => {
    if (uniqueImages.length > 0 && !selectedThumbnail) {
      setSelectedThumbnail(uniqueImages[0]);
      setCurrentImageIndex(0);
    }
  }, [uniqueImages, selectedThumbnail]);

  // Handle image selection
  const handleImageSelect = useCallback((imageUrl: string) => {
    const index = uniqueImages.findIndex(img => img === imageUrl);

    flushSync(() => {
      setSelectedThumbnail(imageUrl);
      if (index !== -1) {
        setCurrentImageIndex(index);
      }
    });
  }, [uniqueImages]);

  // Handle slider image change
  const handleSliderImageChange = useCallback((imageUrl: string, index: number) => {
    flushSync(() => {
      setSelectedThumbnail(imageUrl);
      setCurrentImageIndex(index);
    });
  }, []);

  // ランキングIDが指定されていない場合
  if (!rankingId) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">投稿が指定されていません</h1>
          <p className="text-gray-600 mb-4">URLにランキングIDを指定してください。</p>
          <button
            onClick={() => router.push('/')}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            ホームに戻る
          </button>
        </div>
      </div>
    );
  }

  // ローディング中
  if (isLoading) {
    return (
      <div className="relative min-h-screen flex flex-col items-center overflow-x-hidden">
        <div className="flex flex-col w-full max-w-[500px] mx-auto min-h-screen items-center relative shadow-[-10px_0_20px_rgba(0,0,0,0.1),10px_0_20px_rgba(0,0,0,0.1)] bg-gray-100">
          {/* ヘッダー（ローディング中用） */}
          <HeaderItemsReview
            userID=""
            userName=""
            profileImage=""
            rankingID=""
            categoryName=""
            categoryId=""
            subCategoryName=""
            subCategoryId=""
          />

          {/* ローディングアニメーション */}
          <div className="w-full bg-white flex-1 flex items-center justify-center">
            <CatLoadingAnimation />
          </div>
        </div>
      </div>
    );
  }

  // エラー状態
  if (error || !ranking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">エラーが発生しました</h1>
          <p className="text-gray-600 mb-6">{error || 'ランキングデータが見つかりません'}</p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              再読み込み
            </button>
            <button
              onClick={() => router.push('/')}
              className="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors"
            >
              ホームに戻る
            </button>
          </div>
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-3 bg-gray-100 rounded text-left text-sm">
              <strong>デバッグ情報:</strong>
              <br />ユーザー名: {username}
              <br />ランキングID: {rankingId}
              <br />エラー: {error}
            </div>
          )}
        </div>
      </div>
    );
  }

  // 商品リンク
  const productLinks: ProductLinks = {
    amazon: ranking[0].amazon_url || '',
    rakuten: ranking[0].rakuten_url || '',
    yahoo: ranking[0].yahoo_url || '',
    qoo10: ranking[0].qoo10_url || '',
    official: ranking[0].official_url || ''
  };

  // ランキングデータ（保存用）- SavedRankingItem型に合わせる
  const rankingData: SavedRankingItem = {
    id: ranking[0].ranking_ID,
    title: ranking[0].ranking_title,
    description: ranking[0].ranking_description,
    imageUrl: uniqueImages.length > 0 ? uniqueImages[0] : '',
    rating: ranking[0].recommend_rate
  };

  return (
    <BrowserExtensionProtectionProvider>
      <div className="relative min-h-screen flex flex-col items-center overflow-x-hidden">
        {/* SNS Scripts - 条件付きで読み込み */}
        {hasInstagramEmbed && (
          <Script
            src="//www.instagram.com/embed.js"
            strategy="lazyOnload"
          />
        )}
        {hasTwitterEmbed && (
          <Script
            src="https://platform.twitter.com/widgets.js"
            strategy="lazyOnload"
          />
        )}
        {hasTikTokEmbed && (
          <Script
            src="https://www.tiktok.com/embed.js"
            strategy="lazyOnload"
          />
        )}

        <div className="flex flex-col w-full max-w-[500px] mx-auto min-h-screen items-center relative shadow-[-10px_0_20px_rgba(0,0,0,0.1),10px_0_20px_rgba(0,0,0,0.1)] bg-gray-100">
        {/* Header */}
        <HeaderItemsReview
          userID={ranking[0].user_ID}
          userName={userData?.name}
          profileImage={userData?.profileImage}
          rankingID={ranking[0].ranking_ID}
          categoryName={ranking[0]?.category_name || ""}
          categoryId={ranking[0]?.category_id || ""}
          subCategoryName={ranking[0]?.subcategory_name || ""}
          subCategoryId={ranking[0]?.subCategory_ID?.toString() || ""}
        />

        {/* Main image slider */}
        {uniqueImages.length > 0 && (
          <div className="w-full bg-black">
            <ImageSlider
              images={uniqueImages}
              selectedImage={selectedThumbnail}
              onImageChange={handleSliderImageChange}
            />
          </div>
        )}

        {/* Thumbnail images */}
        <ImageItem
          images={uniqueImages}
          onSelectImage={handleImageSelect}
          selectedImage={selectedThumbnail}
        />

        {/* White background content area */}
        <div className="w-full bg-white">
          {/* Breadcrumb */}
          <Breadcrumb
            userId={ranking[0].user_ID}
            categoryName={ranking[0].category_name}
            subcategoryName={ranking[0].subcategory_name}
          />

          {/* Title - 修正: 白背景枠内に収まるように */}
          <div className="flex w-full items-center pt-[8px] pl-[16px] pr-[16px] pb-0 relative">
            <div className="relative w-full text-[18px] tracking-[0] leading-[1.5] font-bold break-words overflow-wrap-anywhere">
              {ranking[0].ranking_title}
            </div>
          </div>

          {/* Star rating with help popup */}
          <div className="flex pl-[16px] pt-[8px] items-center gap-[0px]">
            <p className="text-[12px] pr-[4px]">おすすめ度</p>
            {/* 星マーク */}
            <div className="flex">
              {[1, 2, 3, 4, 5].map((i) => (
                <svg
                  key={i}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 26 26"
                  fill={i <= ranking[0].recommend_rate ? "var(--star-color)" : "var(--star-background-color)"}
                  className="w-4 h-4"
                >
                  <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                </svg>
              ))}
            </div>
            <p className="pl-[4px] text-[#E63B5F]">{ranking[0].recommend_rate}</p>
            <HelpPopup />
          </div>

          {/* Content */}
          <RankingContent
            description={ranking[0].ranking_description || ''}
            rankingId={ranking[0].ranking_ID}
          />

          {/* Footer - 修正: 80pxのスペースを追加 */}
          <Footer
            className="!flex-[0_0_auto] pb-[80px]"
            logoImageMyrank={ranking[0].logo_image_myrank}
          />
        </div>

        <MypageReleaseButtonFooter
          url={typeof window !== 'undefined' ? window.location.href : ''}
          rankingData={rankingData}
          productLinks={productLinks}
        />
        </div>
      </div>
    </BrowserExtensionProtectionProvider>
  );
};

export default PostDetailPage;
