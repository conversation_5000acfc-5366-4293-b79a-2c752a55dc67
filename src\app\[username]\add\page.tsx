"use client"

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { ImageUploadSection } from '../../rankingRegister/[user_ID]/components/ImageUploadSection';
import { ReviewInputBox } from '@/components/ReviewInputBox/ReviewInputBox';
import { CategorySelect } from '@/components/CategorySelect/CategorySelect';
import { HeaderItems } from '@/components/HeaderItems/HeaderItems';
import { RankingRegisterLoadingAnimation } from '@/components/RankingRegisterLoadingAnimation';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';
import { RankingRegisterButtonFooter } from '@/components/RankingRegisterButtonFooter';
import { Footer } from '@/components/Footer';

// リファクタリングで作成したカスタムフック
import { useUserIdentification } from '../../rankingRegister/hooks/useUserIdentification';
import { useImageUpload } from '../../rankingRegister/hooks/useImageUpload';
import { useRankingForm } from '../../rankingRegister/hooks/useRankingForm';

// コンポーネント
import { RatingSection } from '../../rankingRegister/[user_ID]/components/RatingSection';
import { LinkSection } from '../../rankingRegister/[user_ID]/components/LinkSection';

// 各セクションコンポーネントの型定義
type TitleSectionProps = {
  title: string;
  setTitle: (value: string) => void;
  validationErrors: { [key: string]: boolean };
  showErrors: boolean;
};

type RecommendSectionProps = {
  recommendText: string;
  setRecommendText: (value: string) => void;
};

type CategorySectionProps = {
  category: string;
  subCategory: string;
  setCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
  setSubCategory: (e: React.ChangeEvent<HTMLInputElement>) => void;
  categoryId: string;
  parentCategoryUuid?: string;
  validationErrors: { [key: string]: boolean };
  showErrors: boolean;
};

// 各セクションコンポーネントの定義
const TitleSection = ({ title, setTitle, validationErrors, showErrors }: TitleSectionProps) => {
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  return (
    <div className="px-4 py-3">
      <div className="flex justify-between items-center mb-2">
        <p className="text-[#313131] text-[16px] font-bold">タイトル <span className="text-xs text-red-500">(必須)</span></p>
      </div>
      <div className="bg-white border border-gray-200 relative">
        <input
          type="text"
          value={title}
          onChange={handleTitleChange}
          className={`w-full p-3 border-none outline-none text-sm ${validationErrors.title ? 'border-red-500' : ''}`}
          placeholder="クリックしたくなるワードを入れよう！"
          maxLength={40}
        />
        <div className="absolute bottom-1 right-2 text-xs text-gray-400">
          {title.length}/40
        </div>
      </div>
      {validationErrors.title && showErrors && (
        <p className="text-red-500 text-xs mt-1">タイトルを入力してください</p>
      )}
    </div>
  );
};

const RecommendSection = React.memo(({ recommendText, setRecommendText }: RecommendSectionProps) => {
  const handleRecommendTextChange = useCallback((value: string) => {
    setRecommendText(value);
  }, [setRecommendText]);

  return (
    <div className="py-3">
      <div className="px-4 flex flex-col mb-2">
        <p className="text-[#313131] text-[16px] font-bold">おすすめの理由 <span className="text-xs text-gray-400">(任意)</span></p>
      </div>

      <div className="px-4">
        <ReviewInputBox
          className=""
          value={recommendText}
          onChange={handleRecommendTextChange}
        />
      </div>
    </div>
  );
});

const CategorySection = ({ category, subCategory, setCategory, setSubCategory, categoryId, parentCategoryUuid, validationErrors, showErrors }: CategorySectionProps) => {
  console.log('🏷️ [CategorySection] レンダリング:', { category, subCategory, categoryId, parentCategoryUuid });

  return (
    <div className="py-3">
      <div className="px-4 flex flex-col mb-2">
        <p className="text-[#313131] text-[16px] font-bold">サブカテゴリを選択する <span className="text-xs text-red-500">(必須)</span></p>
        <p className="text-[#313131] text-sm mt-1 text-right">カテゴリ：{category}</p>
      </div>
    <div>
      <div>
        {/* parentCategoryUuidが設定されるまでCategorySelectを表示しない */}
        {parentCategoryUuid ? (
          <CategorySelect
            category={category}
            subCategory={subCategory}
            changeCategory={setCategory}
            changeSubCategory={setSubCategory}
            parentCategoryId={parentCategoryUuid}
          />
        ) : (
          <div className="mb-4">
            <div className="flex items-center px-4">
              <input
                type="text"
                value=""
                placeholder="サブカテゴリを選択"
                className="appearance-none border w-full bg-gray-100 py-4 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                readOnly
              />
              <div className="absolute right-4 pointer-events-none pr-[10px]">
                <svg
                  className="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </div>
            </div>
          </div>
        )}
      </div>
      {validationErrors.subCategory && showErrors && (
        <p className="text-red-500 text-xs mt-1">サブカテゴリを選択してください</p>
      )}
    </div>
  </div>
  );
};

/**
 * おすすめ追加ページコンポーネント
 * 新URL: /[username]/add?category=tab1
 */
export default function AddRecommendationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  
  const username = params.username as string;
  
  // URLパラメータから情報を取得
  const categoryParam = searchParams.get('category'); // tab1, tab2 など
  const rankingIdParam = searchParams.get('ranking_id'); // 編集モード用
  const draftIdParam = searchParams.get('draftId'); // 下書き編集用
  
  // Clerk認証を使用
  const { user, isLoaded } = useUser();
  const [userId, setUserId] = useState<string>("");
  const [isUserLoading, setIsUserLoading] = useState(true);

  // Clerk認証チェック
  useEffect(() => {
    const checkUserAuth = async () => {
      console.log('🔧 [AddPage] 認証チェック開始:', {
        isLoaded,
        user: user ? { id: user.id } : null,
        urlUsername: username
      });

      if (!isLoaded) {
        // Clerkがまだ読み込み中
        return;
      }

      if (!user) {
        // ログインしていない場合はsign-inページにリダイレクト
        console.log('❌ [AddPage] 未ログイン - sign-inページにリダイレクト');
        router.push('/sign-in');
        return;
      }

      try {
        // ログインユーザーの情報を取得
        const response = await fetch(`/api/getUser?user_ID=${user.id}`);
        if (response.ok) {
          const userData = await response.json();
          const currentUsername = userData.username;

          console.log('🔧 [AddPage] ユーザー認証チェック:', {
            urlUsername: username,
            currentUsername: currentUsername,
            categoryParam: categoryParam
          });

          // URLのユーザー名と現在のユーザーのユーザー名が一致しない場合は認証エラー
          if (currentUsername && currentUsername !== username) {
            console.log('❌ [AddPage] 認証エラー: 他のユーザーのaddページにアクセス');
            // 他のユーザーのaddページにアクセスしようとした場合はsign-inページにリダイレクト
            router.push('/sign-in');
            return;
          }

          // ユーザー名が取得できない場合もsign-inページにリダイレクト
          if (!currentUsername) {
            console.log('❌ [AddPage] ユーザー名が取得できません');
            router.push('/sign-in');
            return;
          }

          // 認証成功 - ユーザーIDを設定
          setUserId(user.id);
          setIsUserLoading(false);
        } else {
          console.log('❌ [AddPage] ユーザー情報取得エラー');
          router.push('/sign-in');
          return;
        }
      } catch (error) {
        console.error('❌ [AddPage] 認証チェックエラー:', error);
        router.push('/sign-in');
      }
    };

    if (username) {
      checkUserAuth();
    }
  }, [isLoaded, user, username, router, categoryParam]);

  // 画像アップロード処理
  const {
    images,
    setImages,
    handleImageChange,
    handleRemoveImage,
    uploadImages,
    pickFile,
    fileInputRefs,
    showCropper,
    tempImage,
    handleCropComplete,
    handleCropCancel
  } = useImageUpload(userId);
  
  // フォーム処理
  const { 
    title,
    setTitle,
    recommendText,
    setRecommendText,
    category,
    setCategory,
    categoryId,
    setCategoryId,
    subCategory,
    setSubCategory,
    rating,
    setRating,
    hoverRating,
    setHoverRating,
    amazonUrl,
    setAmazonUrl,
    rakutenUrl,
    setRakutenUrl,
    yahooUrl,
    setYahooUrl,
    qoo10Url,
    setQoo10Url,
    officialUrl,
    setOfficialUrl,
    isEditMode,
    setIsEditMode,
    rankingId,
    setRankingId,
    fetchRankingData,
    validationErrors,
    showErrors,
    showSavedModal,
    errorMessage,
    isSubmitting,
    setIsSubmitting,
    submitRanking,
    isFormValid
  } = useRankingForm(userId, uploadImages, setImages);

  // カテゴリ名の状態変数
  const [categoryName, setCategoryName] = useState('');
  // 親カテゴリのUUID（CategorySelectのparent_IDとして使用）
  const [parentCategoryUuid, setParentCategoryUuid] = useState('');

  // カテゴリIDからカテゴリ名を取得する関数
  const findCategoryBySlug = async (categorySlug: string) => {
    try {
      console.log('🔍 [findCategoryBySlug] カテゴリ検索開始:', categorySlug);

      // まずユーザーのカテゴリ一覧を取得
      const response = await fetch(`/api/getCategories?user_ID=${userId}`);
      if (response.ok) {
        const categories = await response.json();
        console.log('📋 [findCategoryBySlug] 取得したカテゴリ一覧:', categories);

        // category_slug, category_ID, またはidでマッチするカテゴリを検索
        const category = categories.find((cat: any) =>
          cat.category_slug === categorySlug ||
          cat.category_ID === categorySlug ||
          cat.id === categorySlug ||
          String(cat.id) === categorySlug
        );

        console.log('🎯 [findCategoryBySlug] 検索結果:', category);

        return category ? {
          id: category.id,
          category_ID: category.category_ID,
          name: category.category_name
        } : null;
      }
    } catch (error) {
      console.error('❌ [findCategoryBySlug] カテゴリ取得エラー:', error);
    }
    return null;
  };

  // 下書きデータを読み込む関数
  const fetchDraftData = async (draftId: string) => {
    try {
      if (!userId) return;

      const response = await fetch(`/api/getDraft?ranking_ID=${draftId}&user_ID=${userId}`);

      if (response.ok) {
        const result = await response.json();

        if (result.success) {
          const draftData = result.data;

          setTitle(draftData.title || '');
          setRecommendText(draftData.description || '');
          setRating(draftData.rating || 1);
          setImages(draftData.images || []);
          setAmazonUrl(draftData.amazonUrl || '');
          setRakutenUrl(draftData.rakutenUrl || '');
          setYahooUrl(draftData.yahooUrl || '');
          setQoo10Url(draftData.qoo10Url || '');
          setOfficialUrl(draftData.officialUrl || '');
          setSubCategory(draftData.subCategory || '');

          // カテゴリ情報を復元
          if (draftData.category) {
            setCategory(draftData.category);
            setCategoryName(draftData.category);
          }
          if (draftData.categoryId) {
            setCategoryId(draftData.categoryId);
            // parentCategoryUuidも設定（CategorySelectが動作するように）
            findCategoryBySlug(draftData.categoryId).then(categoryData => {
              if (categoryData) {
                setParentCategoryUuid(categoryData.id);
              }
            });
          }



          setIsEditMode(true);
          setRankingId(draftId);
        }
      }
    } catch (error) {
      console.error('下書きデータの取得に失敗しました:', error);
    }
  };

  // 初期化処理
  useEffect(() => {
    if (!userId) return;

    console.log('🚀 [初期化処理] 開始:', { userId, draftIdParam, rankingIdParam, categoryParam });

    // 下書き編集モード
    if (draftIdParam) {
      console.log('📝 [初期化処理] 下書き編集モード');
      fetchDraftData(draftIdParam);
    }
    // ランキング編集モード
    else if (rankingIdParam) {
      console.log('✏️ [初期化処理] ランキング編集モード');
      setIsEditMode(true);
      setRankingId(rankingIdParam);
      fetchRankingData(rankingIdParam);
    }

    // カテゴリパラメータがあれば設定
    if (categoryParam) {
      console.log('🏷️ [初期化処理] カテゴリパラメータ設定:', categoryParam);
      setCategoryId(categoryParam);
    }
  }, [userId, draftIdParam, rankingIdParam, categoryParam, fetchRankingData, setIsEditMode, setRankingId, setCategoryId]);
  
  // カテゴリ名を取得
  useEffect(() => {
    if (categoryId && userId) {
      console.log('🔄 [カテゴリ名取得] 開始:', { categoryId, categoryName, userId });

      findCategoryBySlug(categoryId).then(categoryData => {
        if (categoryData) {
          console.log('✅ [カテゴリ名取得] 成功:', categoryData);

          setCategoryName(categoryData.name);
          setCategory(categoryData.name);
          setParentCategoryUuid(categoryData.id); // CategorySelectのparent_IDとして使用するUUID

          // categoryIdが数値IDやUUIDの場合は、category_IDに更新
          if (categoryData.category_ID && categoryData.category_ID !== categoryId) {
            setCategoryId(categoryData.category_ID);
          }
        } else {
          console.log('❌ [カテゴリ名取得] カテゴリが見つかりません:', categoryId);
        }
      }).catch(error => {
        console.error('❌ [カテゴリ名取得] エラー:', error);
      });
    }
  }, [categoryId, userId, setCategory, setCategoryId]);
  
  // カテゴリ変更ハンドラー
  const handleCategoryChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setCategory(e.target.value);
  }, [setCategory]);
  
  // サブカテゴリ変更ハンドラー
  const handleSubCategoryChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSubCategory(e.target.value);
  }, [setSubCategory]);

  // 下書き保存用のローディング状態
  const [isDraftSaving, setIsDraftSaving] = useState(false);

  // サブカテゴリ名またはIDからIDを取得する関数
  const getSubCategoryId = async (subCategoryValue: string, parentId: string) => {
    try {
      const response = await fetch(`/api/getSubCategories?user_ID=${userId}&parent_ID=${parentId}`);
      if (response.ok) {
        const subCategories = await response.json();

        // まずIDで検索
        let subCategory = subCategories.find((sub: any) => sub.id === subCategoryValue);

        // IDで見つからない場合は名前で検索
        if (!subCategory) {
          subCategory = subCategories.find((sub: any) => sub.category_name === subCategoryValue);
        }

        return subCategory ? subCategory.id : null;
      }
    } catch (error) {
      console.error('サブカテゴリID取得エラー:', error);
    }
    return null;
  };

  // 下書き保存処理（必須項目チェックなし）
  const saveDraft = async () => {
    setIsDraftSaving(true);

    try {
      // サブカテゴリIDを取得
      let subCategoryId = null;
      if (subCategory && parentCategoryUuid) {
        subCategoryId = await getSubCategoryId(subCategory, parentCategoryUuid);
      }

      const draftData = {
        user_ID: userId,
        title,
        description: recommendText,
        category,
        categoryId,
        categoryParam, // URLのcategoryパラメータも保存
        subCategory,
        subCategoryId, // サブカテゴリのIDを追加
        rating,
        amazonUrl,
        rakutenUrl,
        yahooUrl,
        qoo10Url,
        officialUrl,
        images: images.filter(img => img) // 空の画像を除外
      };

      const response = await fetch('/api/saveDraft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(draftData),
      });

      if (response.ok) {
        // 下書き保存成功 - アラートは削除
      } else {
        console.error('下書き保存エラー');
      }
    } catch (error) {
      console.error('下書き保存エラー:', error);
    } finally {
      setIsDraftSaving(false);
    }
  };

  // 下書き一覧ページへの遷移
  const goToDraftList = () => {
    if (username) {
      router.push(`/${username}/draftList`);
    }
  };

  if (isUserLoading) {
    return (
      <div className="max-w-[500px] mx-auto flex flex-col min-h-screen bg-white overflow-y-auto">
        <HeaderItems text="おすすめを追加する" />
        <div className="flex-1 flex items-center justify-center">
          <CatLoadingAnimation />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-[500px] mx-auto flex-col min-h-screen items-center relative bg-white">
      {/* ヘッダー */}
      <HeaderItems
        text={isEditMode && !draftIdParam ? "おすすめを編集する" : "おすすめを追加する"}
      />
      
      {/* 画像アップロードセクション */}
      <ImageUploadSection
        images={images}
        handleImageChange={handleImageChange}
        handleRemoveImage={handleRemoveImage}
        pickFile={pickFile}
        fileInputRefs={fileInputRefs}
        showCropper={showCropper}
        tempImage={tempImage}
        handleCropComplete={handleCropComplete}
        handleCropCancel={handleCropCancel}
      />
      
      <div className="flex-1 flex flex-col space-y-4 pb-24">
        {/* タイトルセクション */}
        <TitleSection 
          title={title} 
          setTitle={setTitle} 
          validationErrors={validationErrors} 
          showErrors={showErrors} 
        />
        
        {/* おすすめの理由セクション */}
        <RecommendSection 
          recommendText={recommendText} 
          setRecommendText={setRecommendText} 
        />
        
        {/* おすすめ度セクション */}
        <RatingSection 
          rating={rating} 
          setRating={setRating} 
          hoverRating={hoverRating} 
          setHoverRating={setHoverRating} 
        />
        
        {/* カテゴリセクション */}
        <CategorySection
          category={category}
          subCategory={subCategory}
          setCategory={handleCategoryChange}
          setSubCategory={handleSubCategoryChange}
          categoryId={categoryId}
          parentCategoryUuid={parentCategoryUuid}
          validationErrors={validationErrors}
          showErrors={showErrors}
        />
        
        {/* 商品リンクセクション */}
        <LinkSection
          amazonUrl={amazonUrl}
          setAmazonUrl={setAmazonUrl}
          rakutenUrl={rakutenUrl}
          setRakutenUrl={setRakutenUrl}
          yahooUrl={yahooUrl}
          setYahooUrl={setYahooUrl}
          qoo10Url={qoo10Url}
          setQoo10Url={setQoo10Url}
          officialUrl={officialUrl}
          setOfficialUrl={setOfficialUrl}
        />
      </div>

      {/* Footer */}
      <Footer className="pb-[80px]" />

      {/* フッターボタン */}
      <RankingRegisterButtonFooter
        submitRanking={() => submitRanking()}
        images={images}
        description={recommendText}
        userId={userId}
        title={title}
        isEditMode={isEditMode && !draftIdParam}
        category={category}
        categoryId={categoryId}
        subCategory={subCategory}
        rating={rating}
        amazonUrl={amazonUrl}
        rakutenUrl={rakutenUrl}
        yahooUrl={yahooUrl}
        qoo10Url={qoo10Url}
        officialUrl={officialUrl}
        draftId={draftIdParam || ''}
        onSaveDraft={saveDraft}
        onDraftList={goToDraftList}
      />
      
      {/* ローディングアニメーション */}
      <RankingRegisterLoadingAnimation
        isVisible={isSubmitting}
        isEditMode={isEditMode}
        onComplete={() => {
          setIsSubmitting(false);
          // 新しいURL形式でリダイレクト
          router.replace(`/${username}`);
        }}
      />
      
      {/* エラーメッセージ */}
      {errorMessage && (
        <div className="fixed bottom-20 left-0 right-0 mx-auto w-4/5 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {errorMessage}
        </div>
      )}

      {/* 下書き保存ローディングアニメーション */}
      {isDraftSaving && (
        <RankingRegisterLoadingAnimation
          isVisible={isDraftSaving}
          onComplete={() => setIsDraftSaving(false)}
          isEditMode={false}
          isDraftSaving={true}
        />
      )}
    </div>
  );
}
