import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;
    
    if (!categoryId) {
      return NextResponse.json(
        { success: false, error: 'カテゴリIDが必要です' },
        { status: 400 }
      );
    }
    
    console.log('カテゴリ取得を開始します。カテゴリID:', categoryId);
    
    // データベースからカテゴリを取得
    const category = await prisma.category.findFirst({
      where: {
        category_ID: categoryId,
      },
      select: {
        id: true,
        category_ID: true,
        category_name: true,
        user_ID: true,
        parent_ID: true,
        order: true,
        created_at: true,
        updated_at: true,
      },
    });
    
    if (!category) {
      return NextResponse.json(
        { success: false, error: '指定されたカテゴリが見つかりません' },
        { status: 404 }
      );
    }
    
    console.log('カテゴリが見つかりました:', category.category_name);
    
    // フロントエンド用の形式に変換
    const formattedCategory = {
      id: category.category_ID,
      name: category.category_name,
      userId: category.user_ID,
      username: category.username,
      parentId: category.parent_ID,
      order: category.order,
      createdAt: category.created_at.toISOString(),
      updatedAt: category.updated_at.toISOString(),
    };
    
    // 成功した場合のレスポンスを返す
    return NextResponse.json(
      { 
        success: true, 
        data: formattedCategory,
        name: category.category_name // 下位互換性のため
      },
      {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (error) {
    console.error('カテゴリ取得中にエラーが発生しました:', error);
    return NextResponse.json(
      { success: false, error: 'カテゴリの取得に失敗しました' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  // プリフライトリクエストに対するレスポンス
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}
