import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { convertToSlug } from '@/utils/slugUtils';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// category_IDとcategory_slugを生成する関数
async function generateCategoryId(user_ID: string, parent_ID?: string | null, name?: string, categoryId?: string): Promise<string> {
  // サブカテゴリの場合は既存のロジックを使用
  if (parent_ID) {
    return `subcat_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
  }

  // メインカテゴリでnameが指定されている場合はslugを生成
  if (!parent_ID && name) {
    console.log('🔧 [createCategory] スラッグ生成処理:', {
      name: name,
      categoryId: categoryId,
      hasJapanese: /[ぁ-んァ-ンー一-龥]/.test(name)
    });

    // 新しいスラッグ変換関数を使用
    let baseSlug = await convertToSlug(name);

    // 重複チェック：同じuser_IDで完全に同じbaseSlugを持つ他のカテゴリがあるかチェック
    const exactDuplicateCategories = await prisma.category.findMany({
      where: {
        user_ID,
        parent_ID: null,
        category_slug: baseSlug, // 完全一致のみチェック
        // 更新の場合は自分自身を除外
        ...(categoryId ? { NOT: { id: categoryId } } : {})
      }
    });

    console.log('🔧 [createCategory] 重複チェック結果:', {
      baseSlug: baseSlug,
      exactDuplicateCount: exactDuplicateCategories.length,
      exactDuplicateCategories: exactDuplicateCategories.map(cat => ({
        id: cat.id,
        name: cat.category_name,
        slug: cat.category_slug
      }))
    });

    // 完全に同じスラッグの重複がない場合は純粋なスラッグを返す
    if (exactDuplicateCategories.length === 0) {
      console.log('🔧 [createCategory] 重複なし - 純粋なスラッグを使用:', baseSlug);
      return baseSlug;
    }

    // 重複がある場合の処理
    if (categoryId) {
      // カテゴリIDが提供されている場合（更新時）、ハイブリッド方式を使用
      const idSuffix = categoryId.replace(/-/g, '').slice(-6).toLowerCase();
      const hybridSlug = `${baseSlug}-${idSuffix}`;

      console.log('🔧 [createCategory] 重複あり - ハイブリッドスラッグ生成:', {
        original: name,
        baseSlug: baseSlug,
        categoryId: categoryId,
        idSuffix: idSuffix,
        hybridSlug: hybridSlug
      });

      return hybridSlug;
    } else {
      // カテゴリIDが提供されていない場合（新規作成時）は連番方式
      console.log('🔧 [createCategory] 重複あり - 連番方式でスラッグ生成:', {
        original: name,
        baseSlug: baseSlug
      });

      let cnt = 1;
      let suffix = '';
      while (await prisma.category.findFirst({
        where: {
          user_ID,
          parent_ID: null,
          category_slug: baseSlug + suffix
        }
      })) {
        cnt++;
        suffix = `-${cnt}`;
      }

      const finalSlug = baseSlug + suffix;
      console.log('🔧 [createCategory] 最終スラッグ:', {
        baseSlug: baseSlug,
        suffix: suffix,
        finalSlug: finalSlug
      });

      return finalSlug;
    }
  }

  // nameが指定されていない場合は従来のtabN形式を使用
  const existingCategories = await prisma.category.findMany({
    where: {
      user_ID,
      parent_ID: null,
      category_ID: {
        startsWith: 'tab'
      }
    },
    select: { category_ID: true }
  });

  // tab1, tab2, ... の形式から番号を抽出して最大値を取得
  let maxTabNumber = 0;
  existingCategories.forEach(cat => {
    const match = cat.category_ID?.match(/^tab(\d+)$/);
    if (match) {
      const num = parseInt(match[1], 10);
      if (num > maxTabNumber) {
        maxTabNumber = num;
      }
    }
  });

  const nextTabNumber = maxTabNumber + 1;
  return `tab${nextTabNumber}`;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { category_name, user_ID } = body;

    // カテゴリ作成リクエストログを削除

    // 必須パラメータのチェック
    if (!category_name || !user_ID) {
      return NextResponse.json(
        { error: 'category_nameとuser_IDは必須です' },
        { status: 400 }
      );
    }

    // ユーザーの存在確認
    const user = await prisma.user.findUnique({
      where: { user_ID }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'ユーザーが見つかりません' },
        { status: 404 }
      );
    }

    // 現在のユーザーのカテゴリ数を取得してorder値を決定
    const existingCategories = await prisma.category.findMany({
      where: {
        user_ID,
        parent_ID: null // トップレベルカテゴリのみ
      },
      orderBy: { order: 'desc' },
      take: 1
    });

    const nextOrder = existingCategories.length > 0
      ? (existingCategories[0].order || 0) + 1
      : 1;

    // category_IDとcategory_slugを生成
    const categoryId = await generateCategoryId(user_ID, null, category_name);

    // 新しいカテゴリを作成
    const newCategory = await prisma.category.create({
      data: {
        category_name,
        user_ID,
        parent_ID: null, // トップレベルカテゴリ
        category_ID: categoryId,
        category_slug: categoryId, // slugも設定
        order: nextOrder,
      },
      include: {
        Children: true // サブカテゴリも含める
      }
    });

    // カテゴリ作成成功ログを削除

    // レスポンス形式を統一（subcategoriesフィールドを追加）
    const responseCategory = {
      id: newCategory.id,
      category_name: newCategory.category_name,
      user_ID: newCategory.user_ID,
      domain: newCategory.domain,
      parent_ID: newCategory.parent_ID,
      subcategories: newCategory.Children.map(child => ({
        id: child.id,
        category_name: child.category_name,
        user_ID: child.user_ID,
        domain: child.domain,
        parent_ID: child.parent_ID
      }))
    };

    return NextResponse.json(responseCategory, { status: 201 });

  } catch (error) {
    console.error('カテゴリ作成エラー:', error);
    return NextResponse.json(
      { 
        error: 'カテゴリの作成に失敗しました',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('Prisma disconnect error:', disconnectError);
    }
  }
}
