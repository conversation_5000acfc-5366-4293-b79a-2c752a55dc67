"use client"; // ← 追加（Next.jsのServer Componentエラー対策）

import React, { useState, useEffect } from "react";

interface Props {
  initialValue?: string;
  onSave?: (value: string) => void;
}

export const SelfIntroduction = ({ initialValue = "", onSave }: Props): JSX.Element => {
  const [inputValue, setInputValue] = useState(initialValue);

  // 初期値が変更された場合に更新
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  };

  const handleBlur = () => {
    // フォーカスが外れた時点で保存関数を呼び出す
    if (onSave) {
      console.log("自己紹介文を保存します:", inputValue);
      onSave(inputValue);
    }
  };

  // Enterキーを押したときにも保存する
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // デフォルトの改行を防止
      if (onSave) {
        console.log("Enterキーで自己紹介文を保存します:", inputValue);
        onSave(inputValue);
      }
    }
  };

  return (
    <div className="flex max-w-[500px] h-[140px] items-start p-[16px] relative bg-white border-b [border-bottom-style:solid] border-line">
      <textarea
        className={`w-full h-full text-[14px] tracking-[0] leading-[16.8px] bg-transparent outline-none resize-none
          ${inputValue ? "text-black" : "text-font-gray"}
          focus:caret-black p-1`}
        value={inputValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        placeholder="ここに自己紹介文を入力できます"
      />
    </div>
  );
};
