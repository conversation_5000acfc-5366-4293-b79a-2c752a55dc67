'use client';

import React from 'react';
import { usePathname } from 'next/navigation';

interface HeaderProps {
  className?: string;
  title?: string;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({ 
  className = '', 
  title = 'mypicks.best', 
  showBackButton = true 
}) => {
  const pathname = usePathname();
  
  // 戻るボタンのハンドラー
  const handleBack = () => {
    window.history.back();
  };

  // パスからページタイトルを取得する関数
  const getPageTitleFromPath = (path: string): string => {
    // ガイドページのタイトルマッピング
    const guideTitles: {[key: string]: string} = {
      '/guide/profile-edit': 'プロフィールを設定',
      '/guide/sns-link': 'SNSリンクを追加',
      '/guide/create-ranking': 'おすすめを追加',
      '/guide/change-order': 'おすすめの表示順位を変更',
      '/guide/change-category': 'カテゴリを変更・追加・削除',
      '/guide/change-display': 'カテゴリの表示項目を変更',
      '/guide/share-profile': 'プロフィールを共有',
      '/guide/share-ranking': 'おすすめグを共有',
      '/guide/save-ranking': 'おすすめを保存',
      '/guide/view-saved': '保存したおすすめを閲覧',
      '/guide/buy-card': '購入する',
      '/guide/change-password': 'パスワードの変更',
      '/guide/change-email': 'メールアドレスの変更',
    };

    return guideTitles[path] || title;
  };

  // 現在のパスに基づいてタイトルを決定
  const pageTitle = title === 'myrank' ? getPageTitleFromPath(pathname) : title;

  return (
    <div className={`flex justify-between max-w-[500px] h-[52px] items-center relative shadow-[0px_0px_4px_#00000040] w-full px-4 ${className}`}>
      <div className="flex items-center gap-[8px]">
        {showBackButton && (
          <button 
            onClick={handleBack}
            className="flex items-center justify-center"
            aria-label="戻る"
          >
            <img src="/static/img/imageleftarrow.png" alt="戻る" width={24} height={24} />
          </button>
        )}
        <span className="text-[16px] font-medium text-[#313131]">{pageTitle}</span>
      </div>
    </div>
  );
};

export default Header;
