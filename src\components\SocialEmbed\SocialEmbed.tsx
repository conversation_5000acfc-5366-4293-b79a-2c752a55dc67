'use client';

import React, { useEffect, useRef } from 'react';
import Script from 'next/script';
import { processInstagramEmbeds, cleanInstagramUrl } from '../../utils/instagramEmbedUtils';
import './SocialEmbed.css';



interface SocialEmbedProps {
  html: string;
  platform: 'instagram' | 'tiktok' | 'twitter' | 'x' | 'youtube';
}

/**
 * YouTubeのURL形式を検証し、動画IDを抽出する
 * @param url - 検証するURL
 * @returns 有効な場合は動画ID（11文字）、無効な場合はnull
 */
export const validateYouTubeURL = (url: string): string | null => {
  if (!url || typeof url !== 'string') {
    return null;
  }

  // URLを正規化（前後の空白を削除）
  const cleanUrl = url.trim();

  // 除外するURL形式をチェック
  const excludePatterns = [
    /youtube\.com\/channel\//,
    /youtube\.com\/playlist\?/,
    /youtube\.com\/shorts\//,
    /youtube\.com\/user\//,
    /youtube\.com\/c\//,
    /youtube\.com\/@/
  ];

  for (const pattern of excludePatterns) {
    if (pattern.test(cleanUrl)) {
      return null;
    }
  }

  // 対応するURL形式のパターン
  const patterns = [
    // https://www.youtube.com/watch?v=動画ID
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})(?:&.*)?$/,
    // https://youtu.be/動画ID
    /(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})(?:\?.*)?$/,
    // https://www.youtube.com/embed/動画ID
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})(?:\?.*)?$/
  ];

  for (const pattern of patterns) {
    const match = cleanUrl.match(pattern);
    if (match && match[1]) {
      const videoId = match[1];
      // 動画IDが正確に11文字かチェック
      if (videoId.length === 11) {
        return videoId;
      }
    }
  }

  return null;
};

/**
 * YouTube埋め込み用HTMLを生成する関数
 * @returns 埋め込み用HTML
 */
export const generateYouTubeEmbedHTML = (): string => {
  // 即座にローディングアニメーションを表示するHTMLを生成（重複処理を防ぐため簡素化）
  return `<div class="social-embed-loading youtube-loading">
    <div class="youtube-loading-subtitle">動画を読み込み中...</div>
  </div>`;
};

/**
 * X（旧Twitter）のURL形式を検証し、投稿ID（ステータスID）を抽出する
 * @param url - 検証するURL
 * @returns 有効な場合は投稿ID（10-25桁）、無効な場合はnull
 */
export const validateXURL = (url: string): string | null => {
  if (!url || typeof url !== 'string') {
    return null;
  }

  // URLを正規化（前後の空白を削除）
  const cleanUrl = url.trim();

  // 除外するURL形式をチェック
  const excludePatterns = [
    // プロフィールページ（/status/が含まれない）
    /^(?:https?:\/\/)?(?:www\.)?(twitter\.com|x\.com)\/[a-zA-Z0-9_]{1,15}\/?(?:\?.*)?$/,
    // リスト
    /\/i\/lists\//,
    // コミュニティ
    /\/i\/communities\//,
    // 検索
    /\/search\?/,
    // 設定
    /\/settings\//,
    // その他の特殊ページ
    /\/i\/flow\//,
    /\/i\/bookmarks/,
    /\/i\/moments\//,
    /\/notifications/,
    /\/messages/,
    /\/home/
  ];

  for (const pattern of excludePatterns) {
    if (pattern.test(cleanUrl)) {
      return null;
    }
  }

  // 対応するURL形式のパターン
  // https://x.com/ユーザー名/status/ステータスID または https://twitter.com/ユーザー名/status/ステータスID
  const statusPattern = /^(?:https?:\/\/)?(?:www\.)?(twitter\.com|x\.com)\/([a-zA-Z0-9_]{1,15})\/status\/(\d{10,25})(?:\?.*)?$/;

  const match = cleanUrl.match(statusPattern);
  if (match && match[3]) {
    const statusId = match[3];
    const username = match[2];

    // ユーザー名の長さチェック（1-15文字）
    if (username.length >= 1 && username.length <= 15) {
      // ステータスIDの長さチェック（10-25桁）
      if (statusId.length >= 10 && statusId.length <= 25) {
        return statusId;
      }
    }
  }

  return null;
};

/**
 * X（旧Twitter）の投稿IDから埋め込み用HTMLを生成する
 * @param statusId - X投稿ID（10-25桁）
 * @param originalUrl - 元のURL（埋め込み用）
 * @returns 埋め込み用HTML
 */
export const generateXEmbedHTML = (_statusId: string, originalUrl: string): string => {
  return `<blockquote class="twitter-tweet" data-dnt="true" data-theme="light">
    <a href="${originalUrl}"></a>
  </blockquote>`;
};

/**
 * InstagramのURL形式を検証し、投稿IDを抽出する
 * @param url - 検証するURL
 * @returns 有効な場合は投稿ID、無効な場合はnull
 */
export const validateInstagramURL = (url: string): string | null => {
  if (!url || typeof url !== 'string') {
    return null;
  }

  // URLを正規化（前後の空白を削除）
  const cleanUrl = url.trim();

  // 除外するURL形式をチェック
  const excludePatterns = [
    // プロフィールページ（/p/や/reel/が含まれない）
    /^(?:https?:\/\/)?(?:www\.)?instagram\.com\/[a-zA-Z0-9_.]+\/?(?:\?.*)?$/,
    // ストーリー
    /\/stories\//,
    // ハッシュタグページ
    /\/explore\/tags\//,
    // 場所ページ
    /\/explore\/locations\//,
    // リール一覧ページ
    /\/reels\/?$/,
    // その他の特殊ページ
    /\/accounts\//,
    /\/direct\//,
    /\/explore\/?$/,
    /\/tv\//
  ];

  for (const pattern of excludePatterns) {
    if (pattern.test(cleanUrl)) {
      return null;
    }
  }

  // 対応するURL形式のパターン
  // https://www.instagram.com/p/投稿ID/ または https://www.instagram.com/reel/リールID/
  const postPattern = /^(?:https?:\/\/)?(?:www\.)?instagram\.com\/(p|reel)\/([a-zA-Z0-9_-]+)\/?(?:\?.*)?$/;

  // URLをクリーンアップ（UTMパラメータなどを除去）
  const cleanedUrl = cleanInstagramUrl(cleanUrl);
  const match = cleanedUrl.match(postPattern);
  if (match && match[2]) {
    const postId = match[2];
    const postType = match[1]; // 'p' または 'reel'

    // 投稿IDの長さチェック（通常11文字程度だが、幅を持たせる）
    if (postId.length >= 8 && postId.length <= 20) {
      return postId;
    }
  }

  return null;
};

/**
 * Instagram投稿IDから埋め込み用HTMLを生成する
 * @param postId - Instagram投稿ID
 * @param originalUrl - 元のURL（埋め込み用）
 * @returns 埋め込み用HTML
 */
export const generateInstagramEmbedHTML = (postId: string, originalUrl: string): string => {
  // URLをクリーンアップしてから使用
  const cleanUrl = cleanInstagramUrl(originalUrl);
  return `<blockquote class="instagram-media" data-instgrm-permalink="${cleanUrl}" data-instgrm-version="14">
    <a href="${cleanUrl}"></a>
  </blockquote>`;
};

/**
 * TikTokのURL形式を検証し、動画ID（item_id）を抽出する
 * @param url - 検証するURL
 * @returns 有効な場合は動画ID、無効な場合はnull
 */
export const validateTikTokURL = (url: string): string | null => {
  if (!url || typeof url !== 'string') {
    return null;
  }

  // URLを正規化（前後の空白を削除）
  const cleanUrl = url.trim();

  // 除外するURL形式をチェック
  const excludePatterns = [
    // プロフィールページ（/video/が含まれない）
    /^(?:https?:\/\/)?(?:www\.|m\.)?tiktok\.com\/@[\w.-]+\/?(?:\?.*)?$/,
    // ハッシュタグページ
    /\/tag\//,
    // 音楽ページ
    /\/music\//,
    // おすすめページ
    /\/foryou/,
    // 検索ページ
    /\/search\?/,
    // その他の特殊ページ
    /\/following/,
    /\/live/,
    /\/trending/
  ];

  for (const pattern of excludePatterns) {
    if (pattern.test(cleanUrl)) {
      return null;
    }
  }

  // 対応するURL形式のパターン
  const patterns = [
    // https://www.tiktok.com/@ユーザー名/video/動画ID
    /^(?:https?:\/\/)?(?:www\.|m\.)?tiktok\.com\/@[\w.-]+\/video\/(\d+)(?:\?.*)?$/,
    // https://m.tiktok.com/v/動画ID.html
    /^(?:https?:\/\/)?m\.tiktok\.com\/v\/(\d+)\.html(?:\?.*)?$/,
    // https://www.tiktok.com/embed/動画ID
    /^(?:https?:\/\/)?(?:www\.)?tiktok\.com\/embed\/(\d+)(?:\?.*)?$/,
    // https://vm.tiktok.com/短縮コード/ (短縮URLの場合は短縮コードを返す)
    /^(?:https?:\/\/)?vm\.tiktok\.com\/([\w\d]+)\/?(?:\?.*)?$/,
    // https://vt.tiktok.com/短縮コード/ (TikTok公式短縮URL)
    /^(?:https?:\/\/)?vt\.tiktok\.com\/([\w\d]+)\/?(?:\?.*)?$/
  ];

  for (const pattern of patterns) {
    const match = cleanUrl.match(pattern);
    if (match && match[1]) {
      const videoId = match[1];
      // 動画IDは通常19桁の数字だが、短縮コードの場合は英数字の組み合わせ
      if (/^\d+$/.test(videoId) || /^[\w\d]+$/.test(videoId)) {
        return videoId;
      }
    }
  }

  return null;
};

/**
 * TikTok埋め込み用HTMLを生成する関数
 * @returns 埋め込み用HTML
 */
export const generateTikTokEmbedHTML = (): string => {
  // 即座にローディングアニメーションを表示するHTMLを生成（重複処理を防ぐため簡素化）
  return `<div class="social-embed-loading tiktok-loading">
    <div class="tiktok-loading-subtitle">動画を読み込み中...</div>
  </div>`;
};

/**
 * SNS投稿の埋め込みを表示するコンポーネント
 */
const SocialEmbed: React.FC<SocialEmbedProps> = ({ html, platform }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // プラットフォームに応じたスクリプト処理
    if (html && containerRef.current) {
      if (platform === 'instagram') {
        // 安全なInstagram埋め込み処理
        processInstagramEmbeds(containerRef.current);
      } else if ((platform === 'twitter' || platform === 'x') && window.twttr) {
        window.twttr.widgets.load(containerRef.current);
      } else if (platform === 'tiktok' && window.TikTok) {
        window.TikTok.embed.reload();
      }
    }
  }, [html, platform]);

  return (
    <div className="social-embed-container">
      <div
        ref={containerRef}
        className={`social-embed-content social-embed-${platform}`}
        dangerouslySetInnerHTML={{ __html: html }}
      />
      
      {/* プラットフォームに応じたスクリプトを読み込む（埋め込みHTMLが存在する場合のみ） */}
      {platform === 'instagram' && html && html.includes('instagram-media') && (
        <Script
          src="//www.instagram.com/embed.js"
          strategy="lazyOnload"
        />
      )}

      {(platform === 'twitter' || platform === 'x') && html && html.includes('twitter-tweet') && (
        <Script
          src="https://platform.twitter.com/widgets.js"
          strategy="lazyOnload"
        />
      )}

      {platform === 'tiktok' && html && html.includes('tiktok-embed') && (
        <Script
          src="https://www.tiktok.com/embed.js"
          strategy="lazyOnload"
        />
      )}
    </div>
  );
};

export default SocialEmbed;
