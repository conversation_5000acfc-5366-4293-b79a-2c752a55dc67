/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

// 商品リンクの型定義
interface ProductLinks {
  amazon?: string;
  rakuten?: string;
  yahoo?: string;
  qoo10?: string;
  official?: string;
}

interface Props {
  className?: any;
  url: string;
  rankingData?: {
    id: string;
    title: string;
    description: string;
    imageUrl: string;
    rating: number;
  };
  productLinks?: ProductLinks;
}

export const MypageReleaseButtonFooter　= ({
  className,
  url,
  rankingData,
  productLinks
}: Props): JSX.Element => {
  const router = useRouter();
  const [showModal, setShowModal] = useState(false);
  const [showProductLinksModal, setShowProductLinksModal] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const productLinksModalRef = useRef<HTMLDivElement>(null);
  
  // コンポーネントがマウントされた時に保存状態をチェック
  useEffect(() => {
    if (rankingData) {
      // ローカルストレージから保存したランキングを取得
      const savedRankings = JSON.parse(localStorage.getItem('savedRankings') || '[]');
      // 現在のランキングが保存済みかチェック
      const isAlreadySaved = savedRankings.some((item: any) => item.id === rankingData.id);
      setIsSaved(isAlreadySaved);
    }
  }, [rankingData]);

  // productLinksの変化を監視（デバッグログは削除）
  useEffect(() => {
    // 商品リンクの変化を監視
  }, [productLinks]);

  // Web Share API を使って共有する関数
  const handleShare = async () => {
    // クライアントサイドでのみ実行されることを確認
    if (typeof window === 'undefined') {
      return;
    }
    
    try {
      if (navigator.share) {
        await navigator.share({
          title: document.title,
          url: window.location.href
        });
        // コンテンツが共有されました
      } else {
        // 共有機能に対応していない場合は何もしない
      }
    } catch (error) {
      // 共有エラー
    }
  };

  // ランキングを保存する関数
  const saveRanking = () => {
    if (!rankingData) return;
    
    try {
      // ローカルストレージから保存したランキングを取得
      const savedRankings = JSON.parse(localStorage.getItem('savedRankings') || '[]');
      
      // 既に保存されている場合は削除
      if (isSaved) {
        const updatedRankings = savedRankings.filter((item: any) => item.id !== rankingData.id);
        localStorage.setItem('savedRankings', JSON.stringify(updatedRankings));
        setIsSaved(false);
        alert('ランキングの保存を解除しました');
      } else {
        // 新しいランキングを追加
        const newSavedRanking = {
          ...rankingData,
          savedAt: new Date().toISOString()
        };
        
        const updatedRankings = [...savedRankings, newSavedRanking];
        localStorage.setItem('savedRankings', JSON.stringify(updatedRankings));
        setIsSaved(true);
        alert('ランキングを保存しました');
      }
    } catch (error) {
      console.error('ランキングの保存に失敗しました:', error);
    }
  };

  // 保存したランキングページに遷移する関数
  const handleBookmarkClick = () => {
    if (rankingData) {
      // ランキングデータがある場合は保存/削除処理
      saveRanking();
    } else {
      // ランキングデータがない場合は保存ページに遷移
      router.push('/saveRank');
    }
  };

  // モーダルの外側をクリックしたときにモーダルを閉じる
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setShowModal(false);
      }
      if (productLinksModalRef.current && !productLinksModalRef.current.contains(event.target as Node)) {
        setShowProductLinksModal(false);
      }
    };

    if (showModal || showProductLinksModal) {
      document.addEventListener('mousedown', handleClickOutside as EventListener);
      document.addEventListener('touchstart', handleClickOutside as EventListener);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside as EventListener);
      document.removeEventListener('touchstart', handleClickOutside as EventListener);
    };
  }, [showModal, showProductLinksModal]);

  // モーダル状態の変化を監視
  useEffect(() => {
    // モーダル状態の変化を監視（ログ削除）
  }, [showProductLinksModal]);

  // 商品リンクの数を取得
  const getProductLinksCount = () => {
    if (!productLinks) {
      return 0;
    }

    const validLinks = Object.values(productLinks).filter(link => link && link.trim() !== '');
    return validLinks.length;
  };

  // 詳細ページへのリンク（商品リンクモーダル表示）
  const handleDetailClick = (e?: React.MouseEvent) => {
    // イベントの伝播を防止
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    const linksCount = getProductLinksCount();

    if (linksCount === 0) {
      // リンクがない場合は何もしない
      return;
    } else if (linksCount === 1) {
      // 1つのリンクのみの場合は直接開く
      const singleLink = Object.values(productLinks || {}).find(link => link && link.trim() !== '');
      if (singleLink) {
        window.open(singleLink, '_blank', 'noopener,noreferrer');
      }
    } else {
      // 複数リンクがある場合はモーダルを表示
      setShowProductLinksModal(true);
    }
  };

  // コピーボタンのクリックハンドラー
  const handleCopyClick = () => {
    if (typeof window === 'undefined') return;

    navigator.clipboard.writeText(window.location.href)
      .then(() => {
        setShowModal(false);
      })
      .catch(() => {
        // コピーエラーは無視
        setShowModal(false);
      });
  };

  // Twitterでシェアするハンドラー
  const handleTwitterShare = () => {
    if (typeof window === 'undefined') return;
    
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(document.title)}&url=${encodeURIComponent(window.location.href)}`;
    window.open(twitterUrl, '_blank');
    setShowModal(false);
  };

  // Facebookでシェアするハンドラー
  const handleFacebookShare = () => {
    if (typeof window === 'undefined') return;
    
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`;
    window.open(facebookUrl, '_blank');
    setShowModal(false);
  };

  // LINEでシェアするハンドラー
  const handleLineShare = () => {
    if (typeof window === 'undefined') return;
    
    const lineUrl = `https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(window.location.href)}`;
    window.open(lineUrl, '_blank');
    setShowModal(false);
  };

  return (
    <>
      <div className="relative">
        {/* 商品リンクモーダル */}
        {showProductLinksModal && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-[60]"
            onClick={(e: React.MouseEvent) => {
              if (e.target === e.currentTarget) {
                setShowProductLinksModal(false);
              }
            }}
          >
            <div
              ref={productLinksModalRef}
              className="bg-white rounded-t-lg w-full max-w-[500px]"
              style={{
                animation: 'slideUp 0.3s ease-out'
              }}
            >
              <div className="">
                {/* Amazon */}
                {productLinks?.amazon && (
                  <button
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (productLinks.amazon) {
                        window.open(productLinks.amazon, '_blank', 'noopener,noreferrer');
                      }
                    }}
                    className="w-full flex items-center p-2 border-b border-gray-200 hover:bg-gray-50"
                  >
                    <div className="w-8 h-8 mr-6 flex items-center justify-center">
                      <Image src="/static/img/amazonec.png" alt="Amazon" width={24} height={24} className="object-contain" />
                    </div>
                    <span className="text-sm font-medium text-[#313131]">Amazonで詳細を見る</span>
                    <div className="ml-auto">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="#313131" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </button>
                )}

                {/* 楽天 */}
                {productLinks?.rakuten && (
                  <button
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (productLinks.rakuten) {
                        window.open(productLinks.rakuten, '_blank', 'noopener,noreferrer');
                      }
                    }}
                    className="w-full flex items-center p-2 border-b border-gray-200 hover:bg-gray-50"
                  >
                    <div className="w-8 h-8 mr-6 flex items-center justify-center">
                      <Image src="/static/img/rakutenec.png" alt="楽天" width={24} height={24} className="object-contain" />
                    </div>
                    <span className="text-sm font-medium text-[#313131]">楽天で詳細を見る</span>
                    <div className="ml-auto">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="#313131" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </button>
                )}

                {/* Yahoo!ショッピング */}
                {productLinks?.yahoo && (
                  <button
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (productLinks.yahoo) {
                        window.open(productLinks.yahoo, '_blank', 'noopener,noreferrer');
                      }
                    }}
                    className="w-full flex items-center p-2 border-b border-gray-200 hover:bg-gray-50"
                  >
                    <div className="w-8 h-8 mr-6 flex items-center justify-center">
                      <Image src="/static/img/yahooshopping.png" alt="Yahoo!ショッピング" width={24} height={24} className="object-contain" />
                    </div>
                    <span className="text-sm font-medium text-[#313131]">Yahoo!ショッピングで詳細を見る</span>
                    <div className="ml-auto">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="#313131" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </button>
                )}

                {/* Qoo10 */}
                {productLinks?.qoo10 && (
                  <button
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (productLinks.qoo10) {
                        window.open(productLinks.qoo10, '_blank', 'noopener,noreferrer');
                      }
                    }}
                    className="w-full flex items-center p-2 border-b border-gray-200 hover:bg-gray-50"
                  >
                    <div className="w-8 h-8 mr-6 flex items-center justify-center">
                      <Image src="/static/img/qoo10.png" alt="Qoo10" width={24} height={24} className="object-contain" />
                    </div>
                    <span className="text-sm font-medium text-[#313131]">Qoo10で詳細を見る</span>
                    <div className="ml-auto">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="#313131" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </button>
                )}

                {/* 公式サイト */}
                {productLinks?.official && (
                  <button
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (productLinks.official) {
                        window.open(productLinks.official, '_blank', 'noopener,noreferrer');
                      }
                    }}
                    className="w-full flex items-center p-2 border-b border-gray-200 hover:bg-gray-50"
                  >
                    <div className="w-8 h-8 mr-6 flex items-center justify-center">
                      <Image src="/static/img/homepage.png" alt="公式サイト" width={24} height={24} className="object-contain" />
                    </div>
                    <span className="text-sm font-medium text-[#313131]">公式サイトで詳細を見る</span>
                    <div className="ml-auto">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="#313131" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </button>
                )}

                {/* キャンセルボタン */}
                <button
                  onClick={() => setShowProductLinksModal(false)}
                  className="w-full p-4 text-center text-sm font-medium text-[#313131] hover:bg-gray-50"
                >
                  キャンセル
                </button>
              </div>
            </div>
          </div>
        )}

        {/* シェアモーダル */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div 
              ref={modalRef}
              className="bg-white rounded-lg p-6 w-[300px] max-w-[90%]"
            >
              <h3 className="text-center font-bold text-lg mb-4">シェア</h3>
              
              <div className="grid grid-cols-4 gap-4 mb-6">
                {/* Twitter */}
                <button 
                  onClick={handleTwitterShare}
                  className="flex flex-col items-center"
                >
                  <div className="w-12 h-12 rounded-full bg-[#1DA1F2] flex items-center justify-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                  </div>
                  <span className="text-xs">Twitter</span>
                </button>
                
                {/* Facebook */}
                <button 
                  onClick={handleFacebookShare}
                  className="flex flex-col items-center"
                >
                  <div className="w-12 h-12 rounded-full bg-[#1877F2] flex items-center justify-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  </div>
                  <span className="text-xs">facebook</span>
                </button>
                
                {/* LINE */}
                <button 
                  onClick={handleLineShare}
                  className="flex flex-col items-center"
                >
                  <div className="w-12 h-12 rounded-full bg-[#00B900] flex items-center justify-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white">
                      <path d="M24 10.304c0-5.369-5.383-9.738-12-9.738-6.616 0-12 4.369-12 9.738 0 4.814 4.269 8.846 10.036 9.608.391.084.922.258 1.057.592.121.303.079.778.039 1.085l-.171 1.027c-.053.303-.242 1.186 1.039.647 1.281-.54 6.911-4.069 9.428-6.967 1.739-1.907 2.572-3.843 2.572-5.992zm-18.988-2.595c.129 0 .234.105.234.234v4.153h2.287c.129 0 .233.104.233.233v.842c0 .129-.104.234-.233.234h-3.363c-.063 0-.119-.025-.161-.065l-.001-.001-.002-.002-.001-.001-.003-.003c-.04-.042-.065-.099-.065-.161v-5.229c0-.129.104-.234.233-.234h.842zm14.992 0c.129 0 .233.105.233.234v.842c0 .129-.104.234-.233.234h-2.287v.883h2.287c.129 0 .233.105.233.234v.842c0 .129-.104.233-.233.233h-2.287v.884h2.287c.129 0 .233.105.233.233v.842c0 .129-.104.234-.233.234h-3.363c-.063 0-.12-.025-.162-.065l-.003-.004-.003-.003c-.04-.042-.066-.099-.066-.161v-5.229c0-.062.025-.119.065-.161l.004-.004.003-.003c.042-.04.099-.066.162-.066h3.363zm-10.442.001c.129 0 .234.104.234.233v5.229c0 .128-.105.233-.234.233h-.842c-.129 0-.234-.105-.234-.233v-5.229c0-.129.105-.233.234-.233h.842zm2.127 0h.008l.012.001.013.001.01.001.013.003.008.003.014.004.008.003.013.006.007.003.013.007.007.004.012.009.006.004.013.011.004.004.014.014.002.002.018.023 2.396 3.236v-3.106c0-.129.105-.233.234-.233h.841c.13 0 .234.104.234.233v5.229c0 .128-.104.233-.234.233h-.841l-.06-.008-.004-.001-.015-.005-.007-.003-.012-.004-.011-.006-.007-.003-.014-.009-.002-.002-.06-.058-2.399-3.24v3.106c0 .128-.104.233-.234.233h-.841c-.129 0-.234-.105-.234-.233v-5.229c0-.129.105-.233.234-.233h.841z"/>
                    </svg>
                  </div>
                  <span className="text-xs">LINE</span>
                </button>
                
                {/* コピー */}
                <button 
                  onClick={handleCopyClick}
                  className="flex flex-col items-center"
                >
                  <div className="w-12 h-12 rounded-full bg-gray-500 flex items-center justify-center mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white">
                      <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                  </div>
                  <span className="text-xs">コピー</span>
                </button>
              </div>
              
              <button 
                onClick={() => setShowModal(false)}
                className="w-full py-3 text-center text-sm font-medium"
              >
                キャンセル
              </button>
            </div>
          </div>
        )}
      </div>
      
      <div
      className={`flex w-full max-w-[500px] h-[56px] items-center bg-white shadow-[0px_0px_6px_0px_#00000040] ${className} fixed bottom-0 left-1/2 transform -translate-x-1/2 px-4 justify-between z-30`}
      >
        <div className="inline-flex h-[20%] items-center justify-center gap-[0.5rem] py-0 flex-[0_0_auto]">
          <div className="relative w-[24px] h-[24px] cursor-pointer" onClick={handleBookmarkClick}>
            <Image 
              className="object-cover" 
              alt="Bookmark" 
              src={isSaved ? "https://c.animaapp.com/ZUwMMAhl/img/bookmark-filled.png" : "https://c.animaapp.com/ZUwMMAhl/img/<EMAIL>"}
              fill
              style={{ objectFit: 'cover' }}
            />
          </div>
          <div className="relative w-[24px] h-[24px] cursor-pointer" onClick={handleShare}>
            <Image 
              className="object-cover" 
              alt="Image share" 
              src="https://c.animaapp.com/ZUwMMAhl/img/<EMAIL>"
              fill
              style={{ objectFit: 'cover' }}
            />
          </div>
        </div>
        <div className="flex flex-col w-[80%] items-start justify-center bg-white">
          <div
            className="flex w-[100%] items-center justify-center gap-[10px] p-[10px] bg-button-red hover:bg-[#DD0F2B] active:bg-[#DD0F2B] rounded-[100px] cursor-pointer"
            onClick={(e: React.MouseEvent) => handleDetailClick(e)}
          >
            <div className="w-fit mt-[-1.00px] font-normal text-white text-[14px] text-center">
              詳細を詳しく見る
            </div>
          </div>  
        </div>
      </div>
    </>
  );
};