import React from 'react';
import { AiFillStar } from 'react-icons/ai';

interface RatingSectionProps {
  rating: number;
  setRating: (rating: number) => void;
  hoverRating: number;
  setHoverRating: (rating: number) => void;
}

/**
 * おすすめ度セクションコンポーネント
 */
export const RatingSection: React.FC<RatingSectionProps> = ({
  rating,
  setRating,
  hoverRating,
  setHoverRating
}) => {
  return (
    <div className="px-4 py-3">
      <div className="flex items-center mb-2">
        <p className="text-[#313131] text-[16px] font-bold">おすすめ度 <span className="text-xs text-red-500">(必須)</span></p>
        <div className="relative group ml-1">
          <div className="cursor-pointer bg-gray-200 rounded-full w-5 h-5 flex items-center justify-center">
            <span className="text-xs font-bold">?</span>
          </div>
          <div className="absolute left-0 w-64 bg-white border border-gray-200 p-3 rounded shadow-lg z-10 text-xs text-[#313131] hidden group-hover:block">
            <p className="mb-1">おすすめ度の目安</p>
            <p>星5: 自信をもっておすすめできる</p>
            <p>星4: 良かったが人を選ぶ</p>
            <p>星3: 普通。リピートを迷う</p>
            <p>星2: 改善点がある。自分には合わない</p>
            <p>星1: おすすめできない</p>
          </div>
        </div>
      </div>
      <div className="flex space-x-4 justify-center mt-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => setRating(star)}
            onMouseEnter={() => setHoverRating(star)}
            onMouseLeave={() => setHoverRating(0)}
            className="text-3xl focus:outline-none"
          >
            <AiFillStar 
              className={`${(hoverRating || rating) >= star ? 'text-yellow-400' : 'text-gray-300'}`} 
              size={36}
            />
          </button>
        ))}
      </div>
    </div>
  );
};
