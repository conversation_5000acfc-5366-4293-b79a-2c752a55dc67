# SNS関連API

## SNSマスター取得 API

### エンドポイント

```
GET /api/getSnsMaster
```

### 説明

システムに登録されているSNSマスターデータを取得します。

### パラメータ

| パラメータ名 | 型     | 必須 | 説明               |
|--------------|--------|------|-------------------|
| user_ID      | string | はい | ユーザーのID      |

### レスポンス

成功時（200 OK）:

```json
[
  {
    "id": "uuid",
    "sns_name": "string",
    "sns_icon": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  },
  // ...
]
```

エラー時（404 Not Found）:

```json
{
  "error": true,
  "message": "SNSマスターデータが見つかりません"
}
```

## <a id="create"></a>SNSマスター作成 API

### エンドポイント

```
POST /api/createSnsMaster
```

### 説明

SNSマスターデータを作成します。メインSNS（Instagram、TikTok、X、YouTube、Facebook、LINE）だけでなく、OtherSnsコンポーネントに含まれる全てのSNS（Amazon、note、Rakuten、WEAR、Blog、BIGO LIVE、17Live、TwitCastingなど）もマスターデータとして登録します。

### リクエストボディ

```json
{
  "user_ID": "string"
}
```

### レスポンス

成功時（201 Created）:

```json
{
  "success": true,
  "message": "SNSマスターデータが正常に作成されました",
  "data": [
    {
      "id": "uuid",
      "sns_name": "string",
      "sns_icon": "string",
      "created_at": "datetime",
      "updated_at": "datetime"
    },
    // ...
  ]
}
```

エラー時（400 Bad Request）:

```json
{
  "error": true,
  "message": "SNSマスターデータの作成に失敗しました"
}
```

## <a id="update-user"></a>SNSユーザー情報更新 API

### エンドポイント

```
POST /api/snsUser
```

### 説明

ユーザーのSNS情報を更新します。

### リクエストボディ

```json
{
  "user_ID": "string",
  "sns_ID": "string",
  "sns_url": "string"
}
```

### レスポンス

成功時（200 OK）:

```json
{
  "id": "uuid",
  "user_ID": "string",
  "sns_ID": "string",
  "sns_url": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

エラー時（400 Bad Request）:

```json
{
  "error": true,
  "message": "SNSユーザー情報の更新に失敗しました"
}
```

## 実装上の注意点

- SNSマスター作成APIは、以下の優先順位でユーザーIDを取得します：
  1. リクエストボディからユーザーIDを取得
  2. CookieからユーザーIDを取得
  3. それでもない場合のみ、最終手段としてデフォルト値を使用

- 各SNSにはユニークなIDを割り当て、データベースに既に存在するSNSを除外する重複チェックを実装しています。これにより、同じSNSが重複して登録されることを防ぎ、データベースの一貫性を保ちながら必要なマスターデータを効率的に提供できるようになりました。

- 以前は固定値「user123」を使用していましたが、現在は動的にユーザーIDを取得する方法に変更されています。

## 関連ファイル

- `src/app/api/getSnsMaster/route.ts` - SNSマスター取得API
- `src/app/api/createSnsMaster/route.ts` - SNSマスター作成API
- `src/app/api/snsUser/route.ts` - SNSユーザー情報更新API
- `src/components/OtherSns/OtherSns.tsx` - その他のSNSコンポーネント

## 最終更新日

2025年4月25日
