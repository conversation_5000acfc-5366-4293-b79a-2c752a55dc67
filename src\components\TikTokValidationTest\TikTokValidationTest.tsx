'use client';

import React, { useState } from 'react';
import { validateTikTokURL, generateTikTokEmbedHTML } from '../SocialEmbed';

/**
 * TikTokのバリデーション機能をテストするためのコンポーネント
 */
const TikTokValidationTest: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [validationResult, setValidationResult] = useState<string | null>(null);
  const [embedHtml, setEmbedHtml] = useState('');

  // テスト用のURL例
  const testUrls = [
    // 有効なURL（動画）
    'https://www.tiktok.com/@username/video/1234567890123456789',
    'https://m.tiktok.com/@username/video/1234567890123456789',
    'https://www.tiktok.com/@test.user/video/9876543210987654321?is_copy_url=1',
    'https://m.tiktok.com/v/1234567890123456789.html',
    'https://www.tiktok.com/embed/1234567890123456789',
    'https://vm.tiktok.com/ZMjKqR8Hn/',
    'https://vm.tiktok.com/abc123',
    'https://vt.tiktok.com/ZSkjn4NG9/',
    'https://vt.tiktok.com/xyz789',
    
    // 無効なURL（プロフィール・その他）
    'https://www.tiktok.com/@username',
    'https://www.tiktok.com/@username/',
    'https://www.tiktok.com/@username?tab=videos',
    'https://www.tiktok.com/tag/funny',
    'https://www.tiktok.com/music/original-sound-123456789',
    'https://www.tiktok.com/foryou',
    'https://www.tiktok.com/following',
    'https://www.tiktok.com/live',
    'https://www.tiktok.com/trending',
    'https://www.tiktok.com/search?q=test',
    'https://example.com/not-tiktok',
    'invalid-url'
  ];

  const handleTest = () => {
    const result = validateTikTokURL(testUrl);
    setValidationResult(result);
    
    if (result) {
      const html = generateTikTokEmbedHTML();
      setEmbedHtml(html);
    } else {
      setEmbedHtml('');
    }
  };

  const handleTestUrlClick = (url: string) => {
    setTestUrl(url);
    const result = validateTikTokURL(url);
    setValidationResult(result);
    
    if (result) {
      const html = generateTikTokEmbedHTML();
      setEmbedHtml(html);
    } else {
      setEmbedHtml('');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-6 text-gray-800">TikTok URL バリデーションテスト</h1>
      
      {/* 入力エリア */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          TikTok URL を入力してください:
        </label>
        <div className="flex gap-2">
          <input
            type="text"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://www.tiktok.com/@username/video/..."
          />
          <button
            onClick={handleTest}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            テスト
          </button>
        </div>
      </div>

      {/* 結果表示 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2 text-gray-800">バリデーション結果:</h3>
        <div className={`p-3 rounded-md ${validationResult ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {validationResult ? (
            <>
              <div className="font-semibold">✓ 有効な動画URL</div>
              <div className="text-sm">動画ID: {validationResult}</div>
            </>
          ) : (
            <div className="font-semibold">✗ 無効なURL</div>
          )}
        </div>
      </div>

      {/* 埋め込みHTML表示 */}
      {embedHtml && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-800">生成された埋め込みHTML:</h3>
          <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto">
            {embedHtml}
          </pre>
        </div>
      )}

      {/* テスト用URL一覧 */}
      <div>
        <h3 className="text-lg font-semibold mb-3 text-gray-800">テスト用URL一覧:</h3>
        <div className="space-y-2">
          <div className="text-sm font-medium text-green-600 mb-2">✓ 有効なURL（動画）:</div>
          {testUrls.slice(0, 9).map((url, index) => (
            <button
              key={index}
              onClick={() => handleTestUrlClick(url)}
              className="block w-full text-left px-3 py-2 bg-green-50 hover:bg-green-100 rounded-md text-sm text-green-700 border border-green-200"
            >
              {url}
            </button>
          ))}

          <div className="text-sm font-medium text-red-600 mb-2 mt-4">✗ 無効なURL（プロフィール・その他）:</div>
          {testUrls.slice(9).map((url, index) => (
            <button
              key={index + 9}
              onClick={() => handleTestUrlClick(url)}
              className="block w-full text-left px-3 py-2 bg-red-50 hover:bg-red-100 rounded-md text-sm text-red-700 border border-red-200"
            >
              {url}
            </button>
          ))}
        </div>
      </div>

      {/* 仕様説明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-md">
        <h3 className="text-lg font-semibold mb-2 text-blue-800">バリデーション仕様:</h3>
        <div className="text-sm text-blue-700 space-y-1">
          <div><strong>対応URL形式:</strong></div>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>https://www.tiktok.com/@ユーザー名/video/動画ID</li>
            <li>https://m.tiktok.com/@ユーザー名/video/動画ID</li>
            <li>https://m.tiktok.com/v/動画ID.html</li>
            <li>https://www.tiktok.com/embed/動画ID</li>
            <li>https://vm.tiktok.com/短縮コード/</li>
            <li>https://vt.tiktok.com/短縮コード/ （TikTok公式短縮URL）</li>
            <li>クエリパラメータ（?is_copy_url=1など）は無視</li>
          </ul>
          <div className="mt-2"><strong>除外URL形式:</strong></div>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>プロフィールページ: /@ユーザー名</li>
            <li>ハッシュタグページ: /tag/...</li>
            <li>音楽ページ: /music/...</li>
            <li>おすすめページ: /foryou</li>
            <li>フォロー中: /following</li>
            <li>ライブ: /live</li>
            <li>トレンド: /trending</li>
            <li>検索: /search?...</li>
          </ul>
          <div className="mt-2"><strong>動画ID:</strong> 通常19桁の数字、短縮URLの場合は英数字の組み合わせ</div>
        </div>
      </div>
    </div>
  );
};

export default TikTokValidationTest;
