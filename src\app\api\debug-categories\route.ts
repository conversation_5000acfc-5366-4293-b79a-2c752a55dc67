import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');

    if (!user_ID) {
      return NextResponse.json({ error: 'user_IDが必要です' }, { status: 400 });
    }

    console.log(`デバッグ: ユーザーID=${user_ID}のカテゴリ情報を調査`);

    // 1. ユーザーの全カテゴリを取得（parent_ID制限なし）
    const allUserCategories = await prisma.category.findMany({
      where: { user_ID },
      orderBy: { created_at: 'asc' }
    });

    // 2. parent_IDがnullのカテゴリを取得
    const parentCategories = await prisma.category.findMany({
      where: { user_ID, parent_ID: null },
      orderBy: { created_at: 'asc' }
    });

    // 3. parent_IDが空文字のカテゴリを取得
    const emptyParentCategories = await prisma.category.findMany({
      where: { user_ID, parent_ID: '' },
      orderBy: { created_at: 'asc' }
    });

    // 4. orderフィールドの存在確認
    const categoriesWithOrder = await prisma.category.findMany({
      where: { user_ID },
      select: {
        id: true,
        category_name: true,
        parent_ID: true,
        order: true,
        created_at: true
      },
      orderBy: { created_at: 'asc' }
    });

    const debugInfo = {
      user_ID,
      allUserCategories: {
        count: allUserCategories.length,
        data: allUserCategories
      },
      parentCategories: {
        count: parentCategories.length,
        data: parentCategories
      },
      emptyParentCategories: {
        count: emptyParentCategories.length,
        data: emptyParentCategories
      },
      categoriesWithOrder: {
        count: categoriesWithOrder.length,
        data: categoriesWithOrder
      }
    };

    return NextResponse.json(debugInfo);
  } catch (error) {
    return NextResponse.json({
      error: 'デバッグ情報の取得に失敗しました',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      // Silent error handling
    }
  }
}
