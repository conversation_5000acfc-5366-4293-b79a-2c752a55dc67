import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

/**
 * ランキングフォーム処理のためのカスタムフック
 */
export function useRankingForm(userId: string, uploadImages: () => Promise<string[]>, setImagesCallback?: (images: string[]) => void) {
  const router = useRouter();
  
  // フォーム状態
  const [title, setTitle] = useState('');
  const [recommendText, setRecommendText] = useState('');
  const [category, setCategory] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [subCategory, setSubCategory] = useState('');
  const [rating, setRating] = useState(1);
  const [hoverRating, setHoverRating] = useState(0);
  const [amazonUrl, setAmazonUrl] = useState('');
  const [rakutenUrl, setRakutenUrl] = useState('');
  const [yahooUrl, setYahooUrl] = useState('');
  const [qoo10Url, setQoo10Url] = useState('');
  const [officialUrl, setOfficialUrl] = useState('');
  
  // 編集モード関連
  const [isEditMode, setIsEditMode] = useState(false);
  const [rankingId, setRankingId] = useState<string>('');
  
  // UI状態
  const [showHelpPopup, setShowHelpPopup] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: boolean}>({});
  const [showErrors, setShowErrors] = useState(false);
  const [showSavedModal, setShowSavedModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  /**
   * 既存のランキング情報を取得する関数
   */
  const fetchRankingData = useCallback(async (rankingId: string) => {
    try {
      // ユーザーIDがまだ取得できていない場合は待機
      if (!userId) {
        return;
      }
      
      // APIからランキング情報を取得
      let baseUrl = '';
      if (typeof window !== 'undefined') {
        baseUrl = window.location.origin;
      }
      
      // 正しいAPIエンドポイントとパラメータを使用
      const apiUrl = `${baseUrl}/api/getRank?user_ID=${userId}&ranking_ID=${rankingId}`;
      
      const response = await fetch(apiUrl, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      
      if (!response.ok) {
        throw new Error(`ランキング情報の取得に失敗しました: ${response.status}`);
      }
      
      const data = await response.json();

      // 取得したデータをフォームにセット
      if (data) {
        setTitle(data.ranking_title || '');
        setRecommendText(data.ranking_description || '');

        // おすすめ度（星の数）を設定
        if (data.recommend_rate) {
          setRating(Number(data.recommend_rate));
        } else {
          setRating(1);
        }

        // カテゴリIDとサブカテゴリIDの設定
        const categoryName = data.category_name || '';
        const subCategoryName = data.subcategory_name || '';
        const categoryId = data.category_ID || '';
        const subCategoryId = data.subcategory_ID || '';
        const parentCategoryId = data.parent_category_ID || categoryId; // 親カテゴリIDを取得
        
        // thumbnail_imageフィールドが存在し、配列であれば設定
        if (data.thumbnail_image && Array.isArray(data.thumbnail_image)) {
          // 画像配列を作成（4つのスロットを維持）
          const newImages = ['', '', '', ''];

          // 存在する画像を配列に設定
          data.thumbnail_image.forEach((url: string, index: number) => {
            if (index < 4 && url && url.trim() !== '') {
              newImages[index] = url;
            }
          });

          // コールバックが存在すれば画像を設定
          if (setImagesCallback) {
            setImagesCallback(newImages);
          }
        } else if (data.thumbnail_image && typeof data.thumbnail_image === 'string') {
          // 文字列の場合はコンマ区切りで分割してみる
          // コンマ区切りの画像パスを配列に変換
          const imageUrls = data.thumbnail_image.split(',').filter((url: string) => url.trim() !== '');

          // 画像配列を作成（4つのスロットを維持）
          const newImages = ['', '', '', ''];

          // 存在する画像を配列に設定
          imageUrls.forEach((url: string, index: number) => {
            if (index < 4) {
              newImages[index] = url;
            }
          });

          // コールバックが存在すれば画像を設定
          if (setImagesCallback) {
            setImagesCallback(newImages);
          }
        }
        
        // カテゴリ名とIDを設定
        setCategory(categoryName);
        
        // 親カテゴリIDを設定
        if (parentCategoryId) {
          setCategoryId(parentCategoryId);
        } else if (categoryId) {
          setCategoryId(categoryId);
        }

        // サブカテゴリIDを設定（名前ではなくIDを設定）
        if (subCategoryId) {
          setSubCategory(subCategoryId);
        } else if (subCategoryName) {
          // IDがない場合は名前を設定（後でIDに変換される）
          setSubCategory(subCategoryName);
        }
        
        // URLをセット
        setAmazonUrl(data.amazon_url || '');
        setRakutenUrl(data.rakuten_url || '');
        setYahooUrl(data.yahoo_url || '');
        setQoo10Url(data.qoo10_url || '');
        setOfficialUrl(data.official_url || '');
      }
    } catch (error) {
      setErrorMessage('ランキング情報の取得に失敗しました。もう一度お試しください。');
    }
  }, [userId, setImagesCallback]);
  
  /**
   * ランキング送信処理
   */
  const submitRanking = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    // 編集モードではカテゴリは既に設定されているため、バリデーションをスキップ
    if (!isEditMode && !categoryId) {
      setErrorMessage('カテゴリを選択してください');
      setIsSubmitting(false);
      return;
    }
    
    try {
      setIsSubmitting(true);
      setErrorMessage('');
      
      // 1. 画像をアップロード
      const uploadedImageUrls = await uploadImages();
      
      // 2. ランキングデータを送信
      // ランキングデータの型を定義
      interface RankingData {
        title: string;
        ranking_description: string;
        user_ID: string;
        categoryId: string;
        subCategory?: string;
        subCategoryID?: string; // サブカテゴリIDフィールドを追加
        images: string[];
        amazon_url: string;
        rakuten_url: string;
        yahoo_url: string;
        qoo10_url: string;
        official_url: string;
        recommend_rate: number; // おすすめ度（星の数）のフィールドを追加
        ranking_ID?: string; // 編集モードの場合に使用するオプションプロパティ
      }
      
      const rankingData: RankingData = {
        title,
        ranking_description: recommendText,
        user_ID: userId,
        categoryId: categoryId || 'default_category',
        subCategoryID: subCategory, // サブカテゴリIDを明示的に設定
        images: uploadedImageUrls,
        amazon_url: amazonUrl,
        rakuten_url: rakutenUrl,
        yahoo_url: yahooUrl,
        qoo10_url: qoo10Url,
        official_url: officialUrl,
        recommend_rate: rating, // おすすめ度（星の数）を追加
      };
      
      // 編集モードの場合はランキングIDを追加
      if (isEditMode && rankingId) {
        rankingData.ranking_ID = rankingId;
      }
      
      // 絶対URLを使用してAPIを呼び出す
      let baseUrl = '';
      if (typeof window !== 'undefined') {
        baseUrl = window.location.origin;
      }
      
      // 編集モードでも新規作成モードでも同じエンドポイントを使用
      // saveRankingエンドポイントはランキングIDの有無によって更新か新規作成かを判断する
      const apiUrl = `${baseUrl}/api/saveRanking`;
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify(rankingData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        setErrorMessage('ランキングの保存に失敗しました。');
        setIsSubmitting(false); // エラー時はローディングを停止
        return;
      }

      const result = await response.json();

      // 成功時の処理
      // 注意: ページ遷移はRankingRegisterLoadingAnimationコンポーネントのonCompleteコールバックで行います
      // setIsSubmittingはfalseにせず、アニメーションが完了するまで表示したままにします
      // 成功時はfinallyブロックでsetIsSubmitting(false)を実行しない

    } catch (error) {
      setErrorMessage('ランキングの登録中にエラーが発生しました。');
      setIsSubmitting(false); // エラー時のみここでfalseにする
    }
    // 成功時はsetIsSubmitting(false)をしない（ローディングアニメーション完了後に実行される）
  };

  /**
   * URLが「https://」まで入力されているかチェック
   */
  const isValidUrlInput = (url: string): boolean => {
    const trimmedUrl = url.trim();
    return trimmedUrl.startsWith('https://') && trimmedUrl.length > 8; // 「https://」より長い
  };

  /**
   * 必須項目が入力されているかチェック
   */
  const isFormValid = (images?: string[]) => {
    return (
      (images && images.length > 0 && images.some(img => img && img.trim() !== '')) && // 画像設定（1つ以上の有効な画像）
      title.trim() !== '' && // タイトル入力
      rating > 0 && // おすすめ度選択
      subCategory.trim() !== '' && // サブカテゴリ選択
      (isValidUrlInput(amazonUrl) || isValidUrlInput(rakutenUrl) || isValidUrlInput(yahooUrl) || isValidUrlInput(qoo10Url) || isValidUrlInput(officialUrl)) // 商品リンク1つ以上（「https://」まで入力済み）
    );
  };
  
  return {
    // フォーム状態
    title,
    setTitle,
    recommendText,
    setRecommendText,
    category,
    setCategory,
    categoryId,
    setCategoryId,
    subCategory,
    setSubCategory,
    rating,
    setRating,
    hoverRating,
    setHoverRating,
    amazonUrl,
    setAmazonUrl,
    rakutenUrl,
    setRakutenUrl,
    yahooUrl,
    setYahooUrl,
    qoo10Url,
    setQoo10Url,
    officialUrl,
    setOfficialUrl,
    
    // 編集モード関連
    isEditMode,
    setIsEditMode,
    rankingId,
    setRankingId,
    fetchRankingData,
    
    // UI状態
    showHelpPopup,
    setShowHelpPopup,
    currentStep,
    setCurrentStep,
    validationErrors,
    setValidationErrors,
    showErrors,
    setShowErrors,
    showSavedModal,
    setShowSavedModal,
    errorMessage,
    setErrorMessage,
    isSubmitting,
    setIsSubmitting,
    
    // 処理関数
    submitRanking,
    isFormValid
  };
}
