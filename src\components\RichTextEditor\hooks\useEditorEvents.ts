import { useRef, useEffect } from 'react';
import { trackEmbedState, processExistingLinks } from '../utils';
import { DEBOUNCE_DELAY, EMBED_PROCESS_DELAY } from '../constants';

interface UseEditorEventsProps {
  editorRef: React.RefObject<HTMLDivElement>;
  onChange: (value: string) => void;
  value?: string;
  placeholder: string;
  setIsFocused: (focused: boolean) => void;
  // restoreLostEmbeds: () => void; // 🔧 無効化: 自動復元機能を削除
  processSocialEmbeds: () => void;
}

export const useEditorEvents = ({
  editorRef,
  onChange,
  value,
  placeholder,
  setIsFocused,
  // restoreLostEmbeds, // 🔧 無効化: 自動復元機能を削除
  processSocialEmbeds
}: UseEditorEventsProps) => {
  // デバウンス処理用のタイマーID
  const debounceTimerRef = useRef<number | null>(null);
  // 🔧 緊急修正: 無限ループ防止フラグ
  const isProcessingRef = useRef(false);
  const lastProcessedContentRef = useRef<string>('');
  // 🔧 修正: キーボード操作中フラグを追加（カーソル復元スキップ用）
  const isKeyboardOperationRef = useRef(false);
  const keyboardOperationTimeoutRef = useRef<number | null>(null);
  // 🔧 追加: IME入力状態を追跡
  const isComposingRef = useRef(false);
  // 🔧 追加: SNS埋め込み処理中フラグを追加（カーソル復元スキップ用）
  const isEmbedProcessingRef = useRef(false);

  // 🔧 修正: キーボード操作中フラグを設定する関数
  const markKeyboardOperation = () => {
    isKeyboardOperationRef.current = true;

    if (keyboardOperationTimeoutRef.current !== null) {
      window.clearTimeout(keyboardOperationTimeoutRef.current);
    }

    keyboardOperationTimeoutRef.current = window.setTimeout(() => {
      isKeyboardOperationRef.current = false;
      keyboardOperationTimeoutRef.current = null;
    }, 500); // 500ms後にリセット
  };

  // 🔧 追加: SNS埋め込み処理中フラグを設定する関数
  const markEmbedProcessing = (isProcessing: boolean) => {
    isEmbedProcessingRef.current = isProcessing;
  };

  // 🔧 改善: 自動スクロール機能（カーソルを画面下1/3に維持）
  const scrollToCursor = () => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    // エディターの高さの1/3の位置を理想的なカーソル位置とする
    const idealCursorPosition = editorRect.top + (editorRect.height * 2/3);

    // カーソルが理想位置より下にある場合、または下部の1/3以内にある場合にスクロール
    const shouldScroll = rect.bottom > idealCursorPosition;

    if (shouldScroll) {
      // カーソルを画面の下1/3の位置に配置するためのスクロール量を計算
      const targetCursorY = editorRect.top + (editorRect.height * 2/3);
      const scrollAdjustment = rect.bottom - targetCursorY;
      const targetScrollTop = editorRef.current.scrollTop + scrollAdjustment + 20; // 少し余裕を持たせる

      editorRef.current.scrollTo({
        top: Math.max(0, targetScrollTop), // 負の値にならないように
        behavior: 'smooth'
      });

      if (process.env.NODE_ENV === 'development') {
        console.log('[AUTO_SCROLL] テキスト入力時にカーソルを画面下1/3に配置');
      }
    }
  };

  // エディタの内容が変更されたときに親コンポーネントに通知
  const handleInput = () => {
    if (!editorRef.current) {
      return;
    }

    // 🔧 緊急修正: 無限ループ防止
    if (isProcessingRef.current) {
      return;
    }

    const currentContent = editorRef.current.innerHTML;

    // 🔧 緊急修正: 同じ内容の場合は処理をスキップ
    if (currentContent === lastProcessedContentRef.current) {
      return;
    }

    isProcessingRef.current = true;
    lastProcessedContentRef.current = currentContent;

    // 🔧 カーソル位置を保存（DOM操作前）
    const selection = window.getSelection();
    const range = selection && selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
    let savedCaretInfo: { element: Node; offset: number } | null = null;

    if (range && range.startContainer) {
      try {
        savedCaretInfo = {
          element: range.startContainer,
          offset: range.startOffset
        };
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('[CURSOR_DEBUG] カーソル位置の保存に失敗:', error);
        }
      }
    }

    // 入力前に埋め込み要素の状態を記録
    const embedsBefore = editorRef.current.querySelectorAll('.social-embed');



    trackEmbedState('EMBEDS_BEFORE_INPUT', undefined, { count: embedsBefore.length });

    // 以前のタイマーをキャンセル
    if (debounceTimerRef.current !== null) {
      window.clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }

    // デバウンス処理
    debounceTimerRef.current = window.setTimeout(() => {
      if (!editorRef.current) return;

      const finalContent = editorRef.current.innerHTML;

      // 入力後に埋め込み要素の状態をチェック
      const embedsAfter = editorRef.current.querySelectorAll('.social-embed');

      // 入力後の埋め込み位置情報を記録（デバッグログは削除）

      trackEmbedState('EMBEDS_AFTER_INPUT', undefined, { count: embedsAfter.length });

      // 🔧 修正: 自動復元処理を無効化（キーボード削除との競合を防止）
      // 埋め込み要素の自動復元は意図的な削除操作を妨害するため無効化
      // const significantLoss = embedsAfter.length < embedsBefore.length &&
      //                        (embedsBefore.length - embedsAfter.length) >= 1;

      // if (significantLoss) {
      //   setTimeout(() => {
      //     restoreLostEmbeds();
      //   }, 200);
      // }

      // リンクの属性を処理
      processExistingLinks(editorRef);

      // 🔧 修正: 埋め込み処理の最適化 - 必要な場合のみ実行
      setTimeout(() => {
        if (!editorRef.current) return;

        // 🔧 修正: 新しい埋め込み要素の検出（未処理の要素のみ対象）
        const hasUnprocessedSnsUrls = finalContent.includes('social-embed') &&
                                     finalContent.includes('data-loaded="false"');

        // 🔧 修正: 未処理の埋め込み要素があるかチェック（既存要素は無視）
        const unprocessedEmbeds = editorRef.current.querySelectorAll('.social-embed:not([data-loaded="true"])');
        const hasUnprocessedEmbeds = unprocessedEmbeds.length > 0;

        // 🔧 修正: カーソル移動中は埋め込み処理をスキップ（カーソル位置保持）
        const selection = window.getSelection();
        const isCaretInEmbed = selection && selection.rangeCount > 0 &&
                              selection.getRangeAt(0).commonAncestorContainer.parentElement?.closest('.social-embed');

        // 🔧 重要: ユーザーがテキスト入力中の場合は埋め込み処理をスキップ
        const isUserTyping = selection && selection.rangeCount > 0 &&
                            !isCaretInEmbed &&
                            selection.getRangeAt(0).commonAncestorContainer.nodeType === Node.TEXT_NODE;

        // 🔧 修正: IME入力中の検出を改善（カーソルジャンプ防止）
        // compositionstart/compositionend イベントでより正確に検出
        const isIMEInput = (selection && selection.rangeCount > 0 &&
                          selection.getRangeAt(0).commonAncestorContainer.nodeType === Node.TEXT_NODE &&
                          selection.getRangeAt(0).collapsed) ||
                          isComposingRef.current; // 🔧 追加: composition状態も考慮

        // 🔧 強化: 既存埋め込みがある場合はテキスト入力時の処理を完全にスキップ
        const existingEmbeds = editorRef.current.querySelectorAll('.social-embed[data-loaded="true"]');
        const hasExistingEmbeds = existingEmbeds.length > 0;

        // 🔧 修正: 新しい埋め込み要素がある場合のみ処理を実行（既存埋め込み保護）
        // IME入力中は処理をスキップしてカーソルジャンプを防止
        if (hasUnprocessedEmbeds && !isCaretInEmbed && !isUserTyping && !isIMEInput && !hasExistingEmbeds) {
          // 新しい埋め込み要素を処理

          // 🔧 追加: SNS埋め込み処理開始をマーク
          markEmbedProcessing(true);

          processSocialEmbeds();

          // 🔧 追加: SNS埋め込み処理完了後にフラグをリセット
          setTimeout(() => {
            markEmbedProcessing(false);
          }, 2000); // 2秒後にリセット
        } else {
          // 埋め込み処理をスキップ
        }

        // 🔧 修正: カーソル位置復元処理を再実装
        // DOM操作後にカーソル位置を復元してジャンプ問題を解決
        // ただし、テキスト入力中、IME入力中、キーボード操作中、SNS埋め込み処理中は復元をスキップ
        const isGlobalEmbedProcessing = typeof window !== 'undefined' && (window as any).__isEmbedProcessing;
        if (savedCaretInfo && savedCaretInfo.element && !isUserTyping && !isIMEInput && !isKeyboardOperationRef.current && !isEmbedProcessingRef.current && !isGlobalEmbedProcessing) {
          try {
            // 保存された要素がまだDOM内に存在するかチェック
            const isElementInDOM = editorRef.current.contains(savedCaretInfo.element);

            if (isElementInDOM) {
              // 元の位置に復元
              const newRange = document.createRange();
              const maxOffset = savedCaretInfo.element.nodeType === Node.TEXT_NODE
                ? (savedCaretInfo.element.textContent?.length || 0)
                : savedCaretInfo.element.childNodes.length;

              const safeOffset = Math.min(savedCaretInfo.offset, maxOffset);
              newRange.setStart(savedCaretInfo.element, safeOffset);
              newRange.collapse(true);

              const newSelection = window.getSelection();
              if (newSelection) {
                newSelection.removeAllRanges();
                newSelection.addRange(newRange);

                // カーソル位置を復元完了
              }
            } else {
              // 元の要素が存在しない場合は、エディタの適切な位置にカーソルを設定
              const newRange = document.createRange();
              if (editorRef.current.childNodes.length > 0) {
                // 最後のテキストノードを探す
                let lastTextNode: Node | null = null;
                const walker = document.createTreeWalker(
                  editorRef.current,
                  NodeFilter.SHOW_TEXT,
                  null
                );

                let node: Node | null;
                while (node = walker.nextNode()) {
                  lastTextNode = node;
                }

                if (lastTextNode && lastTextNode.textContent) {
                  newRange.setStart(lastTextNode, lastTextNode.textContent.length);
                } else {
                  newRange.selectNodeContents(editorRef.current);
                  newRange.collapse(false);
                }
              } else {
                newRange.setStart(editorRef.current, 0);
                newRange.collapse(true);
              }

              const newSelection = window.getSelection();
              if (newSelection) {
                newSelection.removeAllRanges();
                newSelection.addRange(newRange);

                // 適切な位置にカーソルを設定完了
              }
            }
          } catch (error) {
            // カーソル位置の復元に失敗
          }
        }

        // 埋め込み処理後、内容が変更された場合のみ親コンポーネントに通知
        const processedContent = editorRef.current.innerHTML;

        // DOM変更の詳細を記録（デバッグログは削除）

        // 🔧 修正: onChange呼び出しの重複を防ぐ
        const contentToSend = processedContent !== finalContent ? processedContent : finalContent;

        // 🔧 重要: IME入力中やテキスト入力中は onChange を呼び出さない（カーソルジャンプ防止）
        if (isUserTyping || isIMEInput) {
          // テキスト入力中またはIME入力中のため、onChange呼び出しをスキップ

          // 🔧 改善: テキスト入力中の積極的な自動スクロール
          setTimeout(() => {
            scrollToCursor();
          }, 50); // より短い遅延で即座にスクロール

          return;
        }

        // 🔧 重要: 前回送信した内容と同じ場合は送信をスキップ（重複防止）
        if (contentToSend !== lastProcessedContentRef.current) {
          onChange(contentToSend);
          lastProcessedContentRef.current = contentToSend;
        }

        // 🔧 緊急修正: 処理完了後にフラグをリセット
        setTimeout(() => {
          isProcessingRef.current = false;
        }, 100);
      }, EMBED_PROCESS_DELAY);

      debounceTimerRef.current = null;
    }, DEBOUNCE_DELAY);
  };

  // フォーカスイベントの監視
  const handleFocus = () => {
    trackEmbedState('EDITOR_FOCUS');
    setIsFocused(true);

    if (!value && editorRef.current) {
      const hasEmbeds = editorRef.current.querySelectorAll('.social-embed').length > 0;
      const hasContent = editorRef.current.textContent?.trim().length > 0;
      
      if (!hasEmbeds && !hasContent) {
        editorRef.current.innerHTML = '';
        trackEmbedState('FOCUS_CLEARED_CONTENT');
      }
    }
  };

  const handleBlur = () => {
    trackEmbedState('EDITOR_BLUR');
    setIsFocused(false);

    if (!value && editorRef.current) {
      const hasEmbeds = editorRef.current.querySelectorAll('.social-embed').length > 0;
      const hasContent = editorRef.current.textContent?.trim().length > 0;
      
      if (!hasEmbeds && !hasContent) {
        editorRef.current.innerHTML = placeholder.replace(/\n/g, '<br>');
        trackEmbedState('BLUR_SET_PLACEHOLDER');
      }
    }
  };

  // クリックイベントの監視（簡素化）
  const handleClick = (e: React.MouseEvent) => {
    // 🔧 デバッグ用: クリックイベントの連続発火を防ぐ
    e.stopPropagation();

    const embedsBeforeClick = editorRef.current?.querySelectorAll('.social-embed').length || 0;

    // クリックイベント発生（デバッグログは削除）

    trackEmbedState('EDITOR_CLICK_START', undefined, {
      target: e.target instanceof Element ? e.target.tagName : 'unknown',
      targetClass: e.target instanceof Element ? e.target.className : 'unknown',
      embedsBeforeClick
    });

    // リンクのクリック処理
    const target = e.target as HTMLElement;
    if (target.tagName === 'A') {
      e.preventDefault();
      e.stopPropagation();
      const url = target.getAttribute('href');
      if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
      }
      return;
    }

    // 🔧 簡素化: SNS埋め込み要素のクリック処理（カーソルジャンプ防止）
    const embedElement = target.closest('.social-embed');
    if (embedElement) {
      e.preventDefault();
      e.stopPropagation();

      // 埋め込み要素がクリックされました - 処理をスキップ

      // 🔧 カーソル操作を一時的に無効化（デバッグ用）
      // カーソルジャンプ問題の原因を特定するため、カーソル操作を無効化
      return;
    }

    // 🔧 簡素化: スペーサー段落のクリック処理（カーソルジャンプ防止）
    if (target.getAttribute('data-embed-spacer')) {
      // スペーサー段落がクリックされました - 処理をスキップ

      // 🔧 カーソル操作を一時的に無効化（デバッグ用）
      // カーソルジャンプ問題の原因を特定するため、カーソル操作を無効化
      return;
    }

    // プレースホルダーのクリック処理
    handlePlaceholderClick();
  };

  // プレースホルダーをクリックした時の処理（簡素化）
  const handlePlaceholderClick = () => {
    // プレースホルダーがクリックされました

    if (!value && editorRef.current) {
      // 親コンポーネントのモーダルを開く関数を呼び出す
      if (typeof onChange === 'function') {
        // カスタムイベントを発火して親コンポーネントに通知
        const event = new CustomEvent('openModal', { bubbles: true });
        editorRef.current.dispatchEvent(event);
      }
    }
  };

  // 🔧 追加: IME入力状態を監視するcompositionイベント
  useEffect(() => {
    if (!editorRef.current) return;

    const handleCompositionStart = () => {
      isComposingRef.current = true;
    };

    // 🔧 追加: compositionupdateイベントでIME入力中の状態を維持
    const handleCompositionUpdate = () => {
      isComposingRef.current = true;
    };

    const handleCompositionEnd = () => {
      // 🔧 修正: IME入力確定後は即座にフラグをリセット
      setTimeout(() => {
        isComposingRef.current = false;
      }, 10); // 非常に短い遅延でフラグをリセット

      // 🔧 修正: IME入力終了後に遅延してonChangeを呼び出し
      setTimeout(() => {
        if (editorRef.current) {
          const currentContent = editorRef.current.innerHTML;
          if (currentContent !== lastProcessedContentRef.current) {
            onChange(currentContent);
            lastProcessedContentRef.current = currentContent;

            // 🔧 追加: IME入力終了後の自動スクロール
            scrollToCursor();
          }
        }
      }, 100); // 100ms遅延
    };

    // 🔧 追加: 入力イベントでの自動スクロール
    const handleInputEvent = () => {
      // 入力操作時に積極的に自動スクロール
      setTimeout(() => {
        scrollToCursor();
      }, 10); // 非常に短い遅延で即座にスクロール
    };

    const editor = editorRef.current;
    editor.addEventListener('compositionstart', handleCompositionStart);
    editor.addEventListener('compositionupdate', handleCompositionUpdate);
    editor.addEventListener('compositionend', handleCompositionEnd);
    editor.addEventListener('input', handleInputEvent);

    return () => {
      editor.removeEventListener('compositionstart', handleCompositionStart);
      editor.removeEventListener('compositionupdate', handleCompositionUpdate);
      editor.removeEventListener('compositionend', handleCompositionEnd);
      editor.removeEventListener('input', handleInputEvent);
    };
  }, [editorRef]);

  // DOM変更を監視するMutationObserver（デバッグログは削除）

  return {
    handleInput,
    handleFocus,
    handleBlur,
    handleClick,
    handlePlaceholderClick
  };
};