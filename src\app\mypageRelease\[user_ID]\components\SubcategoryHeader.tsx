import React from "react";
import { useSortable } from "@dnd-kit/sortable";

interface SubcategoryHeaderProps {
  subcatId: string;
  subcatName: string;
  isExpanded: boolean;
  onToggle: () => void;
  isActive: boolean;
}

export const SubcategoryHeader: React.FC<SubcategoryHeaderProps> = ({
  subcatId,
  subcatName,
  isExpanded,
  onToggle,
  isActive,
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: `subcat_${subcatId}` });

  const style = {
    transform: transform
      ? `translate3d(0,${transform.y}px,0)`
      : undefined,
    transition,
    zIndex: isActive ? 10 : 1,
    opacity: isActive ? 0.8 : 1,
    touchAction: "none" as const,
    position: "relative" as const,
    background: isActive ? "rgba(240,240,240,0.8)" : "transparent",
    borderRadius: "8px",
    boxShadow: isActive ? "0 5px 15px rgba(0,0,0,.2)" : "none",
    width: "100%",
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`w-full ${isActive ? "shadow-lg" : ""}`}
      data-id={`subcat_${subcatId}`}
      {...attributes}
      {...listeners}
    >
      <button
        onClick={(e: React.MouseEvent) => {
          e.preventDefault();
          e.stopPropagation();
          onToggle();
        }}
        className="w-full flex justify-between items-center p-3 bg-gray-100"
      >
        <span className="font-bold">{subcatName}</span>
        <span className="p-1">
          {isExpanded ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 15l7-7 7 7"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          )}
        </span>
      </button>
    </div>
  );
};