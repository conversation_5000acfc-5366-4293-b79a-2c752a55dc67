/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import PropTypes from "prop-types";
import React from "react";

interface Props {
  text: string;
}

export const ItemName = ({ text = "基本情報" }: Props): JSX.Element => {
  return (
    <div className="max-w-[500px] h-[46px] items-center relative bg-background-color-gray1 border-b [border-bottom-style:solid] border-line-color">
      <div className="flex max-w-[500px] h-[46px] items-center pl-[16px] pr-0 py-0 relative">
        <p className="relative w-fit  text-[#aaaaaa] text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
          <span className=" text-[#aaaaaa] text-[12px] tracking-[0]">
            {text}
          </span>
          <span className="text-[10px]">（任意）</span>
        </p>
      </div>
    </div>
  );
};

ItemName.propTypes = {
  text: PropTypes.string,
};
