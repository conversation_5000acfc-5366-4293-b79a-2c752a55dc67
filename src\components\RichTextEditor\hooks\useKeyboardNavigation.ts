import { useEffect, useRef } from 'react';

// グローバル型定義の拡張
declare global {
  interface Window {
    __isIntentionalEmbedDelete?: boolean;
  }
}

interface UseKeyboardNavigationProps {
  editorRef: React.RefObject<HTMLDivElement>;
  handleInput: () => void;
  removeEmbedFromProtection?: (embedId: string) => void;
  markKeyboardDeletion?: () => void;
  markKeyboardOperation?: () => void; // 🔧 追加: キーボード操作マーク関数
}

export const useKeyboardNavigation = ({
  editorRef,
  handleInput,
  removeEmbedFromProtection,
  markKeyboardDeletion
}: UseKeyboardNavigationProps) => {

  // 🔧 追加: 連続Enterキーイベントを防ぐためのタイムスタンプ管理
  const lastEnterTimeRef = useRef<number>(0);
  const isProcessingEnterRef = useRef<boolean>(false);
  const ENTER_DEBOUNCE_TIME = 50; // 50ms以内の連続Enterキーを無視

  // 🔧 改善: 自動スクロール機能（カーソルを画面下1/3に維持）
  const scrollToCursor = () => {
    if (!editorRef.current) return;

    // 🔧 追加: SNS埋め込み処理中は自動スクロールを無効化
    if (typeof window !== 'undefined' && (window as any).__isEmbedProcessing) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[AUTO_SCROLL] SNS埋め込み処理中のため自動スクロールをスキップします');
      }
      return;
    }

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    // エディターの高さの1/3の位置を理想的なカーソル位置とする
    const idealCursorPosition = editorRect.top + (editorRect.height * 2/3);

    // カーソルが理想位置より下にある場合、または下部の1/3以内にある場合にスクロール
    const shouldScroll = rect.bottom > idealCursorPosition;

    if (shouldScroll) {
      // カーソルを画面の下1/3の位置に配置するためのスクロール量を計算
      const targetCursorY = editorRect.top + (editorRect.height * 2/3);
      const scrollAdjustment = rect.bottom - targetCursorY;
      const targetScrollTop = editorRef.current.scrollTop + scrollAdjustment + 20; // 少し余裕を持たせる

      editorRef.current.scrollTo({
        top: Math.max(0, targetScrollTop), // 負の値にならないように
        behavior: 'smooth'
      });


    }
  };

  // 🔧 編集可能な要素を探すヘルパー関数（改善版）
  const findEditableElement = (embedElement: Element, isBackward: boolean): Element | null => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[KEYBOARD_NAV] findEditableElement開始: ${isBackward ? '後方' : '前方'}検索, 基準要素: ${embedElement.tagName}.${embedElement.className}`);
    }

    let targetElement = isBackward
      ? embedElement.previousElementSibling
      : embedElement.nextElementSibling;

    let stepCount = 0;
    // スペーサー段落をスキップして編集可能な要素を探す
    while (targetElement && stepCount < 10) { // 無限ループ防止
      stepCount++;

      if (process.env.NODE_ENV === 'development') {
        console.log(`[KEYBOARD_NAV] 検索ステップ${stepCount}: ${targetElement.tagName}.${targetElement.className}, スペーサー: ${!!targetElement.getAttribute('data-embed-spacer')}, 埋め込み: ${targetElement.classList.contains('social-embed')}`);
      }

      // スペーサー段落の場合はスキップ
      if (targetElement.getAttribute('data-embed-spacer')) {
        targetElement = isBackward
          ? targetElement.previousElementSibling
          : targetElement.nextElementSibling;
        continue;
      }

      // 埋め込み要素の場合もスキップ
      if (targetElement.classList.contains('social-embed')) {
        targetElement = isBackward
          ? targetElement.previousElementSibling
          : targetElement.nextElementSibling;
        continue;
      }

      // 編集可能な要素が見つかった（P, DIV, H1-H6など）
      if (targetElement.tagName === 'P' ||
          targetElement.tagName === 'DIV' ||
          /^H[1-6]$/.test(targetElement.tagName)) {

        if (process.env.NODE_ENV === 'development') {
          console.log(`[KEYBOARD_NAV] 編集可能な要素を発見: ${targetElement.tagName}, 内容: "${targetElement.textContent}"`);
        }
        return targetElement;
      }

      // その他の要素の場合は次を探す
      targetElement = isBackward
        ? targetElement.previousElementSibling
        : targetElement.nextElementSibling;
    }

    // 適切な要素が見つからない場合、新しい段落を作成
    if (process.env.NODE_ENV === 'development') {
      console.log('[KEYBOARD_NAV] 編集可能な要素が見つからないため、新しい段落を作成します');
    }

    const newParagraph = document.createElement('p');
    newParagraph.innerHTML = '<br>';

    if (isBackward) {
      embedElement.parentNode?.insertBefore(newParagraph, embedElement);
    } else {
      embedElement.parentNode?.insertBefore(newParagraph, embedElement.nextSibling);
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('[KEYBOARD_NAV] 新しい段落を作成しました');
    }

    return newParagraph;
  };

  // 🔧 修正: カーソルを要素に移動するヘルパー関数（カーソルジャンプ問題対応）
  const moveCaretToElement = (targetElement: Element, isBackward: boolean, selection: Selection) => {
    try {
      // 🔧 重要: 空のDIV要素への不適切な移動を防止
      if (targetElement.tagName === 'DIV' &&
          (!targetElement.textContent || targetElement.textContent.trim() === '') &&
          targetElement.innerHTML === '') {
        if (process.env.NODE_ENV === 'development') {
          console.log('[KEYBOARD_NAV] 空のDIV要素への移動をスキップしました（カーソルジャンプ防止）');
        }
        return;
      }

      const newRange = document.createRange();

      // 🔧 改善: より確実なカーソル位置設定
      if (isBackward) {
        // 前の要素の最後にカーソルを移動
        if (targetElement.textContent && targetElement.textContent.length > 0) {
          // 最後のテキストノードを探す
          let lastTextNode: Node | null = null;
          const walker = document.createTreeWalker(
            targetElement,
            NodeFilter.SHOW_TEXT,
            null
          );

          let node: Node | null;
          while (node = walker.nextNode()) {
            lastTextNode = node;
          }

          if (lastTextNode && lastTextNode.textContent) {
            newRange.setStart(lastTextNode, lastTextNode.textContent.length);
          } else {
            newRange.selectNodeContents(targetElement);
            newRange.collapse(false);
          }
        } else {
          // 空の要素の場合は要素内にカーソルを設定
          if (targetElement.innerHTML === '<br>') {
            newRange.setStart(targetElement, 0);
          } else {
            targetElement.innerHTML = '<br>';
            newRange.setStart(targetElement, 0);
          }
        }
      } else {
        // 次の要素の最初にカーソルを移動
        if (targetElement.childNodes.length > 0) {
          const firstNode = targetElement.childNodes[0];
          if (firstNode.nodeType === Node.TEXT_NODE) {
            newRange.setStart(firstNode, 0);
          } else if (firstNode.nodeName === 'BR') {
            newRange.setStart(targetElement, 0);
          } else {
            newRange.setStart(targetElement, 0);
          }
        } else {
          // 空の要素の場合はBRタグを追加
          targetElement.innerHTML = '<br>';
          newRange.setStart(targetElement, 0);
        }
      }

      newRange.collapse(true);
      selection.removeAllRanges();
      selection.addRange(newRange);

      // 🔧 カーソル位置を確実に表示するためにフォーカスを設定
      if (editorRef.current) {
        editorRef.current.focus();

        // 🔧 追加: カーソル移動後の自動スクロール
        setTimeout(() => {
          scrollToCursor();
        }, 10);
      }

      // カーソル移動完了（デバッグログは削除）
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('カーソル移動中にエラーが発生しました:', error);
      }
    }
  };

  // 🔧 埋め込み要素境界でのナビゲーション処理（強化版）
  const handleEmbedBoundaryNavigation = (
    e: React.KeyboardEvent<HTMLDivElement>,
    parentElement: HTMLElement | null,
    range: Range,
    selection: Selection
  ) => {
    if (!parentElement) return;

    const isForward = e.key === 'ArrowDown' || e.key === 'ArrowRight';
    const isBackward = e.key === 'ArrowUp' || e.key === 'ArrowLeft';

    // 🔧 前方移動の処理（埋め込み要素の直前での↓キー対応）
    if (isForward) {
      // テキストの最後または段落の最後にいる場合
      const isAtEnd = range.startOffset === (parentElement.textContent?.length || 0) ||
                     (range.commonAncestorContainer.nodeType === Node.TEXT_NODE &&
                      range.startOffset === range.commonAncestorContainer.textContent?.length);

      if (isAtEnd) {
        // 次の要素をチェック（埋め込み要素を含む）
        let nextElement = parentElement.nextElementSibling;

        // スペーサー段落をスキップ
        while (nextElement && nextElement.getAttribute('data-embed-spacer')) {
          nextElement = nextElement.nextElementSibling;
        }

        // 次の要素が埋め込み要素の場合、その後の編集可能要素を探す
        if (nextElement && nextElement.classList.contains('social-embed')) {
          e.preventDefault();

          const targetElement = findEditableElement(nextElement, false);
          if (targetElement) {
            moveCaretToElement(targetElement, false, selection);

            if (process.env.NODE_ENV === 'development') {
              console.log('[KEYBOARD_NAV] 埋め込み要素の直前から直後へ移動しました');
            }
          }
          return;
        }

        // 通常の編集可能要素への移動
        if (nextElement && (nextElement.tagName === 'P' || nextElement.tagName === 'DIV' || /^H[1-6]$/.test(nextElement.tagName))) {
          e.preventDefault();
          moveCaretToElement(nextElement, false, selection);
          return;
        }
      }
    }

    // 🔧 後方移動の処理（埋め込み要素の直後での↑キー対応）
    if (isBackward) {
      // テキストの最初または段落の最初にいる場合
      const isAtStart = range.startOffset === 0;

      if (isAtStart) {
        // 前の要素をチェック（埋め込み要素を含む）
        let prevElement = parentElement.previousElementSibling;

        // スペーサー段落をスキップ
        while (prevElement && prevElement.getAttribute('data-embed-spacer')) {
          prevElement = prevElement.previousElementSibling;
        }

        // 前の要素が埋め込み要素の場合、その前の編集可能要素を探す
        if (prevElement && prevElement.classList.contains('social-embed')) {
          e.preventDefault();

          const targetElement = findEditableElement(prevElement, true);
          if (targetElement) {
            moveCaretToElement(targetElement, true, selection);

            if (process.env.NODE_ENV === 'development') {
              console.log('[KEYBOARD_NAV] 埋め込み要素の直後から直前へ移動しました');
            }
          }
          return;
        }

        // 通常の編集可能要素への移動
        if (prevElement && (prevElement.tagName === 'P' || prevElement.tagName === 'DIV' || /^H[1-6]$/.test(prevElement.tagName))) {
          e.preventDefault();
          moveCaretToElement(prevElement, true, selection);
          return;
        }
      }
    }
  };

  // 🔧 強化: 埋め込み要素削除のヘルパー関数（埋め込み数が多い場合の対策）
  const deleteEmbedElement = (embedElement: Element, selection: Selection) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('[EMBED_DELETE] deleteEmbedElement関数開始');
      }

      // 🔧 最優先: 意図的削除フラグを設定
      window.__isIntentionalEmbedDelete = true;
      if (process.env.NODE_ENV === 'development') {
        console.log('[EMBED_DELETE] 意図的削除フラグを設定しました（最優先）');
      }

      // 🔧 削除前に残存する埋め込み要素を安定化
      if (editorRef.current) {
        const remainingEmbeds = editorRef.current.querySelectorAll('.social-embed:not([data-embed-stable])');
        remainingEmbeds.forEach((embed) => {
          if (embed !== embedElement && embed instanceof HTMLElement) {
            embed.setAttribute('data-embed-stable', 'true');
            if (process.env.NODE_ENV === 'development') {
              console.log('[EMBED_DELETE] 残存埋め込み要素を安定化しました');
            }
          }
        });
      }

      // 🔧 強化: 埋め込み数をチェック
      const totalEmbeds = editorRef.current?.querySelectorAll('.social-embed').length || 0;

      // 埋め込み要素のIDを取得して保護システムから除外
      const embedId = embedElement.getAttribute('data-embed-id');
      if (embedId && removeEmbedFromProtection) {
        removeEmbedFromProtection(embedId);
        if (process.env.NODE_ENV === 'development') {
          console.log(`[EMBED_DELETE] 保護システムから除外: ${embedId}`);
        }
      }

      // 🔧 強化: 埋め込み数が多い場合の特別処理
      if (totalEmbeds >= 5) {
        // 拡張機能のSecurityErrorを防ぐため、削除前に一時的にiframeを無効化
        const iframes = embedElement.querySelectorAll('iframe');
        iframes.forEach(iframe => {
          iframe.style.display = 'none';
          iframe.src = 'about:blank';
        });

        // 少し待ってから削除実行
        setTimeout(() => {
          if (embedElement.isConnected) {
            performEmbedDeletion(embedElement, selection);
          }
        }, 10);
      } else {
        // 埋め込み数が少ない場合は即座に削除
        performEmbedDeletion(embedElement, selection);
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('埋め込み要素の削除中にエラーが発生しました:', error);
      }
      // エラーが発生した場合でも変更を通知
      handleInput();
    }
  };

  // 🔧 強化: 実際の削除処理を分離
  const performEmbedDeletion = (embedElement: Element, selection: Selection) => {
    try {
      // 🔧 重要: 削除前に保護システムから確実に除外（既に上位関数で実行済み）
      const embedId = embedElement.getAttribute('data-embed-id');
      if (process.env.NODE_ENV === 'development') {
        console.log(`[EMBED_DELETE] performEmbedDeletion実行中: ${embedId}`);
      }

      // 埋め込み要素の前後のスペーサー段落を取得
      const prevElement = embedElement.previousElementSibling;
      const nextElement = embedElement.nextElementSibling;

    // 削除前にカーソル移動先を決定
    let targetElement: Element | null = null;
    let cursorAtEnd = false;

    // 🔧 改善: より安全な要素検索（テキスト要素のみを対象）
    let checkElement = prevElement;
    while (checkElement && (checkElement.getAttribute('data-embed-spacer') || checkElement.classList.contains('social-embed'))) {
      checkElement = checkElement.previousElementSibling;
    }
    if (checkElement && (checkElement.tagName === 'P' || checkElement.tagName === 'DIV')) {
      targetElement = checkElement;
      cursorAtEnd = true;
    } else {
      // 前に適切な要素がない場合は次の要素を探す
      checkElement = nextElement;
      while (checkElement && (checkElement.getAttribute('data-embed-spacer') || checkElement.classList.contains('social-embed'))) {
        checkElement = checkElement.nextElementSibling;
      }
      if (checkElement && (checkElement.tagName === 'P' || checkElement.tagName === 'DIV')) {
        targetElement = checkElement;
        cursorAtEnd = false;
      }
    }

    // 🔧 移動先がない場合は新しい段落を作成
    if (!targetElement) {
      const newParagraph = document.createElement('p');
      newParagraph.innerHTML = '<br>';

      // 埋め込み要素の後に新しい段落を挿入
      if (embedElement.parentNode) {
        embedElement.parentNode.insertBefore(newParagraph, embedElement.nextSibling);
        targetElement = newParagraph;
        cursorAtEnd = false;
      }
    }

    // 🔧 修正: スペーサー段落のみを削除（テキスト段落は保護）
    if (prevElement && prevElement.getAttribute('data-embed-spacer') &&
        (!prevElement.textContent || prevElement.textContent.trim() === '')) {
      prevElement.remove();
    }
    if (nextElement && nextElement.getAttribute('data-embed-spacer') &&
        (!nextElement.textContent || nextElement.textContent.trim() === '')) {
      nextElement.remove();
    }

    // 埋め込み要素を削除
    embedElement.remove();

    // カーソルを適切な位置に移動
    if (targetElement) {
      const newRange = document.createRange();
      if (cursorAtEnd) {
        // 前の要素の最後にカーソルを移動
        if (targetElement.textContent && targetElement.textContent.length > 0) {
          newRange.selectNodeContents(targetElement);
          newRange.collapse(false);
        } else {
          newRange.setStart(targetElement, 0);
          newRange.collapse(true);
        }
      } else {
        // 次の要素の最初にカーソルを移動
        newRange.setStart(targetElement, 0);
        newRange.collapse(true);
      }
      selection.removeAllRanges();
      selection.addRange(newRange);
    }
    // 🔧 修正: 移動先がない場合でもエディタの最初に強制移動しない
    // カーソル位置はユーザーの意図した場所に保持される

      // 🔧 重要: 削除後にエディタが空になった場合、または末尾が埋め込み要素の場合は空段落を追加
      setTimeout(() => {
        if (editorRef.current) {
          const lastChild = editorRef.current.lastElementChild;
          const hasNoEditableContent = !editorRef.current.textContent?.trim();
          const endsWithEmbed = lastChild?.classList.contains('social-embed');

          if (hasNoEditableContent || endsWithEmbed) {
            // 編集可能な空段落を追加
            const emptyParagraph = document.createElement('p');
            emptyParagraph.innerHTML = '<br>';
            editorRef.current.appendChild(emptyParagraph);

            // カーソルを新しい段落に移動
            const newRange = document.createRange();
            newRange.setStart(emptyParagraph, 0);
            newRange.collapse(true);

            const newSelection = window.getSelection();
            if (newSelection) {
              newSelection.removeAllRanges();
              newSelection.addRange(newRange);
            }

            if (process.env.NODE_ENV === 'development') {
              console.log('[EMBED_DELETE] 編集可能な空段落を追加しました');
            }
          }
        }
      }, 50);

      // 変更を親コンポーネントに通知
      handleInput();
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('埋め込み要素の削除中にエラーが発生しました:', error);
      }
      // エラーが発生した場合でも変更を通知
      handleInput();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {

    const selection = window.getSelection();

    // 🔧 最優先: 埋め込み要素の削除を強制的にインターセプト
    if ((e.key === 'Backspace' || e.key === 'Delete') && selection && selection.rangeCount > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[KEYBOARD_NAV] 削除キー検知: ${e.key}`);
      }
      const range = selection.getRangeAt(0);

      // 現在のカーソル位置から埋め込み要素を検索
      let currentElement = range.commonAncestorContainer.nodeType === 1
        ? range.commonAncestorContainer as HTMLElement
        : range.commonAncestorContainer.parentElement;

      // 埋め込み要素内にカーソルがある場合
      const embedElement = currentElement?.closest('.social-embed');
      if (embedElement) {
        e.preventDefault();
        if (process.env.NODE_ENV === 'development') {
          console.log('[EMBED_DELETE] 埋め込み要素内での削除操作を検知しました');
        }
        deleteEmbedElement(embedElement, selection);
        return;
      }

      // 埋め込み要素の直前/直後での削除操作をチェック
      if (currentElement) {
        let targetEmbed: Element | null = null;

        if (e.key === 'Backspace') {
          // Backspace: 前の要素をチェック
          let prevElement = currentElement.previousElementSibling;
          while (prevElement && prevElement.getAttribute('data-embed-spacer')) {
            prevElement = prevElement.previousElementSibling;
          }
          if (prevElement?.classList.contains('social-embed')) {
            targetEmbed = prevElement;
          }
        } else if (e.key === 'Delete') {
          // Delete: 次の要素をチェック
          let nextElement = currentElement.nextElementSibling;
          while (nextElement && nextElement.getAttribute('data-embed-spacer')) {
            nextElement = nextElement.nextElementSibling;
          }
          if (nextElement?.classList.contains('social-embed')) {
            targetEmbed = nextElement;
          }
        }

        if (targetEmbed) {
          e.preventDefault();
          if (process.env.NODE_ENV === 'development') {
            console.log('[EMBED_DELETE] 埋め込み要素の隣接削除操作を検知しました');
          }
          deleteEmbedElement(targetEmbed, selection);
          return;
        }
      }
    }

    // 🔧 選択範囲内に埋め込み要素が含まれている場合の削除処理
    if ((e.key === 'Backspace' || e.key === 'Delete') && selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // 選択範囲がある場合（テキストが選択されている場合）
      if (!range.collapsed) {
        const selectedContent = range.cloneContents();
        const embedsInSelection = selectedContent.querySelectorAll('.social-embed');

        if (embedsInSelection.length > 0) {
          e.preventDefault();

          // 🔧 キーボード削除操作を記録
          if (markKeyboardDeletion) {
            markKeyboardDeletion();
          }

          // 🔧 意図的削除フラグを設定
          window.__isIntentionalEmbedDelete = true;
          if (process.env.NODE_ENV === 'development') {
            console.log('[EMBED_DELETE] 選択範囲削除: 意図的削除フラグを設定しました');
          }

          // 選択範囲内の埋め込み要素を保護システムから除外
          embedsInSelection.forEach(embed => {
            const embedId = embed.getAttribute('data-embed-id');
            if (embedId && removeEmbedFromProtection) {
              removeEmbedFromProtection(embedId);
            }
          });

          // 通常の削除処理を実行
          range.deleteContents();

          // 変更を親コンポーネントに通知
          handleInput();
          return;
        }
      }
    }

    // 🔧 改善: Enterキーの処理（IME入力確定状態を考慮 + 連続イベント防止）
    if (e.key === 'Enter') {
      // 🔧 追加: 連続Enterキーイベントの防止（タイムスタンプ + 処理中フラグ）
      const currentTime = Date.now();
      const timeSinceLastEnter = currentTime - lastEnterTimeRef.current;

      if (timeSinceLastEnter < ENTER_DEBOUNCE_TIME || isProcessingEnterRef.current) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[KEYBOARD_NAV] 連続Enterキーを無視:', {
            timeSinceLastEnter,
            debounceTime: ENTER_DEBOUNCE_TIME,
            isProcessing: isProcessingEnterRef.current
          });
        }
        e.preventDefault();
        return;
      }

      // 🔧 追加: 処理開始フラグを設定
      isProcessingEnterRef.current = true;

      // 🔧 追加: 複数の方法でIME入力状態を確認
      const isIMEComposing = (e.nativeEvent as any).isComposing || false;
      const hasCompositionData = (e.nativeEvent as any).data !== null && (e.nativeEvent as any).data !== undefined;

      // 🔧 修正: IME入力中でない場合は改行処理を実行
      // IME入力確定後（isComposing=false かつ compositionDataがない）の場合は改行を許可
      if (!isIMEComposing && selection && selection.rangeCount > 0) {
        // 🔧 追加: Enterキー処理のタイムスタンプを更新
        lastEnterTimeRef.current = currentTime;

        // 🔧 追加: 処理完了後にフラグをリセット
        setTimeout(() => {
          isProcessingEnterRef.current = false;
        }, ENTER_DEBOUNCE_TIME);
        const range = selection.getRangeAt(0);
        const parentElement = range.commonAncestorContainer.nodeType === 1
          ? range.commonAncestorContainer as HTMLElement
          : range.commonAncestorContainer.parentElement;

        // SNS埋め込み要素の直前でEnterキーが押された場合の処理
        const embedElement = parentElement?.closest('.social-embed');
        if (embedElement) {
          e.preventDefault();

          // 埋め込み要素の前に新しい段落を挿入
          const newParagraph = document.createElement('p');
          newParagraph.innerHTML = '<br>';

          embedElement.parentNode?.insertBefore(newParagraph, embedElement);

          // 新しい段落にカーソルを移動
          const newRange = document.createRange();
          newRange.setStart(newParagraph, 0);
          newRange.collapse(true);

          selection.removeAllRanges();
          selection.addRange(newRange);

          // 変更を親コンポーネントに通知
          handleInput();
          return;
        }

        // SNS埋め込み要素の直前の段落でEnterキーが押された場合
        const nextElement = parentElement?.nextElementSibling;
        if (nextElement && nextElement.classList.contains('social-embed')) {
          e.preventDefault();

          // 現在の段落と埋め込み要素の間に新しい段落を挿入
          const newParagraph = document.createElement('p');
          newParagraph.innerHTML = '<br>';

          parentElement?.parentNode?.insertBefore(newParagraph, nextElement);

          // 新しい段落にカーソルを移動
          const newRange = document.createRange();
          newRange.setStart(newParagraph, 0);
          newRange.collapse(true);

          selection.removeAllRanges();
          selection.addRange(newRange);

          // 変更を親コンポーネントに通知
          handleInput();
          return;
        }

        // 🔧 修正: H2タグ内での改行処理（見出し要素からの脱出も考慮）
        if (parentElement && parentElement.tagName === 'H2') {
          e.preventDefault();

          // 🔧 追加: 見出し要素の最後でEnterキーが押された場合は新しい段落を作成
          const textLength = parentElement.textContent?.replace(/\u200B/g, '').length || 0;
          const isAtEnd = range.startOffset >= textLength;

          if (isAtEnd) {
            // 見出し要素の後に新しい段落を作成
            const newParagraph = document.createElement('p');
            newParagraph.innerHTML = '<br>';

            if (parentElement.nextSibling) {
              parentElement.parentNode?.insertBefore(newParagraph, parentElement.nextSibling);
            } else {
              parentElement.parentNode?.appendChild(newParagraph);
            }

            // 新しい段落にカーソルを移動
            const newRange = document.createRange();
            newRange.setStart(newParagraph, 0);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            if (process.env.NODE_ENV === 'development') {
              console.log('[KEYBOARD_NAV] H2要素から新しい段落に移動しました');
            }
          } else {
            // 見出し要素内での行内改行
            const br = document.createElement('br');
            range.insertNode(br);

            const newRange = document.createRange();
            const nextNode = br.nextSibling;
            if (!nextNode || nextNode.nodeType !== Node.TEXT_NODE) {
              const textNode = document.createTextNode('');
              br.parentNode?.insertBefore(textNode, br.nextSibling);
              newRange.setStart(textNode, 0);
            } else {
              newRange.setStart(nextNode, 0);
            }

            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            if (process.env.NODE_ENV === 'development') {
              console.log('[KEYBOARD_NAV] H2要素内で行内改行を実行しました');
            }
          }

          // 変更を親コンポーネントに通知
          handleInput();

          // 🔧 追加: 自動スクロール処理
          scrollToCursor();

          return;
        }

        // 🔧 修正: H3タグ内での改行処理（見出し要素からの脱出も考慮）
        if (parentElement && parentElement.tagName === 'H3') {
          e.preventDefault();

          // 🔧 追加: 見出し要素の最後でEnterキーが押された場合は新しい段落を作成
          const textLength = parentElement.textContent?.replace(/\u200B/g, '').length || 0;
          const isAtEnd = range.startOffset >= textLength;

          if (isAtEnd) {
            // 見出し要素の後に新しい段落を作成
            const newParagraph = document.createElement('p');
            newParagraph.innerHTML = '<br>';

            if (parentElement.nextSibling) {
              parentElement.parentNode?.insertBefore(newParagraph, parentElement.nextSibling);
            } else {
              parentElement.parentNode?.appendChild(newParagraph);
            }

            // 新しい段落にカーソルを移動
            const newRange = document.createRange();
            newRange.setStart(newParagraph, 0);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            if (process.env.NODE_ENV === 'development') {
              console.log('[KEYBOARD_NAV] H3要素から新しい段落に移動しました');
            }
          } else {
            // 見出し要素内での行内改行
            const br = document.createElement('br');
            range.insertNode(br);

            const newRange = document.createRange();
            const nextNode = br.nextSibling;
            if (!nextNode || nextNode.nodeType !== Node.TEXT_NODE) {
              const textNode = document.createTextNode('');
              br.parentNode?.insertBefore(textNode, br.nextSibling);
              newRange.setStart(textNode, 0);
            } else {
              newRange.setStart(nextNode, 0);
            }

            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            if (process.env.NODE_ENV === 'development') {
              console.log('[KEYBOARD_NAV] H3要素内で行内改行を実行しました');
            }
          }

          // 変更を親コンポーネントに通知
          handleInput();

          // 🔧 追加: 自動スクロール処理
          scrollToCursor();

          return;
        }

        // 🔧 修正: 通常のEnterキーを行内改行（<br>タグ）に変更
        if (!e.shiftKey) {
          e.preventDefault();

          // 現在のカーソル位置に<br>タグを挿入
          const br = document.createElement('br');
          range.insertNode(br);

          // 🔧 修正: カーソル位置を確実に次の行に移動
          const newRange = document.createRange();

          // <br>タグの後に空のテキストノードを作成してカーソルを配置
          const textNode = document.createTextNode('\u200B'); // ゼロ幅スペース
          br.parentNode?.insertBefore(textNode, br.nextSibling);

          // カーソルをテキストノードの先頭に配置
          newRange.setStart(textNode, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          // 🔧 追加: フォーカスを確実に設定
          if (editorRef.current) {
            editorRef.current.focus();
          }

          // 変更を親コンポーネントに通知
          handleInput();

          // 🔧 追加: 自動スクロール処理
          scrollToCursor();


          return;
        }
      }
    }

    // 🔧 修正: Backspaceキーの処理を強化
    if (e.key === 'Backspace') {
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const parentElement = range.commonAncestorContainer.nodeType === 1
          ? range.commonAncestorContainer as HTMLElement
          : range.commonAncestorContainer.parentElement;

        // 🔧 SNS埋め込み要素内でBackspaceが押された場合、要素全体を削除
        const embedElement = parentElement?.closest('.social-embed');
        if (embedElement) {
          e.preventDefault();

          // 🔧 キーボード削除操作を記録
          if (markKeyboardDeletion) {
            markKeyboardDeletion();
          }



          deleteEmbedElement(embedElement, selection);
          return;
        }

        // 🔧 修正: カーソルが埋め込み要素の直後にある場合の処理を改善（過剰削除防止）
        if (parentElement && range.startOffset === 0) {
          // 🔧 重要: 現在の段落が空でない場合は通常のBackspace処理を行う
          const currentText = parentElement.textContent || '';
          if (currentText.trim() && range.startOffset < currentText.length) {
            // 通常のBackspace処理に委ねる（過剰削除防止）
            return;
          }

          // 🔧 重要: 空のDIV要素での不適切な処理を防止
          if (parentElement.tagName === 'DIV' &&
              (!parentElement.textContent || parentElement.textContent.trim() === '') &&
              parentElement.innerHTML === '') {
            if (process.env.NODE_ENV === 'development') {
              console.log('[KEYBOARD_NAV] 空のDIV要素でのBackspace処理をスキップしました（カーソルジャンプ防止）');
            }
            return;
          }

          // 現在の要素の前の兄弟要素をチェック
          let prevSibling = parentElement.previousElementSibling;

          // スペーサー段落をスキップして埋め込み要素を探す
          if (prevSibling && prevSibling.getAttribute('data-embed-spacer') === 'after') {
            prevSibling = prevSibling.previousElementSibling;
          }

          if (prevSibling && prevSibling.classList.contains('social-embed')) {
            e.preventDefault();

            // 🔧 キーボード削除操作を記録
            if (markKeyboardDeletion) {
              markKeyboardDeletion();
            }

            // 🔧 安全な削除: 埋め込み要素のみを削除
            deleteEmbedElement(prevSibling, selection);

            // 🔧 修正: カーソル位置の復元を改善（空のDIV要素への移動を防止）
            setTimeout(() => {
              if (parentElement && parentElement.isConnected) {
                // 空のDIV要素の場合は適切な内容を設定
                if (parentElement.tagName === 'DIV' &&
                    (!parentElement.textContent || parentElement.textContent.trim() === '')) {
                  parentElement.innerHTML = '<br>';
                }

                const newRange = document.createRange();
                newRange.setStart(parentElement, 0);
                newRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(newRange);
              }
            }, 10);
            return;
          }
        }

        // 🔧 テキストノード内でのBackspace処理も改善
        if (range.commonAncestorContainer.nodeType === Node.TEXT_NODE) {
          const textNode = range.commonAncestorContainer;
          const textParent = textNode.parentElement;

          // テキストの最初でBackspaceが押された場合
          if (range.startOffset === 0 && textParent) {
            let prevSibling = textParent.previousElementSibling;

            // スペーサー段落をスキップ
            if (prevSibling && prevSibling.getAttribute('data-embed-spacer') === 'after') {
              prevSibling = prevSibling.previousElementSibling;
            }

            if (prevSibling && prevSibling.classList.contains('social-embed')) {
              e.preventDefault();

              // 🔧 キーボード削除操作を記録
              if (markKeyboardDeletion) {
                markKeyboardDeletion();
              }



              deleteEmbedElement(prevSibling, selection);
              return;
            }
          }
        }
      }
    }

    // 🔧 修正: Deleteキーの処理を強化
    if (e.key === 'Delete') {
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const parentElement = range.commonAncestorContainer.nodeType === 1
          ? range.commonAncestorContainer as HTMLElement
          : range.commonAncestorContainer.parentElement;

        // 🔧 SNS埋め込み要素内でDeleteが押された場合、要素全体を削除
        const embedElement = parentElement?.closest('.social-embed');
        if (embedElement) {
          e.preventDefault();

          // 🔧 キーボード削除操作を記録
          if (markKeyboardDeletion) {
            markKeyboardDeletion();
          }



          deleteEmbedElement(embedElement, selection);
          return;
        }

        // 🔧 カーソルが埋め込み要素の直前にある場合の処理を強化
        if (parentElement) {
          // 現在の要素の次の兄弟要素をチェック
          let nextSibling = parentElement.nextElementSibling;

          // スペーサー段落をスキップして埋め込み要素を探す
          if (nextSibling && nextSibling.getAttribute('data-embed-spacer') === 'before') {
            nextSibling = nextSibling.nextElementSibling;
          }

          if (nextSibling && nextSibling.classList.contains('social-embed')) {
            e.preventDefault();

            // 🔧 キーボード削除操作を記録
            if (markKeyboardDeletion) {
              markKeyboardDeletion();
            }



            deleteEmbedElement(nextSibling, selection);
            return;
          }
        }

        // 🔧 テキストノード内でのDelete処理も改善
        if (range.commonAncestorContainer.nodeType === Node.TEXT_NODE) {
          const textNode = range.commonAncestorContainer;
          const textParent = textNode.parentElement;
          const textContent = textNode.textContent || '';

          // テキストの最後でDeleteが押された場合
          if (range.startOffset === textContent.length && textParent) {
            let nextSibling = textParent.nextElementSibling;

            // スペーサー段落をスキップ
            if (nextSibling && nextSibling.getAttribute('data-embed-spacer') === 'before') {
              nextSibling = nextSibling.nextElementSibling;
            }

            if (nextSibling && nextSibling.classList.contains('social-embed')) {
              e.preventDefault();

              // 🔧 キーボード削除操作を記録
              if (markKeyboardDeletion) {
                markKeyboardDeletion();
              }



              deleteEmbedElement(nextSibling, selection);
              return;
            }
          }
        }

        // 🔧 段落の最後でDeleteが押され、次の要素がSNS埋め込み要素の場合
        if (parentElement && range.startOffset === (parentElement.textContent?.length || 0)) {
          let nextElement = parentElement.nextElementSibling;

          // スペーサー段落をスキップ
          if (nextElement && nextElement.getAttribute('data-embed-spacer') === 'before') {
            nextElement = nextElement.nextElementSibling;
          }

          if (nextElement && nextElement.classList.contains('social-embed')) {
            e.preventDefault();

            // 🔧 キーボード削除操作を記録
            if (markKeyboardDeletion) {
              markKeyboardDeletion();
            }



            deleteEmbedElement(nextElement, selection);
            return;
          }
        }
      }
    }

    // 🔧 修正: 矢印キーの処理を強化（埋め込み境界でのナビゲーション改善）
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown' || e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const parentElement = range.commonAncestorContainer.nodeType === 1
          ? range.commonAncestorContainer as HTMLElement
          : range.commonAncestorContainer.parentElement;

        // 🔧 SNS埋め込み要素内にカーソルがある場合の処理
        const embedElement = parentElement?.closest('.social-embed');
        if (embedElement) {
          e.preventDefault();

          // 🔧 シンプルな移動ロジック
          const isBackward = e.key === 'ArrowUp' || e.key === 'ArrowLeft';
          const targetElement = findEditableElement(embedElement, isBackward);

          if (targetElement) {
            moveCaretToElement(targetElement, isBackward, selection);
          }
          return;
        }

        // 🔧 追加: H2/H3要素からの脱出処理
        if (parentElement && (parentElement.tagName === 'H2' || parentElement.tagName === 'H3') && e.key === 'ArrowDown') {
          // 見出し要素の最後でArrowDownキーが押された場合
          const textLength = parentElement.textContent?.replace(/\u200B/g, '').length || 0; // ゼロ幅スペースを除外
          const isAtEnd = range.startOffset >= textLength;



          if (isAtEnd) {
            e.preventDefault();

            // 見出し要素の後に新しい段落を作成
            const newParagraph = document.createElement('p');
            newParagraph.innerHTML = '<br>';

            // 見出し要素の後に挿入
            if (parentElement.nextSibling) {
              parentElement.parentNode?.insertBefore(newParagraph, parentElement.nextSibling);
            } else {
              parentElement.parentNode?.appendChild(newParagraph);
            }

            // 新しい段落にカーソルを移動
            const newRange = document.createRange();
            newRange.setStart(newParagraph, 0);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            // 変更を通知
            handleInput();

            // 自動スクロール
            scrollToCursor();


            return;
          }
        }

        // 🔧 強化: 埋め込み要素の境界での矢印キー処理（↑↓キーのみ）
        if (parentElement && (e.key === 'ArrowUp' || e.key === 'ArrowDown')) {
          const isUpArrow = e.key === 'ArrowUp';

          // カーソルが段落の境界にある場合のみ処理
          const isAtBoundary = isUpArrow ?
            range.startOffset === 0 :
            range.startOffset === (parentElement.textContent?.length || 0);



          if (isAtBoundary) {
            // 隣接する要素をチェック
            let adjacentElement = isUpArrow ?
              parentElement.previousElementSibling :
              parentElement.nextElementSibling;

            if (process.env.NODE_ENV === 'development') {
              console.log(`[KEYBOARD_NAV] 隣接要素チェック:`, {
                adjacentElement: adjacentElement?.tagName,
                adjacentClass: adjacentElement?.className,
                isEmbed: adjacentElement?.classList.contains('social-embed'),
                isSpacerBefore: adjacentElement?.getAttribute('data-embed-spacer')
              });
            }

            // スペーサー段落をスキップ
            while (adjacentElement && adjacentElement.getAttribute('data-embed-spacer')) {
              adjacentElement = isUpArrow ?
                adjacentElement.previousElementSibling :
                adjacentElement.nextElementSibling;

              if (process.env.NODE_ENV === 'development') {
                console.log(`[KEYBOARD_NAV] スペーサーをスキップ:`, adjacentElement?.tagName);
              }
            }

            // 隣接要素が埋め込み要素の場合
            if (adjacentElement && adjacentElement.classList.contains('social-embed')) {
              e.preventDefault();

              if (process.env.NODE_ENV === 'development') {
                console.log(`[KEYBOARD_NAV] 埋め込み要素を発見、向こう側の要素を探します`);
              }

              // 🔧 修正: 埋め込み要素の向こう側の編集可能要素を探す
              // ↓キーの場合は埋め込み要素の後（前方検索=false）
              // ↑キーの場合は埋め込み要素の前（後方検索=true）
              const targetElement = findEditableElement(adjacentElement, isUpArrow);
              if (targetElement) {
                // 🔧 修正: カーソル位置も正しく設定
                // ↓キーの場合は移動先要素の先頭（false）
                // ↑キーの場合は移動先要素の末尾（true）
                moveCaretToElement(targetElement, isUpArrow, selection);

                if (process.env.NODE_ENV === 'development') {
                  console.log(`[KEYBOARD_NAV] 埋め込み要素をスキップして移動: ${isUpArrow ? '上' : '下'}方向, 移動先: ${targetElement.tagName}`);
                }
              } else {
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[KEYBOARD_NAV] 移動先の要素が見つかりませんでした`);
                }
              }
              return;
            }
          }
        }

        // 🔧 埋め込み要素の境界での自然な移動処理（左右キーと詳細処理）
        handleEmbedBoundaryNavigation(e, parentElement, range, selection);
      }
    }
  };

  // 🔧 beforeinputイベントで削除操作をインターセプト
  const handleBeforeInput = (e: Event) => {
    const inputEvent = e as InputEvent;

    // 削除系の操作をチェック
    if (inputEvent.inputType === 'deleteContentBackward' ||
        inputEvent.inputType === 'deleteContentForward' ||
        inputEvent.inputType === 'deleteByCut' ||
        inputEvent.inputType === 'deleteByDrag') {

      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      let currentElement = range.commonAncestorContainer.nodeType === 1
        ? range.commonAncestorContainer as HTMLElement
        : range.commonAncestorContainer.parentElement;

      // 埋め込み要素内での削除操作を防ぐ
      const embedElement = currentElement?.closest('.social-embed');
      if (embedElement) {
        e.preventDefault();

        // 意図的削除フラグを設定してから削除処理を実行
        window.__isIntentionalEmbedDelete = true;
        deleteEmbedElement(embedElement, selection);
        return;
      }
    }
  };

  // beforeinputイベントリスナーを設定
  useEffect(() => {
    const editor = editorRef.current;
    if (!editor) return;

    editor.addEventListener('beforeinput', handleBeforeInput);

    return () => {
      editor.removeEventListener('beforeinput', handleBeforeInput);
    };
  }, []);

  return {
    handleKeyDown
  };
};