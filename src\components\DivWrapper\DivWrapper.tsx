"use client";  // ← 追加

import PropTypes from "prop-types";
import React, { useState, useEffect } from "react";

interface Props {
  className?: string;
  text: string;
  text1: string;
  initialValue?: string;
  onSave?: (value: string) => void;
}

export const DivWrapper = ({
  className,
  text = "背景画像",
  text1,
  initialValue = "",
  onSave,
}: Props): JSX.Element => {
  const [inputValue, setInputValue] = useState(initialValue);

  // 初期値が変更された場合に更新
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleBlur = () => {
    // フォーカスが外れた時点で保存関数を呼び出す
    if (onSave) {
      onSave(inputValue);
    }
  };

  return (
    <div
      className={`flex max-w-[500px] flex justify-between relative bg-white border-b [border-bottom-style:solid] border-line ${className}`}
    >
      <div className="flex max-w-[250px] h-[48px] items-center pl-[16px] pr-0 py-0 relative">
        <div className="text-[14px] relative w-fit text-black-1 tracking-[0] leading-[normal] whitespace-nowrap">
          {text}
        </div>
      </div>
      <div className="flex max-w-[250px] h-[48px] items-center justify-end pl-0 pr-[16px] py-0 relative bg-white">
        <input
          type="text"
          className={`text-[12px] text-right relative w-full tracking-[0] leading-[normal] whitespace-nowrap outline-none bg-transparent
            ${inputValue ? "text-black" : "text-font-gray"}
            focus:caret-black`}
          value={inputValue}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={text1} // プレースホルダーを利用
        />
      </div>
    </div>
  );
};

DivWrapper.propTypes = {
  text: PropTypes.string,
  text1: PropTypes.string,
  className: PropTypes.string,
  initialValue: PropTypes.string,
  onSave: PropTypes.func,
};
