import { useRef, useCallback } from 'react';
import { debugLog, trackEmbedState, showEmbedError } from '../utils';
import {
  MIN_EXECUTION_INTERVAL,
  MIN_EMBED_PROCESS_INTERVAL,
  FLAG_RESET_DELAY,
  EMBED_FLAG_RESET_DELAY,
  API_CACHE_CLEANUP_DELAY,
  TIKTOK_EMBED_WIDTH,
  TIKTOK_EMBED_HEIGHT,
  SUPPORTED_PLATFORMS
} from '../constants';

// 🔧 Twitter widgets.js の型定義は既存のものを使用

// 🛠️ 1️⃣ デバッグログを追加（クライアントサイドのみ）
let whenTwitterReady: Promise<void>;

if (typeof window !== 'undefined') {
  // ❶ モジュールの一番上で widgets.js の準備完了を検知する Promise を作成
  whenTwitterReady = new Promise<void>((resolve) => {
    if (window.twttr && window.twttr.widgets) {
      resolve();
    } else {
      const check = setInterval(() => {
        if (window.twttr && window.twttr.widgets) {
          clearInterval(check);
          resolve();
        }
      }, 100);
    }
  });
} else {
  // SSR環境では即座に解決するPromiseを作成
  whenTwitterReady = Promise.resolve();
}

// ❷ グローバルに rendered イベントをバインド（クライアントサイドのみ）
let renderedEventBound = false;

if (typeof window !== 'undefined') {
  whenTwitterReady.then(() => {
    if (renderedEventBound) {
      return;
    }
    renderedEventBound = true;
    // 🔧 2️⃣ rendered コールバックも少し待ってから実高さを取得
    if (window.twttr && window.twttr.events) {
      window.twttr.events.bind('rendered', (ev: any) => {
    // target は DIV.twitter-tweet → その直下の iframe を探す
    const node    = ev.target as HTMLElement;
    const iframe  = node.querySelector('iframe');
    const wrapper = node.closest<HTMLElement>('.social-embed');

    if (!iframe || !wrapper) return;

    // rendered event発火時の処理（デバッグログは削除）

    // 50ms 後に高さ測定（CSS-aware height detection使用）
    setTimeout(() => {
      const embedId = wrapper.getAttribute('data-embed-id');

      // 🔧 簡素化: 自然な高さ調整のみ（固定化システム削除）
      const h = getTrueIframeHeight(iframe);

      // 高さ測定完了（デバッグログは削除）

      if (h > 200 && embedId) {
        // 🔧 パーセンテージベースの柔軟なサイズシステム
        const editorContainer = wrapper.closest('[contenteditable]') as HTMLElement;

        // 🔧 iframe をパーセンテージベースで調整
        iframe.style.setProperty('max-width', '100%', 'important');
        iframe.style.setProperty('width', '100%', 'important'); // パーセンテージベース
        iframe.style.setProperty('position', 'static', 'important');
        iframe.style.setProperty('transform', 'none', 'important');
        iframe.style.setProperty('top', '0', 'important');
        iframe.style.setProperty('left', '0', 'important');

        // 🔧 wrapper をパーセンテージベースで調整
        wrapper.style.setProperty('max-width', '95%', 'important');
        wrapper.style.setProperty('width', '95%', 'important');
        wrapper.style.setProperty('position', 'static', 'important');
        wrapper.style.setProperty('transform', 'none', 'important');
        wrapper.style.setProperty('top', 'auto', 'important');
        wrapper.style.setProperty('left', 'auto', 'important');

        // 🔧 高さ情報は記録するが、強制固定はしない
        embedHeightMap.set(embedId, h);

        if (process.env.NODE_ENV === 'development') {
          console.log(`[rendered-delay] 高さ記録とサイズ調整: ${h}px, パーセンテージベース for embed ${embedId}`);
        }
      }

      // 🔧 rendered イベント完了後に安定化マークを設定
      if (!wrapper.hasAttribute('data-embed-stable')) {
        wrapper.setAttribute('data-embed-stable', 'true');
        if (process.env.NODE_ENV === 'development') {
          console.log(`[rendered-delay] 安定化マークを設定しました`);
        }
      }

      // 🔍 rendered event処理完了後の全埋め込み高さを記録
      if (process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          const allEmbeds = document.querySelectorAll('.social-embed[data-platform="twitter"], .social-embed[data-platform="x"]');
          console.log(`🔍 [HEIGHT_DEBUG] rendered event処理完了後 - 全埋め込み数: ${allEmbeds.length}`);
          allEmbeds.forEach((embed, index) => {
            if (embed instanceof HTMLElement) {
              const embedId = embed.getAttribute('data-embed-id');
              const currentHeight = embed.offsetHeight;
              const computedHeight = window.getComputedStyle(embed).height;
              const inlineHeight = embed.style.height;
              const isCurrentWrapper = embed === wrapper;
              console.log(`🔍 [HEIGHT_DEBUG] rendered event完了後 - 埋め込み ${index}: ID=${embedId}, offsetHeight=${currentHeight}px, computedHeight=${computedHeight}, inlineHeight=${inlineHeight}, isCurrentWrapper=${isCurrentWrapper}`);

              // iframe の高さも確認
              const iframe = embed.querySelector('iframe');
              if (iframe) {
                const iframeHeight = iframe.offsetHeight;
                const iframeComputedHeight = window.getComputedStyle(iframe).height;
                const iframeInlineHeight = iframe.style.height;
                console.log(`🔍 [HEIGHT_DEBUG] iframe詳細 - offsetHeight=${iframeHeight}px, computedHeight=${iframeComputedHeight}, inlineHeight=${iframeInlineHeight}`);
              }
            }
          });
        }, 10);
      }
    }, 50);
      });
    }
  });
}

// 🔧 1️⃣ postMessage で height を拾うロジックを強化（埋め込み固有の高さ管理）
// 各埋め込みの高さを保存するマップ
const embedHeightMap = new Map<string, number>();

// 🔧 widgets.load()の重複実行防止システム
const processingEmbeds = new Set<string>();
const processedEmbeds = new Set<string>();
let isTwitterWidgetsLoading = false;

// 🔧 未使用関数を削除（必要に応じて後で追加）

// SSR対応: クライアントサイドでのみイベントリスナーを追加
if (typeof window !== 'undefined') {
  window.addEventListener('message', (e) => {
    if (!/twitter\.com$/.test(e.origin) || !e.data) return;

  let height: number | undefined;
  let iframeId: string | undefined;

  // ① 旧フォーマット {height, id}
  if (typeof e.data.height === 'number' && e.data.id) {
    height = e.data.height;
    iframeId = e.data.id;
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [Debug] postMessage 旧フォーマット:', { height, iframeId });
    }
  }

  // ② 新フォーマット {twttr:{embed:{height,id}}}
  if (!height && e.data.twttr?.embed) {
    height   = e.data.twttr.embed.height;
    iframeId = e.data.twttr.embed.id;
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [Debug] postMessage 新フォーマット:', { height, iframeId });
    }
  }

  if (!height || height < 200 || !iframeId) return;

  // 🔧 強化: 高さ記録とサイズ固定
  const iframe  = document.getElementById(iframeId) as HTMLIFrameElement | null;
  const wrapper = iframe?.closest<HTMLElement>('.social-embed');
  if (wrapper && iframe) {
    const embedId = wrapper.getAttribute('data-embed-id');

    // 🔧 パーセンテージベースの柔軟なサイズシステム

    // 🔧 iframe をパーセンテージベースで調整
    iframe.style.setProperty('max-width', '100%', 'important');
    iframe.style.setProperty('width', '100%', 'important'); // パーセンテージベース
    iframe.style.setProperty('position', 'static', 'important');
    iframe.style.setProperty('transform', 'none', 'important');
    iframe.style.setProperty('top', '0', 'important');
    iframe.style.setProperty('left', '0', 'important');

    // 🔧 wrapper をパーセンテージベースで調整
    wrapper.style.setProperty('max-width', '95%', 'important');
    wrapper.style.setProperty('width', '95%', 'important');
    wrapper.style.setProperty('position', 'static', 'important');
    wrapper.style.setProperty('transform', 'none', 'important');
    wrapper.style.setProperty('top', 'auto', 'important');
    wrapper.style.setProperty('left', 'auto', 'important');

    // 🔧 高さ情報は記録するが、強制固定はしない
    if (embedId) {
      embedHeightMap.set(embedId, height);
      if (process.env.NODE_ENV === 'development') {
        console.log(`[PostMessage] 高さ記録とサイズ調整: ${height}px, パーセンテージベース for embed ${embedId} (iframe ${iframeId})`);
      }
    }
  }
  }, { passive: true });
}

// 🔧 Fix: CSS-aware height detection for cross-origin Twitter iframes
const getTrueIframeHeight = (iframe: HTMLIFrameElement): number => {
  // Method 1: Check for CSS height style set by Twitter widgets.js
  const computedStyle = window.getComputedStyle(iframe);
  const cssHeight = computedStyle.height;
  if (cssHeight && cssHeight !== 'auto' && !cssHeight.includes('%')) {
    const heightValue = parseInt(cssHeight, 10);
    if (heightValue > 200) {
      console.log(`🔍 [Debug] CSS height detected: ${heightValue}px`);
      return heightValue;
    }
  }

  // Method 2: Check inline style height
  if (iframe.style.height) {
    const inlineHeight = parseInt(iframe.style.height, 10);
    if (inlineHeight > 200) {
      console.log(`🔍 [Debug] Inline style height detected: ${inlineHeight}px`);
      return inlineHeight;
    }
  }

  // Method 3: Check height attribute
  if (iframe.height) {
    const attrHeight = parseInt(iframe.height, 10);
    if (attrHeight > 200) {
      console.log(`🔍 [Debug] Height attribute detected: ${attrHeight}px`);
      return attrHeight;
    }
  }

  // Method 4: Fallback to getBoundingClientRect (may return transformed height)
  const rectHeight = iframe.getBoundingClientRect().height;
  console.log(`🔍 [Debug] Fallback getBoundingClientRect height: ${rectHeight}px`);
  return rectHeight;
};

// 🔧 簡素化: 高さ固定システムを削除（自然な調整のみ）

export const useSocialEmbed = (
  editorRef: React.RefObject<HTMLDivElement>,
  onChange: (value: string) => void,
  protectEmbed: (embed: HTMLElement, embedId: string) => void
) => {
  const isInsertingRef = useRef(false);
  const lastInsertTimeRef = useRef(0);
  const insertCountRef = useRef(0);

  const isProcessingEmbedsRef = useRef(false);
  const lastProcessEmbedsTimeRef = useRef(0);
  const processEmbedsCountRef = useRef(0);

  const apiRequestCache = useRef(new Map<string, Promise<string | null>>());

  // 🔧 追加: カーソル位置保持機能
  const savedRangeRef = useRef<Range | null>(null);

  // 🔧 追加: カーソル位置を保存する関数
  const saveCursorPosition = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      savedRangeRef.current = selection.getRangeAt(0).cloneRange();
      if (process.env.NODE_ENV === 'development') {
        console.log('[CURSOR_SAVE] カーソル位置を保存しました:', {
          container: savedRangeRef.current.startContainer.nodeName,
          offset: savedRangeRef.current.startOffset,
          parentElement: savedRangeRef.current.startContainer.parentElement?.tagName
        });
      }
    }
  }, []);

  // 🔧 追加: カーソル位置を復元する関数
  const restoreCursorPosition = useCallback(() => {
    // 🔧 修正: グローバルに保存されたカーソル位置を優先的に使用
    const globalSavedRange = (window as any).__savedCursorRange;
    const rangeToRestore = globalSavedRange || savedRangeRef.current;

    if (rangeToRestore && editorRef.current) {
      try {
        // 🔧 修正: フォーカス時のスクロール位置保持
        const currentScrollPosition = window.scrollY;
        const currentEditorScrollPosition = editorRef.current.scrollTop;

        editorRef.current.focus({ preventScroll: true }); // スクロール防止

        // スクロール位置を復元
        window.scrollTo(0, currentScrollPosition);
        editorRef.current.scrollTop = currentEditorScrollPosition;

        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          selection.addRange(rangeToRestore);
          if (process.env.NODE_ENV === 'development') {
            console.log('[CURSOR_RESTORE] カーソル位置を復元しました:', {
              source: globalSavedRange ? 'グローバル保存' : 'ローカル保存',
              container: rangeToRestore.startContainer.nodeName,
              offset: rangeToRestore.startOffset
            });
          }
        }

        // 🔧 追加: グローバル保存されたカーソル位置をクリア
        if (globalSavedRange) {
          (window as any).__savedCursorRange = null;
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('[CURSOR_RESTORE] カーソル位置復元に失敗:', error);
        }
      }
    }
  }, [editorRef]);

  // Twitter高さ自動調整関数（完全自動高さ調整）
  const setupTwitterAutoHeight = (embedElement: HTMLElement) => {
    try {
      const embedId = embedElement.getAttribute('data-embed-id');
      if (!embedId) return;

      // 既存のiframeがある場合は即座に処理
      const existingIframe = embedElement.querySelector<HTMLIFrameElement>('iframe[id^="twitter-widget"]');
      if (existingIframe) {
        setupIframeHeightAdjustment(embedElement, existingIframe, embedId);

        // 🔧 iframe存在確認後に安定化マークを設定
        if (!embedElement.hasAttribute('data-embed-stable')) {
          embedElement.setAttribute('data-embed-stable', 'true');
          if (process.env.NODE_ENV === 'development') {
            console.log(`[setupTwitterAutoHeight] 既存iframe確認後に安定化マークを設定しました (ID: ${embedId})`);
          }
        }
        return;
      }

      // iframeがまだ生成されていない場合は、MutationObserverで監視
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLElement && node.tagName === 'IFRAME' && node.id.startsWith('twitter-widget')) {
              setupIframeHeightAdjustment(embedElement, node as HTMLIFrameElement, embedId);

              // 🔧 iframe発見時に安定化マークを設定
              if (!embedElement.hasAttribute('data-embed-stable')) {
                embedElement.setAttribute('data-embed-stable', 'true');
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[setupTwitterAutoHeight] iframe発見時に安定化マークを設定しました (ID: ${embedId})`);
                }
              }

              observer.disconnect(); // 目的のiframeが見つかったら監視停止
            }
          });
        });
      });

      observer.observe(embedElement, { childList: true, subtree: true });

      // 10秒後にタイムアウト
      setTimeout(() => {
        observer.disconnect();
        // 🔧 タイムアウト時でもiframeが存在すれば安定化マークを設定
        const iframe = embedElement.querySelector<HTMLIFrameElement>('iframe[id^="twitter-widget"]');
        if (iframe && !embedElement.hasAttribute('data-embed-stable')) {
          embedElement.setAttribute('data-embed-stable', 'true');
          if (process.env.NODE_ENV === 'development') {
            console.log(`[setupTwitterAutoHeight] タイムアウト時に安定化マークを設定しました`);
          }
        }
      }, 10000);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('[RichTextEditor] Twitter高さ自動調整エラー:', error);
      }
    }
  };

  // 🔧 簡素化: iframe高さ調整（自然な調整のみ）
  const setupIframeHeightAdjustment = (wrapper: HTMLElement, iframe: HTMLIFrameElement, embedId: string) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[RichTextEditor] Twitter iframe簡素化高さ調整開始: ${embedId}`);

        // 🔧 詳細ログ: 初期位置情報を記録
        const initialRect = wrapper.getBoundingClientRect();
        const initialStyle = window.getComputedStyle(wrapper);
        console.log(`[POSITION_DEBUG] iframe調整前の位置: top=${initialRect.top}px, height=${initialRect.height}px, position=${initialStyle.position}`);
      }

      // 🔧 iframe要素を完全に静的化（Twitter widgets.js無効化）
      iframe.removeAttribute('height');
      iframe.setAttribute('data-static', 'true'); // 静的化マーク
      iframe.setAttribute('scrolling', 'no'); // スクロール無効化

      // 🔧 高さ情報の記録とサイズ調整
      const recordHeight = () => {
        const h = getTrueIframeHeight(iframe);
        if (h > 200 && embedId) {
          // 🔧 パーセンテージベースの柔軟なサイズシステム（高さ縮小防止）

          // 🔧 iframe をパーセンテージベースで調整（高さ保護）
          iframe.style.setProperty('max-width', '100%', 'important');
          iframe.style.setProperty('width', '100%', 'important'); // パーセンテージベース
          iframe.style.setProperty('position', 'static', 'important');
          iframe.style.setProperty('transform', 'none', 'important');
          iframe.style.setProperty('top', '0', 'important');
          iframe.style.setProperty('left', '0', 'important');
          iframe.style.setProperty('min-height', `${h}px`, 'important'); // 🔧 高さ縮小防止

          // 🔧 wrapper をパーセンテージベースで調整
          wrapper.style.setProperty('max-width', '95%', 'important');
          wrapper.style.setProperty('width', '95%', 'important');
          wrapper.style.setProperty('position', 'static', 'important');
          wrapper.style.setProperty('transform', 'none', 'important');
          wrapper.style.setProperty('top', 'auto', 'important');
          wrapper.style.setProperty('left', 'auto', 'important');

          embedHeightMap.set(embedId, h);
          if (process.env.NODE_ENV === 'development') {
            console.log(`[RichTextEditor] Twitter高さ記録とサイズ調整: ${h}px, パーセンテージベース (ID: ${embedId})`);

            // 🔧 詳細ログ: 高さ変更時の位置情報を記録
            const currentRect = wrapper.getBoundingClientRect();
            const currentStyle = window.getComputedStyle(wrapper);
            console.log(`[POSITION_DEBUG] 高さ記録時の位置: top=${currentRect.top}px, height=${currentRect.height}px, position=${currentStyle.position}`);
          }
        }
      };

      // 即座に高さを記録
      recordHeight();

      // 🔧 強化: Twitter widgets.jsによるサイズ変更を防ぐ（カーソル位置保護付き）
      const mo = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[POSITION_DEBUG] iframe style変更検知: ${embedId}`);
            }

            // 🔧 カーソル位置を保存（DOM操作前）
            const selection = window.getSelection();
            const range = selection && selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

            if (range && range.startContainer) {
              try {
                // カーソル位置情報を保存（現在は使用していないが将来の拡張用）
                const savedCaretInfo = {
                  element: range.startContainer,
                  offset: range.startOffset
                };
                // 将来的にカーソル位置復元機能を実装する際に使用
                if (process.env.NODE_ENV === 'development') {
                  console.log('カーソル位置保存:', savedCaretInfo);
                }
              } catch (error) {
                // カーソル位置の保存に失敗した場合は無視
              }
            }

            // 🔧 パーセンテージベースの柔軟なサイズシステム（高さ縮小防止）
            const currentHeight = getTrueIframeHeight(iframe);

            // 🔧 パーセンテージベースでサイズを強制調整
            iframe.style.setProperty('max-width', '100%', 'important');
            iframe.style.setProperty('width', '100%', 'important'); // パーセンテージベース
            iframe.style.setProperty('position', 'static', 'important');
            iframe.style.setProperty('transform', 'none', 'important');
            iframe.style.setProperty('top', '0', 'important');
            iframe.style.setProperty('left', '0', 'important');

            // 🔧 高さ縮小防止: 現在の高さを最小高さとして設定
            if (currentHeight > 200) {
              iframe.style.setProperty('min-height', `${currentHeight}px`, 'important');
            }

            wrapper.style.setProperty('max-width', '95%', 'important');
            wrapper.style.setProperty('width', '95%', 'important');
            wrapper.style.setProperty('position', 'static', 'important');
            wrapper.style.setProperty('transform', 'none', 'important');
            wrapper.style.setProperty('top', 'auto', 'important');
            wrapper.style.setProperty('left', 'auto', 'important');

            // 🔧 カーソル位置復元処理を一時的に無効化（デバッグ用）
            // カーソルジャンプ問題の原因を特定するため、復元処理を無効化

            if (process.env.NODE_ENV === 'development') {
              console.log(`[SIZE_FIX] Twitter widgets.jsによる変更をパーセンテージベースで修正: height=${currentHeight}px`);
            }

            recordHeight();
          }
        });
      });
      mo.observe(iframe, { attributes: true, attributeFilter: ['style'] });

      // 🔧 追加: wrapper要素の位置も監視して修正
      const wrapperObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
            // 🔧 wrapper要素の位置が変更された場合は修正
            wrapper.style.setProperty('position', 'static', 'important');
            wrapper.style.setProperty('top', 'auto', 'important');
            wrapper.style.setProperty('left', 'auto', 'important');
            wrapper.style.setProperty('transform', 'none', 'important');

            if (process.env.NODE_ENV === 'development') {
              console.log(`[POSITION_FIX] wrapper位置を修正: ${embedId}`);
            }
          }
        });
      });
      wrapperObserver.observe(wrapper, { attributes: true, attributeFilter: ['style'] });

      // 🔧 クリーンアップ用のタイマー（10秒後にObserver停止）
      setTimeout(() => {
        mo.disconnect();
        wrapperObserver.disconnect();
        if (process.env.NODE_ENV === 'development') {
          console.log(`[RichTextEditor] Twitter高さ調整Observer停止 (ID: ${embedId})`);
        }
      }, 10000);

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('[RichTextEditor] iframe高さ調整セットアップエラー:', error);
      }
    }
  };

  // Twitter widgets.jsを動的に読み込む関数
  const loadTwitterWidgetsScript = (callback: () => void) => {
    // 既にスクリプトが読み込まれている場合
    if (window.twttr && typeof window.twttr.widgets?.load === 'function') {
      callback();
      return;
    }

    // 既存のスクリプトタグをチェック
    const existingScript = document.querySelector('script[src*="platform.twitter.com/widgets.js"]');
    if (existingScript) {
      // スクリプトは存在するが、まだ読み込まれていない場合は待機
      const checkTwitterReady = (attempts = 0) => {
        if (window.twttr && typeof window.twttr.widgets?.load === 'function') {
          callback();
        } else if (attempts < 10) {
          setTimeout(() => checkTwitterReady(attempts + 1), 500);
        } else {
          // Twitter widgets.jsの読み込みがタイムアウト
        }
      };
      checkTwitterReady();
      return;
    }

    // 新しいスクリプトタグを作成
    const script = document.createElement('script');
    script.src = 'https://platform.twitter.com/widgets.js';
    script.async = true;
    script.onload = () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[RichTextEditor] Twitter widgets.jsの読み込みが完了しました');
      }
      // 少し待ってからコールバックを実行
      setTimeout(callback, 100);
    };
    script.onerror = () => {
      if (process.env.NODE_ENV === 'development') {
        console.error('[RichTextEditor] Twitter widgets.jsの読み込みに失敗しました');
      }
    };
    document.head.appendChild(script);
  };

  // Instagram埋め込み処理（安全な実装）
  const processInstagramEmbed = (callback?: () => void) => {
    // 新しいユーティリティを使用
    import('../../../utils/instagramEmbedUtils').then(({ processInstagramEmbeds }) => {
      processInstagramEmbeds();
      if (callback) callback();
    }).catch(error => {
      if (process.env.NODE_ENV === 'development') {
        console.error('[RichTextEditor] Instagram埋め込み処理エラー:', error);
      }
      if (callback) callback();
    });
  };

  // YouTubeのビデオIDを抽出する関数
  const extractYouTubeVideoId = (url: string): string | null => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };

  // DOM APIで埋め込みを追加する関数（iframe破壊なし）
  const insertEmbed = useCallback((html: string) => {
    const currentTime = Date.now();

    // 🔧 追加: スクロール位置を保存
    const savedScrollPosition = window.scrollY;
    const savedEditorScrollPosition = editorRef.current?.scrollTop || 0;

    // 🔧 追加: SNS埋め込み処理開始をマーク（カーソルジャンプ防止）
    // グローバルフラグを設定してカーソル復元処理をスキップ
    if (typeof window !== 'undefined') {
      (window as any).__isEmbedProcessing = true;
      // 🔧 重要: 意図的削除フラグを設定してMutationObserver干渉を防ぐ
      (window as any).__isIntentionalEmbedDelete = true;
      // 🔧 追加: スクロール位置をグローバルに保存
      (window as any).__savedScrollPosition = savedScrollPosition;
      (window as any).__savedEditorScrollPosition = savedEditorScrollPosition;

      if (process.env.NODE_ENV === 'development') {
        console.log('[insertEmbed] SNS埋め込み処理フラグを設定しました');
        console.log('[insertEmbed] 意図的削除フラグを設定してMutationObserver干渉を防ぎます');
        console.log('[insertEmbed] スクロール位置を保存しました:', {
          windowScroll: savedScrollPosition,
          editorScroll: savedEditorScrollPosition
        });
      }
    }

    // 🔧 新規埋め込み挿入前に既存の埋め込み要素を保護
    if (editorRef.current) {
      const existingEmbeds = editorRef.current.querySelectorAll('.social-embed[data-platform="twitter"], .social-embed[data-platform="x"]');
      existingEmbeds.forEach((embed) => {
        if (embed instanceof HTMLElement) {
          const iframe = embed.querySelector('iframe[id^="twitter-widget"]');
          if (iframe && !embed.hasAttribute('data-embed-stable')) {
            embed.setAttribute('data-embed-stable', 'true');
            if (process.env.NODE_ENV === 'development') {
              console.log('[insertEmbed] 新規挿入前に既存埋め込みを保護しました');
            }
          }
        }
      });
    }

    // 重複挿入防止（複数の条件でチェック）
    if (isInsertingRef.current) {
      debugLog('🔧 insertEmbed関数は既に実行中のため、処理をスキップします');
      return;
    }

    // 短時間での連続挿入を防止
    if (currentTime - lastInsertTimeRef.current < MIN_EXECUTION_INTERVAL) {
      debugLog('🔧 insertEmbed関数: 短時間での連続挿入を検出、処理をスキップします');
      return;
    }

    if (!editorRef.current) {
      debugLog('エディタ要素が存在しないため、埋め込みを挿入できません');
      return;
    }

    isInsertingRef.current = true;
    lastInsertTimeRef.current = currentTime;
    insertCountRef.current += 1;

    debugLog(`🔧 insertEmbed関数開始 (実行回数: ${insertCountRef.current})`);
    debugLog('🔧 DOM APIで埋め込みを追加します（iframe破壊なし）');

    const insertPosition = editorRef.current;

    // 🔧 修正: 埋め込み要素挿入時の段落分割（テキスト重複防止）
    const selection = window.getSelection();

    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const container = range.commonAncestorContainer;

      // 🔧 重要: テキストノードの途中に挿入する場合は段落を分割
      if (container.nodeType === Node.TEXT_NODE && container.parentElement) {
        const parentParagraph = container.parentElement;
        const textNode = container as Text;
        const offset = range.startOffset;

        // テキストノードの途中にカーソルがある場合
        if (offset > 0 && offset < textNode.textContent!.length) {
          // テキストを分割
          const beforeText = textNode.textContent!.substring(0, offset);
          const afterText = textNode.textContent!.substring(offset);

          // 新しい段落を作成
          const newParagraph = document.createElement('p');
          newParagraph.textContent = afterText;

          // 元のテキストノードを更新
          textNode.textContent = beforeText;

          // 新しい段落を挿入
          parentParagraph.parentNode?.insertBefore(newParagraph, parentParagraph.nextSibling);
        }
      }
    }

    // 🔧 修正: 埋め込み要素の前に最小限のスペーサーを追加
    const beforeSpacer = document.createElement('br');

    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = html;

    // 🔧 デバッグ: HTMLが正しく設定されているか確認
    if (process.env.NODE_ENV === 'development') {
      console.log('[insertEmbed] 受信したHTML:', html);
      console.log('[insertEmbed] tempContainerに設定されたHTML:', tempContainer.innerHTML);
      console.log('[insertEmbed] tempContainer内の要素数:', tempContainer.children.length);
    }

    // HTMLが空の場合は処理を中止
    if (!html || html.trim() === '' || tempContainer.children.length === 0) {
      debugLog('🔧 insertEmbed: 埋め込みHTMLが空のため処理を中止します');
      isInsertingRef.current = false;

      // 🔧 追加: SNS埋め込み処理フラグをリセット
      if (typeof window !== 'undefined') {
        (window as any).__isEmbedProcessing = false;
      }
      return;
    }

    // 🔧 修正: 埋め込み要素の後に最小限のスペーサーを追加
    const afterSpacer = document.createElement('br');

    // 🔧 重複チェック: 既存の類似要素がないか確認
    const existingEmbeds = insertPosition.querySelectorAll('.social-embed');
    const isDuplicate = Array.from(existingEmbeds).some(embed => {
      const tempContent = tempContainer.innerHTML;
      const contentToCheck = typeof tempContent === 'string' ? tempContent.substring(0, 100) : String(tempContent).substring(0, 100);
      return embed.innerHTML.includes(contentToCheck);
    });

    if (!isDuplicate) {
      // 🔧 修正: カーソル位置保持機能を使用
      // エディタにフォーカスを当てて保存されたカーソル位置を復元
      restoreCursorPosition();

      let range: Range;

      // 現在のカーソル位置を取得
      const currentSelection = window.getSelection();
      if (currentSelection && currentSelection.rangeCount > 0) {
        range = currentSelection.getRangeAt(0);

        // 🔧 追加: ゼロ幅スペースを含むテキストノード内の場合は適切な位置に調整
        if (range.startContainer.nodeType === Node.TEXT_NODE) {
          const textNode = range.startContainer as Text;
          const textContent = textNode.textContent || '';

          // ゼロ幅スペースのみの場合は親要素の適切な位置に移動
          if (textContent.match(/^[\u200B\u00A0\s]*$/)) {
            const parentElement = textNode.parentElement;
            if (parentElement && parentElement !== editorRef.current) {
              // 親要素の後に挿入するように調整
              range = document.createRange();
              range.setStartAfter(parentElement);
              range.collapse(true);
              currentSelection.removeAllRanges();
              currentSelection.addRange(range);

              if (process.env.NODE_ENV === 'development') {
                console.log('[insertEmbed] ゼロ幅スペース位置を親要素の後に調整:', {
                  originalContainer: textNode.nodeName,
                  originalContent: textContent,
                  newContainer: range.startContainer.nodeName,
                  newOffset: range.startOffset
                });
              }
            }
          }
        }

        if (process.env.NODE_ENV === 'development') {
          console.log('[insertEmbed] 復元されたカーソル位置を使用:', {
            container: range.startContainer.nodeName,
            offset: range.startOffset,
            parentElement: range.startContainer.parentElement?.tagName
          });
        }
      } else {
        // フォールバック: カーソル位置が取得できない場合はエディタの最後に設定
        if (process.env.NODE_ENV === 'development') {
          console.log('[insertEmbed] カーソル位置が取得できないため、エディタの最後に設定');
        }

        range = document.createRange();
        if (editorRef.current.childNodes.length > 0) {
          range.selectNodeContents(editorRef.current);
          range.collapse(false);
        } else {
          range.setStart(editorRef.current, 0);
          range.collapse(true);
        }
        currentSelection?.removeAllRanges();
        currentSelection?.addRange(range);
      }

      try {
        // 🔧 修正: 埋め込み要素のみを挿入（スペーサーなし）
        const fragment = document.createDocumentFragment();

        // 🔧 修正: tempContainerの要素を確実に複製してfragmentに追加
        if (tempContainer.children.length > 0) {
          // 要素ノードのみを対象にして確実に複製
          for (let i = 0; i < tempContainer.children.length; i++) {
            const element = tempContainer.children[i];
            const clonedElement = element.cloneNode(true);
            fragment.appendChild(clonedElement);

            if (process.env.NODE_ENV === 'development') {
              console.log(`[insertEmbed] 要素${i}を複製:`, {
                original: element.outerHTML,
                cloned: (clonedElement as Element).outerHTML
              });
            }
          }
        } else {
          // フォールバック: innerHTML を使用
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = tempContainer.innerHTML;
          while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
          }

          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] フォールバック: innerHTML使用で要素を追加');
          }
        }

        // 🔧 デバッグ: fragment作成後の確認
        if (process.env.NODE_ENV === 'development') {
          console.log('[insertEmbed] fragment作成完了:', {
            fragmentChildrenCount: fragment.children.length,
            fragmentChildNodesCount: fragment.childNodes.length,
            tempContainerChildrenCount: tempContainer.children.length,
            fragmentHTML: Array.from(fragment.childNodes).map(node =>
              node.nodeType === Node.ELEMENT_NODE ? (node as Element).outerHTML : node.textContent
            ).join(''),
            tempContainerHTML: tempContainer.innerHTML
          });

          // 🔧 重要: fragmentの内容を詳細に確認
          console.log('[insertEmbed] fragment内の各ノード:');
          Array.from(fragment.childNodes).forEach((node, index) => {
            console.log(`  ノード${index}:`, {
              nodeType: node.nodeType,
              nodeName: node.nodeName,
              content: node.nodeType === Node.ELEMENT_NODE ? (node as Element).outerHTML : node.textContent
            });
          });
        }

        // 🔧 修正: range.insertNode()を使用してカーソル位置に挿入
        try {
          // 🔧 デバッグ: 挿入前のエディタ状態
          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] 挿入前のエディタHTML:', editorRef.current.innerHTML);
            console.log('[insertEmbed] 挿入前の.social-embed要素数:', editorRef.current.querySelectorAll('.social-embed').length);
            console.log('[insertEmbed] range.insertNode()実行開始');
          }

          // 🔧 修正: より確実な挿入方法を使用
          const container = range.startContainer;
          const offset = range.startOffset;

          if (container.nodeType === Node.TEXT_NODE) {
            // テキストノード内の場合
            const textNode = container as Text;
            const parent = textNode.parentNode;
            if (parent) {
              if (process.env.NODE_ENV === 'development') {
                console.log('[insertEmbed] テキストノード内への挿入:', {
                  offset,
                  textLength: textNode.textContent?.length,
                  parentNodeName: parent.nodeName
                });
              }

              if (offset === 0) {
                parent.insertBefore(fragment, textNode);
                if (process.env.NODE_ENV === 'development') {
                  console.log('[insertEmbed] テキストノード前に挿入完了');
                }
              } else if (offset >= textNode.textContent!.length) {
                parent.insertBefore(fragment, textNode.nextSibling);
                if (process.env.NODE_ENV === 'development') {
                  console.log('[insertEmbed] テキストノード後に挿入完了');
                }
              } else {
                const afterText = textNode.splitText(offset);
                parent.insertBefore(fragment, afterText);
                if (process.env.NODE_ENV === 'development') {
                  console.log('[insertEmbed] テキストノード分割挿入完了');
                }
              }
            }
          } else {
            // 要素ノード内の場合
            if (process.env.NODE_ENV === 'development') {
              console.log('[insertEmbed] 要素ノード内への挿入:', {
                containerNodeName: container.nodeName,
                offset,
                childNodesLength: container.childNodes.length,
                isEditorElement: container === editorRef.current
              });
            }

            if (offset >= container.childNodes.length) {
              container.appendChild(fragment);
              if (process.env.NODE_ENV === 'development') {
                console.log('[insertEmbed] 要素ノード末尾に挿入完了');
              }
            } else {
              container.insertBefore(fragment, container.childNodes[offset]);
              if (process.env.NODE_ENV === 'development') {
                console.log('[insertEmbed] 要素ノード指定位置に挿入完了');
              }
            }
          }

          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] 直接挿入実行完了');

            // 🔧 重要: 挿入直後の即座確認
            const immediateCheck = editorRef.current.querySelectorAll('.social-embed');
            console.log('[insertEmbed] 挿入直後の即座確認 - .social-embed要素数:', immediateCheck.length);

            if (immediateCheck.length === 0) {
              console.error('[insertEmbed] 挿入処理が失敗しました！最終フォールバックを実行します');

              // 🔧 緊急修正: 最終フォールバック - 直接HTMLを挿入
              try {
                const currentHTML = editorRef.current.innerHTML;
                const embedHTML = tempContainer.innerHTML;
                const newHTML = currentHTML + '<p data-embed-spacer="before"><br></p>' + embedHTML + '<p data-embed-spacer="after"><br></p>';

                editorRef.current.innerHTML = newHTML;

                console.log('[insertEmbed] 最終フォールバック実行完了 - 新しい.social-embed要素数:',
                  editorRef.current.querySelectorAll('.social-embed').length);

              } catch (fallbackError) {
                console.error('[insertEmbed] 最終フォールバックも失敗:', fallbackError);
              }
            }
          }

          // 🔧 デバッグ: 挿入直後のエディタ状態
          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] 挿入直後のエディタHTML:', editorRef.current.innerHTML);
            console.log('[insertEmbed] 挿入直後の.social-embed要素数:', editorRef.current.querySelectorAll('.social-embed').length);
          }

          // 🔧 修正: カーソルを挿入した埋め込み要素の後に移動
          const insertedEmbeds = editorRef.current.querySelectorAll('.social-embed');
          const lastInsertedEmbed = insertedEmbeds[insertedEmbeds.length - 1];
          if (lastInsertedEmbed) {
            range.setStartAfter(lastInsertedEmbed);
            range.collapse(true);

            const selection = window.getSelection();
            selection?.removeAllRanges();
            selection?.addRange(range);
          }

          debugLog('🔧 カーソル位置に埋め込みを挿入しました（insertEmbed）');

          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] 挿入位置詳細:', {
              containerType: range.startContainer.nodeType === Node.TEXT_NODE ? 'TEXT_NODE' : 'ELEMENT_NODE',
              containerName: range.startContainer.nodeName,
              offset: range.startOffset,
              parentElement: range.startContainer.parentElement?.tagName
            });
          }
        } catch (insertError) {
          // range.insertNode()が失敗した場合のフォールバック
          if (process.env.NODE_ENV === 'development') {
            console.error('[insertEmbed] range.insertNode()が失敗、手動挿入を試行:', insertError);
            console.log('[insertEmbed] エラー詳細:', {
              errorMessage: insertError.message,
              errorStack: insertError.stack,
              rangeDetails: {
                startContainer: range.startContainer.nodeName,
                startOffset: range.startOffset,
                collapsed: range.collapsed
              }
            });
          }

          // 🔧 修正: 手動でカーソル位置に挿入（スペーサーなし）
          const manualFragment = document.createDocumentFragment();

          // tempContainerの要素を複製して追加
          Array.from(tempContainer.childNodes).forEach((node) => {
            manualFragment.appendChild(node.cloneNode(true));
          });

          const container = range.startContainer;
          const offset = range.startOffset;

          if (container.nodeType === Node.TEXT_NODE) {
            // テキストノード内の場合
            const textNode = container as Text;
            if (offset === 0) {
              textNode.parentNode?.insertBefore(manualFragment, textNode);
            } else if (offset === textNode.textContent?.length) {
              textNode.parentNode?.insertBefore(manualFragment, textNode.nextSibling);
            } else {
              const afterText = textNode.splitText(offset);
              textNode.parentNode?.insertBefore(manualFragment, afterText);
            }
          } else {
            // 要素ノード内の場合
            if (offset >= container.childNodes.length) {
              container.appendChild(manualFragment);
            } else {
              container.insertBefore(manualFragment, container.childNodes[offset]);
            }
          }

          // 🔧 修正: カーソルを挿入した埋め込み要素の後に移動
          const insertedEmbeds = editorRef.current.querySelectorAll('.social-embed');
          const lastInsertedEmbed = insertedEmbeds[insertedEmbeds.length - 1];
          if (lastInsertedEmbed) {
            const newRange = document.createRange();
            newRange.setStartAfter(lastInsertedEmbed);
            newRange.collapse(true);

            const selection = window.getSelection();
            selection?.removeAllRanges();
            selection?.addRange(newRange);
          }

          debugLog('🔧 手動挿入でカーソル位置に埋め込みを挿入しました');
        }

        // 🔧 デバッグ: 挿入後の確認
        if (process.env.NODE_ENV === 'development') {
          setTimeout(() => {
            const insertedEmbeds = editorRef.current?.querySelectorAll('.social-embed');
            console.log(`[DEBUG] insertEmbed後の.social-embed要素数: ${insertedEmbeds?.length || 0}`);

            if (insertedEmbeds && insertedEmbeds.length === 0) {
              console.error('[DEBUG] 埋め込み要素が挿入されていません！');
              console.log('[DEBUG] エディタの現在のHTML:', editorRef.current?.innerHTML);
              console.log('[DEBUG] tempContainerの内容:', tempContainer.innerHTML);
            }

            insertedEmbeds?.forEach((embed, index) => {
              console.log(`[DEBUG] 挿入後の埋め込み${index}:`, {
                platform: embed.getAttribute('data-platform'),
                embedId: embed.getAttribute('data-embed-id'),
                loaded: embed.getAttribute('data-loaded')
              });
            });
          }, 10);
        }
      } catch (error) {
        debugLog('カーソル位置への挿入に失敗、最後に追加します:', error);
        // フォールバック: 最後に追加
        try {
          // 🔧 修正: 新しいfragmentを作成（スペーサーなし）
          const fallbackFragment = document.createDocumentFragment();

          // 埋め込み要素を複製（深いクローンで確実に複製）
          Array.from(tempContainer.children).forEach((element) => {
            fallbackFragment.appendChild(element.cloneNode(true));
          });

          // エディタの最後に追加
          editorRef.current.appendChild(fallbackFragment);

          // 🔧 修正: カーソルを挿入した埋め込み要素の後に移動
          const insertedEmbeds = editorRef.current.querySelectorAll('.social-embed');
          const lastInsertedEmbed = insertedEmbeds[insertedEmbeds.length - 1];
          if (lastInsertedEmbed) {
            const newRange = document.createRange();
            newRange.setStartAfter(lastInsertedEmbed);
            newRange.collapse(true);

            const selection = window.getSelection();
            selection?.removeAllRanges();
            selection?.addRange(newRange);
          }

          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] フォールバック: エディタの最後に挿入しました');
          }
        } catch (fallbackError) {
          debugLog('フォールバック処理も失敗しました:', fallbackError);

          // 🔧 修正: 最終フォールバック（スペーサーなし）
          try {
            const currentHTML = editorRef.current.innerHTML;
            const embedHTML = tempContainer.innerHTML;
            editorRef.current.innerHTML = currentHTML + embedHTML;

            if (process.env.NODE_ENV === 'development') {
              console.log('[insertEmbed] 最終フォールバック: HTMLを直接挿入しました');
            }
          } catch (finalError) {
            debugLog('最終フォールバック処理も失敗しました:', finalError);
          }
        }
      }
    } else {
      debugLog('🔧 重複する埋め込み要素の挿入をスキップしました');
    }

    // 🔧 修正: 埋め込み処理を即座に実行
    debugLog('🔧 新規追加分の埋め込み処理を開始します');

    // 埋め込み処理を実行（新規追加分のみ）
    setTimeout(() => {
      processSocialEmbeds();
    }, 50);

    // 🔧 修正: onChange呼び出しを遅延（DOM安定化後）
    setTimeout(() => {
      if (editorRef.current) {
        const currentContent = editorRef.current.innerHTML;

        // 🔧 デバッグ: onChange呼び出し前後の.social-embed要素数を確認
        if (process.env.NODE_ENV === 'development') {
          const beforeCount = editorRef.current.querySelectorAll('.social-embed').length;
          console.log('[insertEmbed] onChange呼び出し前の.social-embed要素数:', beforeCount);
        }

        onChange(currentContent);
        debugLog('🔧 埋め込み挿入後にonChangeを呼び出しました');

        // 🔧 デバッグ: onChange呼び出し後の.social-embed要素数を確認
        if (process.env.NODE_ENV === 'development') {
          setTimeout(() => {
            const afterCount = editorRef.current?.querySelectorAll('.social-embed').length || 0;
            console.log('[insertEmbed] onChange呼び出し後の.social-embed要素数:', afterCount);
          }, 10);
        }
      }
    }, 100); // 100ms遅延でDOM安定化を待つ

    // 重複挿入防止フラグをリセット
    setTimeout(() => {
      isInsertingRef.current = false;

      // 🔧 追加: SNS埋め込み処理フラグをリセット + スクロール位置復元
      if (typeof window !== 'undefined') {
        // 🔧 追加: スクロール位置を復元
        const savedWindowScroll = (window as any).__savedScrollPosition;
        const savedEditorScroll = (window as any).__savedEditorScrollPosition;

        if (typeof savedWindowScroll === 'number') {
          window.scrollTo({
            top: savedWindowScroll,
            behavior: 'auto' // 即座に復元
          });

          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] ウィンドウスクロール位置を復元しました:', savedWindowScroll);
          }
        }

        if (typeof savedEditorScroll === 'number' && editorRef.current) {
          editorRef.current.scrollTop = savedEditorScroll;

          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] エディタスクロール位置を復元しました:', savedEditorScroll);
          }
        }

        (window as any).__isEmbedProcessing = false;
        // 🔧 重要: 意図的削除フラグもリセット（500ms後に安全にリセット）
        setTimeout(() => {
          (window as any).__isIntentionalEmbedDelete = false;
          if (process.env.NODE_ENV === 'development') {
            console.log('[insertEmbed] 意図的削除フラグをリセットしました');
          }
        }, 500);

        // 🔧 追加: スクロール位置保存用フラグもクリア
        (window as any).__savedScrollPosition = null;
        (window as any).__savedEditorScrollPosition = null;

        if (process.env.NODE_ENV === 'development') {
          console.log('[insertEmbed] SNS埋め込み処理フラグをリセットしました');
        }
      }

      debugLog(`🔧 insertEmbed関数: 重複挿入防止フラグをリセットしました (実行回数: ${insertCountRef.current})`);
    }, FLAG_RESET_DELAY);
  }, [editorRef, onChange]);

  // 埋め込みHTMLを取得する関数
  const fetchEmbedHtml = async (platform: string, url: string): Promise<string | null> => {
    try {
      debugLog(`${platform}の埋め込みHTMLを取得開始: ${url}`);

      const cacheKey = `${platform}-${url}`;

      // 既存のリクエストがあるかチェック
      if (apiRequestCache.current.has(cacheKey)) {
        debugLog(`${platform}の埋め込みHTML取得: キャッシュされたリクエストを使用: ${url}`);
        return await apiRequestCache.current.get(cacheKey)!;
      }

      // 新しいAPIリクエストを作成
      const requestPromise = (async () => {
        const payload = { platform, postUrl: url };
        debugLog(`APIリクエストペイロード:`, payload);

        const response = await fetch('/api/social-embed', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });

        debugLog(`APIレスポンス受信: ステータス=${response.status}`);

        if (!response.ok) {
          let errorText = '';
          let errorData = null;

          try {
            const clonedResponse = response.clone();
            errorData = await clonedResponse.json();
            errorText = JSON.stringify(errorData, null, 2);
          } catch {
            try {
              errorText = await response.text();
            } catch (textError) {
              errorText = `レスポンス取得エラー: ${textError}`;
            }
          }

          if (response.status === 500) {
            console.error(`🚨 サーバー内部エラー (500) が発生しました:`);
            console.error(`URL: ${url}`);
            console.error(`Platform: ${platform}`);
            console.error(`エラー詳細:`, errorData || errorText);
            console.warn(`サーバーエラーが発生しましたが、処理を続行します`);
            return null;
          }

          console.warn(`APIエラーが発生しましたが、処理を続行します: ${response.status} - ${errorText}`);
          return null;
        }

        const data = await response.json();
        debugLog(`APIレスポンス内容:`, data);

        if (!data.html) {
          debugLog(`APIレスポンスにHTMLが含まれていません:`, data);
          return null;
        }

        const htmlPreview = data.html.substring(0, 100) + (data.html.length > 100 ? '...' : '');
        debugLog(`${platform}の埋め込みHTMLを取得しました (長さ: ${data.html.length}): ${htmlPreview}`);
        return data.html;
      })();

      // キャッシュに保存
      apiRequestCache.current.set(cacheKey, requestPromise);

      // リクエスト完了後にキャッシュから削除
      requestPromise.finally(() => {
        setTimeout(() => {
          apiRequestCache.current.delete(cacheKey);
        }, API_CACHE_CLEANUP_DELAY);
      });

      return await requestPromise;
    } catch (error) {
      debugLog(`埋め込みHTML取得エラー:`, error);
      return null;
    }
  };

  // SNS埋め込みコンテンツの処理
  const processSocialEmbeds = useCallback(() => {
    if (!editorRef.current) return;

    const currentTime = Date.now();

    // 🔧 厳格な再入禁止ロック - 入口でガード
    if (isProcessingEmbedsRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[RichTextEditor] processSocialEmbeds関数は既に実行中のため、処理をスキップします');
      }
      return;
    }

    // 🔧 強化: 短時間での連続実行を防止
    if (currentTime - lastProcessEmbedsTimeRef.current < MIN_EMBED_PROCESS_INTERVAL) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[RichTextEditor] processSocialEmbeds関数: 短時間での連続実行を検出、処理をスキップします');
      }
      return;
    }

    // 🔧 修正: 新規挿入直後の処理は除外し、通常のカーソル移動中のみスキップ
    // 新規挿入から短時間内（500ms以内）の場合はカーソル位置チェックをスキップ
    const timeSinceLastInsert = currentTime - lastInsertTimeRef.current;
    const isRecentInsert = timeSinceLastInsert < 500;

    if (!isRecentInsert) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const parentElement = range.commonAncestorContainer.nodeType === 1
          ? range.commonAncestorContainer as HTMLElement
          : range.commonAncestorContainer.parentElement;

        // カーソルが埋め込み要素内にある場合は処理をスキップ（新規挿入直後は除く）
        if (parentElement?.closest('.social-embed')) {
          if (process.env.NODE_ENV === 'development') {
            console.log('[RichTextEditor] processSocialEmbeds関数: カーソルが埋め込み要素内にあるため、処理をスキップします');
          }
          return;
        }
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('[RichTextEditor] processSocialEmbeds関数: 新規挿入直後のため、カーソル位置チェックをスキップします');
      }
    }

    // 🔧 厳格なロック設定
    isProcessingEmbedsRef.current = true;
    lastProcessEmbedsTimeRef.current = currentTime;
    processEmbedsCountRef.current += 1;

    // 🔧 追加: グローバルフラグも設定（カーソルジャンプ防止）
    if (typeof window !== 'undefined') {
      (window as any).__isEmbedProcessing = true;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`[RichTextEditor] processSocialEmbeds関数開始 (実行回数: ${processEmbedsCountRef.current})`);

      // 🔍 処理開始時の重複チェック
      const allEmbeds = editorRef.current.querySelectorAll('.social-embed[data-platform="twitter"], .social-embed[data-platform="x"]');
      console.log(`🔍 [HEIGHT_DEBUG] processSocialEmbeds開始時 - 全埋め込み数: ${allEmbeds.length}`);

      const embedIdCount = new Map<string, number>();
      allEmbeds.forEach((embed, index) => {
        if (embed instanceof HTMLElement) {
          const embedId = embed.getAttribute('data-embed-id');
          const currentHeight = embed.offsetHeight;
          console.log(`🔍 [HEIGHT_DEBUG] processSocialEmbeds開始時 - 埋め込み ${index}: ID=${embedId}, height=${currentHeight}px`);

          if (embedId) {
            embedIdCount.set(embedId, (embedIdCount.get(embedId) || 0) + 1);
          }
        }
      });

      // 重複があれば警告
      embedIdCount.forEach((count, embedId) => {
        if (count > 1) {
          console.warn(`🔍 [HEIGHT_DEBUG] processSocialEmbeds開始時に重複検出: ID=${embedId}, 数=${count}`);
        }
      });
    }
    trackEmbedState('PROCESS_SOCIAL_EMBEDS_START');

    try {

    // 🔧 修正: 既存の埋め込み要素は保護し、新しい要素のみ処理対象とする
    const existingLoadedEmbeds = editorRef.current.querySelectorAll('.social-embed[data-embed-id][data-loaded="true"]');
    if (process.env.NODE_ENV === 'development') {
      console.log(`[RichTextEditor] 既存の読み込み済み埋め込み要素: ${existingLoadedEmbeds.length}個`);
    }

    // DOM内の埋め込み要素を確認（デバッグログは削除）

    // 未ロード要素のみを対象とする
    const embedElements = editorRef.current?.querySelectorAll('.social-embed:not([data-loaded="true"])');

    if (!embedElements || embedElements.length === 0) {
      console.log('[RichTextEditor] 🔧 未ロードの埋め込み要素が見つかりませんでした');
      return; // 🔧 finally ブロックでロック解放される
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`[RichTextEditor] 🔧 未ロードの埋め込み要素数: ${embedElements.length}`);
    }

    // 処理が必要な埋め込み要素を収集
    const elementsToProcess: Element[] = [];

    embedElements.forEach((el) => {
      const platform = el.getAttribute('data-platform');
      const url = el.getAttribute('data-url');

      if (!platform || !url) return;

      // 既にコンテンツがある場合はスキップ
      const hasExistingContent = el.querySelector('iframe') ||
                                el.querySelector('video') ||
                                el.querySelector('.social-embed-content') ||
                                el.querySelector('.tiktok-iframe-container') ||
                                el.querySelector('blockquote.tiktok-embed');

      if (hasExistingContent) {
        el.setAttribute('data-loaded', 'true');
        el.setAttribute('data-embed-processed', 'true');
        return;
      }

      // 🔧 修正: X/Twitter埋め込み要素の検出ロジック改善
      // blockquote.twitter-tweetが存在する場合も処理対象とする
      const hasTwitterBlockquote = el.querySelector('blockquote.twitter-tweet');
      const hasOnlyLoading = el.querySelector('.social-embed-loading');

      // X/Twitterの場合：ローディング要素があるか、blockquote.twitter-tweetがあり、まだiframeがない場合は処理対象
      if ((platform.toLowerCase() === 'x' || platform.toLowerCase() === 'twitter')) {
        if ((hasOnlyLoading || hasTwitterBlockquote) && !hasExistingContent) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[RichTextEditor] X/Twitter埋め込み要素を処理対象に追加: ${url}`);
          }
          elementsToProcess.push(el);
        }
      }
      // その他のプラットフォーム：ローディングテキストのみの要素を処理対象とする
      else if (hasOnlyLoading && !hasExistingContent) {
        elementsToProcess.push(el);
      }
    });

    // 新しい埋め込み要素がなければ処理を終了
    if (elementsToProcess.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[RichTextEditor] 処理が必要な新しい埋め込み要素はありません');
      }
      return; // 🔧 finally ブロックでロック解放される
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`[RichTextEditor] ${elementsToProcess.length}個の新しい埋め込み要素を処理します`);
    }
    
    // 各埋め込み要素を処理
    elementsToProcess.forEach(el => {
      const platform = el.getAttribute('data-platform');
      const url = el.getAttribute('data-url');

      if (!platform || !url || !SUPPORTED_PLATFORMS.includes(platform.toLowerCase() as any)) return;

      const currentEmbedId = el.getAttribute('data-embed-id') || `${platform}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      el.setAttribute('data-embed-id', currentEmbedId);

      if (process.env.NODE_ENV === 'development') {
        console.log(`[RichTextEditor] ${platform}の埋め込みAPIリクエストを実行します: ${url} (ID: ${currentEmbedId})`);
      }
      
      // APIを使用して埋め込みHTMLを取得
      fetchEmbedHtml(platform, url)
        .then(html => {
          if (!html || el.hasAttribute('data-embed-processed') || el.hasAttribute('data-embed-error')) {
            return;
          }

          // 🔧 修正: X/Twitter埋め込みの重複表示防止
          if (platform.toLowerCase() === 'x' || platform.toLowerCase() === 'twitter') {
            // 既存のblockquote.twitter-tweetを削除（重複防止）
            const existingBlockquotes = el.querySelectorAll('blockquote.twitter-tweet');
            existingBlockquotes.forEach(blockquote => blockquote.remove());
          }

          // ローディング要素のみを削除
          const loadingElements = el.querySelectorAll('.social-embed-loading, .social-embed-placeholder');
          loadingElements.forEach(loading => loading.remove());

          // 埋め込みコンテンツを挿入
          const embedContent = document.createElement('div');
          embedContent.className = `social-embed-content social-embed-${platform.toLowerCase()}`;
          embedContent.innerHTML = html;

          // 🔧 修正: インラインスタイル削除（CSS上書き防止）
          // インラインスタイルでCSS上書きしない - globals.cssのプラットフォーム別スタイルに委ねる
          // (el as HTMLElement).style.cssText = `...`; // ← 削除：CSS上書きを防止

          embedContent.setAttribute('contenteditable', 'false');
          el.appendChild(embedContent);

          // 要素に処理完了マークを設定
          el.setAttribute('data-embed-processed', 'true');
          el.setAttribute('data-loaded', 'true');
          // 🔧 安定化マークは削除（widgets.js処理完了後に設定）

          // 埋め込み要素を保護システムに登録
          if (currentEmbedId) {
            protectEmbed(el as HTMLElement, currentEmbedId);
            trackEmbedState('EMBED_PROCESSED_AND_PROTECTED', currentEmbedId);
          }

          // プラットフォーム固有の処理（少し遅延させて確実に実行）
          setTimeout(() => {
            handlePlatformSpecificProcess(platform, el as HTMLElement);
          }, 200);
        })
        .catch(error => {
          console.error(`[RichTextEditor] 埋め込み取得エラー:`, error);
          showEmbedError(el, '埋め込みの読み込み中にエラーが発生しました。');
          el.setAttribute('data-embed-error', 'fetch-error');
        });
    });

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[RichTextEditor] processSocialEmbeds処理中にエラーが発生:', error);
      }
      trackEmbedState('PROCESS_SOCIAL_EMBEDS_ERROR', undefined, { error: error.message });
    } finally {
      // 🔧 必ずロックを解放
      setTimeout(() => {
        isProcessingEmbedsRef.current = false;

        // 🔧 追加: グローバルフラグもリセット
        if (typeof window !== 'undefined') {
          (window as any).__isEmbedProcessing = false;
        }

        if (process.env.NODE_ENV === 'development') {
          console.log(`[RichTextEditor] processSocialEmbeds処理完了 - 重複実行防止フラグをリセット (実行回数: ${processEmbedsCountRef.current})`);
        }
      }, EMBED_FLAG_RESET_DELAY);
    }
  }, [editorRef, onChange, protectEmbed]);

  // プラットフォーム固有の処理
  const handlePlatformSpecificProcess = (platform: string, el: HTMLElement) => {
    try {
      if (platform.toLowerCase() === 'instagram') {
        if (process.env.NODE_ENV === 'development') {
          console.log('[RichTextEditor] Instagramの埋め込みを処理します');
        }

        // 新しい安全な処理を使用
        processInstagramEmbed();
      } else if ((platform.toLowerCase() === 'twitter' || platform.toLowerCase() === 'x')) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[RichTextEditor] X/Twitterの埋め込みを処理します');
        }

        // 🔧 修正: 新規埋め込み追加時の既存要素保護
        const processTwitterEmbed = () => {
          const embedId = el.getAttribute('data-embed-id');

          // 🔧 重複処理防止チェック
          if (!embedId) {
            if (process.env.NODE_ENV === 'development') {
              console.warn('[RichTextEditor] 埋め込みIDが見つかりません');
            }
            return;
          }

          if (processingEmbeds.has(embedId) || processedEmbeds.has(embedId)) {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[RichTextEditor] 埋め込み ${embedId} は既に処理中または処理済みです`);
            }
            return;
          }

          // 🔧 安定化チェック: iframe変換済みかつ安定化された埋め込みは再処理しない
          const hasIframe = el.querySelector('iframe[id^="twitter-widget"]');
          const isStable = el.hasAttribute('data-embed-stable');

          if (hasIframe && isStable) {
            if (process.env.NODE_ENV === 'development') {
              console.log('[RichTextEditor] iframe変換済み＆安定化された埋め込みのため再処理をスキップします');
            }
            processedEmbeds.add(embedId);
            return;
          }

          // 🔧 Twitter widgets.jsの同時実行防止
          if (isTwitterWidgetsLoading) {
            if (process.env.NODE_ENV === 'development') {
              console.log(`[RichTextEditor] Twitter widgets.jsが実行中のため、埋め込み ${embedId} を待機キューに追加`);
            }
            setTimeout(() => processTwitterEmbed(), 500);
            return;
          }

          processingEmbeds.add(embedId);
          isTwitterWidgetsLoading = true;

          // 🔧 簡素化: 複雑な高さ保護処理を削除（自然な調整のみ）
          if (process.env.NODE_ENV === 'development') {
            console.log('[RichTextEditor] 新規埋め込み処理開始 - 自然な高さ調整を継続');
          }

          if (window.twttr && typeof window.twttr.widgets?.load === 'function') {
            try {
              if (process.env.NODE_ENV === 'development') {
                console.log('[RichTextEditor] twttr.widgets.load()を実行します（特定要素のみ）');

                // 🔍 widgets.load実行前の重複チェックと既存埋め込み高さを記録
                if (editorRef.current) {
                  const existingEmbeds = editorRef.current.querySelectorAll('.social-embed[data-platform="twitter"], .social-embed[data-platform="x"]');
                  console.log(`🔍 [HEIGHT_DEBUG] widgets.load実行前 - 全埋め込み数: ${existingEmbeds.length}`);

                  // 🔧 重複チェック
                  const embedIdCount = new Map<string, number>();
                  existingEmbeds.forEach((embed, index) => {
                    if (embed instanceof HTMLElement) {
                      const embedId = embed.getAttribute('data-embed-id');
                      const currentHeight = embed.offsetHeight;
                      console.log(`🔍 [HEIGHT_DEBUG] widgets.load実行前 - 埋め込み ${index}: ID=${embedId}, height=${currentHeight}px`);

                      if (embedId) {
                        embedIdCount.set(embedId, (embedIdCount.get(embedId) || 0) + 1);
                      }
                    }
                  });

                  // 重複があれば警告
                  embedIdCount.forEach((count, embedId) => {
                    if (count > 1) {
                      console.warn(`🔍 [HEIGHT_DEBUG] widgets.load実行前に重複検出: ID=${embedId}, 数=${count}`);
                    }
                  });
                }
              }

              // 🔧 修正: 適切なタイミングでの高さ調整を許可
              const hasIframe = el.querySelector('iframe');
              const isStable = el.hasAttribute('data-embed-stable');

              if (!hasIframe) {
                // 🔧 初回ロード: 自然な高さ調整を許可
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[RichTextEditor] 初回widgets.load実行: ${embedId}`);
                }
                window.twttr.widgets.load(el);
              } else if (!isStable) {
                // 🔧 iframe存在するが未安定化: 高さ調整を完了させる
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[RichTextEditor] 高さ調整完了のためwidgets.load実行: ${embedId}`);
                }
                window.twttr.widgets.load(el);
              } else {
                // 🔧 完全に安定化済み: スキップ
                if (process.env.NODE_ENV === 'development') {
                  console.log(`[RichTextEditor] 安定化済みのためスキップ: ${embedId}`);
                }
              }

              if (process.env.NODE_ENV === 'development') {
                // 🔍 widgets.load実行直後の既存埋め込み高さを記録
                setTimeout(() => {
                  if (editorRef.current) {
                    const existingEmbeds = editorRef.current.querySelectorAll('.social-embed[data-platform="twitter"], .social-embed[data-platform="x"]');
                    existingEmbeds.forEach((embed, index) => {
                      if (embed !== el && embed instanceof HTMLElement) {
                        const embedId = embed.getAttribute('data-embed-id');
                        const currentHeight = embed.offsetHeight;
                        console.log(`🔍 [HEIGHT_DEBUG] widgets.load実行直後 - 埋め込み ${index}: ID=${embedId}, height=${currentHeight}px`);
                      }
                    });
                  }
                }, 10);
              }

              // 🔧 widgets.js処理完了後に安定化マークを設定
              setTimeout(() => {
                const iframe = el.querySelector('iframe[id^="twitter-widget"]');
                if (iframe) {
                  el.setAttribute('data-embed-stable', 'true');
                  if (process.env.NODE_ENV === 'development') {
                    console.log('[RichTextEditor] iframe変換完了後に安定化マークを設定しました');
                  }
                }
                setupTwitterAutoHeight(el);

                // 🔧 処理完了フラグの更新
                processingEmbeds.delete(embedId);
                processedEmbeds.add(embedId);
                isTwitterWidgetsLoading = false;

                // 🔧 簡素化: 複雑な高さ復元処理を削除（自然な調整のみ）
                if (process.env.NODE_ENV === 'development') {
                  console.log('[RichTextEditor] 処理完了 - 自然な高さ調整を継続');
                }
              }, 500);

            } catch (crossOriginError) {
              // 🔧 エラー時もフラグをリセット
              processingEmbeds.delete(embedId);
              isTwitterWidgetsLoading = false;

              if (crossOriginError.name === 'SecurityError') {
                // 🔧 SecurityErrorは握りつぶす（本番環境でログ出力しない）
                return;
              }
              throw crossOriginError;
            }
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.log('[RichTextEditor] Twitter widgets.jsがまだ読み込まれていません。読み込みを試行します。');
            }
            // Twitter widgets.jsを動的に読み込み
            loadTwitterWidgetsScript(() => {
              if (window.twttr && typeof window.twttr.widgets?.load === 'function') {
                try {
                  // 🔧 動的読み込み時も既存の埋め込み要素を保護
                  if (editorRef.current) {
                    const existingEmbeds = editorRef.current.querySelectorAll('.social-embed[data-platform="twitter"], .social-embed[data-platform="x"]');
                    existingEmbeds.forEach((embed) => {
                      if (embed !== el && embed instanceof HTMLElement) {
                        const embedIframe = embed.querySelector('iframe[id^="twitter-widget"]');
                        if (embedIframe && !embed.hasAttribute('data-embed-stable')) {
                          embed.setAttribute('data-embed-stable', 'true');
                          if (process.env.NODE_ENV === 'development') {
                            console.log('[RichTextEditor] 動的読み込み前に既存埋め込みを保護しました');
                          }
                        }
                      }
                    });
                  }

                  // 🔧 修正: 適切なタイミングでの高さ調整を許可
                  const hasIframe = el.querySelector('iframe');
                  const isStable = el.hasAttribute('data-embed-stable');

                  if (!hasIframe) {
                    // 🔧 初回ロード: 自然な高さ調整を許可
                    if (process.env.NODE_ENV === 'development') {
                      console.log(`[RichTextEditor] 動的読み込み 初回widgets.load実行: ${embedId}`);
                    }
                    window.twttr.widgets.load(el);
                  } else if (!isStable) {
                    // 🔧 iframe存在するが未安定化: 高さ調整を完了させる
                    if (process.env.NODE_ENV === 'development') {
                      console.log(`[RichTextEditor] 動的読み込み 高さ調整完了のためwidgets.load実行: ${embedId}`);
                    }
                    window.twttr.widgets.load(el);
                  } else {
                    // 🔧 完全に安定化済み: スキップ
                    if (process.env.NODE_ENV === 'development') {
                      console.log(`[RichTextEditor] 動的読み込み 安定化済みのためスキップ: ${embedId}`);
                    }
                  }

                  // 🔧 widgets.js処理完了後に安定化マークを設定
                  setTimeout(() => {
                    const iframe = el.querySelector('iframe[id^="twitter-widget"]');
                    if (iframe) {
                      el.setAttribute('data-embed-stable', 'true');
                      if (process.env.NODE_ENV === 'development') {
                        console.log('[RichTextEditor] 動的読み込み後のiframe変換完了で安定化マークを設定しました');
                      }
                    }
                    setupTwitterAutoHeight(el);

                    // 🔧 処理完了フラグの更新
                    processingEmbeds.delete(embedId);
                    processedEmbeds.add(embedId);
                    isTwitterWidgetsLoading = false;

                    // 🔧 簡素化: 複雑な高さ復元処理を削除（自然な調整のみ）
                    if (process.env.NODE_ENV === 'development') {
                      console.log('[RichTextEditor] 動的読み込み完了 - 自然な高さ調整を継続');
                    }
                  }, 500);
                } catch (error) {
                  // 🔧 エラー時もフラグをリセット
                  processingEmbeds.delete(embedId);
                  isTwitterWidgetsLoading = false;

                  if (process.env.NODE_ENV === 'development') {
                    console.error('[RichTextEditor] Twitter widgets.load エラー:', error);
                  }
                }
              }
            });
          }
        };

        processTwitterEmbed();
      } else if (platform.toLowerCase() === 'tiktok') {
        handleTikTokEmbed(el);
      } else if (platform.toLowerCase() === 'youtube') {
        handleYouTubeEmbed(el);
      }
    } catch (scriptError) {
      if (scriptError instanceof DOMException && scriptError.name === 'SecurityError') {
        // 🔧 SecurityErrorは握りつぶす（本番環境でログ出力しない）
        el.setAttribute('data-embed-error', 'security-error');
      } else {
        if (process.env.NODE_ENV === 'development') {
          console.error(`[RichTextEditor] ${platform}のスクリプト処理エラー:`, scriptError);
        }
        el.setAttribute('data-embed-error', 'script-error');
      }
    }
  };

  // TikTok埋め込みの処理
  const handleTikTokEmbed = (el: HTMLElement) => {
    console.log('[RichTextEditor] TikTokの埋め込みを処理します');

    if (window.TikTok) {
      console.log('[RichTextEditor] TikTok.embed.reload()を実行します');
      try {
        window.TikTok.embed.reload();
      } catch (error) {
        console.warn('[RichTextEditor] TikTok埋め込みエラー:', error);
      }
    } else {
      console.log('[RichTextEditor] TikTok埋め込み要素を処理します（直接iframe形式）');

      const tiktokEmbeds = el.querySelectorAll('blockquote.tiktok-embed');
      tiktokEmbeds.forEach((embed) => {
        if (embed.hasAttribute('data-processed')) return;

        const videoId = embed.getAttribute('data-video-id');
        const citeUrl = embed.getAttribute('cite');

        let extractedVideoId = videoId;
        if (!extractedVideoId && citeUrl) {
          const match = citeUrl.match(/\/video\/(\d+)/);
          if (match) {
            extractedVideoId = match[1];
          }
        }

        const newContainer = document.createElement('div');
        newContainer.className = 'tiktok-iframe-container';
        newContainer.setAttribute('data-processed', 'true');

        if (extractedVideoId) {
          const iframe = document.createElement('iframe');
          iframe.src = `https://www.tiktok.com/embed/v2/${extractedVideoId}`;
          iframe.width = TIKTOK_EMBED_WIDTH;
          iframe.height = TIKTOK_EMBED_HEIGHT;
          iframe.allowFullscreen = true;
          iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture');

          newContainer.appendChild(iframe);
        }

        embed.parentNode?.replaceChild(newContainer, embed);
        console.log('[RichTextEditor] TikTok埋め込み要素を置き換えました（直接iframe形式）');

        if (el instanceof HTMLElement) {
          el.setAttribute('data-loaded', 'true');
        }
      });
    }
  };

  // YouTube埋め込みの処理
  const handleYouTubeEmbed = (el: HTMLElement) => {
    console.log('[RichTextEditor] YouTubeの埋め込みを処理します');

    if (window.TikTok) {
      console.log('[RichTextEditor] YouTube.embed.reload()を実行します');
      try {
        // YouTubeにはTikTokのようなreload機能がないため、スキップ
      } catch (error) {
        console.warn('[RichTextEditor] YouTube埋め込みエラー:', error);
      }
    } else {
      console.log('[RichTextEditor] YouTube埋め込み要素を処理します（直接iframe形式）');

      // TikTokと同じ構造：.social-embed-content内のiframeを探す
      const youtubeEmbeds = el.querySelectorAll('.social-embed-content iframe');
      youtubeEmbeds.forEach((embed) => {
        if (embed.hasAttribute('data-processed')) return;

        const url = el.getAttribute('data-url');
        if (!url) return;

        const videoId = extractYouTubeVideoId(url);
        if (!videoId) return;

        // 新しいコンテナを作成（TikTokと同じパターン）
        const newContainer = document.createElement('div');
        newContainer.className = 'youtube-iframe-container';
        newContainer.setAttribute('data-processed', 'true');

        const iframe = document.createElement('iframe');
        iframe.src = `https://www.youtube.com/embed/${videoId}`;
        iframe.width = '560';
        iframe.height = '315';
        iframe.allowFullscreen = true;
        iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share');
        iframe.setAttribute('frameborder', '0');

        newContainer.appendChild(iframe);

        // TikTokと同じように要素を置き換え
        embed.parentNode?.replaceChild(newContainer, embed);
        console.log('[RichTextEditor] YouTube埋め込み要素を置き換えました（直接iframe形式）');

        if (el instanceof HTMLElement) {
          el.setAttribute('data-loaded', 'true');
        }
      });
    }
  };

  return {
    insertEmbed,
    processSocialEmbeds,
    saveCursorPosition
  };
};