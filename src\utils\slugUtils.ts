import <PERSON><PERSON><PERSON> from 'kuroshiro';
import KuromojiAnalyzer from 'kuroshiro-analyzer-kuromoji';
import slugify from 'slugify';

// Kuroshiroインスタンスをグローバルに保持（初期化は一度だけ）
let kuroshiroInstance: <PERSON><PERSON><PERSON> | null = null;
let isInitializing = false;

/**
 * Kuroshiroを初期化する関数
 */
async function initializeKuroshiro(): Promise<Ku<PERSON>hiro> {
  if (kuroshiroInstance) {
    return kuroshiroInstance;
  }

  if (isInitializing) {
    // 初期化中の場合は完了まで待機
    while (isInitializing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    return kuroshiroInstance!;
  }

  isInitializing = true;

  try {
    kuroshiroInstance = new Kuroshiro();
    await kuroshiroInstance.init(new KuromojiAnalyzer());
    console.log('✅ Ku<PERSON>hiro初期化完了');
    return kuroshiroInstance;
  } catch (error) {
    console.error('❌ <PERSON><PERSON>hiro初期化エラー:', error);
    throw error;
  } finally {
    isInitializing = false;
  }
}

/**
 * 日本語（ひらがな・カタカナ・漢字）をローマ字スラッグに変換する関数
 * @param japanese 日本語文字列
 * @returns Promise<string> ローマ字スラッグ
 * 
 * 例:
 * - "渋谷皮膚科" → "shibuya-hifuka"
 * - "横浜" → "yokohama"
 * - "カテゴリ1" → "kategori1"
 */
export async function convertToSlug(japanese: string): Promise<string> {
  try {
    console.log('🔄 スラッグ変換開始:', japanese);

    // 入力値の検証
    if (!japanese || typeof japanese !== 'string' || japanese.trim() === '') {
      console.warn('⚠️ 無効な入力値:', japanese);
      return `category_${Date.now()}`;
    }

    const trimmedInput = japanese.trim();

    // 日本語が含まれているかチェック
    const hasJapanese = /[ぁ-んァ-ンー一-龥]/.test(trimmedInput);
    
    if (!hasJapanese) {
      // 日本語が含まれていない場合は直接slugify
      const result = slugify(trimmedInput, { 
        lower: true, 
        strict: true,
        replacement: '-'
      });
      console.log('✅ 非日本語スラッグ変換完了:', trimmedInput, '->', result);
      return result || `category_${Date.now()}`;
    }

    // Kuroshiroを初期化
    const kuroshiro = await initializeKuroshiro();

    // 漢字・ひらがな・カタカナをローマ字に変換
    const romaji = await kuroshiro.convert(trimmedInput, {
      to: 'romaji',
      mode: 'spaced', // 単語間にスペースを入れる
      romajiSystem: 'passport' // パスポート式ローマ字（ヘボン式）
    });

    console.log('🔄 ローマ字変換結果:', trimmedInput, '->', romaji);

    // スペースをハイフンに変換し、slugifyで最終調整
    const slug = slugify(romaji, {
      lower: true,
      strict: true,
      replacement: '-'
    });

    // 結果の検証
    const finalSlug = slug && slug.trim() !== '' ? slug : `category_${Date.now()}`;
    
    console.log('✅ スラッグ変換完了:', trimmedInput, '->', finalSlug);
    return finalSlug;

  } catch (error) {
    console.error('❌ スラッグ変換エラー:', error);
    console.error('入力値:', japanese);
    
    // エラー時のフォールバック
    const fallbackSlug = `category_${Date.now()}`;
    console.log('🔄 フォールバックスラッグ使用:', fallbackSlug);
    return fallbackSlug;
  }
}

/**
 * 同期版のスラッグ生成（フォールバック用）
 * 日本語が含まれている場合は非同期版を推奨
 */
export function convertToSlugSync(input: string): string {
  try {
    if (!input || typeof input !== 'string' || input.trim() === '') {
      return `category_${Date.now()}`;
    }

    const trimmedInput = input.trim();
    
    // 日本語が含まれている場合は警告
    if (/[ぁ-んァ-ンー一-龥]/.test(trimmedInput)) {
      console.warn('⚠️ 日本語が含まれています。convertToSlug()の使用を推奨します:', trimmedInput);
      return `category_${Date.now()}`;
    }

    // 英数字のみの場合
    const slug = slugify(trimmedInput, {
      lower: true,
      strict: true,
      replacement: '-'
    });

    return slug || `category_${Date.now()}`;
  } catch (error) {
    console.error('❌ 同期スラッグ変換エラー:', error);
    return `category_${Date.now()}`;
  }
}

/**
 * スラッグの妥当性をチェックする関数
 */
export function isValidSlug(slug: string): boolean {
  if (!slug || typeof slug !== 'string') {
    return false;
  }
  
  // 英数字とハイフンのみを許可
  const validPattern = /^[a-z0-9-]+$/;
  return validPattern.test(slug) && slug.length > 0 && slug.length <= 100;
}
