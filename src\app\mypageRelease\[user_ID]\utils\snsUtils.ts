/**
 * SNS関連のユーティリティ関数
 */

/**
 * SNSタイプからアイコン画像のパスを取得
 */
export const getSnsIconSrc = (type: string): string => {
  switch (type.toLowerCase()) {
    case "instagram":
      return "/static/img/instagram.png";
    case "tiktok":
      return "/static/img/tiktok.png";
    case "x":
    case "twitter":
      return "/static/img/x.png";
    case "youtube":
      return "/static/img/youtube.png";
    case "facebook":
      return "/static/img/facebook.png";
    case "line":
      return "/static/img/line.png";
    case "amazon":
      return "/static/img/amazon.png";
    case "note":
      return "/static/img/note.png";
    case "rakuten":
      return "/static/img/rakuten.png";
    case "wear":
      return "/static/img/wear.png";
    case "blog":
      return "/static/img/blog.png";
    case "bigo live":
    case "bigolive":
      return "/static/img/bigo.png";
    case "17live":
      return "/static/img/17live.png";
    case "twitcasting":
    case "ツイキャス":
      return "/static/img/twicasting.png";
    case "pococha":
      return "/static/img/pococha.png";
    case "showroom":
      return "/static/img/showroom.png";
    case "2ch":
      return "/static/img/2ch.png";
    case "ふわっち":
      return "/static/img/fuwatto.png";
    case "clubhouse":
      return "/static/img/clubhouse.png";
    case "voicy":
      return "/static/img/voicy.png";
    case "podcast":
      return "/static/img/podcast.png";
    case "pinterest":
      return "/static/img/pinterest.png";
    case "discord":
      return "/static/img/discord.png";
    case "telegram":
      return "/static/img/telegram.png";
    case "chatwork":
      return "/static/img/chatwork.png";
    case "linkedin":
      return "/static/img/linkedin.png";
    case "sansan":
      return "/static/img/sansan.png";
    case "eight":
      return "/static/img/eight.png";
    case "食べログ":
      return "/static/img/tabelog.png";
    case "retty":
      return "/static/img/retty.png";
    case "language":
    case "website":
      return "/static/img/website.png";
    case "email":
      return "/static/img/email.png";
    default:
      return "/static/img/profile-default.png";
  }
};

/**
 * SNSタイプから表示名を取得
 */
export const getSnsDisplayName = (type: string): string => {
  switch (type.toLowerCase()) {
    case "instagram":
      return "Instagram";
    case "tiktok":
      return "TikTok";
    case "x":
    case "twitter":
      return "Twitter";
    case "youtube":
      return "YouTube";
    case "facebook":
      return "Facebook";
    case "line":
      return "LINE";
    case "amazon":
      return "Amazon";
    case "note":
      return "note";
    case "rakuten":
      return "Rakuten";
    case "wear":
      return "WEAR";
    case "blog":
      return "Blog";
    case "bigo live":
    case "bigolive":
      return "BIGO LIVE";
    case "17live":
      return "17Live";
    case "twitcasting":
    case "ツイキャス":
      return "ツイキャス";
    case "pococha":
      return "Pococha";
    case "showroom":
      return "SHOWROOM";
    case "2ch":
    case "ふわっち":
      return "ふわっち";
    case "clubhouse":
      return "Clubhouse";
    case "voicy":
      return "Voicy";
    case "podcast":
      return "Podcast";
    case "pinterest":
      return "Pinterest";
    case "discord":
      return "Discord";
    case "telegram":
      return "Telegram";
    case "chatwork":
      return "Chatwork";
    case "linkedin":
      return "LinkedIn";
    case "sansan":
      return "Sansan";
    case "eight":
      return "Eight";
    case "食べログ":
      return "食べログ";
    case "retty":
      return "Retty";
    default:
      return type;
  }
};

/**
 * デフォルトのSNS順序
 */
export const DEFAULT_SNS_ORDER = [
  "instagram",
  "tiktok",
  "x",
  "youtube",
  "facebook",
  "line",
  "other"
];