'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function ChangeOrderGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">ランキングの表示順位を変更する方法</h1>
          <div className="pt-6 text-[14px] text-[#313131]">
              <p>1. マイページから編集したいランキングを選択します。</p>
              <p>2. 「編集」ボタンをタップします。</p>
              <p>3. 「順序変更」タブを選択します。</p>
              <p>4. アイテムを上下にドラッグして順序を変更します。</p>
              <p>5. 変更が完了したら「保存」ボタンをタップして確定します。</p>
            </div>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
