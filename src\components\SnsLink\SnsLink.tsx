"use client"; // ← 追加（Next.jsのServer Componentエラー対策）

import React, { useState, useEffect } from "react";
import Image from "next/image";

interface Props {
  className?: string;
  snsIcon: string;
  text: string;
  snsIconClassName?: string;
  initialValue?: string;
  onSave?: (type: string, value: string) => void;
}

export const SnsLink = ({
  className,
  snsIcon = "https://c.animaapp.com/xUiqbHEm/img/snsicon.png",
  text = "Instagram",
  snsIconClassName,
  initialValue = "",
  onSave,
}: Props): JSX.Element => {
  const [inputValue, setInputValue] = useState(initialValue);

  // 初期値が変更された場合に更新
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
  };

  const handleBlur = () => {
    // フォーカスが外れた時点で保存関数を呼び出す
    if (onSave) {
      onSave(text, inputValue);
    }
  };

  // Enterキーが押されたときにも保存する
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault(); // デフォルトの動作を防止
      if (onSave) {
        console.log(`SNSリンク保存(Enter): ${text} => ${inputValue}`);
        onSave(text, inputValue);
      }
      // フォーカスを外す
      (e.target as HTMLInputElement).blur();
    }
  };

  return (
    <div
      className={`flex max-w-[500px] flex justify-between items-center relative bg-white border-b [border-bottom-style:solid] border-[#DDDDDD] ${className}`}
    >
      <div className="flex w-[40%] h-[48px] items-center gap-[6px] pl-[16px] pr-0 py-0 relative">
        <Image
          className={`rounded-[100px] border border-[#DDDDDD] border-[1px] relative w-[22px] h-[22px] ml-[-1.00px] object-cover ${snsIconClassName}`}
          alt={`${text} icon`}
          src={snsIcon}
          width={22}
          height={22}
          priority
        />
        <div className="relative w-fit text-[#313131] text-[14px] tracking-[0] leading-[normal] whitespace-nowrap">
          {text}
        </div>
      </div>
      <div className="flex w-[60%] h-[48px] items-center justify-end pl-[6px] pr-[16px] py-0 relative bg-white">
        <input
          type="text"
          className={`w-full h-[24px] flex items-center text-[12px] tracking-[0] leading-[normal] bg-transparent outline-none text-right
            ${inputValue ? "text-[#313131]" : "text-[#AAAAAA]"}
            focus:caret-[#313131] p-1`}
          value={inputValue}
          onChange={handleChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder="URLを入力する"
        />
      </div>
    </div>
  );
};
