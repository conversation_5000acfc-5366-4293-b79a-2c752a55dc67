import React from 'react';

interface LinkSectionProps {
  amazonUrl: string;
  setAmazonUrl: (url: string) => void;
  rakutenUrl: string;
  setRakutenUrl: (url: string) => void;
  yahooUrl: string;
  setYahooUrl: (url: string) => void;
  qoo10Url: string;
  setQoo10Url: (url: string) => void;
  officialUrl: string;
  setOfficialUrl: (url: string) => void;
}

/**
 * 商品リンクセクションコンポーネント
 */
export const LinkSection: React.FC<LinkSectionProps> = ({
  amazonUrl,
  setAmazonUrl,
  rakutenUrl,
  setRakutenUrl,
  yahooUrl,
  setYahooUrl,
  qoo10Url,
  setQoo10Url,
  officialUrl,
  setOfficialUrl
}) => {
  const handleAmazonUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAmazonUrl(e.target.value);
  };

  const handleRakutenUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRakutenUrl(e.target.value);
  };

  const handleYahooUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setYahooUrl(e.target.value);
  };

  const handleQoo10UrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQoo10Url(e.target.value);
  };

  const handleOfficialUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOfficialUrl(e.target.value);
  };
  return (
    <div className="py-3">
      <div className="px-4 flex justify-between items-center mb-2">
        <p className="text-[#313131] text-[16px] font-bold">商品のリンクを追加する <span className="text-xs text-red-500">(1つ以上追加してください)</span></p>
      </div>
      
      <div className="">
        <div className="flex items-center bg-white border-t border-x border-gray-200 overflow-hidden">
          <div className="px-3 py-2 w-12 flex justify-center items-center h-[48px]">
            <img src="/static/img/amazonec.png" alt="Amazon" className="w-5 h-5" />
          </div>
          <div className="flex items-center h-[48px] text-[#313131] text-sm px-2 border-gray-200">
            Amazon
          </div>
          <div className="flex-1 border-gray-200">
            <input
              type="text"
              value={amazonUrl}
              onChange={handleAmazonUrlChange}
              className="w-full h-[48px] px-3 border-none outline-none text-sm text-right"
              placeholder="https://amazon.co.jp/"
            />
          </div>
        </div>
        
        <div className="flex items-center bg-white border-t border-x border-gray-200 overflow-hidden">
          <div className="px-3 py-2 w-12 flex justify-center items-center h-[48px]">
            <img src="/static/img/rakutenec.png" alt="楽天" className="w-5 h-5" />
          </div>
          <div className="flex items-center h-[48px] text-[#313131] text-sm px-2 border-gray-200">
            楽天
          </div>
          <div className="flex-1 border-gray-200">
            <input
              type="text"
              value={rakutenUrl}
              onChange={handleRakutenUrlChange}
              className="w-full h-[48px] px-3 border-none outline-none text-sm text-right"
              placeholder="https://rakuten.co.jp/"
            />
          </div>
        </div>
        
        <div className="flex items-center bg-white border-t border-x border-gray-200 overflow-hidden">
          <div className="px-3 py-2 w-12 flex justify-center items-center h-[48px]">
            <img src="/static/img/yahooshopping.png" alt="Yahoo!ショッピング" className="w-5 h-5" />
          </div>
          <div className="flex items-center h-[48px] text-[#313131] text-sm px-2 border-gray-200">
            Yahoo!ショッピング
          </div>
          <div className="flex-1 border-gray-200">
            <input
              type="text"
              value={yahooUrl}
              onChange={handleYahooUrlChange}
              className="w-full h-[48px] px-3 border-none outline-none text-sm text-right"
              placeholder="https://shopping.yahoo.co.jp/"
            />
          </div>
        </div>
        
        <div className="flex items-center bg-white border-t border-x border-gray-200 overflow-hidden">
          <div className="px-3 py-2 w-12 flex justify-center items-center h-[48px]">
            <img src="/static/img/qoo10.png" alt="Qoo10" className="w-5 h-5" />
          </div>
          <div className="flex items-center h-[48px] text-[#313131] text-sm px-2 border-gray-200">
            Qoo10
          </div>
          <div className="flex-1 border-gray-200">
            <input
              type="text"
              value={qoo10Url}
              onChange={handleQoo10UrlChange}
              className="w-full h-[48px] px-3 border-none outline-none text-sm text-right"
              placeholder="https://qoo10.jp/"
            />
          </div>
        </div>
        
        <div className="flex items-center bg-white border-t border-x border-b border-gray-200 overflow-hidden">
          <div className="px-3 py-2 w-12 flex justify-center items-center h-[48px]">
            <img src="/static/img/homepage.png" alt="公式サイト" className="w-5 h-5" />
          </div>
          <div className="flex items-center h-[48px] text-[#313131] text-sm px-2 border-gray-200">
            公式サイト
          </div>
          <div className="flex-1 border-gray-200">
            <input
              type="text"
              value={officialUrl}
              onChange={handleOfficialUrlChange}
              className="w-full h-[48px] px-3 border-none outline-none text-sm text-right"
              placeholder="https://"
            />
          </div>
        </div>
        <div className="text-right text-xs mt-2 mr-2">
          ※商品リンクの表示イメージは<a href="#" className="underline text-blue-500">こちら</a>
        </div>
      </div>
    </div>
  );
};
