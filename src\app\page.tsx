'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';

export default function Home() {
  const router = useRouter();
  const { user, isLoaded } = useUser();

  useEffect(() => {
    if (isLoaded) {
      if (user) {
        // ログイン済みの場合は、ユーザーのセットアップ状況を確認
        const checkUserSetup = async () => {
          try {
            const response = await fetch(`/api/getUser?user_ID=${user.id}`);
            if (response.ok) {
              const userData = await response.json();
              if (userData.setup_completed && userData.username) {
                // セットアップ完了済みの場合はユーザーページにリダイレクト
                router.replace(`/${userData.username}`);
              } else {
                // セットアップ未完了の場合はセットアップページにリダイレクト
                router.replace('/setup');
              }
            } else {
              // ユーザーデータが見つからない場合はセットアップページにリダイレクト
              router.replace('/setup');
            }
          } catch (error) {
            console.error('ユーザー情報取得エラー:', error);
            router.replace('/setup');
          }
        };

        checkUserSetup();
      } else {
        // 未ログインの場合はサインインページにリダイレクト
        router.replace('/sign-in');
      }
    }
  }, [user, isLoaded, router]);

  // ローディング表示
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <div className="flex-1 flex items-center justify-center">
        <div className="max-w-[500px] w-full mx-auto bg-white shadow-md min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E63B5F] mx-auto mb-4"></div>
            <p className="text-[#313131]">読み込み中...</p>
          </div>
        </div>
      </div>
    </div>
  );
}
