# 入力系コンポーネント

## 概要

mypicks.bestアプリケーションで使用される入力系コンポーネントの詳細説明です。これらのコンポーネントは、ユーザーからの情報入力を効率的に処理するために設計されています。

## <a id="categoryselect"></a>CategorySelect

カテゴリとサブカテゴリを選択するためのコンポーネントです。

### 機能

- カテゴリの選択
- サブカテゴリの選択
- 選択したカテゴリ・サブカテゴリの表示

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| category | string | はい | 選択されているカテゴリ名 |
| subCategory | string | いいえ | 選択されているサブカテゴリ名 |
| onCategoryChange | (category: string, categoryId: string) => void | はい | カテゴリが変更されたときのコールバック関数 |
| onSubCategoryChange | (subCategory: string, subCategoryId: string) => void | はい | サブカテゴリが変更されたときのコールバック関数 |

### 実装の注意点

- 以前はカテゴリIDが表示されていましたが、ユーザーには内部的なIDではなく、カテゴリ名を表示するように修正されました
- ランキング登録ページに`categoryName`という新しい状態変数を追加し、URLパラメータからカテゴリIDを取得した後、APIを呼び出してそのIDに対応するカテゴリ名を取得するようになっています
- 取得したカテゴリ名を`categoryName`状態変数に設定し、CategorySelectコンポーネントに`category`プロパティとしてカテゴリIDではなくカテゴリ名を渡すようになっています
- 内部的にはIDを保持しつつ、表示用に名前を使用する設計になっています

### 使用例

```jsx
import { CategorySelect } from '@/components/CategorySelect';

const MyComponent = () => {
  const [category, setCategory] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [subCategory, setSubCategory] = useState('');
  const [subCategoryId, setSubCategoryId] = useState('');

  const handleCategoryChange = (categoryName, categoryId) => {
    setCategory(categoryName);
    setCategoryId(categoryId);
  };

  const handleSubCategoryChange = (subCategoryName, subCategoryId) => {
    setSubCategory(subCategoryName);
    setSubCategoryId(subCategoryId);
  };

  return (
    <CategorySelect
      category={category}
      subCategory={subCategory}
      onCategoryChange={handleCategoryChange}
      onSubCategoryChange={handleSubCategoryChange}
    />
  );
};
```

## <a id="inputbox"></a>InputBox

テキスト入力用のボックスコンポーネントです。

### 機能

- テキスト入力
- プレースホルダー表示
- 入力値の検証

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| value | string | はい | 入力値 |
| onChange | (value: string) => void | はい | 入力値が変更されたときのコールバック関数 |
| placeholder | string | いいえ | プレースホルダーテキスト |
| type | string | いいえ | 入力タイプ（text, password, email など） |
| className | string | いいえ | 追加のCSSクラス |

### 使用例

```jsx
import { InputBox } from '@/components/InputBox';

const MyComponent = () => {
  const [value, setValue] = useState('');

  const handleChange = (newValue) => {
    setValue(newValue);
  };

  return (
    <InputBox
      value={value}
      onChange={handleChange}
      placeholder="ユーザー名を入力してください"
      type="text"
    />
  );
};
```

## <a id="inputlist"></a>InputList

複数の入力フィールドを持つリストコンポーネントです。

### 機能

- 複数の入力フィールドの管理
- 入力値の追加・削除
- 入力値の検証

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| items | string[] | はい | 入力アイテムの配列 |
| onItemsChange | (items: string[]) => void | はい | アイテムが変更されたときのコールバック関数 |
| placeholder | string | いいえ | プレースホルダーテキスト |
| user_ID | string | いいえ | ユーザーID |

### 実装の注意点

- 以前は固定値「user123」をデフォルト引数として使用していましたが、現在はUserContext.tsxからユーザーIDを動的に取得するように修正されています
- ユーザーIDが渡されていない場合はUserContextから取得する実装パターンを採用しています
- useEffectの依存配列にcurrentUserIdを追加し、ユーザーIDが変更された場合に適切に再レンダリングされるようになっています

### 使用例

```jsx
import { InputList } from '@/components/InputList';

const MyComponent = () => {
  const [items, setItems] = useState(['']);

  const handleItemsChange = (newItems) => {
    setItems(newItems);
  };

  return (
    <InputList
      items={items}
      onItemsChange={handleItemsChange}
      placeholder="アイテムを入力してください"
    />
  );
};
```

## <a id="richtexteditor"></a>RichTextEditor

リッチテキストエディタコンポーネントです。

### 機能

- リッチテキスト編集
- テンプレート選択
- プレースホルダー表示

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| value | string | はい | 入力値 |
| onChange | (value: string) => void | はい | 入力値が変更されたときのコールバック関数 |
| placeholder | string | いいえ | プレースホルダーテキスト |
| templates | { label: string, value: string }[] | いいえ | 選択可能なテンプレートの配列 |

### 実装の注意点

- テンプレートを選択した際に、改行（\n）がHTMLとして表示されず、テキストが一行に表示されていた問題が修正されています
- dangerouslySetInnerHTMLに渡す前に、改行文字（\n）をHTMLの改行タグ（<br>）に変換する処理が追加されています
- onBlurイベントハンドラ内とdangerouslySetInnerHTMLプロパティの両方で改行変換が実装されています
- この修正により、テンプレートの改行が保持され、ユーザーにとって読みやすく、使いやすい形式で表示されるようになりました

### 使用例

```jsx
import { RichTextEditor } from '@/components/RichTextEditor';

const MyComponent = () => {
  const [value, setValue] = useState('');

  const handleChange = (newValue) => {
    setValue(newValue);
  };

  const templates = [
    { label: 'テンプレート1', value: '商品名：\n価格：\n特徴：' },
    { label: 'テンプレート2', value: '場所：\n営業時間：\nおすすめポイント：' },
  ];

  return (
    <RichTextEditor
      value={value}
      onChange={handleChange}
      placeholder="説明を入力してください"
      templates={templates}
    />
  );
};
```

## <a id="reviewinputbox"></a>ReviewInputBox

レビュー入力用のボックスコンポーネントです。

### 機能

- レビューテキスト入力
- プレースホルダー表示
- 入力値の検証

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| value | string | はい | 入力値 |
| onChange | (value: string) => void | はい | 入力値が変更されたときのコールバック関数 |
| placeholder | string | いいえ | プレースホルダーテキスト |
| className | string | いいえ | 追加のCSSクラス |

### 使用例

```jsx
import { ReviewInputBox } from '@/components/ReviewInputBox';

const MyComponent = () => {
  const [review, setReview] = useState('');

  const handleChange = (newReview) => {
    setReview(newReview);
  };

  return (
    <ReviewInputBox
      value={review}
      onChange={handleChange}
      placeholder="レビューを入力してください"
    />
  );
};
```

## <a id="reviewscore"></a>ReviewScore

レビュースコア入力用のコンポーネントです。

### 機能

- 星評価の入力
- スコアの表示
- スコアの変更

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| score | number | はい | 現在のスコア（1〜5） |
| onChange | (score: number) => void | はい | スコアが変更されたときのコールバック関数 |
| readOnly | boolean | いいえ | 読み取り専用モードかどうか |
| size | 'small' \| 'medium' \| 'large' | いいえ | 星のサイズ |

### 使用例

```jsx
import { ReviewScore } from '@/components/ReviewScore';

const MyComponent = () => {
  const [score, setScore] = useState(0);

  const handleScoreChange = (newScore) => {
    setScore(newScore);
  };

  return (
    <ReviewScore
      score={score}
      onChange={handleScoreChange}
      size="medium"
    />
  );
};
```

## <a id="inputlistwrapper"></a>InputListWrapper

複数の入力リストをラップするコンポーネントです。

### 機能

- 複数の入力リストの管理
- 入力リストの追加・削除
- 入力値の検証

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| lists | { id: string, items: string[] }[] | はい | 入力リストの配列 |
| onListsChange | (lists: { id: string, items: string[] }[]) => void | はい | リストが変更されたときのコールバック関数 |
| placeholder | string | いいえ | プレースホルダーテキスト |

### 使用例

```jsx
import { InputListWrapper } from '@/components/InputListWrapper';

const MyComponent = () => {
  const [lists, setLists] = useState([{ id: '1', items: [''] }]);

  const handleListsChange = (newLists) => {
    setLists(newLists);
  };

  return (
    <InputListWrapper
      lists={lists}
      onListsChange={handleListsChange}
      placeholder="アイテムを入力してください"
    />
  );
};
```

## 最終更新日

2025年4月25日
