'use client';

import React, { useState, useEffect, useRef } from "react";
import { SnsLink } from "../SnsLink"; // インポートパスは調整してください
import { useProfile } from "../../contexts/ProfileContext";

interface Props {
  className: any;
}

export const OtherSns = ({ className }: Props): JSX.Element => {
  const [isOpen, setIsOpen] = useState(false);
  const [contentHeight, setContentHeight] = useState(0);
  const contentRef = useRef<HTMLDivElement>(null);
  const { profileData, updateSnsLink } = useProfile();

  // コンテンツの高さを測定
  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(contentRef.current.scrollHeight);
    }
  }, [profileData]); // profileDataが変更されたときに再測定

  const toggleAccordion = () => {
    setIsOpen((prev) => !prev);
  };

  return (
    <div className={`w-full max-w-[500px] ${className}`}>
      {/* アコーディオンのトリガー */}
      <div
        className="flex justify-between items-center bg-background-color-gray1 border-b border-line h-[48px] cursor-pointer"
        onClick={toggleAccordion}
      >
        <div className="flex items-center pl-[16px] text-black text-[14px]">
          その他のSNSを追加する
        </div>
        <div className="flex items-center justify-center w-[48px] h-[48px] border-l border-line">
          <img
            className={`w-[16px] h-[16px] transform transition-transform duration-300 ${
              isOpen ? "rotate-180" : ""
            }`}
            alt="開閉アイコン"
            src="/static/img/imageopenbutton.png"
          />
        </div>
      </div>

      {/* アコーディオン中身（その他のSNSを追加する） */}
      <div 
        className="overflow-hidden transition-all duration-500 ease-in-out bg-white border-b border-line"
        style={{ 
          maxHeight: isOpen ? `${contentHeight}px` : '0',
          opacity: isOpen ? 1 : 0,
          borderBottomWidth: isOpen ? '1px' : '0',
        }}
      >
        {/* このdivは常に存在し、高さを測定するために使用 */}
        <div ref={contentRef} >
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/facebook.png"
            text="facebook"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["facebook"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/line.png"
            text="LINE"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["LINE"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/amazon.png"
            text="Amazon"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Amazon"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/note.png"
            text="note"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["note"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/rakuten.png"
            text="Rakuten"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Rakuten"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/wear.png"
            text="WEAR"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["WEAR"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/blog.png"
            text="Blog"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Blog"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/bigo.png"
            text="BIGO LIVE"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["BIGO LIVE"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/17live.png"
            text="17Live"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["17Live"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/twicasting.png"
            text="ツイキャス"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["ツイキャス"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/pococha.png"
            text="Pococha"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Pococha"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/showroom.png"
            text="SHOWROOM"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["SHOWROOM"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/fuwatto.png"
            text="ふわっち"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["ふわっち"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/clubhouse.png"
            text="Clubhouse"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Clubhouse"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/voicy.png"
            text="Voicy"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Voicy"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/podcast.png"
            text="Podcast"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Podcast"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/pinterest.png"
            text="Pinterest"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Pinterest"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/discord.png"
            text="Discord"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Discord"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/telegram.png"
            text="Telegram"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Telegram"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/chatwork.png"
            text="Chatwork"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Chatwork"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/linkedin.png"
            text="LinkedIn"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["LinkedIn"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/sansan.png"
            text="Sansan"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Sansan"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/eight.png"
            text="Eight"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Eight"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/tabelog.png"
            text="食べログ"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["食べログ"] || ""}
            onSave={updateSnsLink}
          />
          <SnsLink
            className="!flex-[0_0_auto]"
            snsIcon="/static/img/retty.png"
            text="Retty"
            snsIconClassName="![object-fit:unset]"
            initialValue={profileData.snsLinks["Retty"] || ""}
            onSave={updateSnsLink}
          />
        </div>
      </div>
    </div>
  );
};
