/**
 * ブラウザ拡張機能による干渉を防ぐためのユーティリティ
 * content scriptによるエラーを防御し、アプリケーションの安定性を向上させる
 */

import React from 'react';

// グローバルエラーハンドラーの設定
export const setupGlobalErrorHandling = (): void => {
  if (typeof window === 'undefined') return;

  // 未処理のエラーをキャッチ
  window.addEventListener('error', (event) => {
    // content scriptによるエラーかどうかを判定
    if (isContentScriptError(event.error)) {
      // content scriptのエラーはコンソールに記録するが、アプリケーションには影響させない
      console.warn('[Browser Extension] Content script error detected and handled:', event.error);
      event.preventDefault();
      return false;
    }
  });

  // 未処理のPromise rejectionをキャッチ
  window.addEventListener('unhandledrejection', (event) => {
    if (isContentScriptError(event.reason)) {
      console.warn('[Browser Extension] Content script promise rejection detected and handled:', event.reason);
      event.preventDefault();
      return false;
    }
  });
};

// content scriptによるエラーかどうかを判定
const isContentScriptError = (error: any): boolean => {
  if (!error) return false;

  const errorMessage = String(error.message || error);
  
  // content scriptでよく発生するエラーパターン
  const contentScriptPatterns = [
    'substring is not a function',
    'content.js',
    'content_script',
    'extension',
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    'Cannot read property',
    'Cannot read properties of undefined',
    'Cannot read properties of null'
  ];

  return contentScriptPatterns.some(pattern => 
    errorMessage.toLowerCase().includes(pattern.toLowerCase())
  );
};

// DOM要素の安全な操作のためのヘルパー関数
export const safeStringOperation = {
  /**
   * 安全なsubstring操作
   */
  substring: (str: any, start: number, end?: number): string => {
    // 型チェック
    if (typeof str !== 'string') {
      str = String(str || '');
    }
    
    // 範囲チェック
    if (start < 0) start = 0;
    if (str.length === 0) return '';
    
    try {
      return end !== undefined ? str.substring(start, end) : str.substring(start);
    } catch (error) {
      console.warn('[SafeStringOperation] substring error:', error);
      return '';
    }
  },

  /**
   * 安全なslice操作
   */
  slice: (str: any, start: number, end?: number): string => {
    if (typeof str !== 'string') {
      str = String(str || '');
    }
    
    try {
      return end !== undefined ? str.slice(start, end) : str.slice(start);
    } catch (error) {
      console.warn('[SafeStringOperation] slice error:', error);
      return '';
    }
  },

  /**
   * 安全なsplit操作
   */
  split: (str: any, separator?: string | RegExp, limit?: number): string[] => {
    if (typeof str !== 'string') {
      str = String(str || '');
    }
    
    try {
      return str.split(separator, limit);
    } catch (error) {
      console.warn('[SafeStringOperation] split error:', error);
      return [str];
    }
  }
};

// DOM要素の安全な取得
export const safeDOMOperation = {
  /**
   * 安全なquerySelectorAll
   */
  querySelectorAll: (selector: string, context?: Document | Element): NodeListOf<Element> | null => {
    try {
      const root = context || document;
      return root.querySelectorAll(selector);
    } catch (error) {
      console.warn('[SafeDOMOperation] querySelectorAll error:', error);
      return null;
    }
  },

  /**
   * 安全なquerySelector
   */
  querySelector: (selector: string, context?: Document | Element): Element | null => {
    try {
      const root = context || document;
      return root.querySelector(selector);
    } catch (error) {
      console.warn('[SafeDOMOperation] querySelector error:', error);
      return null;
    }
  },

  /**
   * 安全なgetElementById
   */
  getElementById: (id: string): HTMLElement | null => {
    try {
      return document.getElementById(id);
    } catch (error) {
      console.warn('[SafeDOMOperation] getElementById error:', error);
      return null;
    }
  }
};

// アプリケーション初期化時に呼び出す
export const initializeBrowserExtensionProtection = (): void => {
  setupGlobalErrorHandling();
  
  // グローバルオブジェクトに安全な操作関数を追加
  if (typeof window !== 'undefined') {
    (window as any).__safeStringOperation = safeStringOperation;
    (window as any).__safeDOMOperation = safeDOMOperation;
  }
};

// React コンポーネント用のフック
export const useBrowserExtensionProtection = () => {
  React.useEffect(() => {
    initializeBrowserExtensionProtection();
  }, []);

  return {
    safeStringOperation,
    safeDOMOperation
  };
};

// TypeScript用の型定義
declare global {
  interface Window {
    __safeStringOperation?: typeof safeStringOperation;
    __safeDOMOperation?: typeof safeDOMOperation;
  }
}
