import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // URLパラメータからユーザーIDと親カテゴリIDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');
    const parent_ID = searchParams.get('parent_ID');

    // ユーザーIDと親カテゴリIDは必須
    if (!user_ID || !parent_ID) {
      return NextResponse.json(
        { error: 'ユーザーIDと親カテゴリIDは必須です' },
        { status: 400 }
      );
    }

    // 親カテゴリに紐づくサブカテゴリを取得
    const subCategories = await prisma.category.findMany({
      where: {
        user_ID: user_ID,
        parent_ID: parent_ID
      },
      select: {
        id: true,
        category_name: true,
        category_ID: true,
        created_at: true,
        order: true // orderカラムも取得
      },
      orderBy: {
        order: 'asc' // orderカラムで昇順に並べ替え
      }
    });

    return NextResponse.json(subCategories, { status: 200 });
  } catch (error) {
    console.error('サブカテゴリ取得エラー:', error);
    return NextResponse.json(
      { error: 'サブカテゴリの取得に失敗しました', details: error instanceof Error ? error.message : '不明なエラー' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
