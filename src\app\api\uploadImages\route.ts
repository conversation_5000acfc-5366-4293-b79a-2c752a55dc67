import { NextRequest, NextResponse } from 'next/server';

// 開発環境用の一時的な画像保存処理
export async function POST(request: NextRequest) {
  try {
    // リクエストがJSON形式かFormData形式かを判断
    const contentType = request.headers.get('content-type') || '';
    
    let imageDataArray: string[] = [];
    
    if (contentType.includes('application/json')) {
      // JSON形式の場合（Base64エンコードされた画像データ）
      const jsonData = await request.json();
      console.log('JSONデータを受信:', Object.keys(jsonData));
      
      if (!jsonData.images || !Array.isArray(jsonData.images) || jsonData.images.length === 0) {
        console.log('画像データが見つかりません:', jsonData);
        return NextResponse.json({ error: 'No images found in JSON data' }, { status: 400 });
      }
      
      imageDataArray = jsonData.images;
      console.log(`JSONから${imageDataArray.length}個の画像を受信`);
    } else {
      // FormData形式の場合（ファイルアップロード）
      const formData = await request.formData();
      const files = formData.getAll('images') as File[];
      
      console.log(`受信したファイル数: ${files.length}`);
      
      if (!files || files.length === 0) {
        console.log('ファイルが見つかりません。FormDataのキー:', Array.from(formData.keys()));
        return NextResponse.json({ error: 'No files uploaded' }, { status: 400 });
      }

      // ファイルをBase64エンコードに変換
      imageDataArray = await Promise.all(
        files.map(async (file, index) => {
          // ファイルをArrayBufferに変換
          const buffer = await file.arrayBuffer();

          // Base64エンコード
          const base64 = Buffer.from(buffer).toString('base64');

          // データURLを作成
          return `data:${file.type};base64,${base64}`;
        })
      );
    }

    // 画像URLをそのまま返す（開発環境用の簡易実装）
    return NextResponse.json({ urls: imageDataArray }, { status: 200 });
  } catch (error) {
    console.error('Error processing images:', error);
    return NextResponse.json({ error: 'Failed to process images' }, { status: 500 });
  }
}
