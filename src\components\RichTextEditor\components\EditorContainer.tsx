import React from 'react';

interface EditorContainerProps {
  editorRef: React.RefObject<HTMLDivElement>;
  value?: string;
  isFocused: boolean;
  placeholder: string;
  onInput: () => void;
  onClick: (e: React.MouseEvent) => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLDivElement>) => void;
  onFocus: () => void;
  onBlur: () => void;
}

const EditorContainer: React.FC<EditorContainerProps> = ({
  editorRef,
  value,
  isFocused,
  placeholder,
  onInput,
  onClick,
  onKeyDown,
  onFocus,
  onBlur
}) => {
  return (
    <>
      {/* エディタ本体 */}
      <div
        ref={editorRef}
        contentEditable
        className={`w-full min-h-[200px] p-3 outline-none overflow-x-hidden flex-grow ${
          !value && !isFocused ? 'text-[#AAAAAA]' : 'text-[#313131]'
        } bg-white break-words whitespace-pre-wrap`}
        onInput={onInput}
        onClick={onClick}
        onKeyDown={onKeyDown}
        onFocus={onFocus}
        onBlur={onBlur}
        style={{
          maxWidth: '100%',
          width: '100%',
          overflowX: 'hidden',
          overflowY: 'visible',
          overflowWrap: 'break-word',
          wordWrap: 'break-word',
          wordBreak: 'break-all',
          whiteSpace: 'pre-wrap',
          textOverflow: 'ellipsis',
          msOverflowStyle: 'none',
          scrollbarWidth: 'none',
          position: 'relative',
          zIndex: 10,
          isolation: 'isolate'
        }}
      />
    </>
  );
};

export default EditorContainer;