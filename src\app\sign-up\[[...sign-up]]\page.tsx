'use client';

import { SignUp, useUser } from '@clerk/nextjs';
import { Footer } from '@/components/Footer';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';


export default function Page() {
  const { user, isLoaded } = useUser();
  const router = useRouter();

  // ログイン済みユーザーのリダイレクト処理
  useEffect(() => {
    const handleLoggedInUserRedirect = async () => {
      console.log('🔍 [Sign-Up] useEffect開始');

      if (isLoaded && user) {
        console.log('🔍 [Sign-Up] 高速リダイレクト開始');
        const fastStartTime = performance.now();

        // Clerkのユーザー情報から名前を取得
        const userFullName = user.fullName || `${user.firstName || ''} ${user.lastName || ''}`.trim();
        const userEmail = user.primaryEmailAddress?.emailAddress;

        // 名前が設定されている場合は既存ユーザーかチェック
        if (userFullName && userFullName !== 'Unknown User' && userEmail) {
          try {
            // ユーザー情報を取得してusernameを確認
            const response = await fetch(`/api/getUser?user_ID=${user.id}`);
            if (response.ok) {
              const userData = await response.json();
              if (userData.username && userData.setup_completed) {
                const fastEndTime = performance.now();
                const duration = (fastEndTime - fastStartTime).toFixed(2);
                console.log(`✅ [Sign-Up] 高速リダイレクト実行: ${duration}ms → /${userData.username}`);

                // 動的にユーザー名ページにリダイレクト
                window.location.replace(`/${userData.username}`);
                return;
              }
            }
          } catch (error) {
            console.error('🔍 [Sign-Up] ユーザー情報取得エラー:', error);
          }

          // セットアップが未完了の場合はsetupページにリダイレクト
          router.replace('/setup');
          return;
        }

        // 新規ユーザーの場合はsetupにリダイレクト
        console.log('🔍 [Sign-Up] 新規ユーザー - setupにリダイレクト');
        router.replace('/setup');
      }
    };

    handleLoggedInUserRedirect();
  }, [isLoaded, user, router]);

  // ローディング中の表示
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-[500px] w-full mx-auto bg-white shadow-md p-8 min-h-screen flex flex-col items-center justify-center">
            <CatLoadingAnimation message="Loading..." />
          </div>
        </div>
      </div>
    );
  }

  // ログイン済みの場合はローディング表示
  if (user) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-[500px] w-full mx-auto bg-white shadow-md p-8 min-h-screen flex flex-col items-center justify-center">
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-r from-[#ee7752] via-[#e73c7e] to-[#23a6d5] bg-[length:400%_400%] animate-gradient-move">
      <div className="max-w-[500px] w-full mx-auto flex flex-col min-h-screen bg-white p-14">
        {/* カスタムヘッダー */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            アカウントを作成する
          </h1>
        </div>

        {/* Clerkコンポーネント */}
        <div className="flex-1 flex flex-col">
          <SignUp
            appearance={{
              elements: {
                card: 'bg-transparent shadow-none border-none',
                rootBox: 'w-full',
                headerTitle: 'hidden',
                headerSubtitle: 'hidden',
                footerActionLink: 'hidden',
                footerActionText: 'hidden',
                footer: 'hidden',
                socialButtonsBlock: 'w-full',
                socialButtonsBlockButton: 'w-full h-11 flex items-center justify-start px-3 bg-white border border-gray-300 text-gray-900 hover:bg-gray-50 rounded-full font-medium text-sm transition-colors shadow-none',
                socialButtonsProviderIcon: 'w-5 h-5 mr-2',
                socialButtonsBlockButtonText: 'font-medium text-sm',
              },
              variables: {
                colorPrimary: '#E63B5F',
              },
              layout: {
                socialButtonsPlacement: 'top',
                socialButtonsVariant: 'blockButton',
                showOptionalFields: false,
              },
            }}
            signInUrl="/sign-in"
            forceRedirectUrl="/setup"
          />
        </div>

        {/* アカウントをお持ちの方はこちら */}
        <div className="text-center mt-6">
          <span className="text-gray-600 text-sm">アカウントをお持ちの方は </span>
          <a href="/sign-in" className="text-blue-600 hover:text-blue-800 underline text-sm">
            ログイン
          </a>
        </div>

        {/* フッター */}
        <Footer className="!flex-[0_0_auto] mt-auto w-full" />
      </div>
    </div>
  );
}

