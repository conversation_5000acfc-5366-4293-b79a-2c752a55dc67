import React from 'react';

interface TitleSectionProps {
  title: string;
  setTitle: (title: string) => void;
  validationErrors: {[key: string]: boolean};
  showErrors: boolean;
}

/**
 * タイトル入力セクションコンポーネント
 */
export const TitleSection: React.FC<TitleSectionProps> = ({
  title,
  setTitle,
  validationErrors,
  showErrors
}) => {
  return (
    <div className="px-4 py-3">
      <div className="flex justify-between items-center mb-2">
        <p className="text-[#313131] text-[16px] font-bold">タイトル <span className="text-xs text-red-500">(必須)</span></p>
      </div>
      <div className="bg-white border border-gray-200 relative">
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className={`w-full p-3 border-none outline-none text-sm ${validationErrors.title ? 'border-red-500' : ''}`}
          placeholder="クリックしたくなるワードを入れよう！"
          maxLength={26}
        />
        <div className="absolute bottom-1 right-2 text-xs text-gray-400">
          {title.length}/26
        </div>
      </div>
      {validationErrors.title && showErrors && (
        <p className="text-red-500 text-xs mt-1">タイトルを入力してください</p>
      )}
    </div>
  );
};
