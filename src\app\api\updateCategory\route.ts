import { NextResponse } from 'next/server';
import { PrismaClient, Prisma } from '@prisma/client';
import { convertToSlug } from '@/utils/slugUtils';

let prisma: PrismaClient;
if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  if (!(global as any).prisma) {
    (global as any).prisma = new PrismaClient();
  }
  prisma = (global as any).prisma;
}

// category_IDとcategory_slugを生成する関数
async function generateCategoryId(user_ID: string, parent_ID?: string | null, name?: string, categoryId?: string): Promise<string> {
  // サブカテゴリの場合は既存のロジックを使用
  if (parent_ID) {
    return `subcat_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
  }

  // メインカテゴリでnameが指定されている場合はslugを生成
  if (!parent_ID && name) {


    // 新しいスラッグ変換関数を使用
    let baseSlug = await convertToSlug(name);

    // 重複チェック：同じuser_IDで完全に同じbaseSlugを持つ他のカテゴリがあるかチェック
    const exactDuplicateCategories = await prisma.category.findMany({
      where: {
        user_ID,
        parent_ID: null,
        category_slug: baseSlug, // 完全一致のみチェック
        // 更新の場合は自分自身を除外
        ...(categoryId ? { NOT: { id: categoryId } } : {})
      }
    });

    // 完全に同じスラッグの重複がない場合は純粋なスラッグを返す
    if (exactDuplicateCategories.length === 0) {
      return baseSlug;
    }

    // 重複がある場合の処理
    if (categoryId) {
      // カテゴリIDが提供されている場合（更新時）、ハイブリッド方式を使用
      const idSuffix = categoryId.replace(/-/g, '').slice(-6).toLowerCase();
      const hybridSlug = `${baseSlug}-${idSuffix}`;

      return hybridSlug;
    } else {
      // カテゴリIDが提供されていない場合（新規作成時）は連番方式

      let cnt = 1;
      let suffix = '';
      while (await prisma.category.findFirst({
        where: {
          user_ID,
          parent_ID: null,
          category_slug: baseSlug + suffix
        }
      })) {
        cnt++;
        suffix = `-${cnt}`;
      }

      const finalSlug = baseSlug + suffix;
      return finalSlug;
    }
  }

  // nameが指定されていない場合は従来のtabN形式を使用
  const existingCategories = await prisma.category.findMany({
    where: {
      user_ID,
      parent_ID: null,
      category_ID: {
        startsWith: 'tab'
      }
    },
    select: { category_ID: true }
  });

  // tab1, tab2, ... の形式から番号を抽出して最大値を取得
  let maxTabNumber = 0;
  existingCategories.forEach(cat => {
    const match = cat.category_ID?.match(/^tab(\d+)$/);
    if (match) {
      const num = parseInt(match[1], 10);
      if (num > maxTabNumber) {
        maxTabNumber = num;
      }
    }
  });

  const nextTabNumber = maxTabNumber + 1;
  return `tab${nextTabNumber}`;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { id, category_name, user_ID, parent_ID, order } = body;
    
    // カテゴリ更新リクエストログを削除

    // ログ出力は上に移動しました

    // category_name と user_ID は必須
    if (!category_name || !user_ID) {
      console.error('必須パラメータ不足:', { category_name, user_ID });
      return NextResponse.json(
        { 
          success: false, 
          error: '必須パラメータが不足しています',
          details: {
            category_name: category_name ? '提供済み' : '不足',
            user_ID: user_ID ? '提供済み' : '不足'
          }
        },
        { 
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

    // domain 取得用にユーザー存在チェック
    try {
      const user = await prisma.user.findUnique({ where: { user_ID } });
      if (!user) {
        console.error('ユーザーが見つかりません:', { user_ID });
        return NextResponse.json(
          { 
            success: false, 
            error: 'ユーザーが見つかりません',
            details: { user_ID }
          },
          { 
            status: 404,
            headers: {
              'Access-Control-Allow-Origin': 'http://localhost:3000',
              'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Access-Control-Allow-Credentials': 'true',
            }
          }
        );
      }
    } catch (userError: any) {
      console.error('ユーザー検索エラー:', userError);
      return NextResponse.json(
        { 
          success: false, 
          error: 'ユーザー情報の取得中にエラーが発生しました',
          details: userError instanceof Error ? userError.message : String(userError)
        },
        { 
          status: 500,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

    // id がない場合（undefined）または "new_category_" で始まる場合は新規作成
    if (!id || id.startsWith("new_category_")) {
      try {
        // 同名チェックと付番ロジック
        let baseName = category_name;
        let uniqueName = baseName;
        let count = 1;
        while (true) {
          const dup = await prisma.category.findFirst({
            where: { user_ID, category_name: uniqueName },
          });
          if (!dup) break;
          count++;
          uniqueName = `${baseName}${count}`;
        }

        // order値が指定されている場合はその値を使用、そうでない場合は適切な値を取得
        let orderValue = order !== undefined ? Number(order) : 1;
        
        // order値が指定されていない場合は、最大order値を1加算した値を設定
        if (order === undefined) {
          try {
            // parent_IDがある場合（サブカテゴリ）
            if (parent_ID) {
              const orderResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000"}/api/getMaxOrder?parent_ID=${parent_ID}`);
              if (orderResponse.ok) {
                const orderData = await orderResponse.json();
                if (orderData && orderData.maxOrder) {
                  orderValue = orderData.maxOrder + 1;
                  console.log(`新しいサブカテゴリのorder値: ${orderValue}`);
                }
              }
            } 
            // parent_IDがない場合（カテゴリ）
            else {
              const orderResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000"}/api/getMaxCategoryOrder?user_ID=${user_ID}`);
              if (orderResponse.ok) {
                const orderData = await orderResponse.json();
                if (orderData && orderData.maxOrder) {
                  orderValue = orderData.maxOrder + 1;
                  console.log(`新しいカテゴリのorder値: ${orderValue}`);
                }
              }
            }
          } catch (err) {
            console.error('order値の取得エラー:', err);
            // エラーが発生しても処理を続行するため、デフォルト値を使用
          }
        }
        
        // ユーザーのusernameを取得
        const user = await prisma.user.findUnique({
          where: { user_ID },
          select: { username: true }
        });

        // category_IDとcategory_slugを生成
        const categoryId = await generateCategoryId(user_ID, parent_ID, uniqueName);

        const newCategory = await prisma.category.create({
          data: {
            category_name: uniqueName,
            user_ID,
            parent_ID: parent_ID || null,
            id: `${user_ID}_${Date.now()}`, // 一意のIDを生成
            category_ID: categoryId, // 生成されたcategory_ID
            category_slug: parent_ID ? null : categoryId, // メインカテゴリの場合のみslugを設定
            order: orderValue, // order値を設定
          } as any,
        });

        console.log("カテゴリを作成しました:", newCategory);
        return NextResponse.json(
          { success: true, message: "カテゴリを作成しました", category: newCategory },
          { 
            status: 201,
            headers: {
              'Access-Control-Allow-Origin': 'http://localhost:3000',
              'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Access-Control-Allow-Credentials': 'true',
            }
          }
        );
      } catch (createError: any) {
        console.error('カテゴリ作成エラー:', createError);
        
        // Prisma特有のエラーをより詳細に処理
        if (createError instanceof Prisma.PrismaClientKnownRequestError) {
          // 一意性制約違反のエラー
          if (createError.code === 'P2002') {
            return NextResponse.json(
              { 
                success: false, 
                error: '同じ名前のカテゴリがすでに存在します',
                details: {
                  code: createError.code,
                  fields: createError.meta?.target || '不明'
                }
              },
              { 
                status: 409,
                headers: {
                  'Access-Control-Allow-Origin': 'http://localhost:3000',
                  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
                  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                  'Access-Control-Allow-Credentials': 'true',
                }
              }
            );
          }
          
          // 外部キー制約違反
          if (createError.code === 'P2003') {
            return NextResponse.json(
              { 
                success: false, 
                error: '参照先のデータが存在しません（外部キー制約違反）',
                details: {
                  code: createError.code,
                  fields: createError.meta?.field_name || '不明'
                }
              },
              { 
                status: 400,
                headers: {
                  'Access-Control-Allow-Origin': 'http://localhost:3000',
                  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
                  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                  'Access-Control-Allow-Credentials': 'true',
                }
              }
            );
          }
        }
        
        return NextResponse.json(
          {
            success: false,
            error: 'カテゴリの作成に失敗しました',
            details: createError instanceof Error ? createError.message : String(createError)
          },
          { 
            status: 500,
            headers: {
              'Access-Control-Allow-Origin': 'http://localhost:3000',
              'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Access-Control-Allow-Credentials': 'true',
            }
          }
        );
      }
    }

    // 既存idの場合は更新、もしくは存在しなければ作成
    try {
      const existing = await prisma.category.findFirst({
        where: { user_ID, id },
      });

      if (!existing) {
        try {
          // order値が指定されている場合はその値を使用、そうでない場合は適切な値を取得
          let orderValue = order !== undefined ? Number(order) : 1;
          
          // order値が指定されていない場合は、最大order値を1加算した値を設定
          if (order === undefined) {
            try {
              // parent_IDがある場合（サブカテゴリ）
              if (parent_ID) {
                const orderResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000"}/api/getMaxOrder?parent_ID=${parent_ID}`);
                if (orderResponse.ok) {
                  const orderData = await orderResponse.json();
                  if (orderData && orderData.maxOrder) {
                    orderValue = orderData.maxOrder + 1;
                    console.log(`新しいサブカテゴリのorder値: ${orderValue}`);
                  }
                }
              } 
              // parent_IDがない場合（カテゴリ）
              else {
                const orderResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000"}/api/getMaxCategoryOrder?user_ID=${user_ID}`);
                if (orderResponse.ok) {
                  const orderData = await orderResponse.json();
                  if (orderData && orderData.maxOrder) {
                    orderValue = orderData.maxOrder + 1;
                    console.log(`新しいカテゴリのorder値: ${orderValue}`);
                  }
                }
              }
            } catch (err) {
              console.error('order値の取得エラー:', err);
              // エラーが発生しても処理を続行するため、デフォルト値を使用
            }
          }
          
          // ユーザーのusernameを取得
          const user = await prisma.user.findUnique({
            where: { user_ID },
            select: { username: true }
          });

          // category_IDとcategory_slugを生成
          const categoryId = await generateCategoryId(user_ID, parent_ID, category_name);

          // 指定されたIDでカテゴリを作成
          const created = await prisma.category.create({
            data: {
              id,
              category_name,
              user_ID,
              parent_ID: parent_ID || null,
              category_ID: categoryId, // 生成されたcategory_ID
              category_slug: parent_ID ? null : categoryId, // メインカテゴリの場合のみslugを設定
              order: orderValue, // order値を設定
            } as any,
          });

          console.log("既存カテゴリなし → 新規作成:", created);
          return NextResponse.json(
            { success: true, data: created },
            { 
              status: 201,
              headers: {
                'Access-Control-Allow-Origin': 'http://localhost:3000',
                'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                'Access-Control-Allow-Credentials': 'true',
              }
            }
          );
        } catch (createError: any) {
          console.error('既存IDでの新規作成エラー:', createError);
          
          // Prisma特有のエラーをより詳細に処理
          if (createError instanceof Prisma.PrismaClientKnownRequestError) {
            // 一意性制約違反のエラー
            if (createError.code === 'P2002') {
              return NextResponse.json(
                { 
                  success: false, 
                  error: '同じIDのカテゴリがすでに存在します',
                  details: {
                    code: createError.code,
                    fields: createError.meta?.target || '不明'
                  }
                },
                { 
                  status: 409,
                  headers: {
                    'Access-Control-Allow-Origin': 'http://localhost:3000',
                    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                    'Access-Control-Allow-Credentials': 'true',
                  }
                }
              );
            }
          }
          
          return NextResponse.json(
            {
              success: false,
              error: '指定されたIDでのカテゴリ作成に失敗しました',
              details: createError instanceof Error ? createError.message : String(createError)
            },
            { 
              status: 500,
              headers: {
                'Access-Control-Allow-Origin': 'http://localhost:3000',
                'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                'Access-Control-Allow-Credentials': 'true',
              }
            }
          );
        }
      }

      try {
        // order値が指定されている場合は更新する
        const updateData: any = {
          category_name
          // category_IDは更新時には変更しない（既存の値を保持）
        };

        // order値が指定されている場合は更新データに追加
        if (order !== undefined) {
          updateData.order = Number(order);
        }

        // メインカテゴリ（parent_IDがnull）の場合、category_slugを再生成
        if (!existing.parent_ID) {
          const newSlug = await generateCategoryId(user_ID, null, category_name);
          updateData.category_slug = newSlug;
        }

        const updated = await prisma.category.update({
          where: { id },
          data: updateData,
        });
        console.log("カテゴリ更新成功:", updated);
        return NextResponse.json(
          { success: true, data: updated },
          { 
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': 'http://localhost:3000',
              'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Access-Control-Allow-Credentials': 'true',
            }
          }
        );
      } catch (updateError: any) {
        console.error('カテゴリ更新エラー:', updateError);
        
        // Prisma特有のエラーをより詳細に処理
        if (updateError instanceof Prisma.PrismaClientKnownRequestError) {
          // レコードが見つからない
          if (updateError.code === 'P2025') {
            return NextResponse.json(
              { 
                success: false, 
                error: '更新対象のカテゴリが見つかりません',
                details: {
                  code: updateError.code,
                  id
                }
              },
              { 
                status: 404,
                headers: {
                  'Access-Control-Allow-Origin': 'http://localhost:3000',
                  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
                  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                  'Access-Control-Allow-Credentials': 'true',
                }
              }
            );
          }
        }
        
        return NextResponse.json(
          {
            success: false,
            error: 'カテゴリの更新に失敗しました',
            details: updateError instanceof Error ? updateError.message : String(updateError)
          },
          { 
            status: 500,
            headers: {
              'Access-Control-Allow-Origin': 'http://localhost:3000',
              'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Access-Control-Allow-Credentials': 'true',
            }
          }
        );
      }

    } catch (findError: any) {
      console.error('カテゴリ検索エラー:', findError);
      return NextResponse.json(
        {
          success: false,
          error: 'カテゴリの検索中にエラーが発生しました',
          details: findError instanceof Error ? findError.message : String(findError)
        },
        { 
          status: 500,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

  } catch (error: any) {
    console.error('リクエスト処理エラー:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'カテゴリの処理中に予期しないエラーが発生しました',
        details: error instanceof Error ? error.message : String(error)
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// PUTリクエスト処理を追加
export async function PUT(request: Request) {
  console.log('🔧 [updateCategory] API called at:', new Date().toISOString());
  console.log('🔧 [updateCategory] Request URL:', request.url);
  console.log('🔧 [updateCategory] Request method:', request.method);

  try {
    const body = await request.json();
    console.log('🔧 [updateCategory] Request body:', JSON.stringify(body, null, 2));

    const { id, category_name, user_ID } = body;

    console.log(
      `🔧 [updateCategory] カテゴリ名更新リクエスト: ID=${id}, 名前=${category_name}, ユーザーID=${user_ID}`
    );

    // データベースでカテゴリの存在確認
    console.log('🔧 [updateCategory] データベース確認開始');
    const categoryCheck = await prisma.category.findFirst({
      where: {
        id: id,
        user_ID: user_ID
      }
    });

    console.log('🔧 [updateCategory] 既存カテゴリ確認結果:', {
      found: !!categoryCheck,
      category: categoryCheck,
      searchCriteria: { id, user_ID }
    });

    if (!categoryCheck) {
      // 該当IDのカテゴリが他のユーザーに存在するかチェック
      const categoryWithSameId = await prisma.category.findUnique({
        where: { id: id }
      });

      console.log('🔧 [updateCategory] 同一IDの他ユーザーカテゴリ:', categoryWithSameId);

      // 該当ユーザーの全カテゴリを確認
      const userCategories = await prisma.category.findMany({
        where: { user_ID: user_ID },
        select: { id: true, category_name: true, user_ID: true }
      });

      console.log('🔧 [updateCategory] ユーザーの全カテゴリ:', userCategories);
    }

    // 必須パラメータのバリデーション
    if (!id || !category_name || !user_ID) {
      return NextResponse.json(
        {
          success: false,
          error: '必須パラメータが不足しています',
          details: {
            id: id ? '提供済み' : '不足',
            category_name: category_name ? '提供済み' : '不足',
            user_ID: user_ID ? '提供済み' : '不足'
          }
        },
        {
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'PUT, POST, GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

    // カテゴリの存在確認 - ユーザーIDに関係なく存在確認
    const existingCategory = await prisma.category.findUnique({
      where: {
        id
      }
    });

    if (!existingCategory) {
      return NextResponse.json(
        {
          success: false,
          error: '更新対象のカテゴリが見つかりません',
          details: { id, user_ID }
        },
        {
          status: 404,
          headers: {
            'Access-Control-Allow-Origin': 'http://localhost:3000',
            'Access-Control-Allow-Methods': 'PUT, POST, GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
          }
        }
      );
    }

    // カテゴリの所有者確認
    if (existingCategory.user_ID !== user_ID) {
      console.log('🔧 [updateCategory] ユーザーID不一致:', {
        categoryOwner: existingCategory.user_ID,
        requestUser: user_ID,
        categoryId: id
      });

      // 正しいユーザーIDを使用してカテゴリを更新
      console.log('🔧 [updateCategory] 正しいユーザーIDを使用:', existingCategory.user_ID);
    }

    // 実際の所有者IDを使用
    const actualUserId = existingCategory.user_ID;

    // カテゴリ名を更新（メインカテゴリの場合はslugも更新）
    const updateData: any = { category_name };

    // メインカテゴリ（parent_IDがnull）の場合、category_slugを再生成
    if (!existingCategory.parent_ID) {
      const newSlug = await generateCategoryId(actualUserId, null, category_name, id);

      // history_slugの保存ロジック
      if (existingCategory.category_slug && existingCategory.category_slug !== newSlug) {
        // category_slugが変更される場合、常に現在のslugをhistory_slugに保存
        updateData.history_slug = existingCategory.category_slug;
      }

      updateData.category_slug = newSlug;
    }

    // Prismaクライアントがhistory_slugフィールドを認識しない場合の対応
    let updatedCategory;
    try {
      updatedCategory = await prisma.category.update({
        where: { id },
        data: updateData
      });
    } catch (error) {
      // history_slugフィールドが認識されない場合は、history_slugを除いて再試行
      if (error instanceof Error && error.message.includes('history_slug')) {
        console.warn('⚠️ history_slugフィールドが認識されません。Prismaクライアントの再生成が必要です。');
        const { history_slug, ...updateDataWithoutHistory } = updateData;
        updatedCategory = await prisma.category.update({
          where: { id },
          data: updateDataWithoutHistory
        });

        // 手動でhistory_slugを更新（生のSQL使用）
        if (history_slug) {
          await prisma.$executeRaw`
            UPDATE "Category"
            SET "history_slug" = ${history_slug}
            WHERE "id" = ${id}
          `;
        }
      } else {
        throw error;
      }
    }

    console.log("カテゴリ名を更新しました:", updatedCategory);

    return NextResponse.json(
      {
        success: true,
        message: "カテゴリ名を更新しました",
        category: updatedCategory,
        debug: {
          categoryId: id,
          oldSlug: existingCategory.category_slug,
          newSlug: updatedCategory.category_slug,
          slugChanged: existingCategory.category_slug !== updatedCategory.category_slug,
          timestamp: new Date().toISOString()
        }
      },
      {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'PUT, POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      }
    );
  } catch (error: any) {
    console.error("カテゴリ名更新エラー:", error);
    
    // Prisma特有のエラーをより詳細に処理
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // レコードが見つからない
      if (error.code === 'P2025') {
        return NextResponse.json(
          { 
            success: false, 
            error: '更新対象のカテゴリが見つかりません',
            details: {
              code: error.code,
              message: error.message
            }
          },
          { 
            status: 404,
            headers: {
              'Access-Control-Allow-Origin': 'http://localhost:3000',
              'Access-Control-Allow-Methods': 'PUT, POST, GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Access-Control-Allow-Credentials': 'true',
            }
          }
        );
      }
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: "カテゴリ名の更新中にエラーが発生しました", 
        details: error instanceof Error ? error.message : String(error)
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': 'http://localhost:3000',
          'Access-Control-Allow-Methods': 'PUT, POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
        }
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://mypicks.best',
      'Access-Control-Allow-Methods': 'PUT, POST, GET, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
