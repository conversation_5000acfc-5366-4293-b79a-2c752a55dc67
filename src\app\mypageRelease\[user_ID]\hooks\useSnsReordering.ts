import { useCallback } from "react";
import { DragEndEvent, DragStartEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import type { UserInfo } from "../types";

interface UseSnsReorderingParams {
  user: UserInfo | null;
  userId: string;
  setUser: (user: UserInfo | null) => void;
  setActiveId: (id: string | null) => void;
}

interface UseSnsReorderingReturn {
  saveSnsOrder: (newOrder: string[]) => Promise<void>;
  handleSnsAndSubcategoryDragStart: (e: DragStartEvent) => void;
  handleSnsAndSubcategoryDragEnd: (e: DragEndEvent) => void;
}

export const useSnsReordering = ({
  user,
  userId,
  setUser,
  setActiveId,
}: UseSnsReorderingParams): UseSnsReorderingReturn => {

  /* -------------------------------------------------------------------------- */
  /*                                  SNS順序保存                                 */
  /* -------------------------------------------------------------------------- */
  const saveSnsOrder = useCallback(async (newOrder: string[]) => {
    if (!user || !userId) return;

    try {
      // 順序のみを送信（SNSリンクの内容は送信しない）
      const saveData = {
        user_ID: userId,
        snsOrder: newOrder
      };

      const response = await fetch('/api/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(saveData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`SNS順序の保存に失敗しました: ${response.status}`);
      }

      const responseData = await response.json().catch(() => ({}));
    } catch (error) {
      // SNS順序の保存エラーの処理
      console.error('SNS順序保存エラー:', error);
    }
  }, [user, userId]);

  /* ---------------------- DnD（サブカテゴリ / SNS 並び替え）最適化済み ---------------------- */
  const handleSnsAndSubcategoryDragStart = useCallback((e: DragStartEvent) => {
    requestAnimationFrame(() => {
      setActiveId(e.active.id.toString());
    });
  }, [setActiveId]);

  const handleSnsAndSubcategoryDragEnd = useCallback((e: DragEndEvent) => {
    requestAnimationFrame(() => {
      const { active, over } = e;
      setActiveId(null);
      if (!user || !over || active.id === over.id) return;

      /* SNS DnD */
      // すべてのSNSタイプを処理できるように条件を簡略化
      if (!active.id.toString().startsWith("subcat_")) {
        // 現在のSNS順序を取得
        const order = user.snsOrder?.length
          ? user.snsOrder
          : Object.keys(user.snsLinks);

        // ドラッグ元と先のインデックスを取得
        const oldIndex = order.indexOf(active.id as string);
        const newIndex = order.indexOf(over.id as string);

        if (oldIndex !== -1 && newIndex !== -1) {
          // 新しい順序を計算
          const newOrder = arrayMove(order, oldIndex, newIndex);

          // すべてのSNSリンクが含まれているか確認
          const allSnsKeys = Object.keys(user.snsLinks);
          const missingKeys = allSnsKeys.filter(key => !newOrder.includes(key));

          // 欠落しているSNSリンクがあれば追加
          const completeOrder = [...newOrder];
          if (missingKeys.length > 0) {
            // 欠落しているSNSリンクを確認
            completeOrder.push(...missingKeys);
          }

          // SimpleUserInitializerの更新をブロック
          if (typeof window !== 'undefined' && (window as any).blockSimpleUserInitializerRefresh) {
            (window as any).blockSimpleUserInitializerRefresh();
          }

          // 状態更新を確実に行うために、関数形式で更新
          if (user) {
            const updatedUser = {
              ...user,
              snsOrder: completeOrder,
              // 強制的な再レンダリングのためのタイムスタンプを追加
              _lastSnsUpdate: Date.now()
            };


            // 新しい順序をサーバーに保存（非同期）
            saveSnsOrder(completeOrder);

            // 状態更新
            setUser(updatedUser);

            // 少し遅れてブロックを解除
            setTimeout(() => {
              if (typeof window !== 'undefined' && (window as any).unblockSimpleUserInitializerRefresh) {
                (window as any).unblockSimpleUserInitializerRefresh();
              }
            }, 500);
          }
        }
      }
    });
  }, [user, saveSnsOrder, setUser, setActiveId]);

  return {
    saveSnsOrder,
    handleSnsAndSubcategoryDragStart,
    handleSnsAndSubcategoryDragEnd,
  };
};