'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import './RankingRegisterLoadingAnimation.css';

interface RankingRegisterLoadingAnimationProps {
  isVisible: boolean;
  onComplete?: () => void;
  isEditMode?: boolean;
  isDraftSaving?: boolean;
}

export default function RankingRegisterLoadingAnimation({ isVisible, onComplete, isEditMode = false, isDraftSaving = false }: RankingRegisterLoadingAnimationProps) {
  const [progress, setProgress] = useState(0);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      setShowSuccessMessage(false);
      return;
    }

    // プログレスバーのアニメーション
    const interval = setInterval(() => {
      setProgress((prevProgress) => {
        // 進捗が100%に達したら
        if (prevProgress >= 100) {
          clearInterval(interval);
          setShowSuccessMessage(true);
          
          // 完了後、少し待ってから完了コールバックを呼び出す
          setTimeout(() => {
            if (onComplete) onComplete();
          }, 1000);
          
          return 100;
        }
        
        // 進捗の増加率を調整（最初は速く、後半は遅く）
        const increment = prevProgress < 60 ? 5 : prevProgress < 85 ? 3 : 1;
        return Math.min(prevProgress + increment, 100);
      });
    }, 100);

    return () => {
      clearInterval(interval);
    };
  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="ranking-register-loading-container">
      <div className="ranking-register-loading-content">

        
        <div className="ranking-register-message">
          {showSuccessMessage
            ? (isDraftSaving ? '保存しました' : isEditMode ? '更新しました' : '追加しました')
            : (isDraftSaving ? '保存中...' : isEditMode ? '更新中...' : '追加中...')
          }
        </div>
        
        {!showSuccessMessage && (
          <div className="ranking-register-meter-container">
            <div 
              className="ranking-register-meter-fill" 
              style={{ width: `${progress}%` }}
            ></div>
            <div className="ranking-register-meter-percentage">{progress}%</div>
          </div>
        )}
      </div>
    </div>
  );
}
