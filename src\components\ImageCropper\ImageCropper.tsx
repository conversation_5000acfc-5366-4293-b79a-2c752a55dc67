'use client';

import React, { useState, useCallback } from 'react';
import <PERSON>ropper from 'react-easy-crop';
import { Area } from 'react-easy-crop';
import getCroppedImg from '../../app/imageCrop/cropImage';

interface ImageCropperProps {
  image: string;
  aspectRatio?: number;
  onCropComplete: (croppedImage: string) => void;
  onCancel: () => void;
  isProfileImage?: boolean;
}

/**
 * 画像クロッピングコンポーネント
 * 画像の切り抜き機能を提供します
 */
export const ImageCropper = ({
  image,
  aspectRatio = 1,
  onCropComplete,
  onCancel,
  isProfileImage = false
}: ImageCropperProps): JSX.Element => {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  // クロップエリアが変更されたときの処理
  const handleCropComplete = useCallback((_: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  // クロップを確定する処理
  const handleCropConfirm = async () => {
    try {
      if (!croppedAreaPixels) {
        console.error('ImageCropper - クロップエリアが設定されていません');
        return;
      }
      
      console.log('ImageCropper - クロップ処理開始:', {
        image: (typeof image === 'string' && image.length > 0 ? image.substring(0, 50) : String(image || '').substring(0, 50)) + '...',
        area: croppedAreaPixels,
        rotation
      });
      
      const croppedImage = await getCroppedImg(
        image,
        croppedAreaPixels,
        rotation
      );
      
      // nullチェック
      if (croppedImage === null) {
        console.error('ImageCropper - クロップ画像の生成に失敗しました');
        alert('画像の処理に失敗しました。もう一度お試しください。');
        return;
      }
      
      console.log('ImageCropper - クロップ処理成功:', (typeof croppedImage === 'string' ? croppedImage.substring(0, 50) : String(croppedImage).substring(0, 50)) + '...');
      onCropComplete(croppedImage);
    } catch (error) {
      console.error('ImageCropper - クロップ処理中にエラーが発生しました:', error);
      alert('画像の処理中にエラーが発生しました。もう一度お試しください。');
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
      <div className="relative bg-white rounded-lg w-[90%] max-w-[500px] h-[80vh] max-h-[600px] overflow-hidden flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {isProfileImage ? 'プロフィール画像の編集' : '背景画像の編集'}
          </h3>
        </div>
        
        <div className="relative flex-grow">
          <Cropper
            image={image}
            crop={crop}
            zoom={zoom}
            aspect={aspectRatio}
            rotation={rotation}
            onCropChange={setCrop}
            onCropComplete={handleCropComplete}
            onZoomChange={setZoom}
            onRotationChange={setRotation}
          />
        </div>
        
        <div className="p-4 border-t border-gray-200">
          <div className="mb-4">
            <label htmlFor="zoom" className="block text-sm font-medium text-gray-700 mb-1">
              拡大/縮小
            </label>
            <input
              type="range"
              id="zoom"
              min={1}
              max={3}
              step={0.1}
              value={zoom}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setZoom(Number(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
          </div>

          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              キャンセル
            </button>
            <button
              type="button"
              onClick={handleCropConfirm}
              className="px-4 py-2 text-sm font-medium text-white bg-button-red rounded-md hover:bg-button-red-hover"
            >
              確定
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
