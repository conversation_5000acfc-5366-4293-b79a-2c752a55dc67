const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateRankingUrls() {
  console.log('Starting ranking URL migration...');
  
  try {
    // 既存のランキングデータを取得（ranking_urlがnullでないもの）
    const rankings = await prisma.ranking.findMany({
      where: {
        OR: [
          { amazon_url: null },
          { rakuten_url: null },
          { yahoo_url: null },
          { qoo10_url: null },
          { official_url: null }
        ]
      }
    });

    console.log(`Found ${rankings.length} rankings to potentially migrate`);

    let migratedCount = 0;
    let errorCount = 0;

    for (const ranking of rankings) {
      try {
        // 既存のranking_urlフィールドがあった場合の処理
        // （マイグレーション後はこのフィールドは存在しないため、
        // 実際のデータ移行は手動で行う必要があります）
        
        // デモンストレーション用：空のURLフィールドを初期化
        const updateData = {};
        let hasUpdates = false;

        if (!ranking.amazon_url) {
          updateData.amazon_url = '';
          hasUpdates = true;
        }
        if (!ranking.rakuten_url) {
          updateData.rakuten_url = '';
          hasUpdates = true;
        }
        if (!ranking.yahoo_url) {
          updateData.yahoo_url = '';
          hasUpdates = true;
        }
        if (!ranking.qoo10_url) {
          updateData.qoo10_url = '';
          hasUpdates = true;
        }
        if (!ranking.official_url) {
          updateData.official_url = '';
          hasUpdates = true;
        }

        if (hasUpdates) {
          await prisma.ranking.update({
            where: { id: ranking.id },
            data: updateData
          });
          migratedCount++;
          console.log(`Migrated ranking ${ranking.ranking_ID}`);
        }

      } catch (error) {
        console.error(`Error migrating ranking ${ranking.ranking_ID}:`, error);
        errorCount++;
      }
    }

    console.log(`Migration completed. Migrated: ${migratedCount}, Errors: ${errorCount}`);

  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
migrateRankingUrls();
