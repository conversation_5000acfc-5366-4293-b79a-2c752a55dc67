<!DOCTYPE html>
<html>
<head>
    <title>saveRanking API Test</title>
</head>
<body>
    <h1>saveRanking API Test</h1>
    <button onclick="testSaveRanking()">Test POST Request</button>
    <div id="result"></div>

    <script>
        async function testSaveRanking() {
            const testData = {
                title: "テストランキング",
                user_ID: "user_2zfDuk3M6mp7DkZl27rbEgKJQcD",
                categoryId: "test-category-id",
                images: ["https://example.com/image1.jpg"],
                ranking_description: "テスト用の説明"
            };

            try {
                console.log('POSTリクエスト送信中...');
                const response = await fetch('http://localhost:3000/api/saveRanking', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                console.log('レスポンス受信:', response.status, response.statusText);
                
                if (response.ok) {
                    const result = await response.json();
                    document.getElementById('result').innerHTML = 
                        '<h2>成功!</h2><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                } else {
                    const error = await response.text();
                    document.getElementById('result').innerHTML = 
                        '<h2>エラー: ' + response.status + '</h2><pre>' + error + '</pre>';
                }
            } catch (error) {
                console.error('リクエストエラー:', error);
                document.getElementById('result').innerHTML = 
                    '<h2>リクエストエラー</h2><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
