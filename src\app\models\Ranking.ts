// src/types/prisma.d.ts
import {User} from './User'
export type Ranking = {
    id: string; // Prisma's ObjectId
    ranking_ID: string; // Unique ranking ID
    user_ID: string; // User ID associated with the ranking
    thumbnail_image: string[]; // Array of thumbnail image URLs
    recommend_rate?: number | null; // Optional recommend rate
    ranking_url?: string | null; // Optional URL for the ranking
    ranking_title: string; // Title of the ranking
    ranking_description?: string | null; // Optional description of the ranking
    category_ID?: string | null; // Optional main category
    subCategory_ID?: string | null; // Optional subcategory
    order: number; // Display order for ranking
    RankingUserID: User; // Relation: User who created the ranking
    RankingDomain: User; // Relation: User domain associated with the ranking
    created_at: Date; // Date of creation
    updated_at: Date; // Date of last update
  };
  