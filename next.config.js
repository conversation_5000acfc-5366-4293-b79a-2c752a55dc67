/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  output: 'standalone',
  reactStrictMode: false, // 本番環境でもパフォーマンス最優先
  swcMinify: true,
  // 本番環境での超高速化設定
  poweredByHeader: false,
  generateEtags: false,
  // 本番環境での最適化
  compress: true,
  productionBrowserSourceMaps: false,
  // リダイレクト処理はミドルウェアで実装するため削除

  images: {
    domains: ['storage.cloud.google.com', 'c.animaapp.com', 'localhost'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'http',
        hostname: '127.0.0.1',
      }
    ],
    unoptimized: false, // 本番環境でも最適化を有効化
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // LCP最適化のための設定
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  
  // エイリアス設定を追加
  webpack(config, { isServer }) {
    config.resolve.alias['@'] = path.resolve(__dirname, 'src');

    // Clerkのモジュール解決問題を修正
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };
    }

    // exports定義の問題を修正
    config.module.rules.push({
      test: /\.m?js$/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    // CommonJS互換性の修正
    config.module.rules.push({
      test: /node_modules\/@clerk/,
      type: 'javascript/auto',
    });

    return config;
  },

  // 開発環境の超高速化設定
  ...(process.env.NODE_ENV === 'development' && {
    // オンデマンドエントリを最適化
    onDemandEntries: {
      maxInactiveAge: 60 * 1000,
      pagesBufferLength: 5,
    },
    // 開発サーバーの最適化
    compiler: {
      removeConsole: false,
    },
    // 全ての最適化を無効化（開発環境）
    optimizeFonts: false,
    // TypeScriptチェックを無効化（高速化）
    typescript: {
      ignoreBuildErrors: true,
    },
    // ESLintチェックを無効化（高速化）
    eslint: {
      ignoreDuringBuilds: true,
    },
  }),

  // App Routerの最適化を実験的機能に統合
  experimental: {
    esmExternals: false,
    instrumentationHook: false,
    // ページ遷移の高速化（本番環境対応）
    optimizePackageImports: ['@clerk/nextjs'],
    // プリロードの最適化
    serverComponentsExternalPackages: [],
    // 本番環境での追加最適化
    ...(process.env.NODE_ENV === 'production' && {
      // 静的最適化
      staticPageGenerationTimeout: 60,
      // キャッシュ最適化
      workerThreads: false,
    }),
  },

  // 本番環境での追加最適化
  ...(process.env.NODE_ENV === 'production' && {
    // 本番環境での最適化設定
    compiler: {
      removeConsole: false, // 本番環境でもログを保持（デバッグ用）
    },
    // 静的最適化
    trailingSlash: false,
    // キャッシュ最適化
    onDemandEntries: {
      maxInactiveAge: 25 * 1000,
      pagesBufferLength: 2,
    },
    // 本番環境での高速化
    optimizeFonts: true,
    optimizeImages: true,
  }),
};

module.exports = nextConfig;
