# mypicks.best 機能概要

## 主要機能

mypicks.bestアプリケーションは以下の主要機能を提供しています：

1. [ユーザー認証と管理](#ユーザー認証と管理)
2. [ランキング作成と管理](#ランキング作成と管理)
3. [カテゴリとサブカテゴリ管理](#カテゴリとサブカテゴリ管理)
4. [ランキング保存機能](#ランキング保存機能)
5. [下書き保存機能](#下書き保存機能)
6. [SNSリンク管理](#snsリンク管理)
7. [ユーザープロフィール管理](#ユーザープロフィール管理)
8. [ランキング共有機能](#ランキング共有機能)

各機能の詳細は、対応するドキュメントを参照してください。

## ユーザー認証と管理

ユーザー認証はClerkを使用して実装されています。ユーザーIDはCookieとローカルストレージの両方に保存され、サーバーサイドとクライアントサイドの両方でアクセスできます。

詳細は[ユーザー認証](./user-authentication.md)を参照してください。

## ランキング作成と管理

ユーザーは様々なカテゴリやサブカテゴリでランキングを作成、編集、削除できます。ランキングにはタイトル、説明、サムネイル画像、ランキングアイテムなどの情報を含めることができます。

詳細は[ランキング管理](./ranking-management.md)を参照してください。

## カテゴリとサブカテゴリ管理

ユーザーはカテゴリとサブカテゴリを作成、編集、削除できます。カテゴリは親子関係を持ち、サブカテゴリは親カテゴリに属します。

詳細は[カテゴリ管理](./category-management.md)を参照してください。

## ランキング保存機能

ユーザーは他のユーザーが作成したランキングを保存（ブックマーク）できます。保存したランキングは専用のページで一覧表示され、後で参照することができます。

詳細は[ランキング保存](./ranking-saving.md)を参照してください。

## 下書き保存機能

ユーザーはランキング作成途中で下書きとして保存し、後で編集を再開することができます。下書きは専用のページで一覧表示され、管理することができます。

詳細は[下書き管理](./draft-management.md)を参照してください。

## SNSリンク管理

ユーザーは自分のSNSアカウントへのリンクを設定できます。サポートされているSNSには、Instagram、TikTok、X、YouTube、Facebook、LINE、Amazon、note、Rakuten、WEAR、Blog、BIGO LIVE、17Live、TwitCastingなどがあります。

詳細は[SNS管理](./sns-management.md)を参照してください。

## ユーザープロフィール管理

ユーザーはプロフィール情報（ユーザー名、ドメイン、プロフィールテキスト、プロフィール画像など）を設定・編集できます。

詳細は[プロフィール管理](./profile-management.md)を参照してください。

## ランキング共有機能

ユーザーは作成したランキングをSNSなどで共有できます。Web Share APIを使用して、様々なプラットフォームでの共有をサポートしています。

詳細は[ランキング共有](./ranking-sharing.md)を参照してください。

## 関連ドキュメント

- [API仕様](../api/README.md)
- [コンポーネント](../components/README.md)
- [データベース設計](../database/README.md)

## 最終更新日

2025年4月25日
