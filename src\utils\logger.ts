/**
 * Production-ready logging utility for OAuth authentication
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  component?: string;
}

class Logger {
  private isDevelopment: boolean;
  private enablePersistence: boolean;
  private maxStoredLogs: number;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.enablePersistence = this.isDevelopment; // Only persist logs in development
    this.maxStoredLogs = 50;
  }

  private createLogEntry(level: LogLevel, message: string, data?: any, component?: string): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      component
    };
  }

  private persistLog(entry: LogEntry): void {
    if (!this.enablePersistence || typeof window === 'undefined') return;

    try {
      const existingLogs = JSON.parse(localStorage.getItem('oauth_debug_logs') || '[]');
      existingLogs.push(entry);

      // Keep only the most recent logs
      if (existingLogs.length > this.maxStoredLogs) {
        existingLogs.splice(0, existingLogs.length - this.maxStoredLogs);
      }

      localStorage.setItem('oauth_debug_logs', JSON.stringify(existingLogs));
    } catch (error) {
      console.error('Failed to persist log:', error);
    }
  }

  private shouldLog(level: LogLevel): boolean {
    if (this.isDevelopment) return true;
    
    // In production, only log warnings and errors
    return level === 'warn' || level === 'error';
  }

  debug(message: string, data?: any, component?: string): void {
    if (!this.shouldLog('debug')) return;

    const entry = this.createLogEntry('debug', message, data, component);
    console.log(`🔍 [${component || 'Debug'}] ${message}`, data || '');
    this.persistLog(entry);
  }

  info(message: string, data?: any, component?: string): void {
    if (!this.shouldLog('info')) return;

    const entry = this.createLogEntry('info', message, data, component);
    console.log(`✅ [${component || 'Info'}] ${message}`, data || '');
    this.persistLog(entry);
  }

  warn(message: string, data?: any, component?: string): void {
    if (!this.shouldLog('warn')) return;

    const entry = this.createLogEntry('warn', message, data, component);
    console.warn(`⚠️ [${component || 'Warning'}] ${message}`, data || '');
    this.persistLog(entry);
  }

  error(message: string, data?: any, component?: string): void {
    if (!this.shouldLog('error')) return;

    const entry = this.createLogEntry('error', message, data, component);
    console.error(`❌ [${component || 'Error'}] ${message}`, data || '');
    this.persistLog(entry);
  }

  // OAuth-specific logging methods
  oauthStart(data?: any): void {
    this.info('OAuth認証開始', data, 'OAuth');
  }

  oauthSuccess(data?: any): void {
    this.info('OAuth認証成功', data, 'OAuth');
  }

  oauthError(message: string, data?: any): void {
    this.error(`OAuth認証エラー: ${message}`, data, 'OAuth');
  }

  supabaseSuccess(data?: any): void {
    this.info('Supabaseユーザー登録成功', data, 'Supabase');
  }

  supabaseError(message: string, data?: any): void {
    this.error(`Supabaseエラー: ${message}`, data, 'Supabase');
  }

  redirectStart(destination: string, data?: any): void {
    this.info(`リダイレクト開始: ${destination}`, data, 'Redirect');
  }

  // Utility methods
  clearLogs(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('oauth_debug_logs');
    }
  }

  getLogs(): LogEntry[] {
    if (typeof window === 'undefined') return [];
    
    try {
      return JSON.parse(localStorage.getItem('oauth_debug_logs') || '[]');
    } catch {
      return [];
    }
  }

  exportLogs(): string {
    const logs = this.getLogs();
    return JSON.stringify(logs, null, 2);
  }
}

// Export singleton instance
export const logger = new Logger();

// Legacy compatibility function for existing code
export function persistentLog(message: string, data?: any): void {
  logger.debug(message, data);
}
