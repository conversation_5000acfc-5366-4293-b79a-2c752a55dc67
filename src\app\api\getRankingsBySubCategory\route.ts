import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // URLパラメータからユーザーIDとサブカテゴリIDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');
    const subCategory_ID = searchParams.get('subCategory_ID');

    // ユーザーIDは必須
    if (!user_ID) {
      return NextResponse.json(
        { error: 'ユーザーIDは必須です' },
        { status: 400 }
      );
    }

    // サブカテゴリIDが指定されている場合は特定のサブカテゴリの公開済みランキングを取得
    // 指定されていない場合はユーザーの全ての公開済みランキングを取得
    const whereClause = subCategory_ID
      ? {
          user_ID: user_ID,
          subCategory_ID: subCategory_ID,
          status: 'PUBLISHED' // 公開済みのみ取得
        }
      : {
          user_ID: user_ID,
          status: 'PUBLISHED' // 公開済みのみ取得
        };

    // ランキングデータを取得
    const rankings = await prisma.ranking.findMany({
      where: whereClause,
      select: {
        id: true,
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        thumbnail_image: true,
        recommend_rate: true,
        subCategory_ID: true,
        order: true,
        status: true, // ステータスも取得
        created_at: true,
        updated_at: true
      },
      orderBy: {
        order: 'asc'
      }
    });

    return NextResponse.json(rankings);
  } catch (error) {
    console.error('ランキング取得エラー:', error);
    return NextResponse.json(
      { error: 'ランキングの取得に失敗しました' },
      { status: 500 }
    );
  }
}
