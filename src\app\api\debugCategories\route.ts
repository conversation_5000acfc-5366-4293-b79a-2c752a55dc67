import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * カテゴリデバッグ用APIエンドポイント
 * 指定されたユーザーのカテゴリ情報を詳細に取得
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');
    const searchSlug = searchParams.get('slug');

    if (!user_ID) {
      return NextResponse.json({
        error: 'user_IDが必要です'
      }, { status: 400 });
    }

    console.log('🔍 [debugCategories] デバッグ開始:', { user_ID, searchSlug });

    // 生のSQLでカテゴリ情報を取得
    const rawCategories = await prisma.$queryRaw`
      SELECT 
        id,
        "user_ID",
        "category_ID",
        category_name,
        category_slug,
        history_slug,
        "parent_ID",
        "order",
        created_at,
        updated_at
      FROM "Category"
      WHERE "user_ID" = ${user_ID}
      AND ("parent_ID" IS NULL OR "parent_ID" = '' OR "parent_ID" = id OR "parent_ID" = "category_ID")
      ORDER BY "order" ASC, created_at ASC
    `;

    console.log('🔍 [debugCategories] 生SQL結果:', rawCategories);

    // Prisma経由でも取得
    const prismaCategories = await prisma.category.findMany({
      where: {
        user_ID: user_ID,
        OR: [
          { parent_ID: null },
          { parent_ID: '' },
          { parent_ID: { equals: prisma.category.fields.id } },
          { parent_ID: { equals: prisma.category.fields.category_ID } }
        ]
      },
      select: {
        id: true,
        user_ID: true,
        category_ID: true,
        category_name: true,
        category_slug: true,
        history_slug: true,
        parent_ID: true,
        order: true,
        created_at: true,
        updated_at: true
      },
      orderBy: [
        { order: 'asc' },
        { created_at: 'asc' }
      ]
    });

    console.log('🔍 [debugCategories] Prisma結果:', prismaCategories);

    // 検索対象のslugがある場合の詳細分析
    let searchResults = null;
    if (searchSlug) {
      searchResults = {
        searchSlug,
        foundByCurrentSlug: prismaCategories.find(cat => cat.category_slug === searchSlug),
        foundByHistorySlug: prismaCategories.find(cat => cat.history_slug === searchSlug),
        foundByCategoryID: prismaCategories.find(cat => cat.category_ID === searchSlug),
        allSlugs: prismaCategories.map(cat => ({
          name: cat.category_name,
          current_slug: cat.category_slug,
          history_slug: cat.history_slug,
          category_ID: cat.category_ID
        }))
      };
    }

    return NextResponse.json({
      success: true,
      user_ID,
      searchSlug,
      totalCategories: prismaCategories.length,
      rawSqlCount: Array.isArray(rawCategories) ? rawCategories.length : 0,
      categories: prismaCategories,
      rawCategories: rawCategories,
      searchResults,
      debug: {
        timestamp: new Date().toISOString(),
        prismaVersion: 'latest',
        databaseConnection: 'active'
      }
    });

  } catch (error) {
    console.error('🚨 [debugCategories] エラー:', error);
    return NextResponse.json({
      error: 'デバッグ処理中にエラーが発生しました',
      details: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
