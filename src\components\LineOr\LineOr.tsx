/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
}

export const LineOr = ({ className }: Props): JSX.Element => {
  return (
    <div className={`w-[390px] h-[56px] ${className}`}>
      <div className="relative w-[330px] h-[30px] top-[13px] left-[30px]">
        <img className="absolute w-[330px] h-px top-[14px] left-0 object-cover" alt="Line" src="/static/img/black-line-for-lineor.svg" />
        <div className="flex w-[68px] h-[30px] items-center justify-center gap-[6px] px-[16px] py-[8px] absolute top-0 left-[131px]">
          <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-black text-[12px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
            または
          </div>
        </div>
      </div>
    </div>
  );
};
