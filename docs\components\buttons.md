# ボタン系コンポーネント

## 概要

mypicks.bestアプリケーションで使用されるボタン系コンポーネントの詳細説明です。これらのコンポーネントは、ユーザーインターフェースの操作性を向上させるために設計されています。

## <a id="buttonmaincategory"></a>ButtonMainCategory

カテゴリ選択用のボタンコンポーネントです。

### 機能

- カテゴリ一覧の表示
- カテゴリの選択
- 新しいカテゴリの追加
- カテゴリ名の編集

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| categories | Category[] | はい | 表示するカテゴリの配列 |
| selectedCategory | string | はい | 現在選択されているカテゴリのID |
| onCategorySelect | (categoryId: string) => void | はい | カテゴリが選択されたときのコールバック関数 |
| onCategoryAdd | (categoryName: string) => void | いいえ | 新しいカテゴリが追加されたときのコールバック関数 |
| onCategoryEdit | (categoryId: string, newName: string) => void | いいえ | カテゴリ名が編集されたときのコールバック関数 |

### 実装の注意点

- カテゴリ名が空または"0"のカテゴリを除外するフィルタリングロジックが実装されています
- 新しく追加されたカテゴリ（category_IDが"new_category_"で始まる）は常に表示されます
- カテゴリ追加機能では、Enterキーで編集を確定できます
- 編集モードはBlurイベントで解除されます（他の場所をタップすると編集モードが終了します）
- 「＋」ボタンをクリックすると新しいカテゴリが追加され、すぐに編集モードになります

### 使用例

```jsx
import { ButtonMainCategory } from '@/components/ButtonMainCategory';

const MyComponent = () => {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');

  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId);
  };

  const handleCategoryAdd = (categoryName) => {
    // カテゴリ追加の処理
  };

  const handleCategoryEdit = (categoryId, newName) => {
    // カテゴリ名編集の処理
  };

  return (
    <ButtonMainCategory
      categories={categories}
      selectedCategory={selectedCategory}
      onCategorySelect={handleCategorySelect}
      onCategoryAdd={handleCategoryAdd}
      onCategoryEdit={handleCategoryEdit}
    />
  );
};
```

## <a id="buttonsubcategory"></a>ButtonSubCategory

サブカテゴリ選択用のボタンコンポーネントです。

### 機能

- サブカテゴリ一覧の表示
- サブカテゴリの選択
- スクロール機能（選択されたサブカテゴリが左端に表示される）
- Intersection Observerを使用した自動選択

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| subCategories | SubCategory[] | はい | 表示するサブカテゴリの配列 |
| selectedSubCategory | string | はい | 現在選択されているサブカテゴリのID |
| onSubCategorySelect | (subCategoryId: string) => void | はい | サブカテゴリが選択されたときのコールバック関数 |

### 実装の注意点

- 未分類サブカテゴリが重複表示される問題を解決するため、「未分類」という名前のサブカテゴリをスキップする条件が追加されています
- 初期表示時のサブカテゴリボタン選択状態は、最初のサブカテゴリボタンが選択された状態になるように初期値が設定されています
- 選択中のサブカテゴリボタンの背景色は #E63B5F（テキストは白）、未選択のボタンの背景色は #AAAAAA（テキストは白）です
- サブカテゴリボタンはアンカーリンクとして機能し、対応する見出しまで縦スクロールで遷移します
- 縦スクロールで該当のh2見出しに達したとき、対応するボタンの色が変わり、ナビゲーションの一番左に横スクロールされます

### 使用例

```jsx
import { ButtonSubCategory } from '@/components/ButtonSubCategory';

const MyComponent = () => {
  const [subCategories, setSubCategories] = useState([]);
  const [selectedSubCategory, setSelectedSubCategory] = useState('');

  const handleSubCategorySelect = (subCategoryId) => {
    setSelectedSubCategory(subCategoryId);
  };

  return (
    <ButtonSubCategory
      subCategories={subCategories}
      selectedSubCategory={selectedSubCategory}
      onSubCategorySelect={handleSubCategorySelect}
    />
  );
};
```

## <a id="buttonfooter"></a>ButtonFooter

フッターに配置する標準的なボタンコンポーネントです。

### 機能

- クリック可能なボタン
- カスタマイズ可能なスタイル
- 子要素を含めることが可能

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| onClick | () => void | はい | ボタンがクリックされたときのコールバック関数 |
| className | string | いいえ | 追加のCSSクラス |
| children | ReactNode | いいえ | ボタン内に表示する子要素 |

### 使用例

```jsx
import { ButtonFooter } from '@/components/ButtonFooter';

const MyComponent = () => {
  const handleClick = () => {
    // クリック時の処理
  };

  return (
    <ButtonFooter onClick={handleClick} className="bg-yellow-400">
      次へ進む
    </ButtonFooter>
  );
};
```

## <a id="buttongray"></a>ButtonGray

グレーのボタンコンポーネントです。

### 機能

- クリック可能なグレーのボタン
- カスタマイズ可能なスタイル
- 子要素を含めることが可能

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| onClick | () => void | はい | ボタンがクリックされたときのコールバック関数 |
| className | string | いいえ | 追加のCSSクラス |
| children | ReactNode | いいえ | ボタン内に表示する子要素 |

### 使用例

```jsx
import { ButtonGray } from '@/components/ButtonGray';

const MyComponent = () => {
  const handleClick = () => {
    // クリック時の処理
  };

  return (
    <ButtonGray onClick={handleClick}>
      キャンセル
    </ButtonGray>
  );
};
```

## <a id="buttontogeledit"></a>ButtonToggelEdit

編集モードを切り替えるボタンコンポーネントです。

### 機能

- 編集モードの切り替え
- 表示モードと編集モードの切り替え表示

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| isEditing | boolean | はい | 現在編集モードかどうか |
| onToggle | () => void | はい | 編集モードが切り替えられたときのコールバック関数 |
| className | string | いいえ | 追加のCSSクラス |

### 使用例

```jsx
import { ButtonToggelEdit } from '@/components/ButtonToggelEdit';

const MyComponent = () => {
  const [isEditing, setIsEditing] = useState(false);

  const handleToggle = () => {
    setIsEditing(!isEditing);
  };

  return (
    <ButtonToggelEdit
      isEditing={isEditing}
      onToggle={handleToggle}
    />
  );
};
```

## <a id="mypagereleasebuttonfooter"></a>MypageReleaseButtonFooter

マイページリリース画面のフッターボタンコンポーネントです。

### 機能

- ランキング追加ボタン
- ブックマークアイコン（ランキング保存機能）
- 共有ボタン

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| categoryName | string | はい | 選択されているカテゴリ名 |
| onAddRanking | () => void | はい | ランキング追加ボタンがクリックされたときのコールバック関数 |
| onShare | () => void | はい | 共有ボタンがクリックされたときのコールバック関数 |
| rankingId | string | いいえ | 現在表示しているランキングのID |

### 実装の注意点

- ブックマークアイコンをクリックすると、ランキングデータがローカルストレージに保存されます
- 既に保存されているランキングの場合は、再度クリックすると削除されます
- ブックマークアイコンの表示状態が保存状態に応じて変化します

### 使用例

```jsx
import { MypageReleaseButtonFooter } from '@/components/MypageReleaseButtonFooter';

const MyComponent = () => {
  const handleAddRanking = () => {
    // ランキング追加の処理
  };

  const handleShare = () => {
    // 共有の処理
  };

  return (
    <MypageReleaseButtonFooter
      categoryName="グルメ"
      onAddRanking={handleAddRanking}
      onShare={handleShare}
      rankingId="ranking123"
    />
  );
};
```

## <a id="rankingregisterbuttonfooter"></a>RankingRegisterButtonFooter

ランキング登録画面のフッターボタンコンポーネントです。

### 機能

- 下書き保存ボタン
- 下書き一覧ボタン
- 登録ボタン

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| onSaveDraft | () => void | はい | 下書き保存ボタンがクリックされたときのコールバック関数 |
| onDraftList | () => void | はい | 下書き一覧ボタンがクリックされたときのコールバック関数 |
| onRegister | () => void | はい | 登録ボタンがクリックされたときのコールバック関数 |

### 実装の注意点

- 下書き保存ボタンをクリックすると、現在の入力内容（タイトル、説明、画像など）がローカルストレージに保存されます
- 下書き一覧ボタンをクリックすると、下書き一覧ページに遷移します

### 使用例

```jsx
import { RankingRegisterButtonFooter } from '@/components/RankingRegisterButtonFooter';

const MyComponent = () => {
  const handleSaveDraft = () => {
    // 下書き保存の処理
  };

  const handleDraftList = () => {
    // 下書き一覧ページへの遷移
  };

  const handleRegister = () => {
    // 登録の処理
  };

  return (
    <RankingRegisterButtonFooter
      onSaveDraft={handleSaveDraft}
      onDraftList={handleDraftList}
      onRegister={handleRegister}
    />
  );
};
```

## <a id="redbuttonfooter"></a>RedButtonFooter

赤色のフッターボタンコンポーネントです。主に削除や危険な操作に使用されます。

### 機能

- クリック可能な赤色のボタン
- カスタマイズ可能なスタイル
- 子要素を含めることが可能

### プロパティ

| プロパティ名 | 型 | 必須 | 説明 |
|--------------|-----|------|------|
| onClick | () => void | はい | ボタンがクリックされたときのコールバック関数 |
| className | string | いいえ | 追加のCSSクラス |
| children | ReactNode | いいえ | ボタン内に表示する子要素 |

### 使用例

```jsx
import { RedButtonFooter } from '@/components/RedButtonFooter';

const MyComponent = () => {
  const handleDelete = () => {
    // 削除処理
  };

  return (
    <RedButtonFooter onClick={handleDelete}>
      削除する
    </RedButtonFooter>
  );
};
```

## 最終更新日

2025年4月25日
