/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";
import Link from 'next/link';

interface RankingRegisterHeaderItemsProps {
  img: any;
  userID: string;
  title: string;
}

export const RankingRegisterHeaderItems = ({
  img = "https://c.animaapp.com/GmJTluXx/img/------.png",
  userID,
  title,
}: RankingRegisterHeaderItemsProps): JSX.Element => {
  return (
    <div className="flex w-full h-[52px] items-center relative shadow-[0px_0px_4px_#00000040] bg-white">
      <div className="flex w-[50%] h-[52px] items-center gap-2 pl-4 pr-0 py-0 relative">
        <Link href={`/mypageEdit/${userID}`}>
          <img
            className="w-8 h-8 object-cover cursor-pointer"
            alt="Image left arrow"
            src="https://c.animaapp.com/GmJTluXx/img/<EMAIL>"
          />
        </Link>
        <div className="relative w-fit font-normal text-[#313131] text-lg text-center tracking-[0] leading-[21.6px] whitespace-nowrap">
          {title} 
        </div>
      </div>
      <div className="flex w-[50%] h-[52px] items-center justify-end pl-0 pr-4 py-0 relative">
        <div className="inline-flex h-[30px] items-center justify-center px-4 py-2.5 relative flex-[0_0_auto] bg-[rgba(246,247,248,1)] rounded-[100px] border border-solid border-[#DDDDDD]">
          <img className="relative flex-[0_0_auto] mt-[-1.10px] mb-[-0.02px]" alt="Img" src={img} />
        </div>
      </div>
    </div>
  );
};
