import { useEffect, useState } from "react";
import { ReadonlyURLSearchParams } from "next/navigation";
import { updateUrlWithCategory } from "../utils/urlUtils";
import type { CategoryType, RankingType } from "../types";

interface UseUrlInitializationParams {
  categories: CategoryType[];
  searchParams: ReadonlyURLSearchParams;
  initializedFromUrl: boolean;
  userId?: string;
  pageUserId: string;
  updateCategoryID: (id: string) => void;
  updateSelectedCategoryName: (name: string) => void;
  updateSelectedCategoryIndex: (index: number) => void;
  updateSubCategories: (subcats: CategoryType[]) => void;
  updateSubcategoryOrder: (order: string[]) => void;
  updateSubCategory: (id: string) => void;
  setRankingsBySubcategory: (rankings: Record<string, RankingType[]> | ((prev: Record<string, RankingType[]>) => Record<string, RankingType[]>)) => void;
  setIgnoreNextCategorySelect: (ignore: boolean) => void;
  setInitializedFromUrl: (initialized: boolean) => void;
}

interface UseUrlInitializationReturn {
  isInitializing: boolean;
  initializationError: string | null;
}

export const useUrlInitialization = ({
  categories,
  searchParams,
  initializedFromUrl,
  userId,
  pageUserId,
  updateCategoryID,
  updateSelectedCategoryName,
  updateSelectedCategoryIndex,
  updateSubCategories,
  updateSubcategoryOrder,
  updateSubCategory,
  setRankingsBySubcategory,
  setIgnoreNextCategorySelect,
  setInitializedFromUrl,
}: UseUrlInitializationParams): UseUrlInitializationReturn => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);

  // ページ初期化時のカテゴリ設定
  useEffect(() => {
    console.log('🔄 useUrlInitialization useEffect 実行:', {
      initializedFromUrl,
      categoriesLength: categories.length,
      categories: categories.map(c => ({ id: c.id, name: c.category_name })),
      userId,
      pageUserId
    });

    // 既に初期化済みなら何もしない
    if (initializedFromUrl || categories.length === 0) {
      console.log('🚫 useUrlInitialization 早期リターン:', { initializedFromUrl, categoriesLength: categories.length });
      return;
    }

    // 初期化処理を開始
    setIsInitializing(true);
    setInitializationError(null);
    
    // 初期化処理を開始する前にフラグを設定
    setInitializedFromUrl(true);

    // useSearchParamsを使用してURLパラメータを取得
    const categoryParam = searchParams.get("category");
    const categoryIndexParam = searchParams.get("categoryIndex");
    const subCategoryParam = searchParams.get("subcategory");

    // カテゴリインデックスが指定されていれば使用、そうでなければカテゴリIDから探す
    let selectedCategoryIndex = -1;
    if (categoryIndexParam) {
      selectedCategoryIndex = parseInt(categoryIndexParam, 10);
      // インデックスが範囲外ならリセット
      if (selectedCategoryIndex < 0 || selectedCategoryIndex >= categories.length) {
        selectedCategoryIndex = -1;
      }
    }
    
    // インデックスが無効で、カテゴリIDが指定されていれば、IDからインデックスを探す
    if (selectedCategoryIndex === -1 && categoryParam && categories.length > 0) {
      const normalizedCategoryParam = String(categoryParam);
      selectedCategoryIndex = categories.findIndex((c) => String(c.id) === normalizedCategoryParam);
    }

    // 有効なカテゴリが見つからない場合は最初のカテゴリを使用
    if (selectedCategoryIndex === -1 && categories.length > 0) {
      selectedCategoryIndex = 0;
    }

    // 選択されたカテゴリを取得
    const selectedCat = categories[selectedCategoryIndex];
    
    // カテゴリ状態を更新
    updateCategoryID(selectedCat.id);
    updateSelectedCategoryName(selectedCat.category_name);
    updateSelectedCategoryIndex(selectedCategoryIndex);
    
    // サブカテゴリを設定
    const subcats = (selectedCat.subcategories || []).sort((a: any, b: any) => (a.order || 0) - (b.order || 0));
    updateSubCategories(subcats);
    updateSubcategoryOrder(subcats.map((s) => s.id));
    
    // サブカテゴリが指定されていればそれを使用、なければ最初のサブカテゴリを選択
    let selectedSubcategoryId = "";
    if (subCategoryParam && subcats.some((s) => s.id === subCategoryParam)) {
      selectedSubcategoryId = subCategoryParam;
      updateSubCategory(selectedSubcategoryId);
    } else if (subcats.length > 0) {
      selectedSubcategoryId = subcats[0].id;
      updateSubCategory(selectedSubcategoryId);
    } else {
      updateSubCategory("");
      setIsInitializing(false);
      return;
    }
    
    // 選択されたサブカテゴリのランキングデータを取得
    const requestUserId = userId || pageUserId;
    
    if (!requestUserId) {
      setInitializationError('ユーザーIDが取得できません');
      setIsInitializing(false);
      return;
    }
    
    // すべてのサブカテゴリのランキングデータを取得する関数
    const fetchAllSubcategoryRankings = async () => {
      try {
        // まず、すべてのランキングデータを取得
        const allRankingsUrl = `/api/getRanks?user_ID=${encodeURIComponent(requestUserId)}`;
        
        const allRankingsResponse = await fetch(allRankingsUrl);
        const allRankingsText = await allRankingsResponse.text();
        
        let allRankingsData;
        try {
          allRankingsData = allRankingsText.trim() ? JSON.parse(allRankingsText) : [];
        } catch (parseError) {
          allRankingsData = [];
        }
        
        // レスポンス形式に応じてデータを取得
        const allRankings = Array.isArray(allRankingsData) 
          ? allRankingsData 
          : allRankingsData.ranks && Array.isArray(allRankingsData.ranks) 
            ? allRankingsData.ranks 
            : [];
        
        // サブカテゴリごとにランキングを振り分ける
        const newRankingsBySubcategory: Record<string, RankingType[]> = {};
        
        if (subcats && subcats.length > 0) {
          subcats.forEach(subcat => {
            // このサブカテゴリに属するランキングをフィルタリング
            const rankingsForSubcat = allRankings.filter(ranking => 
              ranking.subCategory_ID === subcat.id || ranking.subcategory_id === subcat.id
            );
            
            // order値の昇順でソート（小さい値が上に表示）
            const sortedRankings = [...rankingsForSubcat].sort((a, b) => {
              const orderA = a.order !== undefined ? a.order : 999999;
              const orderB = b.order !== undefined ? b.order : 999999;
              return orderA - orderB;
            });
            
            newRankingsBySubcategory[subcat.id] = sortedRankings;
          });
        }
        
        // ランキングデータを設定
        console.log('🔄 useUrlInitialization: ランキングデータを設定', {
          newRankingsBySubcategory,
          keys: Object.keys(newRankingsBySubcategory),
          setRankingsBySubcategoryType: typeof setRankingsBySubcategory
        });
        setRankingsBySubcategory(newRankingsBySubcategory);
        console.log('✅ useUrlInitialization: setRankingsBySubcategory 実行完了');
        
        // 選択されたサブカテゴリのランキングがない場合、個別に取得する
        if (!newRankingsBySubcategory[selectedSubcategoryId] || newRankingsBySubcategory[selectedSubcategoryId].length === 0) {
          const specificUrl = `/api/getRankBySubcategory?user_ID=${encodeURIComponent(requestUserId)}&subCategoryID=${encodeURIComponent(selectedSubcategoryId)}`;
          
          const specificResponse = await fetch(specificUrl);
          const specificText = await specificResponse.text();
          
          let specificData;
          try {
            specificData = specificText.trim() ? JSON.parse(specificText) : {};
          } catch (parseError) {
            specificData = {};
          }
          
          const specificRankings = Array.isArray(specificData) ? specificData : Array.isArray(specificData.ranks) ? specificData.ranks : [];
          
          if (specificRankings.length > 0) {
            // order値の昇順でソート（小さい値が上に表示）
            const sortedRankings = [...specificRankings].sort((a, b) => {
              const orderA = a.order !== undefined ? a.order : 999999;
              const orderB = b.order !== undefined ? b.order : 999999;
              return orderA - orderB;
            });
            
            setRankingsBySubcategory(prev => ({
              ...prev,
              [selectedSubcategoryId]: sortedRankings
            }));
          }
        }
      } catch (error) {
        setInitializationError(
          error instanceof Error ? error.message : "ランキング取得に失敗しました"
        );
      } finally {
        setIsInitializing(false);
      }
    };
    
    // 非同期関数を呼び出し
    fetchAllSubcategoryRankings();

    // ButtonMainCategoryコンポーネントからの次のカテゴリ選択を無視するように設定
    setIgnoreNextCategorySelect(true);
    
    // カテゴリ情報をURLに反映
    // [username]ページの場合はURL更新をスキップ
    try {
      if (!window.location.pathname.match(/^\/[^\/]+$/)) {
        updateUrlWithCategory(selectedCat.id, subCategoryParam || (subcats.length > 0 ? subcats[0].id : ""), selectedCategoryIndex);
      }
    } catch (error) {
      console.error('初期化時のURL更新エラー:', error);
    }
    
  }, [
    categories,
    searchParams,
    initializedFromUrl,
    userId,
    pageUserId,
    updateCategoryID,
    updateSelectedCategoryName,
    updateSelectedCategoryIndex,
    updateSubCategories,
    updateSubcategoryOrder,
    updateSubCategory,
    setRankingsBySubcategory,
    setIgnoreNextCategorySelect,
    setInitializedFromUrl,
  ]);

  return {
    isInitializing,
    initializationError,
  };
};