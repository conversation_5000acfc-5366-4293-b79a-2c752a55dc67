'use client';

import React from 'react';
import Link from 'next/link';
import { Header } from "@/components/Header/Header";
import { Footer } from "@/components/Footer";

export default function ChangeCategoryGuidePage() {
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-white w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <Header />

      {/* メインコンテンツ */}
      <div className="w-full flex-1 pt-6 px-4">
        <div className="mb-6">
          <h1 className="text-[18px] font-bold text-[#313131]">カテゴリを変更・追加・削除する方法</h1>
        </div>
        
        <div className="mt-8 space-y-6">
          <div>
            <h2 className="relative text-[16px] font-bold text-[#313131] mb-3 pb-1">
              ①カテゴリを変更する
              <span className="absolute left-0 -bottom-0.5 w-full h-[3px] bg-[#FFD700]"></span>
            </h2>
            <div className="text-[14px] text-[#313131]">
              <p>1. マイページから「カテゴリ管理」を選択します。</p>
              <p>2. 変更したいカテゴリを選択します。</p>
              <p>3. カテゴリ名を編集します。</p>
              <p>4. 「保存」ボタンをタップして変更を確定します。</p>
            </div>
          </div>
          
          <div>
          <h2 className="relative text-[16px] font-bold text-[#313131] mb-3 pb-1">
            ②新しいカテゴリを追加する
            <span className="absolute left-0 -bottom-0.5 w-full h-[3px] bg-[#FFD700]"></span>
          </h2>
            <div className="text-[14px] text-[#313131]">
              <p>1. マイページから「カテゴリ管理」を選択します。</p>
              <p>2. 「カテゴリ追加」ボタンをタップします。</p>
              <p>3. 新しいカテゴリ名を入力します。</p>
              <p>4. 必要に応じて親カテゴリを選択します。</p>
              <p>5. 「追加」ボタンをタップして新しいカテゴリを作成します。</p>
            </div>
          </div>
          
          <div>
            <h2 className="relative text-[16px] font-bold text-[#313131] mb-3 pb-1">
              ③カテゴリを削除する
              <span className="absolute left-0 -bottom-0.5 w-full h-[3px] bg-[#FFD700]"></span>
            </h2>
            <div className="text-[14px] text-[#313131]">
              <p>1. マイページから「カテゴリ管理」を選択します。</p>
              <p>2. 削除したいカテゴリの横にある「削除」ボタンをタップします。</p>
              <p>3. 確認ダイアログで「はい」を選択して削除を確定します。</p>
              <p className="text-[#E63B5F]">※注意: カテゴリを削除すると、そのカテゴリに属するランキングも削除されます。</p>
            </div>
          </div>
        </div>
      </div>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
