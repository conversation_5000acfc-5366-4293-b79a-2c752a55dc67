/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React, { useState, useRef, useCallback, useEffect } from "react";
import RichTextEditor from "../RichTextEditor";
import { validateYouTubeURL, generateYouTubeEmbedHTML, validateXURL, generateXEmbedHTML, validateTikTokURL, generateTikTokEmbedHTML, validateInstagramURL, generateInstagramEmbedHTML } from '../SocialEmbed';
import { loadAllSnsScripts } from '../../utils/loadSnsScripts';

interface ReviewInputBoxProps {
  className: string;
  value: string;
  onChange: any;
}

// SNSリンクの型を定義
interface SnsLinks {
  instagram: string;
  tiktok: string;
  x: string;
  youtube: string;
  [key: string]: string; // インデックスシグネチャを追加
}

// SNSプラットフォームの型を定義
interface SnsPlatform {
  name: string;
  placeholder: string;
  key: keyof SnsLinks;
}

const ReviewInputBoxComponent: React.FC<ReviewInputBoxProps> = ({ className, value, onChange }) => {
  // console.log('🔍 [ReviewInputBox] Component rendered:', {
  //   valueType: typeof value,
  //   valueLength: typeof value === 'string' ? value.length : 'N/A',
  //   hasSocialEmbed: typeof value === 'string' ? value.includes('social-embed') : false,
  //   timestamp: new Date().toISOString()
  // });

  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false); // テンプレートモーダル用
  const [isSnsModalOpen, setIsSnsModalOpen] = useState(false); // SNSモーダル用
  const [isFullScreen, setIsFullScreen] = useState(false); // 全画面表示モード用
  const [isModalOpen, setIsModalOpen] = useState(false); // 入力エリアモーダル用
  const [snsLinks, setSnsLinks] = useState<SnsLinks>({
    instagram: "",
    tiktok: "",
    x: "",
    youtube: "",
  });
  
  // モーダルの内部要素を参照するためのref
  const templateModalRef = useRef<HTMLDivElement>(null);
  const snsModalRef = useRef<HTMLDivElement>(null);
  const editorContainerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // RichTextEditorから現在のDOM内容を取得する関数を保持
  const getCurrentContentRef = useRef<(() => string) | null>(null);

  // RichTextEditorに埋め込みを挿入する関数を保持
  const insertEmbedRef = useRef<((html: string) => void) | null>(null);

  // テンプレートの内容は変更せず、元のフォーマットに戻す
  const templates = [
    `
    [使った商品]


    [商品の特徴]


    [使用感]


    [良いところ]


    [合わない人]


    [どんな人におすすめか]`
  ];

  const snsPlatforms: SnsPlatform[] = [
    { name: "Instagram", placeholder: "https://www.instagram.com/", key: "instagram" },
    { name: "TikTok", placeholder: "https://vt.tiktok.com/", key: "tiktok" },
    { name: "X", placeholder: "https://x.com/", key: "x" },
    { name: "YouTube", placeholder: "https://www.youtube.com/", key: "youtube" },
  ];

  // SNSプラットフォームごとの画像パスを取得する関数
  const getSnsIconSrc = (platform: keyof SnsLinks): string => {
    const iconMap: Record<keyof SnsLinks, string> = {
      instagram: "/static/img/instagram.png",
      tiktok: "/static/img/tiktok.png",
      x: "/static/img/x.png",
      youtube: "/static/img/youtube.png"
    };
    return iconMap[platform];
  };

  // SNSリンクの変更処理
  const handleSnsChange = (platform: keyof SnsLinks, url: string) => {
    setSnsLinks((prev) => ({ ...prev, [platform]: url }));
  };

  // 追加ボタンの有効判定（有効なURLが1つでもあれば有効化）
  const isAddButtonActive = Object.entries(snsLinks).some(([platform, url]) => {
    if (!url.trim()) return false;

    // Instagramの場合は特別なバリデーションを適用
    if (platform === 'instagram') {
      return validateInstagramURL(url) !== null;
    }

    // YouTubeの場合は特別なバリデーションを適用
    if (platform === 'youtube') {
      return validateYouTubeURL(url) !== null;
    }

    // Xの場合は特別なバリデーションを適用
    if (platform === 'x') {
      return validateXURL(url) !== null;
    }

    // TikTokの場合は特別なバリデーションを適用
    if (platform === 'tiktok') {
      return validateTikTokURL(url) !== null;
    }

    // その他のプラットフォームは入力があれば有効
    return true;
  });
  
  // YouTubeのURLからビデオIDを抽出する関数
  const extractYouTubeVideoId = (url: string): string | null => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };
  
  // SNS投稿のURLから埋め込みHTMLを生成する関数
  const generateEmbedHtml = (platform: keyof SnsLinks, url: string): string => {
    if (!url || url.trim() === "") return "";

    switch(platform) {
      case "instagram":
        const postId = validateInstagramURL(url);
        if (!postId) return "";
        return `<div class="social-embed" data-platform="instagram" data-url="${url}">
          <div class="social-embed-loading instagram-loading">
            <div class="instagram-loading-subtitle">投稿を読み込み中...</div>
          </div>
        </div>`;
  
        case "tiktok":
          const tiktokVideoId = validateTikTokURL(url);
          if (!tiktokVideoId) return "";
          const tiktokEmbedHTML = generateTikTokEmbedHTML();
          return `<div class="social-embed" data-platform="tiktok" data-url="${url}">
            ${tiktokEmbedHTML}
          </div>`;
             
        case "x":
          const statusId = validateXURL(url);
          if (!statusId) return "";
          // 🔧 修正: TikTok/YouTubeと統一したローディング表示
          return `<div class="social-embed" data-platform="x" data-url="${url}">
            <div class="social-embed-loading x-loading">
              <div class="x-loading-subtitle">投稿を読み込み中...</div>
            </div>
          </div>`;

        
        
      case "youtube":
        const videoId = validateYouTubeURL(url);
        if (!videoId) return "";
        const embedHTML = generateYouTubeEmbedHTML();
        return `<div class="social-embed" data-platform="youtube" data-url="${url}">
          ${embedHTML}
        </div>`;
        
      default:
        return "";
    }
  };

  // デバッグ用のログ出力関数（ログ出力は削除）
  const debugLog = (message: string, data?: any) => {
    // ログ出力は削除されました
  };

  // SNSリンク入力フィールドをリセットする関数
  const resetSnsLinks = () => {
    debugLog('SNSリンク入力フィールドをリセットします');
    setSnsLinks({
      instagram: "",
      tiktok: "",
      x: "",
      youtube: "",
    });
  };

  // 🔧 修正: 重複実行防止フラグ（より強固な制御）
  const isProcessingRef = useRef(false);
  const lastProcessTimeRef = useRef(0);

  // SNSの投稿を追加
  const addSnsPosts = () => {
    const currentTime = Date.now();

    // 🔧 修正: 重複実行防止（時間ベースの制御も追加）
    if (isProcessingRef.current) {
      return;
    }

    // 🔧 修正: 短時間での連続実行を防止（1秒以内の連続実行を禁止）
    if (currentTime - lastProcessTimeRef.current < 1000) {
      return;
    }

    isProcessingRef.current = true;
    lastProcessTimeRef.current = currentTime;

    let embedHtml = "";

    // 入力されたURLごとに埋め込みHTMLを生成
    const validLinks = Object.entries(snsLinks)
      .filter(([, url]) => url.trim() !== "");

    debugLog(`有効なSNSリンク数: ${validLinks.length}`);

    // 🔧 修正: より確実な一意ID生成のための基準値
    const baseTimestamp = Date.now();
    const processId = Math.random().toString(36).substring(2, 15);
    const sessionId = Math.random().toString(36).substring(2, 10);

    validLinks.forEach(([platform, url], index) => {
      debugLog(`${platform}のURL処理中: ${url}`);
      const html = generateEmbedHtml(platform as keyof SnsLinks, url);

      if (html) {
        debugLog(`${platform}の埋め込みHTML生成成功 (長さ: ${html.length})`);

        // 🔧 修正: 絶対に重複しない一意IDを生成（複数の要素を組み合わせ）
        const uniqueId = `embed-${baseTimestamp}-${processId}-${sessionId}-${index}-${Math.random().toString(36).substring(2, 15)}`;

        // 埋め込みHTMLに一意IDを追加
        const embedHtmlWithId = html.replace(
          'class="social-embed"',
          `class="social-embed" data-embed-id="${uniqueId}"`
        );

        debugLog(`${platform}の埋め込みに一意ID追加: ${uniqueId}`);
        embedHtml += embedHtmlWithId + "\n";
      } else {
        debugLog(`${platform}の埋め込みHTML生成失敗`);
      }
    });
    
    debugLog(`生成された埋め込みHTML: ${embedHtml ? 'あり' : 'なし'}`);
    if (embedHtml) {
      debugLog('埋め込みHTMLの先頭部分:', embedHtml.substring(0, 100) + '...');
    }
    
    // 埋め込みHTMLが生成された場合はエディタに挿入
    if (embedHtml) {
      debugLog('🔧 新方式: DOM APIで埋め込みを追加します（iframe破壊なし）');

      // 🔧 修正: insertEmbed関数を使用してDOM APIで追加
      if (insertEmbedRef.current) {
        debugLog('🔧 insertEmbed関数を使用して埋め込みを追加します');
        insertEmbedRef.current(embedHtml);
        debugLog('🔧 DOM APIでの埋め込み追加完了');
      } else {
        debugLog('⚠️ insertEmbed関数が利用できません。フォールバック処理を実行します');
        // フォールバック: 従来の方式
        const currentValue = typeof value === 'string' ? value : '';
        const newValue = currentValue ? currentValue + "\n" + embedHtml : embedHtml;
        onChange(newValue);
      }

      // SNSリンク入力フィールドをリセット
      resetSnsLinks();

      // モーダルを閉じる
      debugLog('SNSモーダルを閉じます');
      setIsSnsModalOpen(false);

      // 🔧 追加: スクロール位置を保存してからエディタモーダルを開く
      const currentScrollPosition = window.scrollY;

      // エディタを即座に開く（ローディングアニメーションが即座に表示されるため遅延不要）
      debugLog('エディタモーダルを即座に開きます');
      setIsModalOpen(true);
      debugLog('エディタモーダルを開きました');

      // 🔧 追加: モーダル開閉後にスクロール位置を復元
      setTimeout(() => {
        window.scrollTo({
          top: currentScrollPosition,
          behavior: 'auto'
        });
        if (process.env.NODE_ENV === 'development') {
          console.log('[ReviewInputBox] エディタモーダル開閉後にスクロール位置を復元しました:', currentScrollPosition);
        }
      }, 100);
    } else {
      debugLog('埋め込みHTMLが生成されなかったため、処理をスキップします');
      // 埋め込みHTMLが生成されなかった場合でもフィールドをリセット
      resetSnsLinks();
    }

    // 🔧 修正: 重複実行防止フラグをより安全にリセット（遅延実行）
    setTimeout(() => {
      isProcessingRef.current = false;
    }, 2000); // 2秒後にリセット（埋め込み処理完了を待つ）
  };

  // テンプレートを選択したら入力エリアに反映
  const selectTemplate = (template: string) => {
    // テンプレートモーダルを閉じる
    setIsTemplateModalOpen(false);

    // 🔧 修正: 同じテンプレートでも確実に反映されるように一意性を追加
    const timestamp = Date.now();
    const uniqueTemplate = template + `<!-- template-${timestamp} -->`;

    // 値を更新
    onChange(uniqueTemplate);

    // エディタモーダルを開く
    setIsModalOpen(true);
  };

  // リッチテキストエディタの値が変更されたときのハンドラ
  const handleRichTextChange = useCallback((newValue: string | { target: { value: string } }) => {
    // console.log('🔍 [ReviewInputBox] handleRichTextChange called:', {
    //   newValueType: typeof newValue,
    //   newValueLength: typeof newValue === 'string' ? newValue.length : 'N/A',
    //   currentValueLength: typeof value === 'string' ? value.length : 'N/A',
    //   hasSocialEmbed: typeof newValue === 'string' ? newValue.includes('social-embed') : false,
    //   timestamp: new Date().toISOString()
    // });

    // 値が直接渡される場合とイベントオブジェクトが渡される場合の両方に対応
    if (typeof newValue === 'string') {
      onChange(newValue);
    } else if (typeof newValue === 'object' && newValue !== null && 'target' in newValue && newValue.target && typeof newValue.target === 'object' && 'value' in newValue.target) {
      onChange(newValue.target.value);
    }
  }, [onChange]);

  // 全画面表示モードの切り替え
  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  // 全画面表示時にESCキーで閉じられるようにする
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isFullScreen) {
        setIsFullScreen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullScreen]);

  // カスタムイベントをリッスンして、モーダルを開く
  useEffect(() => {
    const handleOpenModal = () => {
      setIsModalOpen(true);
    };

    // エディタコンテナにイベントリスナーを追加
    const container = editorContainerRef.current;
    if (container) {
      container.addEventListener('openModal', handleOpenModal);
    }

    return () => {
      if (container) {
        container.removeEventListener('openModal', handleOpenModal);
      }
    };
  }, []);

  // モーダルが開いているときに背景のスクロールを防止する
  useEffect(() => {
    // いずれかのモーダルが開いているかチェック
    const isAnyModalOpen = isTemplateModalOpen || isSnsModalOpen || isModalOpen;

    // bodyのスタイルを更新
    if (isAnyModalOpen) {
      // モーダルが開いているときはスクロールを無効化
      document.body.style.overflowY = 'hidden';
    } else {
      // モーダルが閉じているときはスクロールを有効化
      document.body.style.overflowY = 'auto';
    }

    // コンポーネントのアンマウント時にスクロールを元に戻す
    return () => {
      document.body.style.overflowY = 'auto';
    };
  }, [isTemplateModalOpen, isSnsModalOpen, isModalOpen]); // モーダル状態の変更を監視

  // SNS埋め込みコンテンツの高さを監視し、適切なスクロール処理を実装
  useEffect(() => {
    // 🚫 一時的に無効化（スクロール競合防止）
    return () => {};

    /* 以下のコードは一時的にコメントアウト
    if (!contentRef.current) return;

    const contentElement = contentRef.current;
    let resizeObserver: ResizeObserver | null = null;
    let mutationObserver: MutationObserver | null = null;

    // 🔧 修正: リアルタイム高さ計算関数
    const updateLayoutRealtime = () => {
      if (!contentElement) return;

      // 埋め込み要素の高さを即座に再計算
      const snsEmbeds = contentElement.querySelectorAll('.social-embed');
      let totalEmbedHeight = 0;

      snsEmbeds.forEach((embed) => {
        const iframe = embed.querySelector('iframe') as HTMLIFrameElement;
        const blockquote = embed.querySelector('blockquote') as HTMLElement;
        const embedContent = embed.querySelector('.social-embed-content') as HTMLElement;

        if (iframe && iframe.offsetHeight > 0) {
          totalEmbedHeight += iframe.offsetHeight + 40; // margin考慮
        } else if (blockquote && blockquote.offsetHeight > 0) {
          totalEmbedHeight += blockquote.offsetHeight + 40; // margin考慮
        } else if (embedContent && embedContent.offsetHeight > 0) {
          totalEmbedHeight += embedContent.offsetHeight + 40; // margin考慮
        }
      });

      // コンテンツの総高さを計算
      const textHeight = contentElement.scrollHeight - totalEmbedHeight;
      const totalHeight = textHeight + totalEmbedHeight;
      const containerHeight = contentElement.offsetHeight;

      // レイアウトを即座に更新
      if (totalHeight > containerHeight) {
        contentElement.style.overflowY = 'auto';
        contentElement.style.maxHeight = isFullScreen ? '90vh' : '60vh';
      } else {
        contentElement.style.overflowY = 'hidden';
      }

      // 埋め込み要素がある場合は下部にパディングを追加
      if (snsEmbeds.length > 0) {
        contentElement.style.paddingBottom = '50px';
      }

      console.log(`[ReviewInputBox] レイアウト更新: 総高さ=${totalHeight}px, コンテナ高さ=${containerHeight}px, 埋め込み数=${snsEmbeds.length}`);
    };

    // 🔧 新機能: ResizeObserverで埋め込み要素の高さ変更をリアルタイム監視
    const setupResizeObserver = () => {
      if (!window.ResizeObserver) return;

      resizeObserver = new ResizeObserver((entries) => {
        let needsUpdate = false;

        entries.forEach((entry) => {
          const target = entry.target as HTMLElement;

          // iframe要素の高さ変更を検出
          if (target.tagName === 'IFRAME' && target.closest('.social-embed')) {
            console.log(`[ReviewInputBox] iframe高さ変更検出: ${target.offsetHeight}px`);
            needsUpdate = true;
          }

          // 埋め込みコンテンツの高さ変更を検出
          if (target.classList.contains('social-embed-content')) {
            console.log(`[ReviewInputBox] 埋め込みコンテンツ高さ変更検出: ${target.offsetHeight}px`);
            needsUpdate = true;
          }
        });

        if (needsUpdate) {
          // 高さ変更を検出したら即座にレイアウトを更新
          requestAnimationFrame(updateLayoutRealtime);
        }
      });

      // 既存の埋め込み要素を監視対象に追加
      const observeExistingEmbeds = () => {
        const iframes = contentElement.querySelectorAll('.social-embed iframe');
        const embedContents = contentElement.querySelectorAll('.social-embed-content');

        iframes.forEach((iframe) => {
          if (resizeObserver) {
            resizeObserver.observe(iframe);
            console.log(`[ReviewInputBox] iframe監視開始: ${iframe.getAttribute('src')?.substring(0, 50)}...`);
          }
        });

        embedContents.forEach((content) => {
          if (resizeObserver) {
            resizeObserver.observe(content);
            console.log(`[ReviewInputBox] 埋め込みコンテンツ監視開始`);
          }
        });
      };

      observeExistingEmbeds();
      return resizeObserver;
    };

    // 🔧 修正: MutationObserverで新しい埋め込み要素の追加を監視
    const setupMutationObserver = () => {
      mutationObserver = new MutationObserver((mutations) => {
        let hasNewEmbeds = false;

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as HTMLElement;

                // 新しい埋め込み要素が追加された場合
                if (element.classList.contains('social-embed') || element.querySelector('.social-embed')) {
                  hasNewEmbeds = true;

                  // 新しいiframe要素をResizeObserverに追加
                  const newIframes = element.querySelectorAll('iframe');
                  const newEmbedContents = element.querySelectorAll('.social-embed-content');

                  newIframes.forEach((iframe) => {
                    if (resizeObserver) {
                      resizeObserver.observe(iframe);
                      console.log(`[ReviewInputBox] 新しいiframe監視開始: ${iframe.getAttribute('src')?.substring(0, 50)}...`);
                    }
                  });

                  newEmbedContents.forEach((content) => {
                    if (resizeObserver) {
                      resizeObserver.observe(content);
                      console.log(`[ReviewInputBox] 新しい埋め込みコンテンツ監視開始`);
                    }
                  });
                }
              }
            });
          }
        });

        if (hasNewEmbeds) {
          // 新しい埋め込み要素が追加されたら即座にレイアウトを更新
          setTimeout(updateLayoutRealtime, 100);
        }
      });

      mutationObserver.observe(contentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class', 'data-loaded']
      });

      return mutationObserver;
    };

    // 監視を開始
    resizeObserver = setupResizeObserver();
    mutationObserver = setupMutationObserver();

    // 初回レイアウト更新
    setTimeout(updateLayoutRealtime, 100);

    // クリーンアップ
    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
        console.log('[ReviewInputBox] ResizeObserver停止');
      }
      if (mutationObserver) {
        mutationObserver.disconnect();
        console.log('[ReviewInputBox] MutationObserver停止');
      }
    };
    */
  }, [isFullScreen]); // 🔧 修正: valueを依存配列から除外してMutationObserver再設定を防ぐ

  return (
    <div
      className={`inline-flex flex-col w-full items-center justify-center py-0 relative ${className} ${isFullScreen ? 'fixed inset-0 z-50 bg-white' : ''}`}
      ref={editorContainerRef}
      style={{ maxWidth: '100%', overflow: 'hidden', overflowX: 'hidden !important' }}
    >
      <div className={`inline-flex flex-col w-full ${isFullScreen ? 'h-screen' : 'min-h-[300px]'} items-start relative bg-[#F6F7F8] bg-background-color-gray1 border border-solid border-line max-w-full overflow-hidden`} style={{ overflowX: 'hidden !important', maxHeight: isFullScreen ? '100vh' : '80vh' }}>

        {/* テンプレートボタンとSNSボタンは表示しない */}

        {/* リッチテキストエディタ */}
        <div
          className="w-full flex-1 overflow-y-auto overflow-x-hidden"
          onClick={() => {
            // 値の有無に関わらずクリックしたらモーダルを開く
            setIsModalOpen(true);
          }}
          style={{
            overflowX: 'hidden !important',
            minHeight: '280px'
          }}
        >
          {value || Object.values(snsLinks).some(link => link.trim() !== "") ? (
            // 🔧 修正: 埋め込み要素がある場合は静的HTMLで表示（RichTextEditor競合回避）
            typeof value === 'string' && value.includes('social-embed') ? (
              // 埋め込み要素がある場合は、静的HTMLで表示（processSocialEmbeds競合を回避）
              <div className="p-4 text-[#313131] overflow-y-auto" style={{
                overflowX: 'hidden',
                wordBreak: 'break-all',
                overflowWrap: 'break-word',
                whiteSpace: 'pre-wrap',
                maxWidth: '100%',
                minHeight: 'auto',
                maxHeight: isFullScreen ? '90vh' : '60vh',
                paddingBottom: '20px'
              }}>
                {/* 🔧 修正: RichTextEditorの代わりに静的HTMLを使用 */}
                <div
                  className="ranking-content"
                  dangerouslySetInnerHTML={{ __html: value }}
                  style={{
                    pointerEvents: 'none', // クリック無効化
                    userSelect: 'none' // 選択無効化
                  }}
                />
              </div>
            ) : (
              // 通常のテキストの場合はdangerouslySetInnerHTMLを使用
              <div
                ref={contentRef}
                className="ranking-content p-4 text-[#313131] overflow-y-auto"
                dangerouslySetInnerHTML={{ __html: typeof value === 'string' ? value : '' }}
                style={{
                  overflowX: 'hidden',
                  wordBreak: 'break-all',
                  overflowWrap: 'break-word',
                  whiteSpace: 'pre-wrap',
                  maxWidth: '100%',
                  minHeight: 'auto',
                  maxHeight: isFullScreen ? '90vh' : '60vh',
                  paddingBottom: '20px'
                }}
              />
            )
          ) : (
            // 値がなく、SNSリンクも入力されていない場合はプレースホルダーを表示
            <div
              className="p-4 text-gray-400 cursor-text flex items-center justify-center"
              style={{
                overflowX: 'hidden',
                wordBreak: 'break-all',
                overflowWrap: 'break-word',
                whiteSpace: 'pre-wrap',
                maxWidth: '100%',
                minHeight: '280px'
              }}
            >
              ここをクリックしておすすめの理由を入力しよう！<br />SNSの投稿を埋め込むと、より魅力的に伝えることができます。
            </div>
          )}
        </div>
      </div>

      {/* テンプレートモーダル */}
      {isTemplateModalOpen && (
        <div 
          className="fixed inset-0 flex items-center justify-center bg-black/50 z-[9999999]"
          onClick={(e: React.MouseEvent<HTMLDivElement>) => {
            // モーダル外をクリックした場合にモーダルを閉じる
            if (e.target === e.currentTarget) {
              setIsTemplateModalOpen(false);
            }
          }}
        >
          <div ref={templateModalRef} className="bg-white rounded-lg w-[90%] max-w-[400px] p-4 shadow-lg relative">
            
            {/* タイトル & 閉じるボタン */}
            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <h2 className="text-lg font-semibold">テンプレートを使う</h2>
              <button type="button" onClick={() => setIsTemplateModalOpen(false)} className="text-gray-500 hover:text-gray-700">
                ✕
              </button>
            </div>

            {/* テンプレートリスト */}
            <div className="mt-4">
              {templates.map((template, index) => (
                <button 
                  type="button"
                  key={index} 
                  onClick={() => selectTemplate(template)}
                  className="bg-gray-100 text-black text-sm p-3 rounded-md text-left"
                >
                  {template}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* SNSモーダル */}
      {isSnsModalOpen && (
        <div 
          className="fixed inset-0 flex items-center justify-center bg-black/50 z-[9999999]"
          onClick={(e: React.MouseEvent<HTMLDivElement>) => {
            // モーダル外をクリックした場合にモーダルを閉じる
            if (snsModalRef.current && !snsModalRef.current.contains(e.target as Node)) {
              resetSnsLinks();
              setIsSnsModalOpen(false);
            }
          }}
        >
          <div ref={snsModalRef} className="bg-white rounded-lg w-[90%] max-w-[400px] p-4 shadow-lg relative">
            
            {/* タイトル & 閉じるボタン */}
            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <h2 className="text-lg font-semibold">SNSの投稿を埋め込む</h2>
              <button type="button" onClick={() => {
                resetSnsLinks();
                setIsSnsModalOpen(false);
              }} className="text-gray-500 hover:text-gray-700">
                ✕
              </button>
            </div>

            {/* SNSリスト */}
            <div className="">
              {snsPlatforms.map(({ name, placeholder, key }) => {
                // YouTube、X、TikTok、Instagramの場合はバリデーション結果を取得
                const isYouTube = key === 'youtube';
                const isX = key === 'x';
                const isTikTok = key === 'tiktok';
                const isInstagram = key === 'instagram';
                const hasInput = snsLinks[key].trim() !== '';
                const isValidYouTube = isYouTube ? validateYouTubeURL(snsLinks[key]) !== null : true;
                const isValidX = isX ? validateXURL(snsLinks[key]) !== null : true;
                const isValidTikTok = isTikTok ? validateTikTokURL(snsLinks[key]) !== null : true;
                const isValidInstagram = isInstagram ? validateInstagramURL(snsLinks[key]) !== null : true;
                const showValidation = (isYouTube || isX || isTikTok || isInstagram) && hasInput;
                const isValid = isYouTube ? isValidYouTube : isX ? isValidX : isTikTok ? isValidTikTok : isInstagram ? isValidInstagram : true;

                return (
                  <div key={key} className="flex flex-col">
                    <div className="flex items-center justify-between border-b border-gray-200 py-2">
                      <div className="flex items-center gap-2">
                        <img src={getSnsIconSrc(key)} className="w-5 h-5" alt={name} />
                        <span className="text-sm">{name}</span>
                      </div>
                      <input
                        type="text"
                        className={`text-sm outline-none bg-transparent w-[65%] text-right
                          ${showValidation
                            ? (isValid ? "text-green-600" : "text-red-500")
                            : (snsLinks[key] ? "text-black" : "text-gray")
                          }`}
                        placeholder={placeholder}
                        value={snsLinks[key]}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSnsChange(key, e.target.value)}
                      />
                    </div>

                  </div>
                );
              })}
            </div>

            {/* バリデーションメッセージ（常に1つ分のスペースを確保） */}
            <div className="mt-4 space-y-2 min-h-[40px]">
              {/* 入力されたURLがある場合のみバリデーションメッセージを表示 */}
              {Object.entries(snsLinks).map(([platform, url]) => {
                const hasInput = url.trim() !== '';
                const isInstagram = platform === 'instagram';
                const isYouTube = platform === 'youtube';
                const isX = platform === 'x';
                const isTikTok = platform === 'tiktok';

                // Instagram、YouTube、X、TikTokのみバリデーション対象
                if (!isInstagram && !isYouTube && !isX && !isTikTok) return null;

                // 入力がない場合はスキップ
                if (!hasInput) return null;

                const isValidInstagram = isInstagram ? validateInstagramURL(url) !== null : true;
                const isValidYouTube = isYouTube ? validateYouTubeURL(url) !== null : true;
                const isValidX = isX ? validateXURL(url) !== null : true;
                const isValidTikTok = isTikTok ? validateTikTokURL(url) !== null : true;
                const isValid = isInstagram ? isValidInstagram : isYouTube ? isValidYouTube : isX ? isValidX : isTikTok ? isValidTikTok : true;

                return (
                  <div key={platform} className={`text-sm px-3 py-2 rounded-md ${isValid ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"}`}>
                    {isInstagram && (
                      isValid
                        ? "✓ Instagram: 有効な投稿URLです"
                        : "✗ Instagram: 無効なURLです（投稿URLのみ対応）"
                    )}
                    {isYouTube && (
                      isValid
                        ? "✓ YouTube: 有効な動画URLです"
                        : "✗ YouTube: 無効なURLです（動画URLのみ対応）"
                    )}
                    {isX && (
                      isValid
                        ? "✓ X: 有効な投稿URLです"
                        : "✗ X: 無効なURLです（投稿URLのみ対応）"
                    )}
                    {isTikTok && (
                      isValid
                        ? "✓ TikTok: 有効な動画URLです"
                        : "✗ TikTok: 無効なURLです（動画URLのみ対応）"
                    )}
                  </div>
                );
              })}
            </div>

            {/* フッターボタン */}
            <div className="flex justify-between items-center mt-4 gap-2">
              <button
                type="button"
                onClick={() => {
                  resetSnsLinks();
                  setIsSnsModalOpen(false);
                }}
                className="w-1/2 py-2 border border-gray-300 rounded-md text-gray-600"

              >
                キャンセル
              </button>
              <button 
                type="button"
                onClick={addSnsPosts}
                className={`w-1/2 py-2 rounded-md text-white ${isAddButtonActive ? "bg-[#E63B5F]" : "bg-gray-300"}`}
                disabled={!isAddButtonActive}

              >
                追加する
              </button>
            </div>

          </div>
        </div>
      )}
      
      {/* 入力エリアモーダル */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-start justify-center z-[999999]" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
          <div className="flex flex-col w-full max-w-[500px] max-auto h-screen bg-white overflow-hidden shadow-[-10px_0_20px_rgba(0,0,0,0.1),10px_0_20px_rgba(0,0,0,0.1)]">
            <div className="flex w-full items-center relative flex-[0_0_auto] border-b [border-bottom-style:solid] border-line sticky top-0 z-[999990] bg-white">
              <button 
                type="button"
                className="flex w-1/2 h-12 items-center justify-center gap-1 relative border-r [border-right-style:solid] [border-left-style:solid] border-line bg-[white]"
                onClick={() => setIsTemplateModalOpen(true)}
              >
                <img className="relative w-5 h-5 object-cover" src="/static/img/template.png" alt="Template Icon" />
                <div className="w-fit text-black-1 text-[13px] text-center leading-[normal] whitespace-nowrap relative font-normal tracking-[0]">
                  テンプレートを使う
                </div>
              </button>
              <button
                type="button"
                className="flex w-1/2 h-12 items-center justify-center gap-1 relative bg-[white]"
                onClick={() => {
                  // 🔧 追加: SNS埋め込みモーダルを開く前にカーソル位置を保存
                  const editorElement = document.querySelector('.rich-text-editor [contenteditable]') as HTMLElement;
                  if (editorElement) {
                    const selection = window.getSelection();
                    if (selection && selection.rangeCount > 0) {
                      const range = selection.getRangeAt(0).cloneRange();
                      // カーソル位置をグローバルに保存
                      (window as any).__savedCursorRange = range;
                      if (process.env.NODE_ENV === 'development') {
                        console.log('[SNS_MODAL] カーソル位置を保存しました:', {
                          container: range.startContainer.nodeName,
                          offset: range.startOffset,
                          parentElement: range.startContainer.parentElement?.tagName
                        });
                      }
                    }
                  }
                  setIsSnsModalOpen(true);
                }}
              >
                <img className="relative w-5 h-5 object-cover" src="/static/img/sns.png" alt="SNS Icon" />
                <div className="w-fit text-black-1 text-[13px] text-center leading-[normal] whitespace-nowrap relative font-normal tracking-[0]">
                  SNSの投稿を追加
                </div>
              </button>
            </div>

            <div className="flex-1 overflow-auto">
              <RichTextEditor
                value={value} // 🔧 修正: valueプロパティを使用（initialHtmlは使用しない）
                onChange={onChange}
                isFullScreen={true}
                showToolbar={true} // モーダル内ではツールバーを常に表示
                instanceId="modal-editor" // 🔧 修正: モーダル内エディタの識別ID
                onGetCurrentContent={(getCurrentContentFn) => {
                  getCurrentContentRef.current = getCurrentContentFn;
                }}
                onInsertEmbed={(insertEmbedFn) => {
                  insertEmbedRef.current = insertEmbedFn;
                }}
              />
            </div>

            {/* 閉じるボタン */}
            <div className="flex justify-end p-4 border-t border-gray-200">
              <button
                type="button"
                onClick={() => {
                  // 🔧 修正: モーダルを閉じる前に現在のエディタ内容を取得して同期
                  if (getCurrentContentRef.current) {
                    const currentContent = getCurrentContentRef.current();
                    // HTMLコメントを除去してクリーンな内容を取得
                    const cleanContent = currentContent.replace(/<!--.*?-->/g, '');
                    onChange(cleanContent);
                  }
                  setIsModalOpen(false);
                }}
                className="px-6 py-2 bg-gray-800 text-white rounded-md shadow-md"
                aria-label="閉じる"
              >
                閉じる
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// メモ化されたコンポーネントをエクスポート
export const ReviewInputBox = React.memo(ReviewInputBoxComponent, (prevProps, nextProps) => {
  // valueとonChangeが同じ場合は再レンダリングを防ぐ
  return (
    prevProps.value === nextProps.value &&
    prevProps.onChange === nextProps.onChange &&
    prevProps.className === nextProps.className
  );
});
