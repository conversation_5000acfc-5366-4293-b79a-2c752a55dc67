'use client';

import { useSignUp } from '@clerk/nextjs';
import { useState } from 'react';

export default function TestOAuthPage() {
  const { isLoaded, signUp } = useSignUp();
  const [status, setStatus] = useState('');

  const testGoogleOAuth = async () => {
    if (!isLoaded || !signUp) {
      setStatus('❌ Clerk not loaded');
      return;
    }

    setStatus('🔄 Starting Google OAuth test...');
    
    try {
      console.log('🧪 Test OAuth - Starting Google authentication');
      
      await signUp.authenticateWithRedirect({
        strategy: 'oauth_google',
        redirectUrl: `${window.location.origin}/sso-callback`,
        redirectUrlComplete: `${window.location.origin}/setup`,
      });
      
      setStatus('✅ OAuth redirect initiated');
    } catch (error: any) {
      console.error('🧪 Test OAuth - Error:', error);
      setStatus(`❌ Error: ${error.message || 'Unknown error'}`);
    }
  };

  if (!isLoaded) {
    return <div className="p-8">Loading Clerk...</div>;
  }

  return (
    <div className="min-h-screen p-8 bg-gray-100">
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow">
        <h1 className="text-2xl font-bold mb-4">OAuth Test Page</h1>
        
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">
            Clerk Status: {isLoaded ? '✅ Loaded' : '❌ Not Loaded'}
          </p>
          <p className="text-sm text-gray-600 mb-2">
            SignUp Object: {signUp ? '✅ Available' : '❌ Not Available'}
          </p>
        </div>

        <button
          onClick={testGoogleOAuth}
          disabled={!isLoaded || !signUp}
          className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed mb-4"
        >
          Test Google OAuth
        </button>

        <div className="text-sm">
          <strong>Status:</strong> {status}
        </div>

        <div className="mt-4 text-xs text-gray-500">
          <p>This page is for testing OAuth functionality.</p>
          <p>Check the browser console for detailed logs.</p>
        </div>
      </div>
    </div>
  );
}
