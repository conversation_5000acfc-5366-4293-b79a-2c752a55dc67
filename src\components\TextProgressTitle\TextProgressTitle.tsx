/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import PropTypes from "prop-types";
import React from "react";

interface Props {
  className: any;
  text: string;
}

export const TextProgressTitle = ({ className, text = "アカウントを作成" }: Props): JSX.Element => {
  return (
    <div className={`w-[390px] items-center pl-[30px] pr-0 pt-[80px] pb-[20px] relative ${className}`}>
      <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-bold text-black text-[18px] tracking-[0] leading-[27px] whitespace-nowrap">
        {text}
      </div>
    </div>
  );
};

TextProgressTitle.propTypes = {
  text: PropTypes.string,
};
