import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('受信した公開リクエスト:', data);
    
    // 必須フィールドのバリデーション
    if (!data.ranking_ID) {
      return NextResponse.json(
        { success: false, error: 'ランキングIDが必要です' },
        { status: 400 }
      );
    }
    
    if (!data.user_ID) {
      return NextResponse.json(
        { success: false, error: 'ユーザーIDが必要です' },
        { status: 400 }
      );
    }
    
    console.log(`下書き公開処理を開始します: ランキングID=${data.ranking_ID}, ユーザーID=${data.user_ID}`);
    
    // 下書きを公開済みに変更
    const result = await prisma.ranking.update({
      where: {
        ranking_ID: data.ranking_ID,
        user_ID: data.user_ID, // セキュリティのため、ユーザーIDも条件に含める
        status: 'DRAFT', // 下書きのもののみ公開可能
      },
      data: {
        status: 'PUBLISHED',
        updated_at: new Date(),
      },
    });
    
    console.log('下書き公開完了:', result);
    
    // 成功した場合のレスポンスを返す
    return NextResponse.json(
      { 
        success: true, 
        data: result,
        message: 'ランキングを公開しました'
      },
      {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (error) {
    console.error('下書き公開中にエラーが発生しました:', error);
    
    // Prismaのエラーハンドリング
    if (error.code === 'P2025') {
      // レコードが見つからない場合
      return NextResponse.json(
        { success: false, error: '指定された下書きが見つかりません' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'ランキングの公開に失敗しました' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  // プリフライトリクエストに対するレスポンス
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}
