import React from 'react';
import Image from 'next/image';
import { IoMdClose } from 'react-icons/io';
import { BsCamera } from 'react-icons/bs';
import { ImageCropper } from '../../../../components/ImageCropper/ImageCropper';

interface ImageUploadSectionProps {
  images: string[];
  handleImageChange: (e: React.ChangeEvent<HTMLInputElement>, index: number) => void;
  handleRemoveImage: (index: number, e?: React.MouseEvent) => void;
  // トリミング機能用の追加プロパティ
  pickFile: (index: number) => void;
  fileInputRefs: React.MutableRefObject<(HTMLInputElement | null)[]>;
  showCropper: boolean;
  tempImage: string | null;
  handleCropComplete: (croppedImage: string) => void;
  handleCropCancel: () => void;
}

/**
 * 画像アップロードセクションコンポーネント（トリミング機能付き）
 */
export const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  images,
  handleImageChange,
  handleRemoveImage,
  pickFile,
  fileInputRefs,
  showCropper,
  tempImage,
  handleCropComplete,
  handleCropCancel
}) => {
  return (
    <>
      <div className="w-full px-4 py-6 bg-white">
        <div className="flex w-full gap-2">
          {[1, 2, 3, 4].map((stepNum) => {
            const index = stepNum - 1;
            const image = images[index];

            // 1番目は常に表示、2番目以降は前の番号に画像がある場合のみ表示
            const shouldShow = stepNum === 1 || images[index - 1];

            if (!shouldShow) return null;

            return (
              <div key={stepNum} className="relative w-[22%] aspect-square bg-gray-100 rounded-md flex flex-col items-center justify-center overflow-hidden border border-gray-200">
                {/* 左上の番号 */}
                <div className="absolute top-0 left-0 w-6 h-6 flex items-center justify-center bg-[rgba(135,135,135,0.7)] text-white text-[14px]">
                  {stepNum}
                </div>

                {image ? (
                  <>
                    <Image
                      src={image}
                      alt={`アップロード画像 ${stepNum}`}
                      width={100}
                      height={100}
                      style={{ objectFit: 'cover', width: '100%', height: '100%' }}
                    />
                    {/* 1番目の画像は削除できないが変更は可能 */}
                    {stepNum !== 1 && (
                      <button
                        type="button"
                        onClick={(e: React.MouseEvent) => handleRemoveImage(index, e)}
                        className="absolute top-1 right-1 bg-white rounded-full w-5 h-5 flex items-center justify-center shadow-md z-30"
                      >
                        <IoMdClose className="text-sm text-gray-700" />
                      </button>
                    )}
                    {/* 隠しファイル入力 */}
                    <input
                      ref={(el: HTMLInputElement | null) => (fileInputRefs.current[index] = el)}
                      type="file"
                      accept="image/*"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleImageChange(e, index)}
                      style={{ display: 'none' }}
                    />
                    {/* 画像をクリックしてトリミング画面を開く - 1番目の画像も変更可能 */}
                    <div
                      className="absolute inset-0 z-25 cursor-pointer"
                      onClick={() => {
                        console.log(`画像 ${stepNum} がクリックされました`);
                        pickFile(index);
                      }}
                      title={`画像 ${stepNum} を変更`}
                    >
                    </div>
                  </>
                ) : (
                  <>
                    <BsCamera className="text-[#E63B5F] text-2xl mb-1" />
                    {stepNum === 1 && <div className="text-xs text-[#E63B5F]">必須</div>}
                    {/* 隠しファイル入力 */}
                    <input
                      ref={(el: HTMLInputElement | null) => (fileInputRefs.current[index] = el)}
                      type="file"
                      accept="image/*"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleImageChange(e, index)}
                      style={{ display: 'none' }}
                    />
                    {/* 空の枠をクリックしてファイル選択を開く */}
                    <div
                      className="absolute inset-0 z-25 cursor-pointer"
                      onClick={() => {
                        console.log(`空の画像枠 ${stepNum} がクリックされました`);
                        pickFile(index);
                      }}
                      title={`画像 ${stepNum} を追加`}
                    />
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* 画像クロッパー */}
      {showCropper && tempImage && (
        <ImageCropper
          image={tempImage}
          aspectRatio={1} // 正方形にトリミング
          onCropComplete={handleCropComplete}
          onCancel={handleCropCancel}
          isProfileImage={false}
        />
      )}
    </>
  );
};
