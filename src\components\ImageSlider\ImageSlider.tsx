import React, { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";

interface Props {
  images: string[];
  selectedImage: string | null;
  onImageChange?: (imageUrl: string, index: number) => void; // オプショナルに変更
}

export const ImageSlider = ({ images, selectedImage, onImageChange }: Props): JSX.Element => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [startX, setStartX] = useState<number>(0);
  const [translateX, setTranslateX] = useState<number>(0);
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);
  
  const sliderRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // サムネイル選択時のみ同期（スライダー操作時は無視）
  const [isInternalChange, setIsInternalChange] = useState<boolean>(false);

  // 外部同期を完全に無効化（サムネイル選択時のみ手動で同期）
  const [lastSyncedImage, setLastSyncedImage] = useState<string | null>(null);

  useEffect(() => {
    // 内部変更中は外部同期をスキップ
    if (isInternalChange) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 useEffect: 内部変更中のためスキップ');
      }
      return;
    }

    // 初回のみ、または明示的なサムネイル選択時のみ同期（不要な実行を防ぐ）
    if (selectedImage && images.length > 0 && !isDragging && !isTransitioning && selectedImage !== lastSyncedImage) {
      const index = images.findIndex(img => img === selectedImage);
      if (index !== -1 && index !== currentIndex) { // 現在のインデックスと異なる場合のみ実行
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 useEffect: 初回またはサムネイル選択による変更', { selectedImage, index, currentIndex });
        }
        setCurrentIndex(index);
        const containerWidth = containerRef.current?.offsetWidth || 0;
        setTranslateX(-index * containerWidth);
        setLastSyncedImage(selectedImage);
      }
    }
  }, [selectedImage, images, currentIndex, isDragging, isTransitioning, isInternalChange, lastSyncedImage]);

  // 初期化用useEffectを削除（上記のuseEffectと重複しているため）

  // スライド移動処理
  const moveToIndex = useCallback((index: number) => {
    if (index < 0 || index >= images.length || index === currentIndex) return;

    setIsTransitioning(true);
    setCurrentIndex(index);
    const containerWidth = containerRef.current?.offsetWidth || 0;
    setTranslateX(-index * containerWidth);

    // アニメーション完了後に親コンポーネントに通知
    setTimeout(() => {
      setIsTransitioning(false);
      if (onImageChange) {
        onImageChange(images[index], index);
      }
    }, 300);
  }, [images, currentIndex, onImageChange]);

  // マウス/タッチイベント処理
  const handleStart = useCallback((clientX: number) => {
    setIsDragging(true);
    setStartX(clientX);
    setIsTransitioning(false);
  }, []);

  const handleMove = useCallback((clientX: number) => {
    if (!isDragging) return;

    const diff = clientX - startX;
    const containerWidth = containerRef.current?.offsetWidth || 0;

    // 新しい位置を計算（ピクセル単位）
    let newTranslateX = -currentIndex * containerWidth + diff;

    // スライド範囲を制限（最初の画像より前、最後の画像より後に行かないように）
    const minTranslateX = -(images.length - 1) * containerWidth; // 最後の画像の位置
    const maxTranslateX = 0; // 最初の画像の位置

    // 境界を超えないように制限
    if (newTranslateX > maxTranslateX) {
      newTranslateX = maxTranslateX;
    } else if (newTranslateX < minTranslateX) {
      newTranslateX = minTranslateX;
    }

    setTranslateX(newTranslateX);
  }, [isDragging, startX, currentIndex, images.length]);

  const handleEnd = useCallback(() => {
    if (!isDragging) return;

    console.log('🎯 handleEnd: スライド操作終了', { currentIndex, translateX });

    setIsDragging(false);
    const containerWidth = containerRef.current?.offsetWidth || 0;
    const threshold = containerWidth * 0.2; // 20%のしきい値
    const diff = translateX + currentIndex * containerWidth;

    const originalIndex = currentIndex; // 元のインデックスを保存
    let targetIndex = currentIndex;

    if (Math.abs(diff) > threshold) {
      if (diff > 0 && currentIndex > 0) {
        // 左にスワイプ（前の画像）- 境界チェック
        targetIndex = currentIndex - 1;
      } else if (diff < 0 && currentIndex < images.length - 1) {
        // 右にスワイプ（次の画像）- 境界チェック
        targetIndex = currentIndex + 1;
      }
    }

    console.log('🎯 handleEnd: 目的インデックス決定', { originalIndex, targetIndex });

    // 内部変更フラグを設定（外部同期を防ぐ）
    setIsInternalChange(true);

    // 直接目的の位置にアニメーション
    setIsTransitioning(true);
    setCurrentIndex(targetIndex);
    setTranslateX(-targetIndex * containerWidth);

    console.log('🎯 handleEnd: アニメーション開始', { targetIndex, translateX: -targetIndex * containerWidth });

    // アニメーション完了後の処理
    setTimeout(() => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🎯 handleEnd: アニメーション完了');
      }
      setIsTransitioning(false);

      // 親コンポーネントに新しい画像を通知（同期用）
      if (targetIndex !== originalIndex && onImageChange) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🎯 handleEnd: 親コンポーネントに通知', { targetIndex, image: images[targetIndex] });
        }
        setLastSyncedImage(images[targetIndex]); // 同期済みとしてマーク
        onImageChange(images[targetIndex], targetIndex);
      }

      // 内部変更フラグをリセット
      setTimeout(() => {
        setIsInternalChange(false);
        if (process.env.NODE_ENV === 'development') {
          console.log('🎯 handleEnd: 内部変更フラグリセット');
        }
      }, 100);
    }, 300);
  }, [isDragging, translateX, currentIndex, images.length, images]);

  // マウスイベント
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    handleStart(e.clientX);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    handleMove(e.clientX);
  };

  const handleMouseUp = () => {
    handleEnd();
  };

  // タッチイベント
  const handleTouchStart = (e: React.TouchEvent) => {
    handleStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    handleMove(e.touches[0].clientX);
  };

  const handleTouchEnd = () => {
    handleEnd();
  };

  // グローバルマウスイベントの設定
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        handleMove(e.clientX);
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDragging) {
        handleEnd();
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging, handleMove, handleEnd]);

  if (!images || images.length === 0) {
    return <div></div>;
  }

  return (
    <div
      ref={containerRef}
      className="w-full flex justify-center items-center p-0 bg-black overflow-hidden cursor-grab active:cursor-grabbing"
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{ userSelect: 'none' }}
    >
      <div
        ref={sliderRef}
        className="flex"
        style={{
          transform: `translateX(${translateX}px)`,
          transition: isTransitioning || !isDragging ? 'transform 0.3s ease-out' : 'none',
          width: '100%',
          height: '100%',
        }}
      >
        {images.map((image, index) => (
          <div
            key={index}
            className="flex-shrink-0 flex justify-center items-center"
            style={{
              width: '100%',
              minWidth: '100%',
              flexBasis: '100%'
            }}
          >
            <div className="relative w-full max-w-[1080px]" style={{ aspectRatio: '4/5', maxHeight: 'calc(100vw * 1.25)' }}>
              <Image
                src={image}
                alt={`Image ${index + 1}`}
                fill
                style={{ objectFit: 'contain', pointerEvents: 'none' }}
                priority={index === 0}
                quality={90}
                sizes="(max-width: 500px) 100vw, 500px"
                draggable={false}
                onDragStart={(e) => e.preventDefault()}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
