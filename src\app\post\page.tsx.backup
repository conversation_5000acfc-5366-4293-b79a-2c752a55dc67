'use client';

import React, { useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';

const PostRedirectPage = (): JSX.Element => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const rankingId = searchParams.get('ranking');

  useEffect(() => {
    const redirectToNewFormat = async () => {
      if (rankingId) {
        try {
          // ランキングIDからユーザー情報を取得
          const rankingResponse = await fetch(`/api/getRankingDetail?ranking_ID=${rankingId}`);
          if (rankingResponse.ok) {
            const rankingData = await rankingResponse.json();
            if (rankingData && rankingData.length > 0) {
              const userId = rankingData[0].user_ID;

              // ユーザーIDからユーザー名を取得
              const userResponse = await fetch(`/api/getUser?user_ID=${userId}`);
              if (userResponse.ok) {
                const userData = await userResponse.json();
                const username = userData.username;
                if (username) {
                  // 新しいURL形式にリダイレクト
                  router.replace(`/${username}/post?ranking=${rankingId}`);
                  return;
                }
              }
            }
          }
        } catch (error) {
          console.error('リダイレクト処理エラー:', error);
        }
      }

      // フォールバック: ホームページにリダイレクト
      router.replace('/');
    };

    redirectToNewFormat();
  }, [rankingId, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <CatLoadingAnimation />
        <p className="mt-4 text-gray-600">新しいページに移動しています...</p>
      </div>
    </div>
  );
};

export default PostRedirectPage;
