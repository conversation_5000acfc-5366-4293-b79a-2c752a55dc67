'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import './ProfileSaveLoadingAnimation.css';

interface ProfileSaveLoadingAnimationProps {
  isVisible: boolean;
  onComplete?: () => void;
}

export default function ProfileSaveLoadingAnimation({ isVisible, onComplete }: ProfileSaveLoadingAnimationProps) {
  const [progress, setProgress] = useState(0);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      setShowSuccessMessage(false);
      return;
    }

    // プログレスバーのアニメーション
    const interval = setInterval(() => {
      setProgress((prevProgress) => {
        // 進捗が100%に達したら
        if (prevProgress >= 100) {
          clearInterval(interval);
          setShowSuccessMessage(true);
          
          // 完了後、少し待ってから完了コールバックを呼び出す
          setTimeout(() => {
            if (onComplete) onComplete();
          }, 1000);
          
          return 100;
        }
        
        // 進捗の増加率を調整（最初は速く、後半は遅く）
        const increment = prevProgress < 60 ? 5 : prevProgress < 85 ? 3 : 1;
        return Math.min(prevProgress + increment, 100);
      });
    }, 100);

    return () => {
      clearInterval(interval);
    };
  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="profile-save-loading-container">
      <div className="profile-save-loading-content">
        <div className="profile-save-icon">
          {showSuccessMessage ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          ) : null}
        </div>
        
        <div className="profile-save-message">
          {showSuccessMessage ? '保存しました' : 'プロフィールを保存中...'}
        </div>
        
        {!showSuccessMessage && (
          <div className="profile-save-meter-container">
            <div 
              className="profile-save-meter-fill" 
              style={{ width: `${progress}%` }}
            ></div>
            <div className="profile-save-meter-percentage">{progress}%</div>
          </div>
        )}
      </div>
    </div>
  );
}
