import { NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';

// グローバルスコープでPrismaClientのインスタンスを作成
// これにより、ホットリロード時の複数インスタンス作成を防止
let prisma: PrismaClient;

if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  // 開発環境では、グローバル変数にPrismaClientを保存して再利用
  if (!(global as any).prisma) {
    (global as any).prisma = new PrismaClient();
  }
  prisma = (global as any).prisma;
}

export async function GET(request: Request) {
  try {
    // URLからuser_IDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get('user_ID');

    // user_IDのバリデーション
    if (!user_ID) {
      console.log('カテゴリ取得エラー: user_IDが提供されていません');
      return NextResponse.json(
        { error: "user_IDが必要です", details: "クエリパラメータにuser_IDを指定してください" },
        { status: 400 }
      );
    }

    console.log(`カテゴリ取得リクエスト: ユーザーID=${user_ID}`);

    try {
      // ユーザーの存在確認
      const user = await prisma.user.findUnique({
        where: {
          user_ID: user_ID
        }
      });

      if (!user) {
        console.log(`カテゴリ取得エラー: ユーザーID=${user_ID}のユーザーが見つかりません`);
        return NextResponse.json(
          { error: "ユーザーが見つかりません", details: `ユーザーID: ${user_ID}` },
          { status: 404 }
        );
      }

      // まず、ユーザーの全カテゴリを取得して分析
      console.log('🔍 [getCategories] カテゴリ取得開始:', { user_ID });
      const allUserCategories = await prisma.category.findMany({
        where: { user_ID },
        select: {
          id: true,
          category_ID: true,
          category_name: true,
          category_slug: true,
          history_slug: true,  // history_slugを追加
          parent_ID: true,
          order: true,
          created_at: true,
          Children: {
            select: {
              id: true,
              category_ID: true,
              category_name: true,
              category_slug: true,
              history_slug: true,  // サブカテゴリにもhistory_slugを追加
              order: true,
              created_at: true
            }
          }
        },
        orderBy: [
          { order: 'asc' },
          { created_at: 'asc' }
        ]
      });

      console.log('🔍 [getCategories] 取得結果:', allUserCategories.length, '件');
      allUserCategories.forEach((cat, index) => {
        console.log(`🔍 [getCategories] [${index}] ${cat.category_name}:`);
        console.log(`    history_slug: ${JSON.stringify(cat.history_slug)} (型: ${typeof cat.history_slug})`);
        console.log(`    category_slug: ${JSON.stringify(cat.category_slug)}`);
        console.log(`    parent_ID: ${JSON.stringify(cat.parent_ID)}`);
      });

      // console.log(`ユーザーの全カテゴリ: ${allUserCategories.length}件`);
      // allUserCategories.forEach(cat => {
      //   console.log(`カテゴリ: id=${cat.id}, name=${cat.category_name}, parent_ID=${cat.parent_ID}`);
      // });

      // トップレベルカテゴリを特定（parent_IDがnull、空文字、または自分自身のIDの場合）
      const categories = allUserCategories.filter(cat => {
        const isTopLevel = cat.parent_ID === null ||
                          cat.parent_ID === '' ||
                          cat.parent_ID === cat.id ||
                          cat.parent_ID === cat.category_ID;
        // console.log(`カテゴリ ${cat.category_name}: parent_ID=${cat.parent_ID}, isTopLevel=${isTopLevel}`);
        return isTopLevel;
      });

      // console.log(`ユーザーID=${user_ID}のカテゴリ取得結果: ${categories.length}件`);
      // if (categories.length > 0) {
      //   console.log('取得したカテゴリ:', categories.map(c => ({ id: c.id, name: c.category_name, order: c.order })));
      // }


      // console.log(`ユーザーID=${user_ID}の${categories.length}件の親カテゴリを取得しました`);
      
      /**
       * カテゴリデータを階層構造に変換する関数
       * @param categories 元のカテゴリ配列
       * @returns 親カテゴリとサブカテゴリの階層構造を持つ配列
       */
      const organizeCategories = (categories: any[]) => {
        // 結果の配列を初期化
        const result = categories.map(parent => {
          // 親カテゴリとそのサブカテゴリを含むオブジェクトを返す
          return {
            id: parent.id,
            category_ID: parent.category_ID, // category_IDを追加
            category_name: parent.category_name,
            category_slug: parent.category_slug, // category_slugを追加
            history_slug: parent.history_slug, // 🔧 history_slugを追加（重要！）
            user_ID: parent.user_ID,
            parent_ID: parent.parent_ID,
            order: parent.order || 0, // orderを追加
            subcategories: parent.Children
              .sort((a: any, b: any) => (a.order || 0) - (b.order || 0))
              .map((child: any) => ({
                id: child.id,
                category_ID: child.category_ID, // category_IDを追加
                category_name: child.category_name,
                category_slug: child.category_slug, // category_slugを追加
                history_slug: child.history_slug, // 🔧 サブカテゴリにもhistory_slugを追加
                user_ID: child.user_ID,
                parent_ID: child.parent_ID,
                order: child.order || 0
              }))
          };
        });

        return result;
      };
      
      // カテゴリデータを階層構造に変換
      const organizedCategories = organizeCategories(categories);

      // 🔧 変換後のhistory_slug確認ログ
      console.log('🔧 [getCategories] 変換後のhistory_slug確認:');
      organizedCategories.forEach((cat, index) => {
        console.log(`🔧 [${index}] ${cat.category_name}:`);
        console.log(`    変換後history_slug: ${JSON.stringify(cat.history_slug)} (型: ${typeof cat.history_slug})`);
      });
      
      // 取得したカテゴリの詳細をログに出力（デバッグ用）
      if (organizedCategories.length > 0) {
        console.log("階層構造に変換したカテゴリ:", JSON.stringify(organizedCategories, null, 2));
      } else {
        console.log(`ユーザーID=${user_ID}のカテゴリが見つかりませんでした`);
        
        // カテゴリが存在しない場合、データベースに直接確認
        const allCategoriesCheck = await prisma.category.findMany({
          take: 10 // 最大10件まで取得
        });
        
        console.log(`データベース内の全カテゴリ数: ${allCategoriesCheck.length}`);
        if (allCategoriesCheck.length > 0) {
          console.log("データベース内のカテゴリサンプル:", JSON.stringify(allCategoriesCheck.slice(0, 3), null, 2));
        }

      }

      // 階層構造のカテゴリデータをレスポンスとして返す
      return NextResponse.json(organizedCategories, {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        }
      });
    } catch (dbError: any) {
      return NextResponse.json(
        {
          error: "データベース操作中にエラーが発生しました",
          details: dbError.message || "不明なデータベースエラー",
          code: dbError.code
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    return NextResponse.json(
      {
        error: "カテゴリの取得中にエラーが発生しました",
        details: error.message || "不明なエラー"
      },
      { status: 500 }
    );
  }
}
