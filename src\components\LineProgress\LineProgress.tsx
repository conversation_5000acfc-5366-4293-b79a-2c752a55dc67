/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import PropTypes from "prop-types";
import React from "react";

interface Props {
  className: any;
  overlapGroupClassName: any;
  lineArrowClassName: any;
  lineArrow: string;
}

export const LineProgress = ({
  className,
  overlapGroupClassName,
  lineArrowClassName,
  lineArrow = "/static/img/linearrow.svg",
}: Props): JSX.Element => {
  return (
    <div className={`inline-flex items-start justify-center pt-[20px] pb-[40px] px-0 relative ${className}`}>
      <div className="relative w-[320px] h-[66px]">
        <div className={`relative h-[66px] ${overlapGroupClassName}`}>
          
          <div className="inline-flex items-center justify-center gap-[60px] absolute top-0 left-[52px]">
            <div className="inline-flex flex-col items-center gap-[6px] relative flex-[0_0_auto]">
              <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-black-1 text-[12px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
                連絡先認証
              </div>
              <div className="flex flex-col w-[46px] h-[46px] items-center justify-center gap-[10px] p-[10px] relative bg-button-yellow rounded-[100px] border-[3px] border-solid border-button-yellow">
                <div className="relative w-fit [font-family:'Roboto',Helvetica] font-normal text-black-1 text-[15px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
                  1
                </div>
              </div>
            </div>
            <div className="inline-flex flex-col items-center gap-[6px] relative flex-[0_0_auto]">
              <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-black-1 text-[12px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
                プロフィール設定
              </div>
              <div className="flex flex-col w-[46px] h-[46px] items-center justify-center gap-[10px] p-[10px] relative bg-white rounded-[100px] border-[3px] border-solid border-button-yellow">
                <div className="relative w-fit [font-family:'Roboto',Helvetica] font-normal text-black-1 text-[15px] text-center tracking-[0] leading-[normal] whitespace-nowrap">
                  2
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

LineProgress.propTypes = {
  lineArrow: PropTypes.string,
};
