/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  className: any;
}

export const TextTitle = ({ className }: Props): JSX.Element => {
  return (
    <div className={`flex w-[390px] items-center gap-[10px] pt-[16px] pb-0 px-[16px] relative ${className}`}>
      <div className="relative w-[358px] mt-[-1.00px] [font-family:'Roboto',Helvetica] font-bold text-black text-[18px] tracking-[0] leading-[21.6px]">
        あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよ
      </div>
    </div>
  );
};
