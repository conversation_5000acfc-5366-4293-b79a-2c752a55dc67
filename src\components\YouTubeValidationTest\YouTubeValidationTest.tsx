'use client';

import React, { useState } from 'react';
import { validateYouTubeURL, generateYouTubeEmbedHTML } from '../SocialEmbed';

/**
 * YouTubeのバリデーション機能をテストするためのコンポーネント
 */
const YouTubeValidationTest: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [validationResult, setValidationResult] = useState<string | null>(null);
  const [embedHtml, setEmbedHtml] = useState('');

  // テスト用のURL例
  const testUrls = [
    // 有効なURL
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://youtu.be/dQw4w9WgXcQ',
    'https://www.youtube.com/embed/dQw4w9WgXcQ',
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s',
    
    // 無効なURL
    'https://www.youtube.com/channel/UCuAXFkgsw1L7xaCfnd5JJOw',
    'https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMHjMZOz59Oq8_7Qd',
    'https://www.youtube.com/shorts/abc123',
    'https://www.youtube.com/user/username',
    'https://www.youtube.com/@username',
    'https://www.youtube.com/c/channelname',
    'https://example.com/not-youtube',
    'invalid-url'
  ];

  const handleTest = () => {
    const result = validateYouTubeURL(testUrl);
    setValidationResult(result);
    
    if (result) {
      const html = generateYouTubeEmbedHTML();
      setEmbedHtml(html);
    } else {
      setEmbedHtml('');
    }
  };

  const handleTestUrlClick = (url: string) => {
    setTestUrl(url);
    const result = validateYouTubeURL(url);
    setValidationResult(result);
    
    if (result) {
      const html = generateYouTubeEmbedHTML();
      setEmbedHtml(html);
    } else {
      setEmbedHtml('');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-6 text-gray-800">YouTube URL バリデーションテスト</h1>
      
      {/* 入力エリア */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          YouTube URL を入力してください:
        </label>
        <div className="flex gap-2">
          <input
            type="text"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="https://www.youtube.com/watch?v=..."
          />
          <button
            onClick={handleTest}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            テスト
          </button>
        </div>
      </div>

      {/* 結果表示 */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2 text-gray-800">バリデーション結果:</h3>
        <div className={`p-3 rounded-md ${validationResult ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {validationResult ? (
            <>
              <div className="font-semibold">✓ 有効な動画URL</div>
              <div className="text-sm">動画ID: {validationResult}</div>
            </>
          ) : (
            <div className="font-semibold">✗ 無効なURL</div>
          )}
        </div>
      </div>

      {/* 埋め込みHTML表示 */}
      {embedHtml && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-800">生成された埋め込みHTML:</h3>
          <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto">
            {embedHtml}
          </pre>
        </div>
      )}

      {/* テスト用URL一覧 */}
      <div>
        <h3 className="text-lg font-semibold mb-3 text-gray-800">テスト用URL一覧:</h3>
        <div className="space-y-2">
          <div className="text-sm font-medium text-green-600 mb-2">✓ 有効なURL（動画）:</div>
          {testUrls.slice(0, 4).map((url, index) => (
            <button
              key={index}
              onClick={() => handleTestUrlClick(url)}
              className="block w-full text-left px-3 py-2 bg-green-50 hover:bg-green-100 rounded-md text-sm text-green-700 border border-green-200"
            >
              {url}
            </button>
          ))}
          
          <div className="text-sm font-medium text-red-600 mb-2 mt-4">✗ 無効なURL（チャンネル・プレイリスト・ショートなど）:</div>
          {testUrls.slice(4).map((url, index) => (
            <button
              key={index + 4}
              onClick={() => handleTestUrlClick(url)}
              className="block w-full text-left px-3 py-2 bg-red-50 hover:bg-red-100 rounded-md text-sm text-red-700 border border-red-200"
            >
              {url}
            </button>
          ))}
        </div>
      </div>

      {/* 仕様説明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-md">
        <h3 className="text-lg font-semibold mb-2 text-blue-800">バリデーション仕様:</h3>
        <div className="text-sm text-blue-700 space-y-1">
          <div><strong>対応URL形式:</strong></div>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>https://www.youtube.com/watch?v=動画ID</li>
            <li>https://youtu.be/動画ID</li>
            <li>https://www.youtube.com/embed/動画ID</li>
          </ul>
          <div className="mt-2"><strong>除外URL形式:</strong></div>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>チャンネルURL: /channel/...</li>
            <li>プレイリスト: /playlist?...</li>
            <li>ショート動画: /shorts/...</li>
            <li>ユーザーページ: /user/..., /c/..., /@...</li>
          </ul>
          <div className="mt-2"><strong>動画ID:</strong> 11文字の英数字・記号（-, _ を含む）</div>
        </div>
      </div>
    </div>
  );
};

export default YouTubeValidationTest;
