// src/types/prisma.d.ts
import {User} from './User'
// src/types/prisma.d.ts
export type Category = {
  id: string; // Prisma's ObjectId
  user_ID: string; // User ID associated with the category
  CategoryUserID: User; // Relation: User who owns the category
  category_ID: string; // Unique category ID
  category_name: string; // Name of the category
  category_slug?: string | null; // Category slug for URL
  parent_ID?: string | null; // Parent category ID
  Parent?: Category | null; // Optional relation to the parent category
  Children: Category[]; // Array of child categories
  order: number; // Display order
  created_at: Date; // Date of creation
  updated_at: Date; // Date of last update
};
