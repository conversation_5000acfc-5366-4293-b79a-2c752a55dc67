'use client';

import React, { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { getUserId } from "../../contexts/UserContext";
import { ImageCropper } from "../ImageCropper";
import { normalizeImagePath, base64ToBlob } from "../../utils/imageUtils";
import { useImagePicker } from "../../hooks/useImagePicker";

interface InputListProps {
  profileImage?: string;
  backgroundImage?: string;
  onImageChange?: (imageUrl: string) => void;
  onBackgroundImageChange?: (imageUrl: string) => void;
  user_ID?: string;
  className?: string;
}

/**
 * InputListコンポーネント
 * プロフィール画像と背景画像の編集機能を提供します
 */
export const InputList = ({ 
  profileImage, 
  backgroundImage,
  onImageChange, 
  onBackgroundImageChange,
  user_ID,
  className
}: InputListProps): JSX.Element => {
  const [isLoading, setIsLoading] = useState(true);
  const [imgSrc, setImgSrc] = useState<string>("/static/img/defaultUserIcon.png");
  const [bgSrc, setBgSrc] = useState<string>("/static/img/imagebackground.png");
  const { user: clerkUser, isLoaded } = useUser();
  
  // プロフィール画像用のuseImagePicker
  const { 
    imgSrc: profileSrc, 
    pickFile: pickProfile,
    fileInputRef: profileFileInputRef,
    handleFileChange: handleProfileFileChange,
    showCropper: showProfileCropper,
    tempImage: tempProfileImage,
    aspect: profileAspect,
    handleCropComplete: handleProfileCropComplete,
    handleCropCancel: handleProfileCropCancel
  } = useImagePicker({
    aspect: 1,
    onDone: (imageData) => {
      // 前回の画像と比較して同じ場合は何もしない
      if (profileSrcRef.current === imageData) {
        return;
      }
      
      // 現在の画像を保存
      profileSrcRef.current = imageData;
      
      // プロフィール画像を設定
      setImgSrc(imageData);
      
      // コールバックがある場合のみ呼び出す、ただし状態更新の連鎖を防止するためにタイマーを使用
      if (onImageChange) {
        // タイマーを使用して状態更新の連鎖を防止
        setTimeout(() => {
          onImageChange(imageData);
        }, 0);
      }
    },
    defaultImage: "/static/img/profile-default.png"
  });

  // 背景画像用のuseImagePicker
  const { 
    imgSrc: _bgSrc, 
    pickFile: pickBg,
    fileInputRef: bgFileInputRef,
    handleFileChange: handleBgFileChange,
    showCropper: showBgCropper,
    tempImage: tempBgImage,
    aspect: bgAspect,
    handleCropComplete: handleBgCropComplete,
    handleCropCancel: handleBgCropCancel
  } = useImagePicker({
    aspect: 9/16,
    onDone: (imageData) => {
      // 前回の画像と比較して同じ場合は何もしない
      if (bgSrcRef.current === imageData) {
        return;
      }
      
      // 現在の画像を保存
      bgSrcRef.current = imageData;
      
      // 背景画像を設定
      setBgSrc(imageData);
      
      // コールバックがある場合のみ呼び出す、ただし状態更新の連鎖を防止するためにタイマーを使用
      if (onBackgroundImageChange) {
        // タイマーを使用して状態更新の連鎖を防止
        setTimeout(() => {
          onBackgroundImageChange(imageData);
        }, 0);
      }
    },
    defaultImage: "/static/img/imagebackground.png"
  });
  
  // ユーザーIDが渡されていない場合はClerkから取得
  const currentUserId = user_ID || (isLoaded && clerkUser?.id) || getUserId();

  // デバッグログ（頻繁な実行を避けるため、条件付きで出力）
  useEffect(() => {
    if (currentUserId) {
      console.log('🔍 [InputList] ユーザーID取得:', {
        user_ID,
        clerkUserId: clerkUser?.id,
        isLoaded,
        currentUserId,
        timestamp: new Date().toISOString()
      });
    }
  }, [currentUserId]); // currentUserIdが変更された時のみログ出力

  // プロフィール画像を取得
  useEffect(() => {
    let isMounted = true; // コンポーネントがマウントされているかを追跡

    // ユーザーが新しく画像を設定した場合は、データベースからの再取得を行わない
    // Base64形式の画像はユーザーが新しく設定した画像と判断
    if (imgSrc && imgSrc.startsWith('data:image')) {
      return;
    }

    // 直接渡されたプロフィール画像があればそれを使用
    if (profileImage && profileImage.trim() !== "") {
      // Blob URLの場合はデータベースから画像を取得する
      if (profileImage.startsWith('blob:')) {
        // データベースからプロフィール画像を取得する関数
        const fetchProfileImageFromDB = async () => {
          try {
            if (!isMounted) return;
            setIsLoading(true);

            // ユーザーIDが存在するか確認
            if (!currentUserId) {
              console.error("InputList - ユーザーIDが存在しないため、デフォルト画像を使用します");
              if (isMounted) {
                setImgSrc('/static/img/defaultUserIcon.png');
                setIsLoading(false);
              }
              return;
            }

            // APIからプロフィール画像を取得
            const response = await fetch(`/api/profile?user_ID=${currentUserId}`);
            if (response.ok && isMounted) {
              const data = await response.json();

              if (data.profileImage && data.profileImage.trim() !== "") {
                // パスの正規化
                const correctedPath = normalizeImagePath(data.profileImage);

                if (isMounted) {
                  setImgSrc(correctedPath);
                  if (onImageChange) {
                    onImageChange(correctedPath);
                  }
                  setIsLoading(false);
                }
                return;
              }
            }

            // データベースから画像が取得できなかった場合はデフォルト画像を設定
            if (isMounted) {
              setImgSrc('/static/img/profile-default.png');
              if (onImageChange) {
                onImageChange('/static/img/profile-default.png');
              }
            }
          } catch (error) {
            console.error("InputList - データベースからの画像取得エラー:", error);
            if (isMounted) {
              setImgSrc('/static/img/defaultUserIcon.png');
              if (onImageChange) {
                onImageChange('/static/img/defaultUserIcon.png');
              }
            }
          } finally {
            if (isMounted) {
              setIsLoading(false);
            }
          }
        };

        fetchProfileImageFromDB();
        return;
      }

      // パスの正規化
      const correctedPath = normalizeImagePath(profileImage);

      // プロフィール画像を設定
      if (isMounted) {
        setImgSrc(correctedPath);

        if (onImageChange) {
          onImageChange(correctedPath);
        }
        setIsLoading(false);
      }
      return;
    }

    // プロフィール画像がpropsから渡されていない場合はAPIから取得
    // ただし、ユーザーIDがない場合は取得しない
    if (!currentUserId) {
      if (isMounted) {
        setIsLoading(false);
      }
      return;
    }

    const fetchProfileImage = async () => {
      try {
        if (!isMounted) return;
        setIsLoading(true);

        // APIからプロフィール画像を取得
        const response = await fetch(`/api/profile?user_ID=${currentUserId}`);
        if (response.ok && isMounted) {
          const data = await response.json();

          if (data.profileImage && data.profileImage.trim() !== "") {
            // パスの正規化
            const correctedPath = normalizeImagePath(data.profileImage);

            // 画像を設定
            setImgSrc(correctedPath);

            // 重複処理を防ぐためのチェック
            if (onImageChange && correctedPath !== profileSrcRef.current) {
              profileSrcRef.current = correctedPath;
              onImageChange(correctedPath);
            }
          }
        } else if (response.ok === false) {
          console.error("InputList - APIエラー:", await response.text());
        }
      } catch (error) {
        console.error("InputList - エラー発生:", error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchProfileImage();

    // クリーンアップ関数
    return () => {
      isMounted = false;
    };
  }, [profileImage, currentUserId]); // onImageChangeを依存配列から除外して無限ループを防止

  // 直前の背景画像を保持する ref
  const bgSrcRef = React.useRef<string | null>(null);

  // 直前のプロフィール画像を保持する ref
  const profileSrcRef = React.useRef<string | null>(null);
  // 背景画像を取得
  useEffect(() => {
    let isMounted = true; // コンポーネントがマウントされているかを追跡

    // ユーザーが新しく背景画像を設定した場合は、データベースからの再取得を行わない
    // Base64形式の画像はユーザーが新しく設定した画像と判断
    if (bgSrc && bgSrc.startsWith('data:image')) {
      return;
    }

    // 直接渡された背景画像があればそれを使用
    if (backgroundImage && backgroundImage.trim() !== "") {
      // パスの正規化
      const correctedPath = normalizeImagePath(backgroundImage);

      // 前回の画像と比較して同じ場合は何もしない
      if (bgSrcRef.current === correctedPath) {
        return;
      }

      // 現在の画像を保存
      bgSrcRef.current = correctedPath;

      // 背景画像を設定
      if (isMounted) {
        setBgSrc(correctedPath);

        if (onBackgroundImageChange) {
          onBackgroundImageChange(correctedPath);
        }
      }
      return;
    }

    // ユーザーIDがない場合は取得しない
    if (!currentUserId) {
      return;
    }

    // 背景画像がpropsから渡されていない場合はAPIから取得
    const fetchBackgroundImage = async () => {
      try {
        if (!isMounted) return;

        // APIから背景画像を取得
        const response = await fetch(`/api/getUser?user_ID=${currentUserId}`);
        if (!response.ok) {
          throw new Error(`APIエラー: ${response.status}`);
        }

        const data = await response.json();

        // 背景画像URLがあれば設定
        if (data.background_image && isMounted) {
          // パスの正規化
          const correctedPath = normalizeImagePath(data.background_image);

          // 直前に処理済みなら何もしない
          if (bgSrcRef.current === correctedPath) {
            return;
          }

          // 現在の画像を保存
          bgSrcRef.current = correctedPath;

          // 背景画像を設定
          setBgSrc(correctedPath);
        }
      } catch (error) {
        console.error("InputList - 背景画像取得エラー:", error);
      }
    };

    fetchBackgroundImage();

    // クリーンアップ関数
    return () => {
      isMounted = false;
    };
  }, [backgroundImage, currentUserId]); // onBackgroundImageChangeを依存配列から除外して無限ループを防止

  // 背景画像をデータベースに直接保存する場合のハンドラ
  const handleBgCropCompleteWithDbSave = (croppedImage: string) => {
    // 親コンポーネントから背景画像変更ハンドラが渡されていない場合は、
    // 直接APIを呼び出して背景画像を保存する
    if (!onBackgroundImageChange) {
      saveBackgroundImageToDatabase(croppedImage);
    } else {
      handleBgCropComplete(croppedImage);
    }
  };

  // 背景画像をデータベースに保存する関数
  const saveBackgroundImageToDatabase = async (imageData: string) => {
    try {
      
      // ユーザーIDを取得（セッションまたはコンテキストから）
      const sessionResponse = await fetch('/api/getSession');
      if (!sessionResponse.ok) {
        const errorText = await sessionResponse.text();
        return;
      }
      
      const sessionData = await sessionResponse.json();
      
      // ユーザーIDを取得
      const userId = sessionData.user?.id || currentUserId;
      if (!userId) {
        return;
      }
      
      // Base64画像をBlobに変換
      const blob = base64ToBlob(imageData);
      
      // FormDataを作成
      const formData = new FormData();
      formData.append('image', blob, 'background.jpg');
      formData.append('user_ID', userId);
      formData.append('imageType', 'background'); // 背景画像として指定
      
      // 画像アップロードAPIを呼び出し
      const uploadResponse = await fetch('/api/uploadImage', {
        method: 'POST',
        body: formData,
      });
      
      if (!uploadResponse.ok) {
        return;
      }
      
      const uploadData = await uploadResponse.json();
      
      // アップロードされた画像のパスを取得
      const uploadedImagePath = uploadData.imagePath;
      
      if (!uploadedImagePath) {
        return;
      }
      
      // ユーザー情報を更新して背景画像を設定;
      
      // 画像パスが長すぎる場合は切り詰める（データベースの制約に合わせる）
      let processedImagePath = uploadedImagePath;
      if (typeof processedImagePath === 'string' && processedImagePath.length > 255) {
        // 拡張子を維持しながら切り詰める
        const extension = processedImagePath.split('.').pop() || '';
        processedImagePath = processedImagePath.substring(0, Math.max(0, 250 - extension.length)) + '.' + extension;
      } else if (typeof processedImagePath !== 'string') {
        processedImagePath = String(processedImagePath || '');
        if (processedImagePath.length > 255) {
          const extension = processedImagePath.split('.').pop() || '';
          processedImagePath = processedImagePath.substring(0, Math.max(0, 250 - extension.length)) + '.' + extension;
        }
      }
       
      // ユーザー情報更新APIを呼び出し
      const updateResponse = await fetch('/api/updateUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          background_image: processedImagePath
        }),
      });
      
      if (!updateResponse.ok) {
        return;
      }
      
      const updateData = await updateResponse.json();
      
      // 背景画像のURLを更新
      if (onBackgroundImageChange) {
        onBackgroundImageChange(processedImagePath);
      }
      
    } catch (error) {
    }
  };

  return (
    <div className={`flex flex-col ${className || ''}`}>
      {/* プロフィール画像 */}
      <div className="flex max-w-[500px] justify-between items-center relative bg-white border-b border-line">
        <div className="flex max-w-[250px] h-[48px] items-center pl-[16px] pr-0 py-0 relative">
          <div className="relative w-fit text-black-1 text-[14px] tracking-[0] leading-[normal] whitespace-nowrap">
            プロフィール画像
          </div>
        </div>
        <div className="flex w-[52px] h-[48px] items-center justify-end pl-0 pr-[16px] py-0 relative bg-white">
          <button
            type="button"
            onClick={pickProfile}
            className="relative w-[30px] h-[30px] p-0 border-0 bg-transparent"
            aria-label="プロフィール画像を変更"
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={imgSrc}
              alt="プロフィール画像"
              className="w-[30px] h-[30px] object-cover rounded-full"
            />
          </button>
          <input
            type="file"
            accept="image/*"
            onChange={handleProfileFileChange}
            ref={profileFileInputRef}
            style={{ display: "none" }}
          />
        </div>
      </div>

      {/* 背景画像 */}
      <div className="flex max-w-[500px] justify-between items-center relative bg-white border-b border-line">
        <div className="flex max-w-[250px] h-[48px] items-center pl-[16px] pr-0 py-0 relative">
          <div className="relative w-fit text-black-1 text-[14px] tracking-[0] leading-[normal] whitespace-nowrap">
            背景画像
          </div>
        </div>
        <div className="flex w-[52px] h-[48px] items-center justify-end pl-0 pr-[16px] py-0 relative bg-white">
          <button
            type="button"
            onClick={pickBg}
            className="relative w-[30px] h-[30px] p-0 border-0 bg-transparent"
            aria-label="背景画像を変更"
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={bgSrc}
              alt="背景画像"
              className="w-[30px] h-[30px] object-cover rounded"
            />
          </button>
          <input
            type="file"
            accept="image/*"
            onChange={handleBgFileChange}
            ref={bgFileInputRef}
            style={{ display: "none" }}
          />
        </div>
      </div>

      {/* プロフィール画像クロッパー */}
      {showProfileCropper && tempProfileImage && (
        <ImageCropper
          image={tempProfileImage}
          aspectRatio={profileAspect}
          onCropComplete={handleProfileCropComplete}
          onCancel={handleProfileCropCancel}
          isProfileImage={true}
        />
      )}
      
      {/* 背景画像クロッパー */}
      {showBgCropper && tempBgImage && (
        <ImageCropper
          image={tempBgImage}
          aspectRatio={bgAspect}
          onCropComplete={handleBgCropCompleteWithDbSave}
          onCancel={handleBgCropCancel}
          isProfileImage={false}
        />
      )}
    </div>
  );
};
