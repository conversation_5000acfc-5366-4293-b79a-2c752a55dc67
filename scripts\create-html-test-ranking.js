const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createHtmlTestRanking() {
  console.log('Creating test ranking with HTML content...');
  
  try {
    // 1. 既存のユーザーを確認
    console.log('\n1. Checking existing users...');
    const users = await prisma.user.findMany({
      select: {
        user_ID: true,
        domain: true,
        name: true
      },
      take: 3
    });
    
    if (users.length === 0) {
      console.log('❌ No users found. Please create a user first.');
      return;
    }
    
    console.log('Available users:');
    users.forEach(user => {
      console.log(`- ${user.name} (ID: ${user.user_ID}, Domain: ${user.domain})`);
    });

    // 2. 既存のカテゴリを確認
    console.log('\n2. Checking existing categories...');
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        category_ID: true,
        category_name: true,
        user_ID: true,
        parent_ID: true
      },
      take: 5
    });
    
    if (categories.length === 0) {
      console.log('❌ No categories found. Please create a category first.');
      return;
    }
    
    console.log('Available categories:');
    categories.forEach(category => {
      console.log(`- ${category.category_name} (ID: ${category.id}, User: ${category.user_ID})`);
    });

    // 3. テスト用のHTML文字列を含むランキングデータを作成
    console.log('\n3. Creating test ranking with HTML content...');
    
    const testUser = users[0];
    const testCategory = categories.find(cat => cat.user_ID === testUser.user_ID) || categories[0];
    
    // HTML文字列を含むテストデータ
    const htmlContent = `
<p>この商品は<strong>本当におすすめ</strong>です！</p>
<p>特に以下の点が素晴らしいです：</p>
<ul>
  <li>品質が<em>非常に高い</em></li>
  <li>コストパフォーマンスが良い</li>
  <li>デザインが<u>美しい</u></li>
</ul>
<blockquote>
  <p>「これまで使った中で最高の商品です」</p>
</blockquote>
<p><a href="https://example.com" target="_blank">公式サイト</a>もチェックしてみてください。</p>
<h3 style="color: red; text-decoration: underline;">まとめ</h3>
<p>総合的に見て、この商品は<strong>5つ星</strong>の評価に値します。</p>
    `.trim();

    const testRankingData = {
      ranking_ID: `html_test_rank_${Date.now()}`,
      user_ID: testUser.user_ID,
      domain: testUser.domain,
      ranking_title: 'HTML表示テスト用ランキング',
      ranking_description: htmlContent,
      amazon_url: 'https://amazon.co.jp/test-html-product',
      rakuten_url: 'https://rakuten.co.jp/test-html-product',
      yahoo_url: 'https://shopping.yahoo.co.jp/test-html-product',
      qoo10_url: null,
      official_url: 'https://example.com/test-html-product',
      recommend_rate: 5,
      thumbnail_image: ['https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=HTML+Test'],
      subCategory_ID: testCategory.id,
      order: 1
    };

    console.log('Creating test ranking with HTML data:', {
      ...testRankingData,
      ranking_description: testRankingData.ranking_description.substring(0, 100) + '...'
    });
    
    const createdRanking = await prisma.ranking.create({
      data: testRankingData
    });
    
    console.log('✅ HTML test ranking created successfully!');
    console.log('Ranking ID:', createdRanking.ranking_ID);
    console.log('User ID:', createdRanking.user_ID);
    console.log('Access URL:', `http://localhost:3001/mypageReleaseDetail/${createdRanking.user_ID}/${createdRanking.ranking_ID}`);
    
    // 4. 作成されたランキングを確認
    console.log('\n4. Verifying created ranking...');
    const verifyRanking = await prisma.ranking.findFirst({
      where: {
        ranking_ID: createdRanking.ranking_ID
      },
      select: {
        ranking_ID: true,
        ranking_title: true,
        ranking_description: true,
        user_ID: true
      }
    });
    
    if (verifyRanking) {
      console.log('✅ Ranking verification successful');
      console.log('HTML content length:', verifyRanking.ranking_description.length);
      console.log('HTML content preview:', verifyRanking.ranking_description.substring(0, 200) + '...');
    } else {
      console.log('❌ Ranking verification failed');
    }

  } catch (error) {
    console.error('❌ Error during HTML test ranking creation:', error);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.meta) {
      console.error('Error meta:', error.meta);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 実行
createHtmlTestRanking();
