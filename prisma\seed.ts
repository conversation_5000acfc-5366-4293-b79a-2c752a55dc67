import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';

// 環境変数を読み込む
dotenv.config();

const prisma = new PrismaClient();

async function main() {
  try {
    // ユーザーIDが環境変数で指定されていない場合はエラーを出力して終了
    if (!process.env.DEFAULT_USER_ID) {
      process.exit(1);
    }
    
    const userId = process.env.DEFAULT_USER_ID;
    
    // 既存のカテゴリを確認
    const existingCategories = await prisma.category.findMany({
      where: {
        user_ID: userId,
        parent_ID: null
      }
    });
    
    
  } catch (error) {

    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
