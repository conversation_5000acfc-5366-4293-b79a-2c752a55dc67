# OAuth Authentication Test Plan

## Test Steps

1. **Clear Browser Data**
   - Clear all cookies, localStorage, and sessionStorage
   - Open a new incognito/private window

2. **Navigate to Sign-Up Page**
   - Go to `http://localhost:3000/sign-up`
   - Verify the page loads correctly
   - Check that Google OAuth button is visible

3. **Initiate OAuth Flow**
   - Click the Google OAuth button
   - Verify redirect to Google authentication
   - Complete Google authentication

4. **Verify Callback Processing**
   - Check if redirected to `/sso-callback` or `/sign-up#/continue`
   - Monitor console logs for authentication detection
   - Verify Supabase user registration

5. **Confirm Final Redirect**
   - Ensure final redirect to `/setup` page
   - Verify user is properly authenticated

## Expected Console Logs

### Sign-Up Page
- `🔥🔥🔥 [SignUp] BUTTON CLICKED - FIRST LOG`
- `✅ [OAuth-Start] 新規登録OAuth認証開始`
- `🔍 [OAuth-Start] リダイレクトURL設定`

### SSO Callback or Continue Hash
- `🔍 [SignUp] OAuth認証完了ハッシュ検出` (if #/continue)
- `🔍 [SSO-Callback] ページ初期化` (if sso-callback)
- `✅ [AuthSuccess] 認証成功処理開始`
- `✅ [Supabase] ユーザー情報登録成功`
- `🔄 [AuthSuccess] setupページにリダイレクト`

## Success Criteria

- [ ] User successfully authenticates with Google
- [ ] User data is saved to Supabase database
- [ ] User is redirected to `/setup` page
- [ ] No authentication errors in console
- [ ] Process completes within 15 seconds

## Troubleshooting

If authentication fails:
1. Check Clerk dashboard OAuth settings
2. Verify redirect URLs are configured correctly
3. Check browser console for specific error messages
4. Ensure environment variables are properly set
