"use client";

import React from "react";
import { HeaderItems } from "@/components/HeaderItems";
import { Footer } from "@/components/Footer";

export default function LegalNotice() {
  return (
    <div className="max-w-[500px] mx-auto flex flex-col min-h-screen relative bg-white">
      <HeaderItems text="特定商取引法に基づく表記" />

      {/* メインコンテンツ */}
      <main className="w-full flex-grow p-4">
        <div className="bg-white">
          <h1 className="text-[18px] font-bold text-[#313131] mb-6">特定商取引法に基づく表記</h1>
          
          <table className="w-full border-collapse mb-8">
            <tbody>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)] w-1/3">事業者名</th>
                <td className="p-3 text-[14px] text-[#313131]">Affiliencer.LLC</td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">代表者名</th>
                <td className="p-3 text-[14px] text-[#313131]">水谷昌裕</td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">所在地</th>
                <td className="p-3 text-[14px] text-[#313131]">〒810-0044<br />福岡県福岡市博多区博多駅前1丁目23番2号ParkFront博多駅前1丁目5F-B</td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">連絡先</th>
                <td className="p-3 text-[14px] text-[#313131]">
                  メールアドレス: <EMAIL><br />
                  ※お問い合わせはメールでお願いいたします
                </td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">サービス名</th>
                <td className="p-3 text-[14px] text-[#313131]">mypicks.best（マイピックスベスト）</td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">料金</th>
                <td className="p-3 text-[14px] text-[#313131]">
                  Webサイトに表示されています。<br />
                </td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">支払方法</th>
                <td className="p-3 text-[14px] text-[#313131]">
                  クレジットカード決済
                </td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">支払時期</th>
                <td className="p-3 text-[14px] text-[#313131]">
                  -
                </td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">サービス提供時期</th>
                <td className="p-3 text-[14px] text-[#313131]">
                  お支払い確認後、即時にサービスを提供いたします
                </td>
              </tr>
              <tr className="border-b border-[var(--line-color)]">
                <th className="text-left p-3 text-[14px] text-[#313131] font-medium bg-[rgba(246,247,248,0.5)]">返品・キャンセル</th>
                <td className="p-3 text-[14px] text-[#313131]">
                  デジタルコンテンツの性質上、購入後のキャンセル・返金はお受けできません。
                </td>
              </tr>
            </tbody>
          </table>

          <section className="mb-8">
            <h2 className="text-[16px] font-bold text-[#313131] mb-3">サービス利用に関する注意事項</h2>
            <p className="text-[14px] text-[#313131] mb-2">
              本サービスは、インターネット接続環境が必要です。通信料はお客様のご負担となります。
              また、システムメンテナンス等によりサービスを一時停止する場合がございます。あらかじめご了承ください。
            </p>
          </section>

          <p className="text-[12px] text-[#AAAAAA] text-right mt-8">
            最終更新日: 2025年4月7日
          </p>
        </div>
      </main>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
