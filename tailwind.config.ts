import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      // カスタムz-index値
      zIndex: {
        '9999': '9999',
        '10000': '10000',
        'modal': '10000',
        'modal-overlay': '9999',
      },
      // カスタムカラー 
      colors:{
        'primary':'var(--button-primary)',
        button: {
          DEFAULT: 'var(--button-yellow)',
          red: 'var(--button-red)',
          black:'var(--button-black)',
          gray:'var(--button-gray)',
          gray2:'var(--button-gray2)',
        },
        font: {
          DEFAULT: 'var(--button-black)',
          gray: 'var(--button-gray)',
          red: 'var(--button-red)',
          link: 'var(--button-link)',
          link2: 'var(--button-link2)',
        },
        background: {
          DEFAULT: 'var(--background-black)',
          lightBlack: 'var(--background-light-black)',
          gray: 'var(--background-gray)',
        },
        line:{
          DEFAULT: 'var(--line-color)',
          light: 'var(--line-color-light)',
        }
      },
      // ここに追加（グラデーションアニメーション）
      animation: {
        'gradient-move': 'gradient 15s ease infinite',
      },
      keyframes: {
        gradient: {
          '0%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
          '100%': { backgroundPosition: '0% 50%' },
        },
      },
      backgroundSize: {
        '400': '400% 400%',
      },
    },
    // カスタムフォント
    fontFamily:{
        'roboto': ['Roboto', 'sans-serif'],
        'noto-sans-jp': ['Noto Sans JP', 'sans-serif'],
    }
  },
  plugins: [],
}

export default config
