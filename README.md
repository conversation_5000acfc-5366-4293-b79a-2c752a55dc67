This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.


## ディレクトリ構成
### 考え方
- できるだけ部品は分けて管理したい
  - あとでゴチャゴチャになるのは避けたい
- 細かくしすぎて分かりにくくもしたくない
- 最低限の分け方で管理する

### ディレクトリ
- prisma → Supabaseを操作する機能は全部ここに入れる？
- public　→ 静的ファイルの管理用だが、cloud storage使うからいらない
- src
  - app
    - api　→　バックエンドの機能はすべてここ
      - get →　取得
      - post　→　作成
      - put 　→　更新
    - components　→　UIの部品管理する場所
      - atoms　→　独立して使用できるUI
      - molecules → atomsを複数使って構成するUI
      - templates → ページ全体のUIやページへの制御など含めたもの
    - pages → ルーティング先はここにしたい。基本componens/templatesから引っ張ってきて表示させるだけ
    - 　utils → モジュール化されたロジック管理
  - state #ここの使い方確認したい。状態管理？
- styles　→ cssの管理
- types → tsの型定義

## コーディングルール
### 考え方
- 最低限の分け方で管理する

### ルール
- 変数,関数：頭文字小文字のキャメルケース（testButton）
  - 変数名は長くなってもいいので分かりやすく
  - ブール値は必ず先頭にisを（isTrue）
  - 配列は必ずsつける（items）
- 定数：全大文字のスネークケース（DATABASE_URL）
- 環境変数：全大文字のスネークケース（DATABASE_URL）
- クラス：頭文字大文字のキャメルケース（TestButton）
- 型：頭文字大文字のキャメルケース（TestButton）

### その他
- 複雑な処理はutilsの中でモジュール化する
- フォント、カラーは統一で管理する。直接（#****）とかで指定しない
- コメントでガンガン説明して！！！


## API
### 方針
- API仕様はredocを使って管理する

### 使い方
```bash
npm i
cd /backend_api
npx @redocly/cli build-docs swagger.yaml
```

## フロントエンドロジック

### ページ構成
- **CreateAccountPage**: アカウント作成ページで、ユーザーがLINEまたはGoogleで登録するためのボタンを提供します。進捗状況を示すためのタイトルとラインプログレスバーが含まれています。
- **MypageEditPage**: ユーザーのマイページ編集を行うページで、アニメーションのトグル機能を持ち、カテゴリやサブカテゴリの選択が可能です。
- **MypageReleasePage**: ユーザーの公開ページで、ユーザー情報とランキングデータを表示します。カテゴリとサブカテゴリに基づいてランキングをフィルタリングする機能があります。
- **MypageReleaseDetailPage**: 特定のランキングの詳細を表示するページで、ランキングの評価を星で表示します。
- **ProfileEditPage**: プロフィール編集ページで、ユーザーの基本情報やSNSリンクを編集できます。
- **RankingRegisterPage**: ランキング登録ページで、ユーザーが新しいランキングを作成し、画像やカテゴリを選択することができます。

### 共通ロジック
- **データ取得**: 各ページは、必要なデータを取得するために`useEffect`フックを使用し、APIエンドポイントにリクエストを送信します。
- **状態管理**: `useState`フックを使用して、ユーザー情報、ランキングデータ、カテゴリ情報などの状態を管理します。
- **エラーハンドリング**: APIリクエストが失敗した場合、詳細なエラーメッセージをコンソールに出力し、ユーザーに通知します。
- **UIコンポーネント**: 各ページは、再利用可能なUIコンポーネントを使用して、コードの重複を避け、可読性を向上させています。


メモ
version: '3'
services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_SUPABASE_URL: ${NEXT_PUBLIC_SUPABASE_URL}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      DATABASE_URL: ${DATABASE_URL}
