import React, { useState, ChangeEvent } from 'react';

interface ButtonImageSaveWrapperProps {
  className?: any;
  onImagesChange?: (image: string[]) => void;
}

export const ButtonImageSaveWrapper = ({
  onImagesChange,
}: ButtonImageSaveWrapperProps): JSX.Element => {
  const [images, setImages] = useState<string[]>(['']); // 最初は1つの空スロット

  const handleImageChange = (index: number, event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 既存のBlobURLを解放
      if (images[index] && images[index].startsWith('blob:')) {
        URL.revokeObjectURL(images[index]);
      }
      
      // 新しいBlobURLを作成
      const objectUrl = URL.createObjectURL(file);
      console.log(`画像 ${index + 1} のBlobURL作成: ${objectUrl}`);
      
      const updatedImages = [...images];
      updatedImages[index] = objectUrl;

      // 新たな空スロットを追加（最大4つまで）
      if (updatedImages.length < 4 && !updatedImages.includes('')) {
        updatedImages.push('');
      }

      setImages(updatedImages);
      
      // 空の値を除外して親コンポーネントに通知
      const nonEmptyImages = updatedImages.filter(img => img !== '');
      console.log(`親コンポーネントに通知する画像数: ${nonEmptyImages.length}`);
      onImagesChange?.(nonEmptyImages);
    }
  };

  const handleDelete = (index: number) => {
    // BlobURLを解放
    if (images[index] && images[index].startsWith('blob:')) {
      console.log(`画像 ${index + 1} のBlobURL解放: ${images[index]}`);
      URL.revokeObjectURL(images[index]);
    }
    
    const updatedImages = [...images];
    updatedImages[index] = '';

    // 最後の空スロット以外に空があれば削除
    const nonEmpty = updatedImages.filter((img) => img !== '');
    if (nonEmpty.length < 4) {
      updatedImages.length = nonEmpty.length;
      updatedImages.push('');
    }

    setImages(updatedImages);
    
    // 空の値を除外して親コンポーネントに通知
    const nonEmptyImages = updatedImages.filter(img => img !== '');
    console.log(`親コンポーネントに通知する画像数: ${nonEmptyImages.length}`);
    onImagesChange?.(nonEmptyImages);
  };

  const handleClick = (index: number) => {
    const inputElement = document.getElementById(`image-input-${index}`) as HTMLInputElement;
    inputElement?.click();
  };

  return (
    <div className="w-full gap-4 p-4 flex items-start relative !flex-[0_0_auto] flex-wrap">
      {images.map((image: string, index: number) => (
        <div
          key={index}
          className="relative flex flex-col w-[76px] h-[76px] items-start bg-[rgba(246,247,248,1)] rounded-md overflow-hidden"
          onClick={() => handleClick(index)}
        >
          <input
            type="file"
            accept="image/*"
            id={`image-input-${index}`}
            style={{ display: 'none' }}
            onChange={(event) => handleImageChange(index, event)}
          />

          {/* 左上ナンバー */}
          <div className="absolute top-0 left-0 w-5 h-5 flex items-center justify-center bg-background rounded-tl-md z-10">
            <div className="text-white text-sm font-bold">{index + 1}</div>
          </div>

          {/* 右上削除ボタン */}
          {image && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(index);
              }}
              className="absolute top-0 right-0 w-5 h-5 flex items-center justify-center text-white text-xs font-bold bg-black/50 z-10"
            >
              ×
            </button>
          )}

          {/* 画像 or カメラアイコン（追加ボタンは1つだけ） */}
          {image ? (
            <img
              src={image}
              alt={`選択された画像 ${index + 1}`}
              className="w-full h-full object-cover"
            />
          ) : (
            !images.some((img, i) => img === '' && i !== index) && (
              <div className="flex flex-col w-full h-full items-center justify-center gap-0.5">
                <img
                  className="w-[22px] h-[22px] object-cover"
                  alt="カメラアイコン"
                  src="https://c.animaapp.com/GmJTluXx/img/<EMAIL>"
                />
                {index === 0 && <div className="text-button-red text-xs">必須</div>}
              </div>
            )
          )}
        </div>
      ))}
    </div>
  );
};
