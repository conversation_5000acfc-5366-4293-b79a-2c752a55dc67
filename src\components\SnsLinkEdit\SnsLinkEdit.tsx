"use client";

import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import Image from "next/image";

interface SnsLinkType {
  id?: string;
  sns_ID: number;
  sns_name: string;
  sns_image: string;
  account_ID: string;
  account_type?: string | null;
}

interface Props {
  className?: string;
  userId: string;
  onSnsLinksChange?: (links: SnsLinkType[]) => void;
}

export const SnsLinkEdit = ({
  className,
  userId,
  onSnsLinksChange
}: Props): JSX.Element => {
  const [snsLinks, setSnsLinks] = useState<SnsLinkType[]>([]);
  const [availableSns, setAvailableSns] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSns, setSelectedSns] = useState("");
  const [accountId, setAccountId] = useState("");

  // SNSリンクとSNSマスターデータを取得
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // ユーザーのSNSリンクを取得
        const snsResponse = await fetch(`/api/snsUser?user_ID=${userId}`);
        const snsData = await snsResponse.json();
        
        if (snsData.snsLinks) {
          setSnsLinks(snsData.snsLinks);
          if (onSnsLinksChange) {
            onSnsLinksChange(snsData.snsLinks);
          }
        }
        
        // 利用可能なSNSマスターデータを取得
        const snsMasterResponse = await fetch('/api/getSnsMaster');
        const snsMasterData = await snsMasterResponse.json();
        
        if (snsMasterData.snsMasters) {
          setAvailableSns(snsMasterData.snsMasters);
        }
        
        setLoading(false);
      } catch (error) {
        console.error("SNSデータの取得中にエラーが発生しました:", error);
        setLoading(false);
        toast.error("SNSデータの取得に失敗しました");
      }
    };

    if (userId) {
      fetchData();
    }
  }, [userId, onSnsLinksChange]);

  // SNSリンクを追加または更新
  const handleAddSnsLink = async () => {
    if (!selectedSns || !accountId) {
      toast.error("SNSとアカウントIDを入力してください");
      return;
    }

    try {
      // 選択されたSNSの情報を取得
      const selectedSnsInfo = availableSns.find(sns => sns.sns_name === selectedSns);
      
      if (!selectedSnsInfo) {
        toast.error("選択されたSNSが見つかりません");
        return;
      }

      const response = await fetch('/api/snsUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_ID: userId,
          sns_name: selectedSns,
          account_ID: accountId
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(data.message);
        
        // 既存のリンクを更新または新しいリンクを追加
        const updatedLinks = [...snsLinks];
        const existingLinkIndex = updatedLinks.findIndex(link => link.sns_name === selectedSns);
        
        if (existingLinkIndex >= 0) {
          updatedLinks[existingLinkIndex] = {
            ...updatedLinks[existingLinkIndex],
            account_ID: accountId
          };
        } else {
          updatedLinks.push({
            id: data.snsLink.id,
            sns_ID: selectedSnsInfo.sns_ID,
            sns_name: selectedSns,
            sns_image: selectedSnsInfo.sns_image,
            account_ID: accountId
          });
        }
        
        setSnsLinks(updatedLinks);
        if (onSnsLinksChange) {
          onSnsLinksChange(updatedLinks);
        }
        
        // フォームをリセット
        setSelectedSns("");
        setAccountId("");
      } else {
        toast.error(data.error || "SNSリンクの保存に失敗しました");
      }
    } catch (error) {
      console.error("SNSリンクの保存中にエラーが発生しました:", error);
      toast.error("SNSリンクの保存に失敗しました");
    }
  };

  // SNSリンクを削除
  const handleDeleteSnsLink = async (id: string) => {
    try {
      const response = await fetch(`/api/snsUser?id=${id}&user_ID=${userId}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(data.message);
        
        // 削除されたリンクを除外
        const updatedLinks = snsLinks.filter(link => link.id !== id);
        setSnsLinks(updatedLinks);
        
        if (onSnsLinksChange) {
          onSnsLinksChange(updatedLinks);
        }
      } else {
        toast.error(data.error || "SNSリンクの削除に失敗しました");
      }
    } catch (error) {
      console.error("SNSリンクの削除中にエラーが発生しました:", error);
      toast.error("SNSリンクの削除に失敗しました");
    }
  };

  if (loading) {
    return <div className="text-center py-4">Loading...</div>;
  }

  return (
    <div className={`w-full max-w-[500px] ${className}`}>
      {/* ヘッダー */}
      <div className="flex max-w-[500px] h-[46px] items-center pl-[16px] pr-0 py-0 relative border-y border-y-[var(--line-color)] bg-[rgba(246,247,248,1)]">
        <p className="relative w-fit text-font-gray text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
          <span className="text-[rgb(170,170,170)] text-[12px] tracking-[0]">SNSリンク</span>
        </p>
      </div>

      {/* 既存のSNSリンク一覧 */}
      {snsLinks.length > 0 ? (
        <div className="w-full">
          {snsLinks.map((link) => (
            <div key={link.id} className="flex max-w-[500px] justify-between items-center relative bg-white border-b [border-bottom-style:solid] border-[#DDDDDD]">
              <div className="flex w-[40%] h-[48px] items-center gap-[6px] pl-[16px] pr-0 py-0 relative">
                <Image
                  className="relative w-[22px] h-[22px] ml-[-1.00px] object-cover"
                  alt={link.sns_name}
                  src={link.sns_image}
                  width={22}
                  height={22}
                  priority
                />
                <div className="relative w-fit text-[#313131] text-[14px] tracking-[0] leading-[normal] whitespace-nowrap">
                  {link.sns_name}
                </div>
              </div>
              <div className="flex w-[60%] h-[48px] items-center justify-end pl-[6px] pr-[16px] py-0 relative bg-white">
                <div className="flex items-center justify-between w-full">
                  <span className="text-[#313131] text-[12px] tracking-[0] leading-[normal]">
                    {link.account_ID}
                  </span>
                  <button
                    onClick={() => handleDeleteSnsLink(link.id!)}
                    className="ml-2 text-red-500 text-[12px]"
                  >
                    削除
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-4 text-[#AAAAAA] text-[14px]">
          SNSリンクがありません
        </div>
      )}

      {/* 新しいSNSリンクを追加 */}
      <div className="mt-4 p-4 bg-white border border-[#DDDDDD] rounded">
        <h3 className="text-[14px] font-medium text-[#313131] mb-3">SNSリンクを追加</h3>
        
        <div className="mb-3">
          <label className="block text-[12px] text-[#AAAAAA] mb-1">SNS</label>
          <select
            value={selectedSns}
            onChange={(e) => setSelectedSns(e.target.value)}
            className="w-full p-2 border border-[#DDDDDD] rounded text-[14px] text-[#313131]"
          >
            <option value="">選択してください</option>
            {availableSns.map((sns) => (
              <option key={sns.sns_ID} value={sns.sns_name}>
                {sns.sns_name}
              </option>
            ))}
          </select>
        </div>
        
        <div className="mb-3">
          <label className="block text-[12px] text-[#AAAAAA] mb-1">アカウントID</label>
          <input
            type="text"
            value={accountId}
            onChange={(e) => setAccountId(e.target.value)}
            placeholder="@username または URL"
            className="w-full p-2 border border-[#DDDDDD] rounded text-[14px] text-[#313131]"
          />
        </div>
        
        <button
          onClick={handleAddSnsLink}
          className="w-full bg-yellow-400 text-black font-medium py-3 rounded-full max-w-xs mx-auto block"
        >
          追加する
        </button>
      </div>
    </div>
  );
};

export default SnsLinkEdit;
