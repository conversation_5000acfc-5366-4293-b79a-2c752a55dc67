'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import CatLoadingAnimation from '@/components/CatLoadingAnimation/CatLoadingAnimation';

export default function DraftListRedirectPage() {
  const router = useRouter();
  const { user, isLoaded } = useUser();

  useEffect(() => {
    if (isLoaded) {
      if (user) {
        // ログイン済みの場合、ユーザー名を取得して新しいURL形式にリダイレクト
        const fetchUserAndRedirect = async () => {
          try {
            const response = await fetch(`/api/getUser?user_ID=${user.id}`);
            if (response.ok) {
              const userData = await response.json();
              const username = userData.username;
              if (username) {
                router.replace(`/${username}/draftList`);
              } else {
                router.replace('/');
              }
            } else {
              router.replace('/');
            }
          } catch (error) {
            console.error('ユーザー情報取得エラー:', error);
            router.replace('/');
          }
        };

        fetchUserAndRedirect();
      } else {
        // ログインしていない場合、ログインページにリダイレクト
        router.replace('/sign-in?redirect=/draftList');
      }
    }
  }, [user, isLoaded, router]);

  // ローディング中の表示
  return (
    <div className="relative min-h-screen flex flex-col max-w-[500px] mx-auto items-center justify-center overflow-hidden">
      {/* 背景オーバーレイ */}
      <div className="absolute inset-0 -z-10 bg-[#8B4513]/80 w-full" style={{ height: '200%' }}></div>
      
      {/* ヘッダー */}
      <div className="flex justify-between max-w-[500px] h-[52px] items-center relative bg-white shadow-[0px_0px_4px_#00000040] w-full">
        <div className="flex max-w-[250px] h-[52px] items-center gap-[8px] pl-[16px] pr-0 py-0 relative bg-white">
          <p className="relative flex-[0_0_auto]">下書き一覧</p>
        </div>
      </div>

      {/* メインコンテンツ */}
      <div className="w-full bg-white" style={{ height: 'auto', flex: '1 1 0%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        <div className="flex-1 flex items-center justify-center">
          <CatLoadingAnimation />
        </div>
      </div>
    </div>
  );
}
