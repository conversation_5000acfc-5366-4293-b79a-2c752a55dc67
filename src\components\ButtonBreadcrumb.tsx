

import React from "react";

interface Props {
  className: any;
  category: string,
  subCategory: string
}

export const ButtonBreadcrumb = ({ className,category,subCategory }: Props): JSX.Element => {
  return (
    <div className={`inline-flex items-start gap-[6px] pl-[16px] pr-0 pt-[16px] pb-0 relative ${className}`}>
      <div className="relative w-fit mt-[-1.00px] [font-family:'Roboto',Helvetica] font-normal text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap">
        {category}
      </div>
      <div className="relative w-fit mt-[-1.00px]  text-black-1 text-[12px] tracking-[0] leading-[normal] whitespace-nowrap">
        &gt;
      </div>
      <div className="relative w-fit mt-[-1.00px]  text-font-link text-[12px] tracking-[0] leading-[normal] underline whitespace-nowrap">
      {subCategory}
      </div>
    </div>
  );
};
