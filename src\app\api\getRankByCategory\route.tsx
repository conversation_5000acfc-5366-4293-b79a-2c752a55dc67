import { NextRequest } from "next/server";
import { PrismaClient } from "@prisma/client";

// Prismaクライアントのインスタンスを作成
const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // リクエストURLからuser_IDを取得
    const { searchParams } = new URL(request.url);
    const user_ID = searchParams.get("user_ID");
    const categoryID = searchParams.get("categoryID");
    
    if (!user_ID) {
      return new Response(JSON.stringify({ error: 'user_IDが提供されていません' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }
    
    if (!categoryID) {
      return new Response(JSON.stringify({ error: 'categoryIDが提供されていません' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // まず、指定されたカテゴリに属するすべてのサブカテゴリを取得
    const subcategories = await prisma.category.findMany({
      where: {
        user_ID: String(user_ID),
        parent_ID: categoryID
      },
      select: {
        id: true
      }
    });

    // サブカテゴリのIDリストを作成
    const subcategoryIds = subcategories.map(subcat => subcat.id);
    
    console.log(`カテゴリID ${categoryID} に属するサブカテゴリ: ${subcategoryIds.join(', ')}`);

    // サブカテゴリに関連する公開済みランキングを取得
    let rankings: any[] = [];

    if (subcategoryIds.length > 0) {
      rankings = await prisma.ranking.findMany({
        where: {
          user_ID: String(user_ID),
          status: 'PUBLISHED', // 公開済みのみ取得
          subCategory_ID: {
            in: subcategoryIds
          }
        },
        select: {
          id: true,
          ranking_ID: true,
          ranking_title: true,
          ranking_description: true,
          amazon_url: true,
          rakuten_url: true,
          yahoo_url: true,
          qoo10_url: true,
          official_url: true,
          thumbnail_image: true,
          recommend_rate: true,
          subCategory_ID: true,
          user_ID: true,
          domain: true,
          order: true,
          status: true, // ステータスも取得
          created_at: true,
          updated_at: true
        },
        orderBy: {
          order: 'asc'
        }
      });

      console.log(`取得した公開済みランキング数: ${rankings.length}`);
    } else {
      console.log(`カテゴリID ${categoryID} に属するサブカテゴリが見つかりませんでした`);
    }

    // データをJSON形式で返す
    return new Response(JSON.stringify(rankings), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error("Error fetching rankings by category:", error);
    // エラーハンドリング - より詳細なエラー情報を返す
    return new Response(JSON.stringify({ 
      error: 'データの取得に失敗しました', 
      details: error instanceof Error ? error.message : String(error)
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
