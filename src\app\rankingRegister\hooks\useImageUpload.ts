import { useState, useRef } from 'react';
import { fileToBase64 } from '../../../utils/imageUtils';

/**
 * 画像アップロード処理のためのカスタムフック（トリミング機能付き）
 */
export function useImageUpload(userId: string) {
  const [images, setImages] = useState<string[]>(['', '', '', '']);
  const [imageFiles, setImageFiles] = useState<File[]>([]);

  // トリミング機能用の状態
  const [showCropper, setShowCropper] = useState<boolean>(false);
  const [tempImage, setTempImage] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
  const fileInputRefs = useRef<(HTMLInputElement | null)[]>([null, null, null, null]);

  /**
   * 画像ファイル選択を開始する
   */
  const pickFile = (index: number) => {
    const fileInput = fileInputRefs.current[index];
    if (fileInput) {
      fileInput.click();
    }
  };

  /**
   * 画像選択ハンドラー（トリミング機能付き）
   */
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // ファイル入力をリセット（同じファイルを選択した場合でもイベントが発生するように）
      if (fileInputRefs.current[index]) {
        fileInputRefs.current[index]!.value = '';
      }

      // ファイルをBase64に変換してトリミング画面を表示
      fileToBase64(file).then(imageUrl => {
        setTempImage(imageUrl);
        setCurrentImageIndex(index);
        setShowCropper(true);
      }).catch(error => {
        console.error(`Error converting file to Base64 for index ${index}:`, error);
      });
    }
  };

  /**
   * クロップ確定時の処理
   */
  const handleCropComplete = (croppedImage: string) => {
    const newImages = [...images];
    newImages[currentImageIndex] = croppedImage;
    setImages(newImages);

    // Base64データをBlobに変換してFileオブジェクトを作成
    try {
      const byteCharacters = atob(croppedImage.split(',')[1]);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'image/jpeg' });
      const file = new File([blob], `cropped-image-${currentImageIndex}.jpg`, { type: 'image/jpeg' });

      const newImageFiles = [...imageFiles];
      newImageFiles[currentImageIndex] = file;
      setImageFiles(newImageFiles);
    } catch (error) {
      console.error(`Error processing cropped image for index ${currentImageIndex}:`, error);
    }

    setShowCropper(false);
    setTempImage(null);
  };

  /**
   * クロップキャンセル時の処理
   */
  const handleCropCancel = () => {
    setShowCropper(false);
    setTempImage(null);
  };

  /**
   * 画像削除ハンドラー
   */
  const handleRemoveImage = (index: number, e?: React.MouseEvent) => {
    // イベントの伝播を停止
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    const newImages = [...images];
    const newImageFiles = [...imageFiles];
    
    // 削除する画像の位置のデータをクリア
    newImages[index] = '';
    if (newImageFiles[index]) {
      newImageFiles[index] = undefined as unknown as File;
    }
    
    setImages(newImages);
    setImageFiles(newImageFiles);
  };

  /**
   * 画像のアップロード処理
   */
  const uploadImages = async () => {
    // 編集モードで既存の画像がある場合の処理
    const existingImages = images.filter(img => {
      // Base64でない画像は既存の画像と判断
      return img && !img.startsWith('data:image');
    });
    

    
    // アップロードが必要な画像の処理
    const uploadPromises = imageFiles.map(async (file, index) => {
      // 既存の画像がある場合はそのまま返す
      if (images[index] && !images[index].startsWith('data:image')) {
        return images[index];
      }

      // ファイルがない場合は空文字を返す
      if (!file) {
        return '';
      }
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('user_ID', userId);
      formData.append('imageType', 'ranking'); // ランキング用の画像であることを明示
      
      try {
        // 絶対URLを使用してAPIを呼び出す
        let baseUrl = '';
        if (typeof window !== 'undefined') {
          baseUrl = window.location.origin;
        }
        const apiUrl = `${baseUrl}/api/uploadImage`;
        
        // 独自のAPIエンドポイントを使用して画像をアップロード
        const response = await fetch(apiUrl, {
          method: 'POST',
          body: formData,
        });
        

        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('画像アップロードエラー:', errorData);
          throw new Error(errorData.error || '画像のアップロードに失敗しました');
        }
        
        const data = await response.json();
        return data.url; // 画像のURLを返す
      } catch (error) {
        console.error('画像アップロードエラー:', error);
        return '';
      }
    });
    
    const uploadedUrls = await Promise.all(uploadPromises);
    const filteredUrls = uploadedUrls.filter(url => url !== '');
    
    // 有効な画像がない場合は、既存の画像を返す
    if (filteredUrls.length === 0 && existingImages.length > 0) {
      return existingImages;
    }
    
    return filteredUrls;
  };
  
  return {
    images,
    setImages,
    imageFiles,
    setImageFiles,
    handleImageChange,
    handleRemoveImage,
    uploadImages,
    // トリミング機能用の追加プロパティ
    pickFile,
    fileInputRefs,
    showCropper,
    tempImage,
    handleCropComplete,
    handleCropCancel
  };
}
