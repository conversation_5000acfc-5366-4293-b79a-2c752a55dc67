'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Header } from '@/components/Header/Header';
import { Footer } from '@/components/Footer';
import { HeaderItems } from '@/components/HeaderItems';

export default function ChangePasswordPage() {
  const router = useRouter();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // パスワード更新処理
  const handleUpdatePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    // 入力検証
    if (!currentPassword || !newPassword || !confirmPassword) {
      setError('すべての項目を入力してください');
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setError('新しいパスワードと確認用パスワードが一致しません');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // ここにAPIリクエストを実装
      // 例: await updatePassword(currentPassword, newPassword);
      
      // 成功時の処理
      alert('パスワードが正常に更新されました');
      router.push('/settings');
    } catch (err) {
      console.error('パスワード更新エラー:', err);
      setError('パスワードの更新に失敗しました。もう一度お試しください。');
    } finally {
      setIsLoading(false);
    }
  };

  // パスワードを忘れた場合の処理
  const handleForgotPassword = () => {
    router.push('/forgot-password');
  };

  return (
    <div className="max-w-[500px] mx-auto flex flex-col min-h-screen relative bg-white">
      <HeaderItems text="パスワードを変更" />

      {/* メインコンテンツ */}
      <main className="flex-1 px-10 py-6">
        <form onSubmit={handleUpdatePassword}>
          {/* 現在のパスワード */}
          <div className="mb-6">
            <label 
              htmlFor="current-password" 
              className="block text-[14px] text-[#313131] mb-2"
            >
              現在のパスワード
            </label>
            <input
              id="current-password"
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              className="w-full p-3 border border-[#DDDDDD] rounded text-[#313131] bg-white"
              placeholder=""
            />
          </div>

          {/* 新しいパスワード */}
          <div className="mb-6">
            <label 
              htmlFor="new-password" 
              className="block text-[14px] text-[#313131] mb-2"
            >
              新しいパスワード
            </label>
            <input
              id="new-password"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="w-full p-3 border border-[#DDDDDD] rounded text-[#313131] bg-white"
              placeholder=""
            />
          </div>

          {/* パスワードを確認 */}
          <div className="">
            <label 
              htmlFor="confirm-password" 
              className="block text-[14px] text-[#313131] mb-2"
            >
              パスワードを確認
            </label>
            <input
              id="confirm-password"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full p-3 border border-[#DDDDDD] rounded text-[#313131] bg-white"
              placeholder=""
            />
          </div>

          {/* エラーメッセージ */}
          {error && (
            <div className="mb-4 text-red-500 text-[14px]">
              {error}
            </div>
          )}

          {/* 更新ボタン */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-[#E63B5F] text-white py-3 rounded-full mb-4 mt-14 hover:bg-[#DD0F2B]"
          >
            {isLoading ? '処理中...' : 'パスワードを更新'}
          </button>

          {/* パスワードを忘れた場合 */}
          <div className="text-center">
            <button
              type="button"
              onClick={handleForgotPassword}
              className="text-[14px] text-[#AAAAAA]"
            >
              パスワードを忘れた場合はこちら
            </button>
          </div>
        </form>
      </main>

      {/* フッター */}
      <Footer className="!flex-[0_0_auto] !bg-white" />
    </div>
  );
}
