"use client";

import { useState, useEffect } from "react";
import { RankingData, UserData, SavedRankingItem } from "../types";
import { DEFAULT_USER_DATA, DEFAULT_LOGO_IMAGE, SAVED_RANKING_CATEGORY, ERROR_MESSAGES } from "../constants";

interface UseRankingDataResult {
  ranking: RankingData[] | null;
  userData: UserData | null;
  isLoading: boolean;
  error: string | null;
}

export const useRankingData = (userId: string, rankingId: string): UseRankingDataResult => {
  const [ranking, setRanking] = useState<RankingData[] | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId || !rankingId) {
      setError(ERROR_MESSAGES.INVALID_URL);
      setIsLoading(false);
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      // First, try to get data from saved rankings
      try {
        const savedRankings = localStorage.getItem('savedRankings');
        if (savedRankings) {
          const parsedRankings: SavedRankingItem[] = JSON.parse(savedRankings);
          const savedRanking = parsedRankings.find(item => item.id === rankingId);
          
          if (savedRanking) {
            // Found in saved rankings
            const formattedRanking: RankingData[] = [{
              ranking_ID: savedRanking.id,
              ranking_title: savedRanking.title,
              ranking_description: savedRanking.description,
              thumbnail_image: [savedRanking.imageUrl],
              recommend_rate: savedRanking.rating,
              category_name: SAVED_RANKING_CATEGORY,
              category_id: '',
              subcategory_name: '',
              logo_image_myrank: DEFAULT_LOGO_IMAGE
            }];
            
            setRanking(formattedRanking);
            
            // Set dummy user data for saved rankings
            setUserData(DEFAULT_USER_DATA);
            
            setIsLoading(false);
            return;
          }
        }
      } catch (err) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error reading saved rankings:', err);
        }
      }

      // If not found in saved rankings, fetch from API
      try {
        // Fetch ranking data
        const rankingResponse = await fetch(`/api/getRank?user_ID=${userId}&ranking_ID=${rankingId}`);
        if (!rankingResponse.ok) {
          throw new Error(`ランキングデータの取得に失敗しました。ステータス: ${rankingResponse.status}`);
        }
        
        const rankingData = await rankingResponse.json();
        if (!rankingData || rankingData.error) {
          throw new Error(rankingData?.error || ERROR_MESSAGES.RANKING_NOT_FOUND);
        }
        
        setRanking([rankingData]);

        // Fetch user data
        const userResponse = await fetch(`/api/getUser?user_ID=${userId}`);
        if (!userResponse.ok) {
          throw new Error(`ユーザー情報の取得に失敗しました。ステータス: ${userResponse.status}`);
        }
        
        const userDataResult = await userResponse.json();
        if (!userDataResult || userDataResult.error) {
          throw new Error(userDataResult?.error || ERROR_MESSAGES.USER_NOT_FOUND);
        }
        
        setUserData(userDataResult);

        // Try to get auth token and fetch profile data
        try {
          const token = localStorage.getItem('clerkToken') || 'dummy-auth-token';
          const profileResponse = await fetch(`/api/profile?user_ID=${userId}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (profileResponse.ok) {
            const profileData = await profileResponse.json();
            if (profileData) {
              setUserData(prev => ({
                ...prev,
                profileImage: profileData.profileImage,
                name: prev?.name || profileData.name
              }));
            }
          }
        } catch (profileError) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Profile fetch error:', profileError);
          }
        }

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.FETCH_ERROR;
        setError(errorMessage);
        
        if (process.env.NODE_ENV === 'development') {
          console.error('Data fetch error:', err);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [userId, rankingId]);

  return { ranking, userData, isLoading, error };
};