import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: Request) {
  try {
    // リクエストボディをJSONとしてパース
    const body = await request.json();

    // ユーザーIDが提供されていない場合はエラー
    if (!body.user_ID) {
      return NextResponse.json({ error: 'ユーザーIDが必要です' }, { status: 400 });
    }

    // 日付フィールドを有効な Date オブジェクトとして変換
    const createdAt = body.created_at ? new Date(body.created_at) : new Date();
    const updatedAt = body.updated_at ? new Date(body.updated_at) : new Date();

    // カテゴリ数を取得
    const categoryCount = await prisma.category.count({
      where: {
        user_ID: body.user_ID,
      },
    });

    // 既存のカテゴリを検索
    const existingCategory = await prisma.category.findFirst({
      where: { user_ID: body.user_ID },
    });

    // ユーザー情報をアップサート
    await prisma.user.upsert({
      where: { user_ID: body.user_ID },
      update: {},
      create: {
        user_ID: body.user_ID,
        username: `user-${body.user_ID}`,
        account_type: "email",
        email: "<EMAIL>",
        name: "Test User",
        setup_completed: false, // setup_completedフィールドを追加
      },
    });

    // カテゴリIDとサブカテゴリIDを生成
    const category_ID = `category_${body.user_ID}_${categoryCount}`;
    const subCategory_ID = `subCategory_${body.user_ID}_${categoryCount}`;

    // カテゴリを作成
    if (category_ID) {
      const createdCategory = await prisma.category.create({
        data: {
          user_ID: body.user_ID,
          domain: "example.com",
          category_ID: category_ID,
          category_name: body.category,
          parent_ID: "",
          created_at: createdAt,
          updated_at: updatedAt,
        },
      });

      // サブカテゴリを作成
      if (subCategory_ID) {
        await prisma.category.create({
          data: {
            user_ID: body.user_ID,
            domain: "example.com",
            category_ID: subCategory_ID,
            category_name: body.subCategory,
            parent_ID: createdCategory.id, // 親カテゴリの自動生成されたIDを使用
            created_at: createdAt,
            updated_at: updatedAt,
          },
        });
      }
    }

    // 成功した場合のレスポンスを返す
    return NextResponse.json(
      { success: true },
      {
        status: 201,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } catch (error) {
    console.error('Error during Prisma operation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to insert data' },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    }
  );
}